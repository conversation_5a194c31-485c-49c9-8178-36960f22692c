package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.analysis.*;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.TimeTypeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.to.AnswerShareTo;
import com.cas.nc.questionnaire.common.to.ReportShareTo;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.ValidateUtils;
import com.cas.nc.questionnaire.common.vo.analysis.*;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.cas.nc.questionnaire.dao.query.QstTitleQuery;
import com.cas.nc.questionnaire.server.AnalysisServer;
import com.cas.nc.questionnaire.server.DownloadAnswerServer;
import com.cas.nc.questionnaire.server.DownloadWordServer;
import com.cas.nc.questionnaire.server.mapstruct.*;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import com.cas.nc.questionnaire.service.QstTitleService;
import com.cas.nc.questionnaire.service.ShareSettingService;
import com.lowagie.text.Document;
import com.lowagie.text.PageSize;
import com.lowagie.text.rtf.RtfWriter2;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.*;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.isListChecked;
import static com.cas.nc.questionnaire.common.enums.TimeTypeEnum.*;
import static com.cas.nc.questionnaire.common.enums.TitleDimensionalTypeEnum.isJuZhen;
import static com.cas.nc.questionnaire.common.enums.TitleDimensionalTypeEnum.isXuanZe;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.*;
import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_TITLE_SPLIT;
import static com.cas.nc.questionnaire.common.utils.DateUtil.*;

@RestController
@RequestMapping("/questionnaire/analysis")
public class AnalysisController extends BaseController {

    @Resource
    private AnalysisServer analysisServer;
    @Resource
    private DownloadWordServer downloadWordServer;
    @Resource
    private DownloadAnswerServer downloadAnswerServer;
    @Resource
    private QstTitleService qstTitleService;
    @Resource
    private ShareSettingService shareSettingService;
    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;

    @RequestMapping(value = "/report", method = {RequestMethod.POST})
    public ApiReturnResult report(@RequestBody AnalysisReportReqVo vo) {
        logger.info("AnalysisController.report param[{}]", JSONUtil.toJSONString(vo));
        Assert.notNull(vo, "param");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        AnalysisReportReqDto reqDto = ReportConverter.INSTANCE.to(vo);
        Long loginUserId = getUserIdNoException();

        ReportShareTo shareTo = shareSettingService.getReportShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
        reqDto.setUserId(shareTo.getUserId());

        AnalysisReportRepDto repDto = analysisServer.report(reqDto);

        AnalysisReportRepVo repVo = ReportConverter.INSTANCE.to(repDto);
        repVo.setIndependentShowFlag(shareTo.getIndependentShowFlag());

        result.setData(repVo);

        logger.info("AnalysisController.report result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/listfillblank", method = {RequestMethod.POST})
    public ApiReturnResult listFillBlank(@RequestBody AnalysisListFillBlankReqVo vo) {
        logger.info("AnalysisController.listFillBlank param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        AnalysisListFillBlankReqDto reqDto = ListFillBlankConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());
        validateParam(ListFillBlankConverter.INSTANCE.to(reqDto));

        AnalysisListFillBlankRepDto repDto = analysisServer.listFillBlank(reqDto);

        AnalysisListFillBlankRepVo repVo = ListFillBlankConverter.INSTANCE.to(repDto);
        repVo.setPage(reqDto.getPage());
        repVo.setPageSize(reqDto.getPageSize());

        result.setData(repVo);

        logger.info("AnalysisController.listFillBlank result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/crossanalysis", method = {RequestMethod.POST})
    public ApiReturnResult crossAnalysis(@RequestBody AnalysisReportCrossAnalysisReqVo vo) {
        logger.info("AnalysisController.crossAnalysis param[{}]", JSONUtil.toJSONString(vo));

        validateParam(vo);
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        AnalysisReportCrossAnalysisReqDto reqDto = CrossAnalysisConverter.INSTANCE.to(vo);
        Long loginUserId = getUserIdNoException();

        ReportShareTo shareTo = shareSettingService.getReportShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
        reqDto.setUserId(shareTo.getUserId());

        AnalysisReportCrossAnalysisRepDto repDto = analysisServer.crossAnalysis(reqDto);
        AnalysisReportCrossAnalysisRepVo repVo = CrossAnalysisConverter.INSTANCE.to(repDto);
        repVo.setIndependentShowFlag(shareTo.getIndependentShowFlag());
        result.setData(repVo);

        logger.info("AnalysisController.crossAnalysis result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/downloadword", method = RequestMethod.POST)
    public ApiReturnResult downloadWord(@RequestBody DownloadWordReqVo vo) throws IOException {
        logger.info("AnalysisController.downloadWord param[{}]", JSONUtil.toJSONString(vo));
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getQuestionnaireId(), "questionnaireId");
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        DownloadWordReqDto reqDto = DownloadWordConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());
//        reqDto.setUserId(776765372559829947L);
        QstQuestionnaireInfoPo questionnaireInfoPo = downloadWordServer.getQuestionnaire(reqDto.getQuestionnaireId(), reqDto.getUserId());
        reqDto.setTitle(questionnaireInfoPo.getTitle());

        String fileName = URLEncoder.encode(questionnaireInfoPo.getTitle(), "UTF-8");
        OutputStream outputStream = getHttpServletResponse().getOutputStream();
        getHttpServletResponse().setContentType("multipart/form-data");
        getHttpServletResponse().setHeader("content-disposition", "attachment;filename=" + fileName + ".doc");

        Document doc = new Document(PageSize.A4);
        try {
            // 创建word文档,并设置纸张的大小
            //建立一个书写器与document对象关联，通过书写器可以将文档写入到输出流中
            RtfWriter2.getInstance(doc, outputStream);
            doc.open();
            downloadWordServer.writeWordDoc(reqDto, doc);
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("AnalysisController.downloadWord error[{}]", e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        } finally {
            doc.close();
            if (outputStream != null) {
                outputStream.close();
            }
        }
        logger.info("AnalysisController.downloadWord result[{}]", result);

        return result;
    }

    @RequestMapping(value = "/downloadanswer", method = RequestMethod.GET)
    public ApiReturnResult downloadAnswer(DownloadAnswerReqVo vo) throws IOException {
        logger.info("AnalysisController.downloadAnswer param[{}]", JSONUtil.toJSONString(vo));
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getQuestionnaireId(), "questionnaireId");
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        DownloadAnswerReqDto reqDto = DownloadAnswerConverter.INSTANCE.to(vo);
        Long loginUserId = getUserIdNoException();

        AnswerShareTo shareTo = shareSettingService.getAnswerShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
        reqDto.setUserId(shareTo.getUserId());

        QstQuestionnaireInfoPo questionnaireInfoPo = downloadWordServer.getQuestionnaire(reqDto.getQuestionnaireId(), reqDto.getUserId());
        reqDto.setBizId(questionnaireInfoPo.getBizId());
        reqDto.setBizNo(questionnaireInfoPo.getBizNo());

        String fileName = URLEncoder.encode(questionnaireInfoPo.getTitle(), "UTF-8");
        OutputStream outputStream = getHttpServletResponse().getOutputStream();
        getHttpServletResponse().setContentType("application/vnd.ms-excel");
        getHttpServletResponse().setHeader("content-disposition", "attachment;filename=" + fileName + ".xls");

        try {
            outputStream.flush();
            Workbook workbook = downloadAnswerServer.downloadAnswer(reqDto);
            workbook.write(outputStream);
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {

            logger.error("AnalysisController.downloadWord error[{}]", e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        } finally {

            if (outputStream != null) {
                outputStream.close();
            }
        }
        logger.info("AnalysisController.downloadAnswer result[{}]", result);

        return result;
    }

    @RequestMapping(value = "/downloadfillblank", method = RequestMethod.GET)
    public ApiReturnResult downloadFillBlank(DownloadFillBlankReqVo vo) throws IOException {
        logger.info("AnalysisController.downloadFillBlank param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        DownloadFillBlankReqDto reqDto = DownloadFillBlankConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());
        validateParam(reqDto);

        QstQuestionnaireInfoPo questionnairePo = downloadWordServer.getQuestionnaire(reqDto.getQuestionnaireId(), reqDto.getUserId());

        OutputStream outputStream = getHttpServletResponse().getOutputStream();
        String fileName = URLEncoder.encode(constructFileName(reqDto, questionnairePo.getTitle()), "UTF-8");
        getHttpServletResponse().setContentType("application/vnd.ms-excel");
        getHttpServletResponse().setHeader("content-disposition", "attachment;filename=" + fileName + ".xls");

        try {
            outputStream.flush();
            Workbook workbook = downloadAnswerServer.downloadFillBlank(reqDto);
            workbook.write(outputStream);

        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {

            logger.error("AnalysisController.downloadFillBlank error[{}]", e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        } finally {

            if (outputStream != null) {
                outputStream.close();
            }
        }
        logger.info("AnalysisController.downloadFillBlank result[{}]", result);

        return result;
    }

    @RequestMapping(value = "/sourceanalysis", method = {RequestMethod.POST})
    public ApiReturnResult sourceAnalysis(@RequestBody SourceAnalysisReqVo vo) {
        logger.info("AnalysisController.sourceAnalysis param[{}]", JSONUtil.toJSONString(vo));
        ValidateUtils.validateNotNullExclude(vo, "userId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        SourceAnalysisReqDto reqDto = SourceAnalysisConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        SourceAnalysisRepDto repDto = analysisServer.sourceAnalysis(reqDto);
        result.setData(SourceAnalysisConverter.INSTANCE.to(repDto));

        logger.info("AnalysisController.sourceAnalysis result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/provinceanalysis", method = {RequestMethod.POST})
    public ApiReturnResult provinceAnalysis(@RequestBody ProvinceAnalysisReqVo vo) {
        logger.info("AnalysisController.provinceAnalysis param[{}]", JSONUtil.toJSONString(vo));
        ValidateUtils.validateNotNullExclude(vo, "userId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ProvinceAnalysisReqDto reqDto = ProvinceAnalysisConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        ProvinceAnalysisRepDto repDto = analysisServer.provinceAnalysis(reqDto);
        result.setData(ProvinceAnalysisConverter.INSTANCE.to(repDto));

        logger.info("AnalysisController.provinceAnalysis result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/timeanalysis", method = {RequestMethod.POST})
    public ApiReturnResult timeAnalysis(@RequestBody TimeAnalysisReqVo vo) {
        logger.info("AnalysisController.timeAnalysis param[{}]", JSONUtil.toJSONString(vo));
        ValidateUtils.validateNotNullExclude(vo, "userId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        TimeAnalysisReqDto reqDto = TimeAnalysisConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());
        TimeTypeEnum.toEnum(vo.getTimeType());
        validateDate(vo);

        TimeAnalysisRepDto repDto = analysisServer.timeAnalysis(reqDto);
        result.setData(TimeAnalysisConverter.INSTANCE.to(repDto));

        logger.info("AnalysisController.timeAnalysis result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/listchecked", method = {RequestMethod.POST})
    public ApiReturnResult listChecked(@RequestBody ListCheckedReqVo vo) {
        logger.info("AnalysisController.listChecked param[{}]", JSONUtil.toJSONString(vo));
        ValidateUtils.validateNotNullExclude(vo, "userId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ListCheckedReqDto reqDto = ListCheckedConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());
        Assert.isTrue(isListChecked(vo.getRuleType()), RULE_TYPE_NOT_EXIST);

        ListCheckedRepDto repDto = analysisServer.listChecked(reqDto);
        result.setData(repDto);

        logger.info("AnalysisController.listChecked result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/customreport", method = {RequestMethod.POST})
    public ApiReturnResult customReport(@RequestBody CustomReportReqVo reqVo) {
        logger.info("AnalysisController.customReport param[{}]", JSONUtil.toJSONString(reqVo));
        ValidateUtils.validateNotNullExclude(reqVo, "userId", "sharePwd");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        CustomReportReqDto reqDto = ReportConverter.INSTANCE.to(reqVo);

        Long loginUserId = getUserIdNoException();
        ReportShareTo shareTo = shareSettingService.getReportShareTo(reqVo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
        reqDto.setUserId(shareTo.getUserId());

        AnalysisReportRepDto repDto = analysisServer.customReport(reqDto);

        AnalysisReportRepVo repVo = ReportConverter.INSTANCE.to(repDto);
        repVo.setIndependentShowFlag(shareTo.getIndependentShowFlag());

        result.setData(repVo);

        logger.info("AnalysisController.customReport result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    public void validateParam(DownloadFillBlankReqDto reqDto) {
        Assert.notNull(reqDto, "param");
        Assert.notNull(reqDto.getQuestionnaireId(), "questionnaireId");
        Assert.notNull(reqDto.getSerialNumber(), "serialNumber");
        if (reqDto.getRowNumber() != null) {
            Assert.isTrue(reqDto.getRowNumber() > 0, "rowNumber", MUST_BE_GREATER_THAN_ZERO);
        }
        if (reqDto.getColumnNumber() != null) {
            Assert.isTrue(reqDto.getColumnNumber() > 0, "columnNumber", MUST_BE_GREATER_THAN_ZERO);
        }

        QstTitleQuery query = new QstTitleQuery();
        query.setUserId(reqDto.getUserId());
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setSerialNumber(reqDto.getSerialNumber());
        query.setTableColumns("type");

        QstTitlePo po = qstTitleService.selectOne(query);
        Assert.notNull(po, TITLE_NOT_EXIST);

        boolean fillBlankType = isFillBlank(po.getType());
        Assert.isTrue(fillBlankType, TITLE_TYPE_NOT_FILL_BLANK);
        if (isTwoDimensionalFillBlank(po.getType())) {
            boolean flag = reqDto.getRowNumber() != null && reqDto.getColumnNumber() != null;
            Assert.isTrue(flag, TITLE_TYPE_NOT_MATCH);
        } else if (DUOXIANGDANHANGTIANKONG.key().intValue() == po.getType().intValue()) {
            Assert.notNull(reqDto.getRowNumber(), TITLE_TYPE_NOT_MATCH);
        } else {
            Assert.isTrue(reqDto.getRowNumber() == null, TITLE_TYPE_NOT_MATCH);
            Assert.isTrue(reqDto.getColumnNumber() == null, TITLE_TYPE_NOT_MATCH);
        }
    }

    private String constructFileName(DownloadFillBlankReqDto reqDto, String title) {

        StringBuffer buffer = new StringBuffer();
        buffer.append(title);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append("填空下载");
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append("第");
        buffer.append(reqDto.getSerialNumber());
        if (reqDto.getRowNumber() != null) {
            buffer.append(ANSWER_TITLE_SPLIT);
            buffer.append(reqDto.getSerialNumber());
        }
        return buffer.toString();
    }

    private void validateParam(AnalysisReportCrossAnalysisReqVo vo) {
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getHideNull(), "hideNull");
        Assert.notNull(vo.getCrossAnalysisDependentVariableList(), "crossAnalysisDependentVariableList");
        Assert.notNull(vo.getIndependentVariableTitleList(), "independentVariableTitleList");
        Assert.isTrue(vo.getCrossAnalysisDependentVariableList().size() < 3, CROSS_ANALYSIS_VARIABLE_LIMIT);
        Assert.isTrue(vo.getIndependentVariableTitleList().size() < 3, CROSS_ANALYSIS_VARIABLE_LIMIT);

        vo.getCrossAnalysisDependentVariableList().forEach(v -> {
            {
                Assert.notNull(v.getDependentVariableType(), "dependentVariableType");

                if (isXuanZe(v.getDependentVariableType())) {
                    Assert.notNull(v.getDependentVariableTitle(), "dependentVariableTitle");
                } else if (isJuZhen(v.getDependentVariableType())) {
                    Assert.notNull(v.getDependentVariableTitle(), "dependentVariableTitle");
                    Assert.notNull(v.getDependentVariableOption(), "dependentVariableOption");
                } else {
                    throw new ServerException(VALIDATE_PARAM_EXCEPTION);
                }

            }
        });

        Set<Integer> independentVariableTitleSet = new HashSet<>(vo.getIndependentVariableTitleList());
        if (independentVariableTitleSet.size() != vo.getIndependentVariableTitleList().size()) {
            throw new ServerException(TITLE_REPEAT);
        }

        List<Integer> dependentVariableList = vo.getCrossAnalysisDependentVariableList().stream().map(CrossAnalysisDependentVariableVo::getDependentVariableTitle).collect(Collectors.toList());

        Set<Integer> dependentVariableSet = new HashSet<>(dependentVariableList);
        if (dependentVariableList.size() != dependentVariableSet.size()) {
            throw new ServerException(TITLE_REPEAT);
        }

        dependentVariableList.retainAll(vo.getIndependentVariableTitleList());
        if (!CollectionUtils.isEmpty(dependentVariableList)) {
            throw new ServerException(TITLE_REPEAT);
        }
    }

    private void validateDate(TimeAnalysisReqVo vo) {
        if (isDay(vo.getTimeType())) {
            Date beginTime = parseDate(YYYYMMDD, vo.getBeginTime());
            Assert.notNull(beginTime, DATE_FORMAT_EXCEPTION);
            Date endTime = parseDate(YYYYMMDD, vo.getEndTime());
            Assert.notNull(endTime, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(endTime.getTime() >= beginTime.getTime(), BEGIN_END_TIME_COMPARE_ERROR);
            Assert.isTrue(vo.getBeginTime().length() == 10, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(vo.getEndTime().length() == 10, DATE_FORMAT_EXCEPTION);
        } else if (isWeek(vo.getTimeType())) {
            Assert.isTrue(vo.getBeginTime().length() == 6, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(vo.getEndTime().length() == 6, DATE_FORMAT_EXCEPTION);
            Date beginTime = parseDate(YYYY, vo.getBeginTime().substring(0, 4));
            Assert.notNull(beginTime, DATE_FORMAT_EXCEPTION);
            Date endTime = parseDate(YYYY, vo.getEndTime().substring(0, 4));
            Assert.notNull(endTime, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(endTime.getTime() >= beginTime.getTime(), BEGIN_END_TIME_COMPARE_ERROR);
            Integer beginWeek = Integer.valueOf(vo.getBeginTime().substring(4, 6));
            Integer endWeek = Integer.valueOf(vo.getEndTime().substring(4, 6));
            Assert.isTrue(endWeek >= beginWeek, BEGIN_END_TIME_COMPARE_ERROR);

        } else if (isMonth(vo.getTimeType())) {
            Date beginTime = parseDate(YYYYMM, vo.getBeginTime());
            Assert.notNull(beginTime, DATE_FORMAT_EXCEPTION);
            Date endTime = parseDate(YYYYMM, vo.getEndTime());
            Assert.notNull(endTime, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(endTime.getTime() >= beginTime.getTime(), BEGIN_END_TIME_COMPARE_ERROR);
            Assert.isTrue(vo.getBeginTime().length() == 7, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(vo.getEndTime().length() == 7, DATE_FORMAT_EXCEPTION);
        }
    }

    @RequestMapping(value = "/commonsourceanalysis", method = {RequestMethod.POST})
    public ApiReturnResult commonSourceAnalysis(@RequestBody SourceAnalysisReqVo vo) {
        logger.info("AnalysisController.commonSourceAnalysis param[{}]", JSONUtil.toJSONString(vo));
//        ValidateUtils.validateNotNullExclude(vo, "userId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        SourceAnalysisReqDto reqDto = SourceAnalysisConverter.INSTANCE.to(vo);
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            reqDto.setUserId(qstQuestionnaireInfoPo.getUserId());
        }
//        reqDto.setUserId(getUserId());

        SourceAnalysisRepDto repDto = analysisServer.sourceAnalysis(reqDto);
        result.setData(SourceAnalysisConverter.INSTANCE.to(repDto));

        logger.info("AnalysisController.commonSourceAnalysis result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/commonlistchecked", method = {RequestMethod.POST})
    public ApiReturnResult commonListChecked(@RequestBody ListCheckedReqVo vo) {
        logger.info("AnalysisController.commonListChecked param[{}]", JSONUtil.toJSONString(vo));
//        ValidateUtils.validateNotNullExclude(vo, "userId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ListCheckedReqDto reqDto = ListCheckedConverter.INSTANCE.to(vo);
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            reqDto.setUserId(qstQuestionnaireInfoPo.getUserId());
        }
//        reqDto.setUserId(getUserId());
        Assert.isTrue(isListChecked(vo.getRuleType()), RULE_TYPE_NOT_EXIST);

        ListCheckedRepDto repDto = analysisServer.listChecked(reqDto);
        result.setData(repDto);

        logger.info("AnalysisController.commonListChecked result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/commontimeanalysis", method = {RequestMethod.POST})
    public ApiReturnResult commonTimeAnalysis(@RequestBody TimeAnalysisReqVo vo) {
        logger.info("AnalysisController.commonTimeAnalysis param[{}]", JSONUtil.toJSONString(vo));
//        ValidateUtils.validateNotNullExclude(vo, "userId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        TimeAnalysisReqDto reqDto = TimeAnalysisConverter.INSTANCE.to(vo);
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            reqDto.setUserId(qstQuestionnaireInfoPo.getUserId());
        }
//        reqDto.setUserId(getUserId());
        TimeTypeEnum.toEnum(vo.getTimeType());
        validateDate(vo);

        TimeAnalysisRepDto repDto = analysisServer.timeAnalysis(reqDto);
        result.setData(TimeAnalysisConverter.INSTANCE.to(repDto));

        logger.info("AnalysisController.commonTimeAnalysis result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/commonprovinceanalysis", method = {RequestMethod.POST})
    public ApiReturnResult commonProvinceAnalysis(@RequestBody ProvinceAnalysisReqVo vo) {
        logger.info("AnalysisController.commonProvinceAnalysis param[{}]", JSONUtil.toJSONString(vo));
//        ValidateUtils.validateNotNullExclude(vo, "userId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ProvinceAnalysisReqDto reqDto = ProvinceAnalysisConverter.INSTANCE.to(vo);
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            reqDto.setUserId(qstQuestionnaireInfoPo.getUserId());
        }
//        reqDto.setUserId(getUserId());

        ProvinceAnalysisRepDto repDto = analysisServer.provinceAnalysis(reqDto);
        result.setData(ProvinceAnalysisConverter.INSTANCE.to(repDto));

        logger.info("AnalysisController.commonProvinceAnalysis result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/commonreport", method = {RequestMethod.POST})
    public ApiReturnResult commonReport(@RequestBody AnalysisReportReqVo vo) {
        logger.info("AnalysisController.commonReport param[{}]", JSONUtil.toJSONString(vo));
        Assert.notNull(vo, "param");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        AnalysisReportReqDto reqDto = ReportConverter.INSTANCE.to(vo);
//        Long loginUserId = getUserIdNoException();
        ReportShareTo shareTo = null;
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            Long loginUserId = qstQuestionnaireInfoPo.getUserId();
            shareTo = shareSettingService.getReportShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
            reqDto.setUserId(shareTo.getUserId());
        }

        AnalysisReportRepDto repDto = analysisServer.report(reqDto);

        AnalysisReportRepVo repVo = ReportConverter.INSTANCE.to(repDto);
        if(shareTo != null) {
            repVo.setIndependentShowFlag(shareTo.getIndependentShowFlag());
        }

        result.setData(repVo);

        logger.info("AnalysisController.commonReport result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/commoncustomreport", method = {RequestMethod.POST})
    public ApiReturnResult commonCustomReport(@RequestBody CustomReportReqVo reqVo) {
        logger.info("AnalysisController.commonCustomReport param[{}]", JSONUtil.toJSONString(reqVo));
        ValidateUtils.validateNotNullExclude(reqVo, "userId", "sharePwd");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        CustomReportReqDto reqDto = ReportConverter.INSTANCE.to(reqVo);

//        Long loginUserId = getUserIdNoException();
        ReportShareTo shareTo = null;
        if(StringUtils.isNotBlank(reqVo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(reqVo.getQuestionnaireId());
            Long loginUserId = qstQuestionnaireInfoPo.getUserId();
            shareTo = shareSettingService.getReportShareTo(reqVo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
            reqDto.setUserId(shareTo.getUserId());
        }

        AnalysisReportRepDto repDto = analysisServer.customReport(reqDto);

        AnalysisReportRepVo repVo = ReportConverter.INSTANCE.to(repDto);
        if(shareTo != null) {
            repVo.setIndependentShowFlag(shareTo.getIndependentShowFlag());
        }

        result.setData(repVo);

        logger.info("AnalysisController.commonCustomReport result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

}

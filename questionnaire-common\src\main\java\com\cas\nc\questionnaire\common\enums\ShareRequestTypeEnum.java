package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;

public enum ShareRequestTypeEnum {
    SHARE_REPORT(1, "分享统计结果"),
    SHARE_ANSWER(2, "分享答案详情"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    ShareRequestTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static ShareRequestTypeEnum toEnum(int key) {
        for (ShareRequestTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.SHARE_REQUEST_TYPE_NOT_EXIST);
    }

    public static boolean isShareReport(int key) {
        return SHARE_REPORT.key.intValue() == key;
    }

    public static boolean isShareAnswer(int key) {
        return SHARE_ANSWER.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

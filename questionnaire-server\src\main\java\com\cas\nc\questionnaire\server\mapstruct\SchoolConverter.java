package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.school.SchoolQueryProvinceRepDto;
import com.cas.nc.questionnaire.common.vo.school.SchoolQueryProvinceRepVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SchoolConverter {
    SchoolConverter INSTANCE = Mappers.getMapper(SchoolConverter.class);

    SchoolQueryProvinceRepVo to(SchoolQueryProvinceRepDto repDto);
}
package com.cas.nc.questionnaire.common.enums;

import java.util.Arrays;
import java.util.List;


public enum QuestionnaireStatusEnum {
    INIT(1, "初始化"),
    RUN(2, "运行"),
    PAUS<PERSON>(3, "暂停"),
    MARK_DELETING(4, "标记删除"),
    ELS<PERSON>(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    QuestionnaireStatusEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static boolean isUnpublished(int key){
        return !isPublished(key);
    }

    public static boolean isPublished(int key) {
        return RUN.key.intValue() == key;
    }

    public static boolean isPause(int key) {
        return PAUSE.key.intValue() == key;
    }

    public static boolean isRun(int key) {
        return RUN.key.intValue() == key;
    }

    public static boolean isInit(int key) {
        return INIT.key.intValue() == key;
    }

    public static List<Integer> MY_LIST_QUERY_LIST = Arrays.asList(
            INIT.key,
            RUN.key,
            PAUSE.key
    );

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

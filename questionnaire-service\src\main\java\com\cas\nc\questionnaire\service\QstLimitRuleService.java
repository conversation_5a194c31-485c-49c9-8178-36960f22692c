package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;
import com.cas.nc.questionnaire.dao.query.QstLimitRuleQuery;

import java.util.Date;
import java.util.List;

public interface QstLimitRuleService {
    /**
     * 数据插入
     *
     * @param qstLimitRulePo
     * @return
     */
    int insert(QstLimitRulePo qstLimitRulePo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstLimitRuleQuery query);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstLimitRulePo> selectList(QstLimitRuleQuery query);

    List<QstLimitRulePo> selectList(List<String> questionnaireList, Date beginDate, Date endDate);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstLimitRulePo selectOne(QstLimitRuleQuery query);

    /**
     * 数据插入或者更新
     *
     * @param qstLimitRulePo
     * @return
     */
    int insertUpdate(QstLimitRulePo qstLimitRulePo);

    /**
     * 数据更新
     *
     * @param query
     * @return
     */
    int update(QstLimitRuleQuery query);

    /**
     * 查询单条记录
     *
     * @param questionnaireId
     * @return
     */
    QstLimitRulePo selectOne(String questionnaireId);

    /**
     * 查询单条记录
     *
     * @param questionnaireId
     * @return
     */
    QstLimitRulePo selectOne(String questionnaireId, Long userId);

    int updateDealTime(QstLimitRuleQuery query);
}

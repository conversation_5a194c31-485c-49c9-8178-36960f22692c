package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.ProvinceAnalysisRepDto;
import com.cas.nc.questionnaire.common.dto.analysis.ProvinceAnalysisReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.ProvinceAnalysisReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ProvinceAnalysisConverter {
    ProvinceAnalysisConverter INSTANCE = Mappers.getMapper(ProvinceAnalysisConverter.class);

    ProvinceAnalysisReqDto to(ProvinceAnalysisReqVo vo);

    ProvinceAnalysisRepDto to(ProvinceAnalysisRepDto repDto);
}
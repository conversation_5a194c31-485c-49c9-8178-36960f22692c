package com.cas.nc.questionnaire.common.enums;


public enum SourceEnum {
    WECHAT(1, "微信"),
    MOBILE(2, "手机浏览器"),
    PC(3, "pc"),
    ELSE(99, "其他");

    private final Integer key;
    private final String value;


    SourceEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static SourceEnum toEnum(int key) {
        for (SourceEnum value : values()) {
            if (key == value.key) {
                return value;
            }
        }
        return ELSE;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }
}

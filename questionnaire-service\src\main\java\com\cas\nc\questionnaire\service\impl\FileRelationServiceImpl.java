package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.dao.nosharddao.FileRelationDao;
import com.cas.nc.questionnaire.dao.po.FileRelationPo;
import com.cas.nc.questionnaire.dao.query.FileRelationQuery;
import com.cas.nc.questionnaire.service.FileRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class FileRelationServiceImpl implements FileRelationService {

    @Resource
    private FileRelationDao fileRelationDao;

    @Override
    public FileRelationPo selectOne(String fileId) {
        FileRelationQuery query = new FileRelationQuery();
        query.setFileId(fileId);

        return fileRelationDao.selectOne(query);
    }

    @Override
    public int insert(FileRelationPo po) {
        return fileRelationDao.insert(po);
    }

}

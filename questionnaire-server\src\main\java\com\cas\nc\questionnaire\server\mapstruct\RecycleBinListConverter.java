package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.mylist.MyListRecycleBinListRepDto;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RecycleBinListConverter {
    RecycleBinListConverter INSTANCE = Mappers.getMapper(RecycleBinListConverter.class);

    MyListRecycleBinListRepDto to(QstQuestionnaireInfoPo bean);
}
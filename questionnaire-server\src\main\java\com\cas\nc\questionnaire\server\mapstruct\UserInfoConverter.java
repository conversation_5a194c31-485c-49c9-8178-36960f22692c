package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.user.*;
import com.cas.nc.questionnaire.common.vo.user.*;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserInfoConverter {
    UserInfoConverter INSTANCE = Mappers.getMapper(UserInfoConverter.class);

    UserInfoRespDto to(UserInfoPo userInfoPo);

    UserInfoPo to(UserInfoSaveReqDto reqDto);

    UserInfoListReqDto to(UserInfoListReqVo vo);

    UserInfoListRespVo to(UserInfoListRespDto repDto);

    UserInfoSaveReqDto to(UserInfoSaveReqVo vo);

    UserInfoUpdateReqDto to(UserInfoUpdateReqVo vo);

    UserInfoRespVo to(UserInfoRespDto respDto);

    UserInfoSetStatusReqDto to(UserInfoSetStatusReqVo vo);
}

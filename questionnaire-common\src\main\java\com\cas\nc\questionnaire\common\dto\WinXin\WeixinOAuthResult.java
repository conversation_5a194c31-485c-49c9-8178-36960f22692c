package com.cas.nc.questionnaire.common.dto.WinXin;

/**
 * 微信OAuth2.0接口返回结果
 */
public class WeixinOAuthResult {
    private String access_token;
    private Integer expires_in;
    private String refresh_token;
    private String openid;
    private String scope;
    private Integer is_snapshotuser;
    private String unionid;
    
    public String getAccess_token() {
        return access_token;
    }
    
    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }
    
    public Integer getExpires_in() {
        return expires_in;
    }
    
    public void setExpires_in(Integer expires_in) {
        this.expires_in = expires_in;
    }
    
    public String getRefresh_token() {
        return refresh_token;
    }
    
    public void setRefresh_token(String refresh_token) {
        this.refresh_token = refresh_token;
    }
    
    public String getOpenid() {
        return openid;
    }
    
    public void setOpenid(String openid) {
        this.openid = openid;
    }
    
    public String getScope() {
        return scope;
    }
    
    public void setScope(String scope) {
        this.scope = scope;
    }
    
    public Integer getIs_snapshotuser() {
        return is_snapshotuser;
    }
    
    public void setIs_snapshotuser(Integer is_snapshotuser) {
        this.is_snapshotuser = is_snapshotuser;
    }
    
    public String getUnionid() {
        return unionid;
    }
    
    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }
} 
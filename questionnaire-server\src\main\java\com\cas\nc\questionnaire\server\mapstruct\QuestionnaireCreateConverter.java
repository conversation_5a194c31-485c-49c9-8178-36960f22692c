package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireCreateReqDto;
import com.cas.nc.questionnaire.common.vo.questionnaire.QuestionnaireCreateReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface QuestionnaireCreateConverter {
    QuestionnaireCreateConverter INSTANCE = Mappers.getMapper(QuestionnaireCreateConverter.class);

    QuestionnaireCreateReqDto to(QuestionnaireCreateReqVo vo);
}
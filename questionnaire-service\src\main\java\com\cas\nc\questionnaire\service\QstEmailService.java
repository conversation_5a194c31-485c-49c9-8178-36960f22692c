package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstEmailPo;
import com.cas.nc.questionnaire.dao.query.QstEmailQuery;

import java.util.List;
import java.util.Map;


public interface QstEmailService {
    /**
     * 数据插入
     *
     * @param qstEmailPo
     * @return
     */
    int insert(QstEmailPo qstEmailPo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstEmailQuery query);

    /**
     * 删除
     *
     * @param emailId
     * @param userId
     * @return
     */
    int delete(String emailId, Long userId);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstEmailPo> selectList(QstEmailQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstEmailPo selectOne(QstEmailQuery query);

    /**
     * 查询单条记录
     *
     * @param emailId
     * @return
     */
    QstEmailPo selectOne(String emailId);

    /**
     * 查询单条记录
     *
     * @param emailId
     * @return
     */
    QstEmailPo selectOne(String emailId, Long userId);

    /**
     * 更新
     *
     * @param query
     * @return
     */
    int update(QstEmailQuery query);

    /**
     * 更新状态为已发送
     *
     * @param emailId
     * @return
     */
    int updateStatus2Send(String emailId);

    /**
     * 查询数量
     */
    int selectListCount(QstEmailQuery query);

    List<QstEmailPo> selectListPage(QstEmailQuery query);

    /**
     * 更新状态为已打开未作答
     *
     * @param emailId
     * @return
     */
    int updateStatus2Open(String emailId);

    /**
     * 更新状态为已作答
     *
     * @param emailId
     * @return
     */
    int updateStatus2Answered(String emailId);

    /**
     * 统计邮件发送状态
     */
    Map<Integer,Integer> analiceMailCount(String questionnaireId ,Long userId);

}

package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;


public enum FilterRuleTypeEnum {
    TITILE(1, "题目"),
    PROVINCE(2, "省份"),
    CITY(3, "城市"),
    TIME_SEGMENT(4, "答题时间段"),
    SOURCE_CHANNEL(5, "来源渠道"),
    IP(6, "ip地址"),
    ANSWER_DATE(7, "提交答卷时间"),
    ANSWER_DURATION(8, "答题时长"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    FilterRuleTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static FilterRuleTypeEnum toEnum(int key) {
        for (FilterRuleTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.DATA_EXCEPTION);
    }

    public static boolean isListChecked(int key) {
        return PROVINCE.key.intValue() == key || CITY.key.intValue() == key || SOURCE_CHANNEL.key.intValue() == key;
    }

    public static boolean isTitle(int key) {
        return TITILE.key.intValue() == key;
    }

    public static boolean isProvince(int key) {
        return PROVINCE.key.intValue() == key;
    }

    public static boolean isCity(int key) {
        return CITY.key.intValue() == key;
    }

    public static boolean isTimeSegment(int key) {
        return TIME_SEGMENT.key.intValue() == key;
    }

    public static boolean isSourceChannel(int key) {
        return SOURCE_CHANNEL.key.intValue() == key;
    }

    public static boolean isIp(int key) {
        return IP.key.intValue() == key;
    }

    public static boolean isAnswerDate(int key) {
        return ANSWER_DATE.key.intValue() == key;
    }

    public static boolean isAnswerDuration(int key) {
        return ANSWER_DURATION.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

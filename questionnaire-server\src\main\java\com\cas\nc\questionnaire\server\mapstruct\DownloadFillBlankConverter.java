package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.DownloadFillBlankReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.DownloadFillBlankReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DownloadFillBlankConverter {
    DownloadFillBlankConverter INSTANCE = Mappers.getMapper(DownloadFillBlankConverter.class);

    DownloadFillBlankReqDto to(DownloadFillBlankReqVo vo);
}
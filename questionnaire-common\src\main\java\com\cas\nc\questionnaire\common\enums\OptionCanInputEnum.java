package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.DATA_EXCEPTION;


public enum OptionCanInputEnum {
    CAN(1, "可以输入"),
    CANNOT(2, "不可以输入"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    OptionCanInputEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static OptionCanInputEnum toEnum(int key) {
        for (OptionCanInputEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(DATA_EXCEPTION);
    }

    public static Boolean convert2Boolean(int key) {
        if (toEnum(key).equals(CAN)) {
            return true;
        }
        return false;
    }

    public static OptionCanInputEnum convert2Enum(Boolean flag) {
        if (flag != null && flag) {
            return CAN;
        }
        return CANNOT;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

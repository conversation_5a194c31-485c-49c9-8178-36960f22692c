package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.setting.GetReportShareRepDto;
import com.cas.nc.questionnaire.common.dto.setting.GetReportShareReqDto;
import com.cas.nc.questionnaire.common.vo.setting.GetReportShareRepVo;
import com.cas.nc.questionnaire.common.vo.setting.GetReportShareReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GetReportShareConverter {
    GetReportShareConverter INSTANCE = Mappers.getMapper(GetReportShareConverter.class);

    GetReportShareReqDto to(GetReportShareReqVo vo);

    GetReportShareRepVo to(GetReportShareRepDto repDto);
}
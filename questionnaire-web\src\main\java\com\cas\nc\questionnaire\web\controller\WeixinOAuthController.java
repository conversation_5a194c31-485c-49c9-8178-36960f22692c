package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.WinXin.WeixinOAuthResult;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.rpc.wechat.WeChatRpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 微信OAuth授权控制器
 */
@RestController
@RequestMapping("/weixin/oauth")
public class WeixinOAuthController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(WeixinOAuthController.class);
    
    @Resource
    private WeChatRpc weChatRpc;
    
    /**
     * 获取微信授权access_token
     *
     * @param code 微信授权返回的code
     * @return 授权结果
     */
    @RequestMapping(value = "/access_token", method = RequestMethod.POST)
    public ApiReturnResult getAccessToken(@RequestParam("code") String code) {
        logger.info("WeixinOAuthController.getAccessToken code[{}]", code);
        try {
            WeixinOAuthResult oAuthResult = weChatRpc.getOAuthAccessToken(code);
            logger.info("WeixinOAuthController.getAccessToken success openid[{}]", oAuthResult.getOpenid());
            return new ApiReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.SUCCESS.value(), oAuthResult);
        } catch (Exception e) {
            logger.error("WeixinOAuthController.getAccessToken Exception code[{}]", code, e);
            return new ApiReturnResult(CodeEnum.WECHAT_API_EXCEPTION.key(), CodeEnum.WECHAT_API_EXCEPTION.value());
        }
    }
} 
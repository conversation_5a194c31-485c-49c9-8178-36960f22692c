<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.TaskDao">
    <insert id="insert" parameterType="TaskPo" useGeneratedKeys="true"
            keyProperty="id">
        insert into task
        (
        <if test="refId != null">
            ref_id,
        </if>
        <if test="foreignRefId != null">
            foreign_ref_id,
        </if>
        <if test="content != null">
            content,
        </if>
        <if test="taskType != null">
            task_type,
        </if>
        <if test="taskStatus != null">
            task_status,
        </if>
        <if test="remark != null">
            remark,
        </if>
        <if test="failNum != null">
            fail_num,
        </if>
        <if test="ip != null">
            ip,
        </if>
        <if test="taskPartition != null">
            task_partition,
        </if>
        route_id,
        update_time,
        create_time
        )
        values(
        <if test="refId != null">
            #{refId},
        </if>
        <if test="foreignRefId != null">
            #{foreignRefId},
        </if>
        <if test="content != null">
            #{content},
        </if>
        <if test="taskType != null">
            #{taskType},
        </if>
        <if test="taskStatus != null">
            #{taskStatus},
        </if>
        <if test="remark != null">
            #{remark},
        </if>
        <if test="failNum != null">
            #{failNum},
        </if>
        <if test="ip != null">
            #{ip},
        </if>
        <if test="taskPartition != null">
            #{taskPartition},
        </if>
        #{routeId},
        NOW(),
        NOW()
        )
    </insert>

    <select id="selectLockTaskList" parameterType="TaskQuery" resultType="TaskPo">
        select id, ref_id,content,foreign_ref_id,task_type,task_status,fail_num,update_time,ip,task_partition,route_id
        from task
        where #{updateTime} >= update_time
        AND task_status = #{taskStatus}
        AND task_type = #{taskType}
        <if test="failMaxNum != null">
            <![CDATA[and fail_num < #{failMaxNum}]]>
        </if>
        order by update_time asc
        limit #{batchNum}
    </select>

    <select id="selectDeleteTaskList" parameterType="TaskQuery" resultType="TaskPo">
        select id
        from task
        where #{updateTime} >= update_time
        <choose>
            <when test="taskStatusList != null">
                AND task_status in
                <foreach collection="taskStatusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </when>
            <otherwise>
                AND task_status = #{taskStatus}
            </otherwise>
        </choose>
        AND route_id = #{routeId}
        order by update_time asc
        limit #{batchNum}
    </select>

    <select id="selectExecuteTaskList" parameterType="TaskQuery" resultType="TaskPo">
        select id, ref_id,content,foreign_ref_id,task_type,task_status,fail_num, ip,task_partition,route_id
        from task
        where <![CDATA[update_time <= #{updateTime}]]>
        AND task_status = #{taskStatus}
        AND task_type = #{taskType}
        <![CDATA[and fail_num < #{failMaxNum}]]>
        order by update_time asc
        limit #{batchNum}
    </select>

    <select id="selectTaskList" parameterType="TaskQuery" resultType="TaskPo">
        select id, ref_id,content,foreign_ref_id,task_type,task_status,fail_num, ip,task_partition,route_id
        from task
        <where>
            <if test="taskStatus != null">
                and task_status = #{taskStatus}
            </if>
            <if test="taskStatusList != null">
                and task_status in
                <foreach collection="taskStatusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
            <if test="taskType != null">
                and task_type = #{taskType}
            </if>
            <if test="taskTypeList != null">
                and task_type in
                <foreach collection="taskTypeList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
            <if test="refId != null">
                and ref_id = #{refId}
            </if>
            <if test="foreignRefId != null">
                and foreign_ref_id = #{foreignRefId}
            </if>
            <if test="taskPartition != null">
                and task_partition = #{taskPartition}
            </if>
            <if test="routeId != null">
                and route_id = #{routeId}
            </if>
        </where>
    </select>

    <update id="updateTaskStatus" parameterType="TaskQuery">
        update task
        set task_status = #{taskStatus},
        <if test="failNum != null">
            fail_num = #{failNum},
        </if>
        update_time = NOW()
        where id = #{id}
        and task_status = #{oldTaskStatus}
        and route_id = #{routeId}
    </update>

    <update id="updateAddFailNum" parameterType="TaskQuery">
        update task
        set fail_num = #{failNum}
        where id = #{id}
        and fail_num = #{oldFailNum}
        and route_id = #{routeId}
    </update>

    <delete id="deleteTask" parameterType="TaskQuery">
        DELETE from task
        where id = #{id}
        and route_id = #{routeId}
    </delete>

</mapper>
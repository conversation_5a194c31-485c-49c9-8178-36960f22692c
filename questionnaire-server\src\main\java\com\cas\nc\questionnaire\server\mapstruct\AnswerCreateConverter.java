package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.answer.*;
import com.cas.nc.questionnaire.common.utils.TypeConversionWorker;
import com.cas.nc.questionnaire.common.vo.answer.*;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(uses = TypeConversionWorker.class)
public interface AnswerCreateConverter {
    AnswerCreateConverter INSTANCE = Mappers.getMapper(AnswerCreateConverter.class);

    AnswerCreateReqDto to(AnswerCreateReqVo vo);

    AnswerCreateRepVo to(AnswerCreateRepDto repDto);

    AnswerOptionPo to(AnswerOptionPo doubleAnswerOption);

    @Mapping(target = "data",source = "data",qualifiedByName = "toStrObjMap")
    @Mapping(target = "answerUserId",source = "userId")
    AnswerCreateReqDto to(AnswerExternalCreateReqVo vo);

    ListExternalAnswererReqDto to(ListExternalAnswererReqVo vo);

    CountExternalAnswererReqDto to(CountExternalAnswererReqVo vo);

    @Mapping(target = "answerUserId",source = "userId")
    ValidateExternalAnsweredReqDto to(ValidateExternalAnsweredReqVo vo);
}
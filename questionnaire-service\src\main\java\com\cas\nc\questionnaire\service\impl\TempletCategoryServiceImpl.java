package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.YnEnum;
import com.cas.nc.questionnaire.dao.nosharddao.TempletCategoryDao;
import com.cas.nc.questionnaire.dao.po.TempletCategoryPo;
import com.cas.nc.questionnaire.dao.query.TempletCategoryQuery;
import com.cas.nc.questionnaire.service.TempletCategoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("templetCategoryService")
public class TempletCategoryServiceImpl implements TempletCategoryService {

    @Resource
    private TempletCategoryDao templetCategoryDao;

    @Override
    public List<TempletCategoryPo> selectAllEffective() {
        TempletCategoryQuery query = new TempletCategoryQuery();
        query.setYn(YnEnum.Y.key());

        return templetCategoryDao.selectList(query);
    }

    @Override
    public TempletCategoryPo selectOneValid(String templetCategoryId) {
        TempletCategoryQuery query = new TempletCategoryQuery();
        query.setTempletCategoryId(templetCategoryId);
        query.setYn(YnEnum.Y.key());

        return templetCategoryDao.selectOne(query);
    }
}

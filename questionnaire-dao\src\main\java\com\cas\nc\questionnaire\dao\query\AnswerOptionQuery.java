package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;

import java.util.Date;
import java.util.List;


public class AnswerOptionQuery extends AnswerOptionPo {
    private String tableColumns;

    private List<Integer> optionNumberList;

    private Boolean inFlag;

    private List<Integer> titleSerialNumberList;

    private Integer firstTitleSerialNumber;

    private Integer secondTitleSerialNumber;

    private Integer thirdTitleSerialNumber;

    private List<String> answerIdList;

    /*开始索引*/
    private int startIndex;
    /*页面大小*/
    private Integer pageSize;

    private Integer province;

    private Integer city;

    private Date neqCreateTime;

    private Date greaterThanCreateTime;

    private Date lessThanCreateTime;

    private Date beginCreateTime;

    private Date endCreateTime;

    private Integer duration;

    private Integer neqDuration;

    private Integer greaterThanDuration;

    private Integer lessThanDuration;

    private Integer beginDuration;

    private Integer endDuration;

    private String ip;

    private List<Integer> optionList;

    private String neqWriteContent;

    private String containWriteContent;

    private String notContainWriteContent;

    private String lessThanWriteContent;

    private String greaterThanWriteContent;

    private Integer beginWriteContent;

    private Integer endWriteContent;

    private Integer neqSerialNumber;

    private Integer lessThanSerialNumber;

    private Integer greaterThanSerialNumber;

    private Integer beginSerialNumber;

    private Integer endSerialNumber;

    private List<Long> provinceList;

    private List<Long> cityList;

    private List<Long> sourceList;

    private List<Integer> serialNumberList;

    /**
     * 填写内容
     */
    private String writeContent;

    public String getWriteContent() {
        return writeContent;
    }

    public void setWriteContent(String writeContent) {
        this.writeContent = writeContent;
    }


    public Boolean getInFlag() {
        return inFlag;
    }

    public void setInFlag(Boolean inFlag) {
        this.inFlag = inFlag;
    }

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public List<Integer> getOptionNumberList() {
        return optionNumberList;
    }

    public void setOptionNumberList(List<Integer> optionNumberList) {
        this.optionNumberList = optionNumberList;
    }

    public List<Integer> getTitleSerialNumberList() {
        return titleSerialNumberList;
    }

    public void setTitleSerialNumberList(List<Integer> titleSerialNumberList) {
        this.titleSerialNumberList = titleSerialNumberList;
    }

    public Integer getFirstTitleSerialNumber() {
        return firstTitleSerialNumber;
    }

    public void setFirstTitleSerialNumber(Integer firstTitleSerialNumber) {
        this.firstTitleSerialNumber = firstTitleSerialNumber;
    }

    public Integer getSecondTitleSerialNumber() {
        return secondTitleSerialNumber;
    }

    public void setSecondTitleSerialNumber(Integer secondTitleSerialNumber) {
        this.secondTitleSerialNumber = secondTitleSerialNumber;
    }

    public Integer getThirdTitleSerialNumber() {
        return thirdTitleSerialNumber;
    }

    public void setThirdTitleSerialNumber(Integer thirdTitleSerialNumber) {
        this.thirdTitleSerialNumber = thirdTitleSerialNumber;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getAnswerIdList() {
        return answerIdList;
    }

    public void setAnswerIdList(List<String> answerIdList) {
        this.answerIdList = answerIdList;
    }

    public Integer getProvince() {
        return province;
    }

    public void setProvince(Integer province) {
        this.province = province;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public Date getGreaterThanCreateTime() {
        return greaterThanCreateTime;
    }

    public void setGreaterThanCreateTime(Date greaterThanCreateTime) {
        this.greaterThanCreateTime = greaterThanCreateTime;
    }

    public Date getLessThanCreateTime() {
        return lessThanCreateTime;
    }

    public void setLessThanCreateTime(Date lessThanCreateTime) {
        this.lessThanCreateTime = lessThanCreateTime;
    }

    public Date getBeginCreateTime() {
        return beginCreateTime;
    }

    public void setBeginCreateTime(Date beginCreateTime) {
        this.beginCreateTime = beginCreateTime;
    }

    public Date getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(Date endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getGreaterThanDuration() {
        return greaterThanDuration;
    }

    public void setGreaterThanDuration(Integer greaterThanDuration) {
        this.greaterThanDuration = greaterThanDuration;
    }

    public Integer getLessThanDuration() {
        return lessThanDuration;
    }

    public void setLessThanDuration(Integer lessThanDuration) {
        this.lessThanDuration = lessThanDuration;
    }

    public Integer getBeginDuration() {
        return beginDuration;
    }

    public void setBeginDuration(Integer beginDuration) {
        this.beginDuration = beginDuration;
    }

    public Integer getEndDuration() {
        return endDuration;
    }

    public void setEndDuration(Integer endDuration) {
        this.endDuration = endDuration;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getNeqDuration() {
        return neqDuration;
    }

    public void setNeqDuration(Integer neqDuration) {
        this.neqDuration = neqDuration;
    }

    public Date getNeqCreateTime() {
        return neqCreateTime;
    }

    public void setNeqCreateTime(Date neqCreateTime) {
        this.neqCreateTime = neqCreateTime;
    }

    public List<Integer> getOptionList() {
        return optionList;
    }

    public void setOptionList(List<Integer> optionList) {
        this.optionList = optionList;
    }

    public String getNeqWriteContent() {
        return neqWriteContent;
    }

    public void setNeqWriteContent(String neqWriteContent) {
        this.neqWriteContent = neqWriteContent;
    }

    public String getContainWriteContent() {
        return containWriteContent;
    }

    public void setContainWriteContent(String containWriteContent) {
        this.containWriteContent = containWriteContent;
    }

    public String getNotContainWriteContent() {
        return notContainWriteContent;
    }

    public void setNotContainWriteContent(String notContainWriteContent) {
        this.notContainWriteContent = notContainWriteContent;
    }

    public String getLessThanWriteContent() {
        return lessThanWriteContent;
    }

    public void setLessThanWriteContent(String lessThanWriteContent) {
        this.lessThanWriteContent = lessThanWriteContent;
    }

    public String getGreaterThanWriteContent() {
        return greaterThanWriteContent;
    }

    public void setGreaterThanWriteContent(String greaterThanWriteContent) {
        this.greaterThanWriteContent = greaterThanWriteContent;
    }

    public Integer getBeginWriteContent() {
        return beginWriteContent;
    }

    public void setBeginWriteContent(Integer beginWriteContent) {
        this.beginWriteContent = beginWriteContent;
    }

    public Integer getEndWriteContent() {
        return endWriteContent;
    }

    public void setEndWriteContent(Integer endWriteContent) {
        this.endWriteContent = endWriteContent;
    }

    public Integer getNeqSerialNumber() {
        return neqSerialNumber;
    }

    public void setNeqSerialNumber(Integer neqSerialNumber) {
        this.neqSerialNumber = neqSerialNumber;
    }

    public Integer getLessThanSerialNumber() {
        return lessThanSerialNumber;
    }

    public void setLessThanSerialNumber(Integer lessThanSerialNumber) {
        this.lessThanSerialNumber = lessThanSerialNumber;
    }

    public Integer getGreaterThanSerialNumber() {
        return greaterThanSerialNumber;
    }

    public void setGreaterThanSerialNumber(Integer greaterThanSerialNumber) {
        this.greaterThanSerialNumber = greaterThanSerialNumber;
    }

    public Integer getBeginSerialNumber() {
        return beginSerialNumber;
    }

    public void setBeginSerialNumber(Integer beginSerialNumber) {
        this.beginSerialNumber = beginSerialNumber;
    }

    public Integer getEndSerialNumber() {
        return endSerialNumber;
    }

    public void setEndSerialNumber(Integer endSerialNumber) {
        this.endSerialNumber = endSerialNumber;
    }

    public List<Long> getProvinceList() {
        return provinceList;
    }

    public void setProvinceList(List<Long> provinceList) {
        this.provinceList = provinceList;
    }

    public List<Long> getCityList() {
        return cityList;
    }

    public void setCityList(List<Long> cityList) {
        this.cityList = cityList;
    }

    public List<Long> getSourceList() {
        return sourceList;
    }

    public void setSourceList(List<Long> sourceList) {
        this.sourceList = sourceList;
    }

    public List<Integer> getSerialNumberList() {
        return serialNumberList;
    }

    public void setSerialNumberList(List<Integer> serialNumberList) {
        this.serialNumberList = serialNumberList;
    }
}

package com.cas.nc.questionnaire.service;


import com.cas.nc.questionnaire.dao.po.LoginauthPo;
import com.cas.nc.questionnaire.dao.query.LoginauthQuery;

import java.util.List;

public interface LoginauthService {
    /**
     * 数据插入
     *
     * @param loginauthPo
     * @return
     */
    int insert(LoginauthPo loginauthPo);

    /**
     * 依据条件查询单条数据
     *
     * @param query
     * @return
     */
    LoginauthPo selectOne(LoginauthQuery query);


    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    LoginauthPo selectOne(Integer id);

    List<LoginauthPo> selectList(LoginauthQuery query);

    int update(LoginauthQuery query);

    int insertUpdate(LoginauthPo loginauthPo);
}

package com.cas.nc.questionnaire.dao.nosharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.QstReportQueryConditionPo;
import com.cas.nc.questionnaire.dao.query.QstReportQueryConditionQuery;

/**
 * 测评报告查询条件DAO接口
 */
public interface QstReportQueryConditionDao extends BaseDao<QstReportQueryConditionPo, QstReportQueryConditionQuery> {
    
    /**
     * 根据问卷ID查询条件
     * @param questionnaireId 问卷ID
     * @return 查询条件
     */
    QstReportQueryConditionPo selectByQuestionnaireId(String questionnaireId);
    
    /**
     * 根据问卷ID删除条件
     * @param questionnaireId 问卷ID
     * @return 影响行数
     */
    int deleteByQuestionnaireId(String questionnaireId);
} 
package com.cas.nc.questionnaire.common.dto.questionnaire;

import java.util.List;

public class QuestionnaireListRepDto {

    private Integer total;

    private Integer allAnswerCount;

    private List<QuestionnaireRepDto> questionnaireRepDtoList;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<QuestionnaireRepDto> getQuestionnaireRepDtoList() {
        return questionnaireRepDtoList;
    }

    public void setQuestionnaireRepDtoList(List<QuestionnaireRepDto> questionnaireRepDtoList) {
        this.questionnaireRepDtoList = questionnaireRepDtoList;
    }

    public Integer getAllAnswerCount() {
        return allAnswerCount;
    }

    public void setAllAnswerCount(Integer allAnswerCount) {
        this.allAnswerCount = allAnswerCount;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstSignRecordDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstSignRecordPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sign_id" jdbcType="VARCHAR" property="signId"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="openid" jdbcType="VARCHAR" property="openid"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="headimgurl" jdbcType="VARCHAR" property="headimgurl"/>
        <result column="sign_time" jdbcType="TIMESTAMP" property="signTime"/>
        <result column="latitude" jdbcType="DECIMAL" property="latitude"/>
        <result column="longitude" jdbcType="DECIMAL" property="longitude"/>
        <result column="sign_date" jdbcType="DATE" property="signDate"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    
    <sql id="sql_columns">
        id, sign_id, questionnaire_id, user_id, openid, nickname, headimgurl, 
        sign_time, latitude, longitude, sign_date, status, update_time, create_time
    </sql>
    
    <sql id="sql_where">
        <where>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.signId">and sign_id = #{item.signId}</if>
            <if test="null != item.questionnaireId">and questionnaire_id = #{item.questionnaireId}</if>
            <if test="null != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.openid">and openid = #{item.openid}</if>
            <if test="null != item.status">and status = #{item.status}</if>
            <if test="null != item.signDate">and sign_date = #{item.signDate}</if>
            <if test="null != item.beginSignTime">and sign_time >= #{item.beginSignTime}</if>
            <if test="null != item.endSignTime">and sign_time &lt;= #{item.endSignTime}</if>
            <if test="null != item.questionnaireIdList and item.questionnaireIdList.size > 0">
                and questionnaire_id in
                <foreach collection="item.questionnaireIdList" item="questionnaireId" open="(" separator="," close=")">
                    #{questionnaireId}
                </foreach>
            </if>
            <if test="null != item.openidList and item.openidList.size > 0">
                and openid in
                <foreach collection="item.openidList" item="openid" open="(" separator="," close=")">
                    #{openid}
                </foreach>
            </if>
            <if test="null != item.signDateList and item.signDateList.size > 0">
                and sign_date in
                <foreach collection="item.signDateList" item="signDate" open="(" separator="," close=")">
                    #{signDate}
                </foreach>
            </if>
        </where>
    </sql>
    
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from qst_sign_record
        <include refid="sql_where"/>
        limit 1
    </select>
    
    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from qst_sign_record
        <include refid="sql_where"/>
        order by sign_time desc
    </select>
    
    <select id="selectListByPage" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from qst_sign_record
        <include refid="sql_where"/>
        order by sign_time desc
        <if test="null != item.startIndex or null != item.pageSize">
            limit
            <if test="null != item.startIndex">#{item.startIndex},</if>
            <if test="null != item.pageSize">#{item.pageSize}</if>
        </if>
    </select>
    
    <select id="selectCount" resultType="int">
        select count(*) from qst_sign_record
        <include refid="sql_where"/>
    </select>
    
    <select id="selectByQuestionnaireIdAndOpenidAndDate" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from qst_sign_record
        where questionnaire_id = #{questionnaireId} and openid = #{openid} and sign_date = #{signDate} and status = 1
        limit 1
    </select>
    
    <delete id="delete">
        delete from qst_sign_record
        <include refid="sql_where"/>
    </delete>
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into qst_sign_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != signId">sign_id,</if>
            <if test="null != questionnaireId">questionnaire_id,</if>
            <if test="null != userId">user_id,</if>
            <if test="null != openid">openid,</if>
            <if test="null != nickname">nickname,</if>
            <if test="null != headimgurl">headimgurl,</if>
            <if test="null != signTime">sign_time,</if>
            <if test="null != latitude">latitude,</if>
            <if test="null != longitude">longitude,</if>
            <if test="null != signDate">sign_date,</if>
            <if test="null != status">status,</if>
            <if test="null != updateTime">update_time,</if>
            <if test="null != createTime">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != signId">#{signId},</if>
            <if test="null != questionnaireId">#{questionnaireId},</if>
            <if test="null != userId">#{userId},</if>
            <if test="null != openid">#{openid},</if>
            <if test="null != nickname">#{nickname},</if>
            <if test="null != headimgurl">#{headimgurl},</if>
            <if test="null != signTime">#{signTime},</if>
            <if test="null != latitude">#{latitude},</if>
            <if test="null != longitude">#{longitude},</if>
            <if test="null != signDate">#{signDate},</if>
            <if test="null != status">#{status},</if>
            <if test="null != updateTime">#{updateTime},</if>
            <if test="null != createTime">#{createTime},</if>
        </trim>
    </insert>
    
    <update id="update">
        update qst_sign_record
        <set>
            <if test="null != signId">sign_id = #{signId},</if>
            <if test="null != questionnaireId">questionnaire_id = #{questionnaireId},</if>
            <if test="null != userId">user_id = #{userId},</if>
            <if test="null != openid">openid = #{openid},</if>
            <if test="null != nickname">nickname = #{nickname},</if>
            <if test="null != headimgurl">headimgurl = #{headimgurl},</if>
            <if test="null != signTime">sign_time = #{signTime},</if>
            <if test="null != latitude">latitude = #{latitude},</if>
            <if test="null != longitude">longitude = #{longitude},</if>
            <if test="null != signDate">sign_date = #{signDate},</if>
            <if test="null != status">status = #{status},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
        </set>
        where id = #{id}
    </update>
</mapper> 
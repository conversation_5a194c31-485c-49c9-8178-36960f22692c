package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.sso.Ticket;
import com.cas.nc.questionnaire.common.utils.IpUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.RequestUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.service.UserInfoService;
import com.cas.nc.questionnaire.web.sso.LoginCheck;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@RestController
public class BaseController {

    protected final static Logger logger = LoggerFactory.getLogger(BaseController.class);
    @Resource
    protected LoginCheck loginCheck;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    protected HttpServletRequest request;

    @ExceptionHandler(ServerException.class)
    public ApiReturnResult errorCodeException(ServerException se, HttpServletRequest request) {
        Map<String, String> paramMap = RequestUtil.getParameterMap(request);
        logger.error("url:[{}] clientIp:[{}] queryString:[{}]", request.getRequestURL(), IpUtil.getIp(request),
                RequestUtil.getQueryStringFromMap(paramMap), se);
        return new ApiReturnResult(se.getCode(), se.getMsg());
    }

    @ExceptionHandler
    public ApiReturnResult errorCodeException(Throwable throwable, HttpServletRequest request) {
        Map<String, String> paramMap = RequestUtil.getParameterMap(request);
        logger.error("url:[{}] clientIp:[{}] queryString:[{}]", request.getRequestURL(), IpUtil.getIp(request), RequestUtil.getQueryStringFromMap(paramMap), throwable);
        return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
    }

    protected HttpServletRequest getHttpServletRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    protected HttpServletResponse getHttpServletResponse() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
    }

    protected Ticket getTicket() {
        HttpServletRequest httpServletRequest = getHttpServletRequest();
        Cookie[] cookies = httpServletRequest.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                logger.info("cookiename:{}", cookie.getName());
                if (cookie.getName().equals(LoginCheck.COOKIE_NAME)) {
                    Ticket ticket = loginCheck.convertToTicket(cookie.getValue());
                    logger.info("BaseController.getTicket ticketInfo[{}]", JSONUtil.toJSONString(ticket));
                    return ticket;
                }
            }
        }
        throw new ServerException(CodeEnum.PLEASE_LOGIN);
    }

    protected Ticket getTicketNoException() {
        HttpServletRequest httpServletRequest = getHttpServletRequest();
        Cookie[] cookies = httpServletRequest.getCookies();
        if (cookies != null && cookies.length > 0) {
            for (Cookie cookie : cookies) {
                logger.info("cookiename:{}", cookie.getName());
                if (cookie.getName().equals(LoginCheck.COOKIE_NAME)) {
                    Ticket ticket = loginCheck.convertToTicket(cookie.getValue());
                    logger.info("BaseController.getTicket ticketInfo[{}]", JSONUtil.toJSONString(ticket));
                    return ticket;
                }
            }
        }

        return null;
    }

    protected Long getUserId() {
        Ticket ticket = getTicket();
        if (ticket.getUserId() != null) {
            return ticket.getUserId();
        }
        throw new ServerException(CodeEnum.PLEASE_LOGIN);
    }

    protected Long getUserIdNoException() {
        Ticket ticket = getTicketNoException();
        if (ticket != null && ticket.getUserId() != null) {
            return ticket.getUserId();
        }
        return null;
    }

    protected String getIp() {
        return IpUtil.getIp(getHttpServletRequest());
    }

}


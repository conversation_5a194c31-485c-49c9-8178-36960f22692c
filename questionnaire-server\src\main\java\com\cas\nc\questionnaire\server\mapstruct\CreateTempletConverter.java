package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.dao.po.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CreateTempletConverter {
    CreateTempletConverter INSTANCE = Mappers.getMapper(CreateTempletConverter.class);


    TempletQuestionnaireInfoPo to(QstQuestionnaireInfoPo qstQuestionnaireInfoPo);

    List<TempletTitlePo> toTitleList(List<QstTitlePo> qstTitlePoList);

    List<TempletOptionPo> toOptionList(List<QstOptionPo> qstOptionPoList);
}
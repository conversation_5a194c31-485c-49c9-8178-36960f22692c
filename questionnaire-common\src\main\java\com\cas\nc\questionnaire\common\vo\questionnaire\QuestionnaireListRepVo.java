package com.cas.nc.questionnaire.common.vo.questionnaire;

import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireRepDto;

import java.util.List;

public class QuestionnaireListRepVo {
    private Integer page;

    private Integer pageSize;

    private Integer total;

    private Integer allAnswerCount;

    private List<QuestionnaireRepVo> questionnaireRepDtoList;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<QuestionnaireRepVo> getQuestionnaireRepDtoList() {
        return questionnaireRepDtoList;
    }

    public void setQuestionnaireRepDtoList(List<QuestionnaireRepVo> questionnaireRepDtoList) {
        this.questionnaireRepDtoList = questionnaireRepDtoList;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getAllAnswerCount() {
        return allAnswerCount;
    }

    public void setAllAnswerCount(Integer allAnswerCount) {
        this.allAnswerCount = allAnswerCount;
    }
}

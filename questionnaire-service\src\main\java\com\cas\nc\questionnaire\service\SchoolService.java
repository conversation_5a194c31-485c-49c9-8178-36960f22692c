package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.SchoolPo;
import com.cas.nc.questionnaire.dao.query.SchoolQuery;

import java.util.List;

public interface SchoolService {
    /**
     * 查询所有数据
     *
     * @return
     */
    List<SchoolPo> selectAll();

    /**
     * 查询所有有效数据
     *
     * @return
     */
    List<SchoolPo> selectAllEffective(String columns);

    /**
     * 数据插入
     *
     * @param schoolPo
     * @return
     */
    int insert(SchoolPo schoolPo);

    /**
     * 依据传入条件查询数据
     *
     * @param query
     * @return
     */
    List<SchoolPo> selectList(SchoolQuery query);
}

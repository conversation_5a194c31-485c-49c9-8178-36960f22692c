package com.cas.nc.questionnaire.common.utils;

import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;

public class Assert {

    public static void notNull(Object object, String message) {
        notNull(object, CodeEnum.IS_NULL.key(), message + CodeEnum.IS_NULL.value());
    }

    public static void notNull(Object object) {
        notNull(object, "[Assertion failed] - the object argument must be not null");
    }

    public static void notNull(Object object, CodeEnum codeEnum) {
        notNull(object, codeEnum.key(), codeEnum.value());
    }

    public static void notNull(Object object, int code, String msg) {
        if (object == null) {
            throw new ServerException(code, msg);
        }

        if (object instanceof String && StringUtils.isEmpty(object.toString())) {
            throw new ServerException(code, msg);
        }

        if (object instanceof Collection) {
            Collection cols = (Collection) object;
            if (cols.size() == 0) {
                throw new ServerException(code, msg);
            }
        }

        if (object instanceof Map) {
            Map map = (Map) object;
            if (map.size() == 0) {
                throw new ServerException(code, msg);
            }
        }

    }

    public static void isTrue(boolean expression, String message) {
        isTrue(expression, CodeEnum.IS_NULL.key(), message);
    }

    public static void isTrue(boolean expression) {
        isTrue(expression, "[Assertion failed] - this expression must be true");
    }

    public static void isTrue(boolean expression, CodeEnum codeEnum) {
        isTrue(expression, codeEnum.key(), codeEnum.value());
    }

    public static void isTrue(boolean expression, CodeEnum codeEnum, String afterStr) {
        isTrue(expression, codeEnum.key(), codeEnum.value() + afterStr);
    }

    public static void isTrue(boolean expression, String beforeStr, CodeEnum codeEnum) {
        isTrue(expression, codeEnum.key(), beforeStr + codeEnum.value());
    }

    public static void isTrue(boolean expression, int code, String msg) {
        if (!expression) {
            throw new ServerException(code, msg);
        }
    }

    public static void notBlank(String str, String beforeStr, CodeEnum codeEnum) {
        notBlank(str, codeEnum.key(), beforeStr + codeEnum.value());
    }

    public static void notBlank(String str, String message) {
        notBlank(str, CodeEnum.IS_NULL.key(), message + CodeEnum.IS_NULL.value());
    }

    public static void notBlank(String str, CodeEnum codeEnum) {
        notBlank(str, codeEnum.key(), codeEnum.value());
    }

    public static void notBlank(String str, int code, String message) {
        if (StringUtil.isBlank(str)) {
            throw new ServerException(code, message);
        }
    }

}

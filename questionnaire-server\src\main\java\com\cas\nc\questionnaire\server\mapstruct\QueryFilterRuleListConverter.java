package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.setting.SettingFilterRuleListRepDto;
import com.cas.nc.questionnaire.dao.po.QstFilterRulePo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface QueryFilterRuleListConverter {
    QueryFilterRuleListConverter INSTANCE = Mappers.getMapper(QueryFilterRuleListConverter.class);

    List<SettingFilterRuleListRepDto> to(List<QstFilterRulePo> poList);
}
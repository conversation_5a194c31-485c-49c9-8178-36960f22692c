package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.templet.ConvertTempletRepDto;
import com.cas.nc.questionnaire.common.dto.templet.ConvertTempletReqDto;
import com.cas.nc.questionnaire.common.dto.templet.CreateTempletReqDto;
import com.cas.nc.questionnaire.common.dto.templet.GetTempletRepDto;
import com.cas.nc.questionnaire.common.dto.templet.ListCategoryRepDto;
import com.cas.nc.questionnaire.common.dto.templet.ListTempletRepDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.common.vo.templet.ConvertTempletRepVo;
import com.cas.nc.questionnaire.common.vo.templet.ConvertTempletReqVo;
import com.cas.nc.questionnaire.common.vo.templet.CreateTempletReqVo;
import com.cas.nc.questionnaire.common.vo.templet.GetTempletRepVo;
import com.cas.nc.questionnaire.common.vo.templet.GetTempletReqVo;
import com.cas.nc.questionnaire.common.vo.templet.ListCategoryRepVo;
import com.cas.nc.questionnaire.common.vo.templet.ListTempletRepVo;
import com.cas.nc.questionnaire.common.vo.templet.ListTempletReqVo;
import com.cas.nc.questionnaire.server.TempletServer;
import com.cas.nc.questionnaire.server.mapstruct.TempletConverter;
import com.cas.nc.questionnaire.service.UserInfoService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.PLEASE_LOGIN;

@RestController
@RequestMapping("/questionnaire/templet")
public class TempletController extends BaseController {

    @Resource
    private TempletServer templetServer;
    @Resource
    private UserInfoService userInfoService;

    @RequestMapping("/listcategory")
    public ApiReturnResult listCategory() {
        logger.info("TempletController.listCategory begin");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Assert.notNull(getUserId(), PLEASE_LOGIN);

        List<ListCategoryRepDto> repDto = templetServer.listCategory();
        List<ListCategoryRepVo> repVo = new ArrayList<>();
        SafeUtil.of(repDto).forEach(v -> {
            ListCategoryRepVo templetRepVo = new ListCategoryRepVo();
            templetRepVo.setTempCategoryId(v.getTempCategoryId());
            templetRepVo.setTitle(v.getTitle());
            templetRepVo.setPriority(v.getPriority());

            repVo.add(templetRepVo);
        });

        result.setData(repVo);

        logger.info("TempletController.listCategory result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/listtemplet")
    public ApiReturnResult listTemplet(@RequestBody ListTempletReqVo vo) {
        logger.info("TempletController.listTemplet begin");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Assert.notNull(getUserId(), PLEASE_LOGIN);

        List<ListTempletRepDto> repDto = templetServer.listTemplet(TempletConverter.INSTANCE.to(vo));

        List<ListTempletRepVo> repVo = new ArrayList<>();
        SafeUtil.of(repDto).forEach(v -> {
            ListTempletRepVo templetRepVo = new ListTempletRepVo();
            templetRepVo.setTempletId(v.getTempletId());
            templetRepVo.setTitle(v.getTitle());

            repVo.add(templetRepVo);
        });

        result.setData(repVo);

        logger.info("TempletController.listTemplet result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/gettemplet")
    public ApiReturnResult getTemplet(@RequestBody GetTempletReqVo vo) {
        logger.info("TempletController.getTemplet param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Assert.notNull(getUserId(), PLEASE_LOGIN);

        GetTempletRepDto repDto = templetServer.getTemplet(TempletConverter.INSTANCE.to(vo));

        GetTempletRepVo repVo = new GetTempletRepVo();
        repVo.setQuestionnairePojo(repDto.getQuestionnairePojo());

        result.setData(repVo);

        logger.info("TempletController.getTemplet result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/converttemplet")
    public ApiReturnResult convertTemplet(@RequestBody ConvertTempletReqVo vo) {
        logger.info("TempletController.convertTemplet begin");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        Long userId = getUserId();
        Assert.notNull(userId, PLEASE_LOGIN);

        ConvertTempletReqDto reqDto = new ConvertTempletReqDto();
        reqDto.setTempletId(vo.getTempletId());
        reqDto.setUserId(userId);

        ConvertTempletRepDto repDto = templetServer.convertTemplet(reqDto);

        ConvertTempletRepVo repVo = new ConvertTempletRepVo();
        repVo.setQuestionnaireId(repDto.getQuestionnaireId());

        result.setData(repVo);

        logger.info("TempletController.convertTemplet result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/createtemplet")
    public ApiReturnResult createTemplet(@RequestBody CreateTempletReqVo vo) {
        logger.info("TempletController.createTemplet begin");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

//        Long userId = getUserId();
//        Assert.notNull(userId, PLEASE_LOGIN);
//        UserInfoPo userInfoPo = userInfoService.selectOne(userId);
//        Assert.notNull(userInfoPo, USER_NONEXISTENT);
//        Assert.isTrue(isAdmin(userInfoPo.getType()), UNKNOWN_RETURN_PAGE);

        CreateTempletReqDto reqDto = new CreateTempletReqDto();
        reqDto.setQuestionnaireId(vo.getQuestionnaireId());
        reqDto.setTempletCategoryId(vo.getTempletCategoryId());
        reqDto.setUserId(776765372559786501L);

        templetServer.createTemplet(reqDto);

        logger.info("TempletController.createTemplet result[{}]", JSONUtil.toJSONString(result));
        return result;
    }
}

package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.design.DesignOptionReqDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.vo.design.DesignOptionReqVo;
import com.cas.nc.questionnaire.server.DesignServer;
import com.cas.nc.questionnaire.server.mapstruct.DesignConverter;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/questionnaire/design")
public class DesignController extends BaseController {

    @Resource
    private DesignServer designServer;

    /**
     * 选择问卷修改模式
     *
     * @param vo
     * @return
     */
    @RequestMapping("/designoption")
    public ApiReturnResult designOption(@RequestBody DesignOptionReqVo vo) {
        logger.info("DesignController.designOption param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        DesignOptionReqDto reqDto = DesignConverter.INSTANCE.to(vo);
        String userId = SequenceUtil.getInstance().parse2UserId(vo.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        designServer.designOption(reqDto);

        logger.info("DesignController.designOption result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

}

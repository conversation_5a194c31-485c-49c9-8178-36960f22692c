package com.cas.nc.questionnaire.dao.sharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.QstEmailPo;
import com.cas.nc.questionnaire.dao.query.QstEmailQuery;
import com.cas.nc.questionnaire.dao.query.QstQuestionnaireInfoQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QstEmailDao extends BaseDao<QstEmailPo, QstEmailQuery> {

    /**
     * 查询符合条件的条数
     *
     * @param query
     * @return
     */
    int selectCount(@Param("item") QstEmailQuery query);

    /**
     * 分页查询
     */
    List<QstEmailPo> selectListPage(@Param("item") QstEmailQuery query);
}
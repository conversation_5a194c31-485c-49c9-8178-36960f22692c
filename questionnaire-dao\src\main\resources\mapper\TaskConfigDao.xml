<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.TaskConfigDao">
    <insert id="insert" parameterType="TaskConfigPo" useGeneratedKeys="true" keyProperty="id">
        insert into task_config
        (
        <if test="taskType != null">
            task_type,
        </if>
        <if test="remark != null">
            remark,
        </if>
        <if test="failMaxNum != null">
            fail_max_num,
        </if>
        <if test="batchNum != null">
            batch_num,
        </if>
        update_time,
        create_time
        )
        values(
        <if test="taskType != null">
            #{taskType},
        </if>
        <if test="remark != null">
            #{remark},
        </if>
        <if test="failMaxNum != null">
            #{failMaxNum},
        </if>
        <if test="batchNum != null">
            #{batchNum},
        </if>
        NOW(),
        NOW()
        )
    </insert>

    <select id="selectTaskConfig" resultType="TaskConfigPo">
    select id,task_type,fail_max_num,batch_num,update_time,create_time,yn,remark
    from task_config
  </select>

</mapper>
package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.LimitTypeEnum;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.IpUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;
import com.cas.nc.questionnaire.dao.query.AnswerInfoQuery;
import com.cas.nc.questionnaire.server.ValidateQstLimitRuleServer;
import com.cas.nc.questionnaire.service.AnswerInfoService;
import com.cas.nc.questionnaire.service.QstLimitRuleService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Md5Encrypt.md5Triple;

@Component
public class ValidateQstLimitRuleServerImpl implements ValidateQstLimitRuleServer {

    @Resource
    private QstLimitRuleService qstLimitRuleService;
    @Resource
    private AnswerInfoService answerInfoService;

    @Override
    public QstLimitRulePo validateQstLimitRule(String questionnaireId, String ip, String deviceCode, String pwd) {
        QstLimitRulePo po = qstLimitRuleService.selectOne(questionnaireId);
        if (po == null) {
            return new QstLimitRulePo();
        }
        if (StringUtil.isNotBlank(po.getPwd())) {
            Assert.notNull(pwd, CodeEnum.ANSWER_PWD_MUST);
            Assert.isTrue(pwd.equalsIgnoreCase(po.getPwd()) || pwd.equalsIgnoreCase(md5Triple(po.getPwd())), CodeEnum.ANSWER_PWD_ERROR);
        }
        validateUsefulTime(po);
        if (po.getCollectNumber() > 0) {
            int answerCount = answerInfoService.selectCount(questionnaireId);
            Assert.isTrue(answerCount < po.getCollectNumber(), CodeEnum.ANSWER_COUNT_LIMIT);
        }
        if (po.getDeviceLimitType() != 0) {
            validateDeviceFrequency(questionnaireId, deviceCode, po);
        }
        if (po.getIpLimitType() != 0) {
            validateIpFrequency(questionnaireId, ip, po);
        }
        if (StringUtil.isNotBlank(po.getIpStart()) && StringUtil.isNotBlank(po.getIpEnd())) {
            Assert.isTrue(IpUtil.isValidRange(po.getIpStart(), po.getIpEnd(), ip), CodeEnum.IP_NO_IN_SCOPE);
        }
        return po;
    }

    private void validateIpFrequency(String questionnaireId, String ip, QstLimitRulePo po) {
        switch (LimitTypeEnum.toEnum(po.getIpLimitType())) {
            case NO_TIME_LIMIT:
                AnswerInfoQuery query = new AnswerInfoQuery();
                query.setIp(ip);
                query.setQuestionnaireId(questionnaireId);
                query.setUserId(SequenceUtil.getInstance().parse2UserId4Long(questionnaireId));
                int allCount = answerInfoService.selectCount(query);
                String countHint = String.format(CodeEnum.NO_TIME_LIMIT_OUT.value(), po.getIpLimitFrequency());
                Assert.isTrue(allCount < po.getIpLimitFrequency(), CodeEnum.NO_TIME_LIMIT_OUT.key(), countHint);
                break;
            case HOURLY:
                int hourlyCount = answerInfoService.selectCountByIpHourly(ip, questionnaireId);
                String hourlyHint = String.format(CodeEnum.HOURLY_FREQUENCY_OUT.value(), po.getIpLimitFrequency());
                Assert.isTrue(hourlyCount < po.getIpLimitFrequency(), CodeEnum.HOURLY_FREQUENCY_OUT.key(), hourlyHint);
                break;
            case DAILY:
                int dailyCount = answerInfoService.selectCountByIpDaily(ip, questionnaireId);
                String dailyHint = String.format(CodeEnum.DAILY_FREQUENCY_OUT.value(), po.getIpLimitFrequency());
                Assert.isTrue(dailyCount < po.getIpLimitFrequency(), CodeEnum.DAILY_FREQUENCY_OUT.key(), dailyHint);
                break;
            case WEEKLY:
                int weeklyCount = answerInfoService.selectCountByIpWeekly(ip, questionnaireId);
                String weeklyHint = String.format(CodeEnum.WEEKLY_FREQUENCY_OUT.value(), po.getIpLimitFrequency());
                Assert.isTrue(weeklyCount < po.getIpLimitFrequency(), CodeEnum.WEEKLY_FREQUENCY_OUT.key(), weeklyHint);
                break;
            case MONTHLY:
                int monthlyCount = answerInfoService.selectCountByIpMonthly(ip, questionnaireId);
                String monthlyHint = String.format(CodeEnum.MONTHLY_FREQUENCY_OUT.value(), po.getIpLimitFrequency());
                Assert.isTrue(monthlyCount < po.getIpLimitFrequency(), CodeEnum.MONTHLY_FREQUENCY_OUT.key(), monthlyHint);
                break;
            case ANNUALLY:
                int annuallyCount = answerInfoService.selectCountByIpAnnually(ip, questionnaireId);
                String annuallyHint = String.format(CodeEnum.ANNUALLY_FREQUENCY_OUT.value(), po.getIpLimitFrequency());
                Assert.isTrue(annuallyCount < po.getIpLimitFrequency(), CodeEnum.ANNUALLY_FREQUENCY_OUT.key(), annuallyHint);
                break;
            default:
                break;
        }
    }

    private void validateDeviceFrequency(String questionnaireId, String deviceCode, QstLimitRulePo po) {
        switch (LimitTypeEnum.toEnum(po.getDeviceLimitType())) {
            case NO_TIME_LIMIT:
                int count = answerInfoService.selectCount(deviceCode, questionnaireId);
                String countHint = String.format(CodeEnum.NO_TIME_LIMIT_OUT.value(), po.getDeviceLimitFrequency());
                Assert.isTrue(count < po.getDeviceLimitFrequency(), CodeEnum.NO_TIME_LIMIT_OUT.key(), countHint);
                break;
            case HOURLY:
                int hourlyCount = answerInfoService.selectCountByDeviceHourly(deviceCode, questionnaireId);
                String hourlyHint = String.format(CodeEnum.HOURLY_FREQUENCY_OUT.value(), po.getDeviceLimitFrequency());
                Assert.isTrue(hourlyCount < po.getDeviceLimitFrequency(), CodeEnum.HOURLY_FREQUENCY_OUT.key(), hourlyHint);
                break;
            case DAILY:
                int dailyCount = answerInfoService.selectCountByDeviceDaily(deviceCode, questionnaireId);
                String dailyHint = String.format(CodeEnum.DAILY_FREQUENCY_OUT.value(), po.getDeviceLimitFrequency());
                Assert.isTrue(dailyCount < po.getDeviceLimitFrequency(), CodeEnum.DAILY_FREQUENCY_OUT.key(), dailyHint);
                break;
            case MONTHLY:
                int monthlyCount = answerInfoService.selectCountByDeviceMonthly(deviceCode, questionnaireId);
                String monthlyHint = String.format(CodeEnum.MONTHLY_FREQUENCY_OUT.value(), po.getDeviceLimitFrequency());
                Assert.isTrue(monthlyCount < po.getDeviceLimitFrequency(), CodeEnum.MONTHLY_FREQUENCY_OUT.key(), monthlyHint);
                break;
            case ANNUALLY:
                int annuallyCount = answerInfoService.selectCountByDeviceAnnually(deviceCode, questionnaireId);
                String annuallyHint = String.format(CodeEnum.ANNUALLY_FREQUENCY_OUT.value(), po.getDeviceLimitFrequency());
                Assert.isTrue(annuallyCount < po.getDeviceLimitFrequency(), CodeEnum.ANNUALLY_FREQUENCY_OUT.key(), annuallyHint);
                break;
        }
    }

    private void validateUsefulTime(QstLimitRulePo po) {
        if (po.getBeginTime() != null && po.getEndTime() == null) {
            String hint = String.format(CodeEnum.QST_NOT_BEGIN.value(), DateUtil.formatTime(po.getBeginTime()));
            Assert.isTrue(DateUtil.compareDate(po.getBeginTime()) != -ONE, CodeEnum.QST_NOT_BEGIN.key(), hint);
        } else if (po.getBeginTime() != null && po.getEndTime() != null) {
            String hint = String.format(CodeEnum.QST_NOT_USEFUL_TIME_NOT_BEGIN.value(), DateUtil.formatTime(po.getBeginTime()), DateUtil.formatTime(po.getEndTime()));
            Assert.isTrue(DateUtil.compareDate(po.getBeginTime()) != -ONE, CodeEnum.QST_NOT_USEFUL_TIME_NOT_BEGIN.key(), hint);

            String hintEnd = String.format(CodeEnum.QST_NOT_USEFUL_TIME_ENDED.value(), DateUtil.formatTime(po.getBeginTime()), DateUtil.formatTime(po.getEndTime()));
            Assert.isTrue(DateUtil.compareDate(po.getEndTime()) != ONE, CodeEnum.QST_NOT_USEFUL_TIME_ENDED.key(), hintEnd);
        } else if (po.getBeginTime() == null && po.getEndTime() != null) {
            String hint = String.format(CodeEnum.QST_END.value(), DateUtil.formatTime(po.getEndTime()));
            Assert.isTrue(DateUtil.compareDate(po.getEndTime()) != ONE, CodeEnum.QST_END.key(), hint);
        }
    }
}

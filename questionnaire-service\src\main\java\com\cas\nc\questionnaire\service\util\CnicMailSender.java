package com.cas.nc.questionnaire.service.util;

import javax.annotation.Resource;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;

import cn.kepu.elearning.jms.mail.MailMessage;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;
import org.springframework.stereotype.Component;

@Component
public class CnicMailSender {

    @Resource
    private JmsTemplate mailTemplate;

    public void send(final MailMessage mailMessage) {
//    	mailTemplate.setPriority(5);
    	mailTemplate.send(new MessageCreator() {
            public Message createMessage(Session session) throws JMSException {
                return session.createObjectMessage(mailMessage);
            }
        });
    }

	public JmsTemplate getMailTemplate() {
		return mailTemplate;
	}

	public void setMailTemplate(JmsTemplate mailTemplate) {
		this.mailTemplate = mailTemplate;
	}

}

package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class TempletOptionPo {
    /*自增id*/
    private Long id;

    /*问卷模板id*/
    private String templetId;

    /*选项id*/
    private String optionId;

    /*用户id*/
    private Long userId;

    /*题目序号*/
    private Integer titleSerialNumber;

    /*序号*/
    private Integer serialNumber;

    /*排列序号*/
    private Integer orderNumber;

    /*内容*/
    private String content;

    /*行标题*/
    private String rowTitle;

    /*行坐标*/
    private Integer rowNumber;

    /*行描述*/
    private String rowDes;

    /*列标题，矩阵题*/
    private String columnTitle;

    /*列坐标*/
    private Integer columnNumber;

    /*分数*/
    private Long score;

    /*计分类型，1：计分，2：不计分*/
    private Integer scoreType;

    /*是否填空，1：填空*/
    private Integer gapFillingType;

    /*填空内容*/
    private String gapFillingContent;

    /*图片url*/
    private String pictureUrl;

    /*图片缩放比例*/
    private Integer pictureScaling;

    /*说明*/
    private String des;

    /*跳题序号，-1 代表结束*/
    private Integer skipNum;

    /*引用题目序号*/
    private Integer quoteNum;

    /*关联题目序号*/
    private Integer relationNum;

    /*是否可空，1：不可空，2：可空*/
    private Integer canInput;

    /* 默认类型：1：默认，2：非默认*/
    private Integer defaultType;

    /*垂直坐标*/
    private Integer verticalNumber;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTempletId() {
        return templetId;
    }

    public void setTempletId(String templetId) {
        this.templetId = templetId;
    }

    public String getOptionId() {
        return optionId;
    }

    public void setOptionId(String optionId) {
        this.optionId = optionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getTitleSerialNumber() {
        return titleSerialNumber;
    }

    public void setTitleSerialNumber(Integer titleSerialNumber) {
        this.titleSerialNumber = titleSerialNumber;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRowTitle() {
        return rowTitle;
    }

    public void setRowTitle(String rowTitle) {
        this.rowTitle = rowTitle;
    }

    public Integer getRowNumber() {
        return rowNumber;
    }

    public void setRowNumber(Integer rowNumber) {
        this.rowNumber = rowNumber;
    }

    public String getRowDes() {
        return rowDes;
    }

    public void setRowDes(String rowDes) {
        this.rowDes = rowDes;
    }

    public String getColumnTitle() {
        return columnTitle;
    }

    public void setColumnTitle(String columnTitle) {
        this.columnTitle = columnTitle;
    }

    public Integer getColumnNumber() {
        return columnNumber;
    }

    public void setColumnNumber(Integer columnNumber) {
        this.columnNumber = columnNumber;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Integer getScoreType() {
        return scoreType;
    }

    public void setScoreType(Integer scoreType) {
        this.scoreType = scoreType;
    }

    public Integer getGapFillingType() {
        return gapFillingType;
    }

    public void setGapFillingType(Integer gapFillingType) {
        this.gapFillingType = gapFillingType;
    }

    public String getGapFillingContent() {
        return gapFillingContent;
    }

    public void setGapFillingContent(String gapFillingContent) {
        this.gapFillingContent = gapFillingContent;
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    public Integer getPictureScaling() {
        return pictureScaling;
    }

    public void setPictureScaling(Integer pictureScaling) {
        this.pictureScaling = pictureScaling;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Integer getSkipNum() {
        return skipNum;
    }

    public void setSkipNum(Integer skipNum) {
        this.skipNum = skipNum;
    }

    public Integer getQuoteNum() {
        return quoteNum;
    }

    public void setQuoteNum(Integer quoteNum) {
        this.quoteNum = quoteNum;
    }

    public Integer getRelationNum() {
        return relationNum;
    }

    public void setRelationNum(Integer relationNum) {
        this.relationNum = relationNum;
    }

    public Integer getCanInput() {
        return canInput;
    }

    public void setCanInput(Integer canInput) {
        this.canInput = canInput;
    }

    public Integer getDefaultType() {
        return defaultType;
    }

    public void setDefaultType(Integer defaultType) {
        this.defaultType = defaultType;
    }

    public Integer getVerticalNumber() {
        return verticalNumber;
    }

    public void setVerticalNumber(Integer verticalNumber) {
        this.verticalNumber = verticalNumber;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
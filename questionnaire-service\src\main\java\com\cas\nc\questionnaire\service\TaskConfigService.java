package com.cas.nc.questionnaire.service;


import com.cas.nc.questionnaire.dao.po.TaskConfigPo;

import java.util.List;
import java.util.Map;

public interface TaskConfigService {
    /**
     * 查询任务配置
     *
     * @return
     */
    List<TaskConfigPo> selectTaskConfig();

    /**
     * 查询所有的任务配置
     *
     * @return
     */
    Map<Integer, TaskConfigPo> getAllTaskConfig();

    /**
     * 加载数据到本地缓存
     *
     */
    void reloadTaskConfigInfo();
}

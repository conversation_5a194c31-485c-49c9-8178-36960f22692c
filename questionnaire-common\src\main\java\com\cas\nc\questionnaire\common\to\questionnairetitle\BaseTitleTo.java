package com.cas.nc.questionnaire.common.to.questionnairetitle;

public class BaseTitleTo {
    /*题目排序*/
    private Integer order;
    /*题目序号*/
    private Integer hao;
    /*题目类型*/
    private String type;
    /*题目标题*/
    private String title;
    /*状态*/
    private Integer status;
    /*是否必答*/
    private Integer require;
    /*题目关联题号*/
    private Integer associateCondition;
    /*跳转类型*/
    private Integer jumpType;
    /*无条件跳转题号*/
    private Integer jumpUncondition;

    private Integer globalOrder;

    private Integer quoteHao;

    /*宽*/
    private Integer width;

    /*高*/
    private Integer height;


    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Integer getHao() {
        return hao;
    }

    public void setHao(Integer hao) {
        this.hao = hao;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getRequire() {
        return require;
    }

    public void setRequire(Integer require) {
        this.require = require;
    }

    public Integer getAssociateCondition() {
        return associateCondition;
    }

    public void setAssociateCondition(Integer associateCondition) {
        this.associateCondition = associateCondition;
    }

    public Integer getJumpType() {
        return jumpType;
    }

    public void setJumpType(Integer jumpType) {
        this.jumpType = jumpType;
    }

    public Integer getJumpUncondition() {
        return jumpUncondition;
    }

    public void setJumpUncondition(Integer jumpUncondition) {
        this.jumpUncondition = jumpUncondition;
    }

    public Integer getGlobalOrder() {
        return globalOrder;
    }

    public void setGlobalOrder(Integer globalOrder) {
        this.globalOrder = globalOrder;
    }

    public Integer getQuoteHao() {
        return quoteHao;
    }

    public void setQuoteHao(Integer quoteHao) {
        this.quoteHao = quoteHao;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }
}

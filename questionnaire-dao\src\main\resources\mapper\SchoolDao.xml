<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.SchoolDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.SchoolPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="school_no" jdbcType="VARCHAR" property="schoolNo"/>
        <result column="school_name" jdbcType="VARCHAR" property="schoolName"/>
        <result column="new_school_name" jdbcType="VARCHAR" property="newSchoolName"/>
        <result column="school_code" jdbcType="VARCHAR" property="schoolCode"/>
        <result column="school_zone" jdbcType="VARCHAR" property="schoolZone"/>
        <result column="school_zone_code" jdbcType="VARCHAR" property="schoolZoneCode"/>
        <result column="school_level" jdbcType="VARCHAR" property="schoolLevel"/>
        <result column="province_id" jdbcType="BIGINT" property="provinceId"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_id" jdbcType="BIGINT" property="cityId"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="area_id" jdbcType="BIGINT" property="areaId"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="street_id" jdbcType="BIGINT" property="streetId"/>
        <result column="street_name" jdbcType="VARCHAR" property="streetName"/>
        <result column="geographical" jdbcType="VARCHAR" property="geographical"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="rotary_longitude" jdbcType="VARCHAR" property="rotaryLongitude"/>
        <result column="rotary_latitude" jdbcType="VARCHAR" property="rotaryLatitude"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,school_no,school_name,new_school_name,school_code,school_zone,school_zone_code,
    school_level,province_id,province_name,city_id,city_name,area_id,
    area_name,street_id,street_name,geographical,longitude,latitude,
    rotary_longitude,rotary_latitude,status,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.schoolNo">and school_no = #{item.schoolNo}</if>
            <if test="null != item.schoolName">and school_name = #{item.schoolName}</if>
            <if test="null != item.newSchoolName">and new_school_name = #{item.newSchoolName}</if>
            <if test="null != item.schoolCode">and school_code = #{item.schoolCode}</if>
            <if test="null != item.schoolZone">and school_zone = #{item.schoolZone}</if>
            <if test="null != item.schoolZoneCode">and school_zone_code = #{item.schoolZoneCode}</if>
            <if test="null != item.schoolLevel">and school_level = #{item.schoolLevel}</if>
            <if test="null != item.provinceId">and province_id = #{item.provinceId}</if>
            <if test="null != item.provinceName">and province_name = #{item.provinceName}</if>
            <if test="null != item.cityId">and city_id = #{item.cityId}</if>
            <if test="null != item.cityName">and city_name = #{item.cityName}</if>
            <if test="null != item.areaId">and area_id = #{item.areaId}</if>
            <if test="null != item.areaName">and area_name = #{item.areaName}</if>
            <if test="null != item.streetId">and street_id = #{item.streetId}</if>
            <if test="null != item.streetName">and street_name = #{item.streetName}</if>
            <if test="null != item.geographical">and geographical = #{item.geographical}</if>
            <if test="null != item.longitude">and longitude = #{item.longitude}</if>
            <if test="null != item.latitude">and latitude = #{item.latitude}</if>
            <if test="null != item.rotaryLongitude">and rotary_longitude = #{item.rotaryLongitude}</if>
            <if test="null != item.rotaryLatitude">and rotary_latitude = #{item.rotaryLatitude}</if>
            <if test="null != item.status">and status = #{item.status}</if>
            <if test="null != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from school
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from school
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from school
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into school
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.schoolNo">school_no,</if>
            <if test="null != item.schoolName">school_name,</if>
            <if test="null != item.newSchoolName">new_school_name,</if>
            <if test="null != item.schoolCode">school_code,</if>
            <if test="null != item.schoolZone">school_zone,</if>
            <if test="null != item.schoolZoneCode">school_zone_code,</if>
            <if test="null != item.schoolLevel">school_level,</if>
            <if test="null != item.provinceId">province_id,</if>
            <if test="null != item.provinceName">province_name,</if>
            <if test="null != item.cityId">city_id,</if>
            <if test="null != item.cityName">city_name,</if>
            <if test="null != item.areaId">area_id,</if>
            <if test="null != item.areaName">area_name,</if>
            <if test="null != item.streetId">street_id,</if>
            <if test="null != item.streetName">street_name,</if>
            <if test="null != item.geographical">geographical,</if>
            <if test="null != item.longitude">longitude,</if>
            <if test="null != item.latitude">latitude,</if>
            <if test="null != item.rotaryLongitude">rotary_longitude,</if>
            <if test="null != item.rotaryLatitude">rotary_latitude,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.schoolNo">#{item.schoolNo},</if>
            <if test="null != item.schoolName">#{item.schoolName},</if>
            <if test="null != item.newSchoolName">#{item.newSchoolName},</if>
            <if test="null != item.schoolCode">#{item.schoolCode},</if>
            <if test="null != item.schoolZone">#{item.schoolZone},</if>
            <if test="null != item.schoolZoneCode">#{item.schoolZoneCode},</if>
            <if test="null != item.schoolLevel">#{item.schoolLevel},</if>
            <if test="null != item.provinceId">#{item.provinceId},</if>
            <if test="null != item.provinceName">#{item.provinceName},</if>
            <if test="null != item.cityId">#{item.cityId},</if>
            <if test="null != item.cityName">#{item.cityName},</if>
            <if test="null != item.areaId">#{item.areaId},</if>
            <if test="null != item.areaName">#{item.areaName},</if>
            <if test="null != item.streetId">#{item.streetId},</if>
            <if test="null != item.streetName">#{item.streetName},</if>
            <if test="null != item.geographical">#{item.geographical},</if>
            <if test="null != item.longitude">#{item.longitude},</if>
            <if test="null != item.latitude">#{item.latitude},</if>
            <if test="null != item.rotaryLongitude">#{item.rotaryLongitude},</if>
            <if test="null != item.rotaryLatitude">#{item.rotaryLatitude},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.schoolNo">school_no = values(school_no),</if>
            <if test="null != item.schoolName">school_name = values(school_name),</if>
            <if test="null != item.newSchoolName">new_school_name = values(new_school_name),</if>
            <if test="null != item.schoolCode">school_code = values(school_code),</if>
            <if test="null != item.schoolZone">school_zone = values(school_zone),</if>
            <if test="null != item.schoolZoneCode">school_zone_code = values(school_zone_code),</if>
            <if test="null != item.schoolLevel">school_level = values(school_level),</if>
            <if test="null != item.provinceId">province_id = values(province_id),</if>
            <if test="null != item.provinceName">province_name = values(province_name),</if>
            <if test="null != item.cityId">city_id = values(city_id),</if>
            <if test="null != item.cityName">city_name = values(city_name),</if>
            <if test="null != item.areaId">area_id = values(area_id),</if>
            <if test="null != item.areaName">area_name = values(area_name),</if>
            <if test="null != item.streetId">street_id = values(street_id),</if>
            <if test="null != item.streetName">street_name = values(street_name),</if>
            <if test="null != item.geographical">geographical = values(geographical),</if>
            <if test="null != item.longitude">longitude = values(longitude),</if>
            <if test="null != item.latitude">latitude = values(latitude),</if>
            <if test="null != item.rotaryLongitude">rotary_longitude = values(rotary_longitude),</if>
            <if test="null != item.rotaryLatitude">rotary_latitude = values(rotary_latitude),</if>
            <if test="null != item.status">status = values(status),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update school
        <set>
            <if test="null != item.schoolNo">school_no = #{item.schoolNo},</if>
            <if test="null != item.schoolName">school_name = #{item.schoolName},</if>
            <if test="null != item.newSchoolName">new_school_name = #{item.newSchoolName},</if>
            <if test="null != item.schoolCode">school_code = #{item.schoolCode},</if>
            <if test="null != item.schoolZone">school_zone = #{item.schoolZone},</if>
            <if test="null != item.schoolZoneCode">school_zone_code = #{item.schoolZoneCode},</if>
            <if test="null != item.schoolLevel">school_level = #{item.schoolLevel},</if>
            <if test="null != item.provinceId">province_id = #{item.provinceId},</if>
            <if test="null != item.provinceName">province_name = #{item.provinceName},</if>
            <if test="null != item.cityId">city_id = #{item.cityId},</if>
            <if test="null != item.cityName">city_name = #{item.cityName},</if>
            <if test="null != item.areaId">area_id = #{item.areaId},</if>
            <if test="null != item.areaName">area_name = #{item.areaName},</if>
            <if test="null != item.streetId">street_id = #{item.streetId},</if>
            <if test="null != item.streetName">street_name = #{item.streetName},</if>
            <if test="null != item.geographical">geographical = #{item.geographical},</if>
            <if test="null != item.longitude">longitude = #{item.longitude},</if>
            <if test="null != item.latitude">latitude = #{item.latitude},</if>
            <if test="null != item.rotaryLongitude">rotary_longitude = #{item.rotaryLongitude},</if>
            <if test="null != item.rotaryLatitude">rotary_latitude = #{item.rotaryLatitude},</if>
            <if test="null != item.status">status = #{item.status},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from school
        <include refid="sql_where"/>
    </delete>
</mapper>
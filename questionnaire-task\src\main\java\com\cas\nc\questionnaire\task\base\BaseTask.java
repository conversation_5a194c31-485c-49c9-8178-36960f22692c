package com.cas.nc.questionnaire.task.base;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public abstract class BaseTask<T> {
    private static Logger logger = LoggerFactory.getLogger(BaseTask.class);

    /**
     * 获取符合执行任务的所有数据
     *
     * @return List
     */
    protected abstract List<T> getTasks();


    /**
     * 任务执行前调用该方法,可做初始化操作,如数据加锁
     *
     * @param task
     * @return boolean true:调用process方法; false: 初始化失败不调用process方法;
     */
    protected abstract boolean beforeProcess(T task);


    /**
     * 任务的主体方法.
     *
     * @param task
     * @return int finishState
     */
    protected abstract boolean process(T task);

    /**
     * 任务执行成功并返回true时调用该方法,进行后续状态修改等操作.
     *
     * @param task
     * @return
     */
    protected abstract boolean afterProcess(T task);

    /**
     * 解锁任务
     */
    protected abstract void unlockTask();

    public void execute() {
        try {
            unlockTask();
            final List<T> tasks = getTasks();
            for (final T task : tasks) {
                if (beforeProcess(task) && process(task)) {
                    afterProcess(task);
                }
            }
        } catch (Exception e) {
            logger.error("BaseTask.execute Exception", e);
        }
    }
}

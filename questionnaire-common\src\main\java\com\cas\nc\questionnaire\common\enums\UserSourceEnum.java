package com.cas.nc.questionnaire.common.enums;

public enum UserSourceEnum {
    QUESTIONNAIRE(1, "问卷"),
    KXPX(2, "科协培训"),
    EL(3, "学习平台"),
    ES(4, "科技云"),
    ELSE(99, "其他");

    private final Integer key;
    private final String value;


    UserSourceEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static UserSourceEnum toEnum(int key) {
        for (UserSourceEnum value : values()) {
            if (key == value.key) {
                return value;
            }
        }
        return ELSE;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }
}

package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.dao.nosharddao.UrlConfigDao;
import com.cas.nc.questionnaire.dao.po.UrlConfigPo;
import com.cas.nc.questionnaire.dao.query.UrlConfigQuery;
import com.cas.nc.questionnaire.service.UrlConfigService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class UrlConfigServiceImpl implements UrlConfigService {
    private static Logger logger = LoggerFactory.getLogger(UrlConfigServiceImpl.class);

    @Resource
    private UrlConfigDao urlConfigDao;


    @Override
    public List<UrlConfigPo> selectAll() {
        UrlConfigQuery query = new UrlConfigQuery();

        return urlConfigDao.selectList(query);
    }

    @Override
    public UrlConfigPo selectOne(String url) {
        try {
//            return urlCache.get(url);
            UrlConfigQuery query = new UrlConfigQuery();
            query.setUrl(url);
            return urlConfigDao.selectOne(query);
        } catch (Exception e) {
            logger.error("UrlConfigServiceImpl.selectOne url[{}]", url, e);
        }
        return null;
    }

    private LoadingCache<String, UrlConfigPo> urlCache = CacheBuilder.newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100)
            .recordStats()
            .build(new CacheLoader<String, UrlConfigPo>() {
                @Override
                public UrlConfigPo load(String url) throws Exception {
                    UrlConfigQuery query = new UrlConfigQuery();
                    query.setUrl(url);
                    return urlConfigDao.selectOne(query);
                }
            });
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.EmailServerConfigDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.EmailServerConfigPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="email_host" jdbcType="VARCHAR" property="emailHost"/>
        <result column="email_port" jdbcType="INTEGER" property="emailPort"/>
        <result column="email_address" jdbcType="VARCHAR" property="emailAddress"/>
        <result column="email_password" jdbcType="VARCHAR" property="emailPassword"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,email_host,email_port,email_address,email_password,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.emailHost and '' != item.emailHost">and email_host = #{item.emailHost}</if>
            <if test="null != item.emailPort and '' != item.emailPort">and email_port = #{item.emailPort}</if>
            <if test="null != item.emailAddress and '' != item.emailAddress">and email_address = #{item.emailAddress}
            </if>
            <if test="null != item.emailPassword and '' != item.emailPassword">and email_password =
                #{item.emailPassword}
            </if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from email_server_config
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from email_server_config
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from email_server_config
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into email_server_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.emailHost">email_host,</if>
            <if test="null != item.emailPort">email_port,</if>
            <if test="null != item.emailAddress">email_address,</if>
            <if test="null != item.emailPassword">email_password,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.emailHost">#{item.emailHost},</if>
            <if test="null != item.emailPort">#{item.emailPort},</if>
            <if test="null != item.emailAddress">#{item.emailAddress},</if>
            <if test="null != item.emailPassword">#{item.emailPassword},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.emailHost">email_host = values(email_host),</if>
            <if test="null != item.emailPort">email_port = values(email_port),</if>
            <if test="null != item.emailAddress">email_address = values(email_address),</if>
            <if test="null != item.emailPassword">email_password = values(email_password),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update email_server_config
        <set>
            <if test="null != item.emailHost">email_host = #{item.emailHost},</if>
            <if test="null != item.emailPort">email_port = #{item.emailPort},</if>
            <if test="null != item.emailAddress">email_address = #{item.emailAddress},</if>
            <if test="null != item.emailPassword">email_password = #{item.emailPassword},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from email_server_config
        <include refid="sql_where"/>
    </delete>

    <select id="selectAll" resultMap="BaseResultMap">
        select * from email_server_config
    </select>
</mapper>
package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.templet.GetTempletReqDto;
import com.cas.nc.questionnaire.common.dto.templet.ListTempletReqDto;
import com.cas.nc.questionnaire.common.vo.templet.GetTempletReqVo;
import com.cas.nc.questionnaire.common.vo.templet.ListTempletReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TempletConverter {
    TempletConverter INSTANCE = Mappers.getMapper(TempletConverter.class);

    ListTempletReqDto to(ListTempletReqVo vo);

    GetTempletReqDto to(GetTempletReqVo vo);
}
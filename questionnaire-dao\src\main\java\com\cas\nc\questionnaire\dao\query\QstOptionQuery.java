package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.QstOptionPo;


public class QstOptionQuery extends QstOptionPo {
    private String tableColumns;
    private String newQuestionnaireId;
    /*开始索引*/
    private int startIndex;
    /*页面大小*/
    private Integer pageSize;

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public String getNewQuestionnaireId() {
        return newQuestionnaireId;
    }

    public void setNewQuestionnaireId(String newQuestionnaireId) {
        this.newQuestionnaireId = newQuestionnaireId;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}

package com.cas.nc.questionnaire.common.dto.area;

import java.util.List;
import java.util.Map;


public class AreaProvinceRepDto {
    private Long id;
    private String name;
    private List<AreaCityRepDto> cityList;
    private Map<String, AreaCityRepDto> cityMap;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<AreaCityRepDto> getCityList() {
        return cityList;
    }

    public void setCityList(List<AreaCityRepDto> cityList) {
        this.cityList = cityList;
    }

    public Map<String, AreaCityRepDto> getCityMap() {
        return cityMap;
    }

    public void setCityMap(Map<String, AreaCityRepDto> cityMap) {
        this.cityMap = cityMap;
    }
}

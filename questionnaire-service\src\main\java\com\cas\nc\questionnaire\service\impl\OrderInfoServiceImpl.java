package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.OrderStatusEnum;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.dao.nosharddao.OrderInfoDao;
import com.cas.nc.questionnaire.dao.po.OrderInfoPo;
import com.cas.nc.questionnaire.dao.query.OrderInfoQuery;
import com.cas.nc.questionnaire.dao.query.QstEmailQuery;
import com.cas.nc.questionnaire.service.OrderInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class OrderInfoServiceImpl implements OrderInfoService {
    private static Logger logger = LoggerFactory.getLogger(OrderInfoServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private OrderInfoDao orderInfoDao;

    @Override
    public int insert(OrderInfoPo orderInfoPo) {
        return orderInfoDao.insert(orderInfoPo);
    }

    @Override
    public OrderInfoPo selectOne(OrderInfoQuery query) {
        return orderInfoDao.selectOne(query);
    }

    @Override
    public OrderInfoPo selectOne(String orderNo) {
        OrderInfoQuery query = new OrderInfoQuery();
        query.setOrderNo(orderNo);
        return selectOne(query);
    }

    @Override
    public int update(OrderInfoQuery query) {
        return orderInfoDao.update(query);
    }

    @Override
    public int delete(OrderInfoQuery query) {
        Assert.notNull(query.getOrderNo(), "orderNo");
        return orderInfoDao.delete(query);
    }

    @Override
    public int updatePrePay(String orderNo, String payUrl, String prePayId) {
        OrderInfoQuery query = new OrderInfoQuery();
        query.setOrderNo(orderNo);
        query.setPayUrl(payUrl);
        query.setPrePayId(prePayId);
        query.setPrePayTime(DateUtil.addMinutes(new Date(), 90));

        return update(query);
    }

    @Override
    public int selectMaxVersion(OrderInfoQuery query) {
        return orderInfoDao.selectMaxVersion(query);
    }

    @Override
    public List<OrderInfoPo> selectList(Long userId) {
        OrderInfoQuery query = new OrderInfoQuery();
        query.setUserId(userId);
        query.setStatus(OrderStatusEnum.SUCCESS.key());

        validateUserId(query);
        return orderInfoDao.selectList(query);
    }

    @Override
    public List<OrderInfoPo> selectListByPage(OrderInfoQuery query) {
        return orderInfoDao.selectListByPage(query);
    }

    @Override
    public int selectCount(OrderInfoQuery query) {
        return orderInfoDao.selectCount(query);
    }

    private void validateUserId(OrderInfoQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }
}

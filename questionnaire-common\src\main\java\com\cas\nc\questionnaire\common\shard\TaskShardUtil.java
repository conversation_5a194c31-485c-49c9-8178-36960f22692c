package com.cas.nc.questionnaire.common.shard;



public class TaskShardUtil {
    public static final int MAX_TABLES = 4;//最大表数量
    public static final int MAX_CURRENT_DBS = 1;//目前的库最大数量

//    public static String getShardKey(String routeField) {
//        long hash = HashUtil.fnv1_31(routeField);
//        long medium = hash % MAX_TABLES;
//        long dataBaseIndex = medium / MAX_TABLES / MAX_CURRENT_DBS + 1;
//        long tableIndex = medium % (MAX_TABLES / MAX_CURRENT_DBS);
//        return getFullValue(dataBaseIndex) + getFullValue(tableIndex);
//    }

//    public static long convertShardKeyToLong(String routeField) {
//        return HashUtil.fnv1_31(routeField);
//    }

//    public static int getDatabase(String id) {
//        long hash = HashUtil.fnv1_31(id);
//        long medium = hash % MAX_TABLES;
//        long dataBaseIndex = medium / (MAX_TABLES / MAX_CURRENT_DBS) + 1;
//        return (int) dataBaseIndex;
//    }

    public static int getTable(String id) {
//        long hash = HashUtil.fnv1_31(id);
//        long medium = hash % MAX_TABLES;
//        long tableIndex = medium % (MAX_TABLES / MAX_CURRENT_DBS);
        int h = id.hashCode() ;
        int tableIndex = (h ^ (h >>> 16)) & MAX_TABLES - 1;
        return tableIndex;
    }

//    private static String getFullValue(long index) {
//        if (index >= 100) {
//            return String.valueOf(index);
//        } else if (index >= 10 && index < 100) {
//            return String.valueOf("0" + index);
//        } else {
//            return String.valueOf("00" + index);
//        }
//    }

//    public static boolean matchDataBase(String mark, String routeId) {
//        return splitMark(mark) == getDatabase(routeId);
//    }

    public static boolean matchTable(String mark, String routeId) {
        return splitMark(mark) == Integer.valueOf(routeId).intValue();
    }

    public static int splitMark(String mark) {
        String[] str = mark.split("_");
        return Integer.valueOf(str[str.length - 1]);
    }

    public static String getSubstringShardKey(String id) {
        return id.substring(1, 7);
    }

    public static String getSubstringDatabase(String id) {
        return id.substring(1, 4);
    }

    public static String getSubstringTable(String id) {
        return id.substring(4, 7);
    }

    public static void main(String[] args) {
        System.out.print(HashUtil.fnv1_31("10.168.153.125") % 4);
    }
}

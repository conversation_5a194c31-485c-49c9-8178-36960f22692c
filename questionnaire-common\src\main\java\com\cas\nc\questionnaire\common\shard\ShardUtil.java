package com.cas.nc.questionnaire.common.shard;



public class ShardUtil {
    public static final int MAX_TABLES = 127;//最大表数量
    public static final int MAX_CURRENT_DBS = 1;//目前的库最大数量

    public static int getDatabase(String id) {
        long hash = HashUtil.fnv1_31(id);
        long medium = hash % MAX_TABLES;
        long dataBaseIndex = medium / (MAX_TABLES / MAX_CURRENT_DBS) + 1;
        return (int) dataBaseIndex;
    }

    public static int getDatabase(long id) {
        long hash = HashUtil.fnv1_31(String.valueOf(id));
        long medium = hash % MAX_TABLES;
        long dataBaseIndex = medium / (MAX_TABLES / MAX_CURRENT_DBS) + 1;
        return (int) dataBaseIndex;
    }

    public static long getTable(String id) {
        long hash = HashUtil.fnv1_31(id);
        long medium = hash % MAX_TABLES;
        long tableIndex = medium % (MAX_TABLES / MAX_CURRENT_DBS);
        return tableIndex;
    }

    public static long getTable(long id) {
        long hash = HashUtil.fnv1_31(String.valueOf(id));
        long medium = hash % MAX_TABLES;
        long tableIndex = medium % (MAX_TABLES / MAX_CURRENT_DBS);
        return tableIndex;
    }

    public static boolean matchDataBase(String mark, String routeId) {
        return splitMark(mark) == getDatabase(routeId);
    }

    public static boolean matchTable(String mark, String routeId) {
        return splitMark(mark) == getTable(routeId);
    }

    public static boolean matchTable(String mark, long routeId) {
        return splitMark(mark) == getTable(routeId);
    }

    public static long splitMark(String mark) {
        String[] str = mark.split("_");
        return Long.valueOf(str[str.length - 1]);
    }

    public static String getSubstringShardKey(String id) {
        return id.substring(1, 7);
    }

    public static String getSubstringDatabase(String id) {
        return id.substring(1, 4);
    }

    public static String getSubstringTable(String id) {
        return id.substring(4, 7);
    }

    public static void main(String[] args) {
        System.out.println(getTable(SequenceUtil.getInstance().parse2UserId("22061412124577676537255978599826848694443212800001")));

        System.out.println(SequenceUtil.getInstance().parse2UserId("22061412124577676537255978599826848694443212800001"));
        System.out.println(getTable("776765372559786020"));

        System.out.println(SequenceUtil.getInstance().generateQuestionnaireId("23"));
    }
}

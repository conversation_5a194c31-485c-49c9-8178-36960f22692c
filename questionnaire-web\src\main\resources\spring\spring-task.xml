<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:task="http://www.springframework.org/schema/task"
       default-autowire="byName"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd">
    <!-- Async 异步注解相关 -->
    <!--<task:annotation-driven executor="asyncExecutor"/>-->
    <!--<task:executor id="asyncExecutor" pool-size="100-500" queue-capacity="100"/>-->
    <task:scheduler id="poolTaskScheduler" pool-size="20"/>

    <bean id="testTask" class="com.cas.nc.questionnaire.task.tasks.EmailSendTask" />

    <task:scheduled-tasks scheduler="poolTaskScheduler">
        <!--自动调整优先级任务-->
        <task:scheduled ref="testTask"  method="execute" cron="0 */1 * * * ?"/>
    </task:scheduled-tasks>
</beans>

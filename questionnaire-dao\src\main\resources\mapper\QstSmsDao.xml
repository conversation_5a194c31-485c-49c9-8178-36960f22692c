<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstSmsDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstSmsPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sms_id" jdbcType="VARCHAR" property="smsId"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="foreign_id" jdbcType="VARCHAR" property="foreignId"/>
        <result column="source_type" jdbcType="INTEGER" property="sourceType"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="phone_type" jdbcType="INTEGER" property="phoneType"/>
        <result column="serial_number" jdbcType="INTEGER" property="serialNumber"/>
        <result column="send_time" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="phones" jdbcType="LONGVARCHAR" property="phones"/>
    </resultMap>
    <sql id="sql_columns">
    id,sms_id,questionnaire_id,user_id,foreign_id,source_type,content,
    status,phone_type,serial_number,send_time,update_time,create_time,
    phones
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.smsId">and sms_id = #{item.smsId}</if>
            <if test="null != item.questionnaireId">and questionnaire_id = #{item.questionnaireId}</if>
            <if test="null != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.foreignId">and foreign_id = #{item.foreignId}</if>
            <if test="null != item.sourceType">and source_type = #{item.sourceType}</if>
            <if test="null != item.content">and content = #{item.content}</if>
            <if test="null != item.status">and status = #{item.status}</if>
            <if test="null != item.phoneType">and phone_type = #{item.phoneType}</if>
            <if test="null != item.serialNumber">and serial_number = #{item.serialNumber}</if>
            <if test="null != item.sendTime">and send_time = #{item.sendTime}</if>
            <if test="null != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime">and create_time = #{item.createTime}</if>
            <if test="null != item.phones">and phones = #{item.phones}</if>

            <if test="null != item.oldStatus">and status = #{item.oldStatus}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_sms
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_sms
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_sms
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into qst_sms
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.smsId">sms_id,</if>
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.foreignId">foreign_id,</if>
            <if test="null != item.sourceType">source_type,</if>
            <if test="null != item.content">content,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.phoneType">phone_type,</if>
            <if test="null != item.serialNumber">serial_number,</if>
            <if test="null != item.sendTime">send_time,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
            <if test="null != item.phones">phones,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.smsId">#{item.smsId},</if>
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.foreignId">#{item.foreignId},</if>
            <if test="null != item.sourceType">#{item.sourceType},</if>
            <if test="null != item.content">#{item.content},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.phoneType">#{item.phoneType},</if>
            <if test="null != item.serialNumber">#{item.serialNumber},</if>
            <if test="null != item.sendTime">#{item.sendTime},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
            <if test="null != item.phones">#{item.phones},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.smsId">sms_id = values(sms_id),</if>
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.foreignId">foreign_id = values(foreign_id),</if>
            <if test="null != item.sourceType">source_type = values(source_type),</if>
            <if test="null != item.content">content = values(content),</if>
            <if test="null != item.status">status = values(status),</if>
            <if test="null != item.phoneType">phone_type = values(phone_type),</if>
            <if test="null != item.serialNumber">serial_number = values(serial_number),</if>
            <if test="null != item.sendTime">send_time = values(send_time),</if>
            <if test="null != item.phones">phones = values(phones),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update qst_sms
        <set>
            <if test="null != item.smsId">sms_id = #{item.smsId},</if>
            <if test="null != item.questionnaireId">questionnaire_id = #{item.questionnaireId},</if>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.foreignId">foreign_id = #{item.foreignId},</if>
            <if test="null != item.sourceType">source_type = #{item.sourceType},</if>
            <if test="null != item.content">content = #{item.content},</if>
            <if test="null != item.status">status = #{item.status},</if>
            <if test="null != item.phoneType">phone_type = #{item.phoneType},</if>
            <if test="null != item.serialNumber">serial_number = #{item.serialNumber},</if>
            <if test="null != item.sendTime">send_time = #{item.sendTime},</if>
            <if test="null != item.phones">phones = #{item.phones},</if>
        </set>
        where sms_id = #{item.smsId}
        and user_id = #{item.userId}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_sms
        <include refid="sql_where"/>
    </delete>
</mapper>
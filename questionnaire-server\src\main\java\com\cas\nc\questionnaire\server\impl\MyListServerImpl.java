package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.mylist.*;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.PaginateUtils;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.common.utils.ThreadUtil;
import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.query.AnswerInfoQuery;
import com.cas.nc.questionnaire.dao.query.AnswerOptionQuery;
import com.cas.nc.questionnaire.dao.query.QstEmailQuery;
import com.cas.nc.questionnaire.dao.query.QstFilterRuleQuery;
import com.cas.nc.questionnaire.dao.query.QstLimitConditionQuery;
import com.cas.nc.questionnaire.dao.query.QstLimitRuleQuery;
import com.cas.nc.questionnaire.dao.query.QstOptionQuery;
import com.cas.nc.questionnaire.dao.query.QstQuestionnaireInfoQuery;
import com.cas.nc.questionnaire.dao.query.QstQuotaOptionRuleQuery;
import com.cas.nc.questionnaire.dao.query.QstQuotaRuleQuery;
import com.cas.nc.questionnaire.dao.query.QstSmsQuery;
import com.cas.nc.questionnaire.dao.query.QstTitleQuery;
import com.cas.nc.questionnaire.server.MyListServer;
import com.cas.nc.questionnaire.server.mapstruct.ExternalListServerConverter;
import com.cas.nc.questionnaire.server.mapstruct.MyListConverter;
import com.cas.nc.questionnaire.server.mapstruct.RecycleBinListConverter;
import com.cas.nc.questionnaire.server.util.ConvertBeanUtil;
import com.cas.nc.questionnaire.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.UN_SEND_QST;
import static com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum.*;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;

@Component
public class MyListServerImpl implements MyListServer {
    private final static Logger logger = LoggerFactory.getLogger(MyListServerImpl.class);

    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;
    @Resource
    private QstOptionService qstOptionService;
    @Resource
    private QstTitleService qstTitleService;
    @Resource
    private QstEmailService qstEmailService;
    @Resource
    private QstFilterRuleService qstFilterRuleService;
    @Resource
    private QstLimitConditionService qstLimitConditionService;
    @Resource
    private QstLimitRuleService qstLimitRuleService;
    @Resource
    private QstQuotaOptionRuleService qstQuotaOptionRuleService;
    @Resource
    private QstQuotaRuleService qstQuotaRuleService;
    @Resource
    private QstSmsService qstSmsService;
    @Resource
    private AnswerInfoService answerInfoService;
    @Resource
    private AnswerOptionService answerOptionService;

    @Override
    public MyListRepDto list(MyListReqDto reqDto) {
        MyListRepDto repDto = new MyListRepDto();
        PaginateUtils<MyListQuestionnaireRepDto> repDtoPaginate = new PaginateUtils<MyListQuestionnaireRepDto>(reqDto.getPage(), reqDto.getPageSize());

        QstQuestionnaireInfoQuery query = ConvertBeanUtil.myListReqDto2Query(reqDto);
        query.setStatusList(MY_LIST_QUERY_LIST);
        int countNum = qstQuestionnaireInfoService.selectCount(query);
        repDtoPaginate.setRecordTotal(countNum);

        query.setPageSize(repDtoPaginate.getPageSize());
        query.setStartIndex(repDtoPaginate.getStartIndex());
        List<QstQuestionnaireInfoPo> queryList = qstQuestionnaireInfoService.selectMyListByPage(query);

        List<MyListQuestionnaireRepDto> repDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(queryList)) {
            for (QstQuestionnaireInfoPo bean : queryList) {
                MyListQuestionnaireRepDto questionnaireRepDto = MyListConverter.INSTANCE.to(bean);
                questionnaireRepDto.setAnswerCount(answerInfoService.selectCount(reqDto.getUserId(), bean.getQuestionnaireId()));
                repDtoList.add(questionnaireRepDto);
            }
        }

        repDto.setQuestionnaireList(repDtoList);
        repDto.setTotal(countNum);
        return repDto;
    }

    @Override
    public List<MyListRecycleBinListRepDto> recycleBinList(MyListRecycleBinListReqDto reqDto) {
        List<MyListRecycleBinListRepDto> repDtoList = new ArrayList<>();
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setUserId(reqDto.getUserId());
        query.setStatus(QuestionnaireStatusEnum.MARK_DELETING.key());

        List<QstQuestionnaireInfoPo> poList = qstQuestionnaireInfoService.selectList(query);
        if (!CollectionUtils.isEmpty(poList)) {
            for (QstQuestionnaireInfoPo bean : poList) {
                MyListRecycleBinListRepDto repDto = RecycleBinListConverter.INSTANCE.to(bean);
                repDto.setAnswerCount(answerInfoService.selectCount(reqDto.getUserId(), reqDto.getQuestionnaireId()));
                repDtoList.add(repDto);
            }
        }
        return repDtoList;
    }

    @Override
    public void delete(MyListDeleteReqDto reqDto) {
        List<String> questionnaireIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reqDto.getQuestionnaireIdList())) {
            questionnaireIdList.addAll(reqDto.getQuestionnaireIdList());
        } else if (reqDto.getQuestionnaireId() != null) {
            questionnaireIdList.add(reqDto.getQuestionnaireId());
        }

        for (String bean : questionnaireIdList) {
            qstQuestionnaireInfoService.updateStatus2Delete(bean, reqDto.getUserId());
        }
    }

    @Override
    public void recycleBinDelete(MyListRecycleBinDeleteReqDto reqDto) {
        if (CollectionUtils.isEmpty(reqDto.getQuestionnaireIdList())) {
            return;
        }

        for (String bean : reqDto.getQuestionnaireIdList()) {
            QstQuestionnaireInfoQuery questionnaireInfoQuery = new QstQuestionnaireInfoQuery();
            questionnaireInfoQuery.setUserId(reqDto.getUserId());
            questionnaireInfoQuery.setQuestionnaireId(bean);
            qstQuestionnaireInfoService.delete(questionnaireInfoQuery);
        }
        ThreadUtil.submit(() -> {
            for (String bean : reqDto.getQuestionnaireIdList()) {
                deleteElse(reqDto.getUserId(), bean);
            }
        });
    }

    @Override
    public void recovery(MyListRecoveryReqDto reqDto) {
        int result = qstQuestionnaireInfoService.updateStatus2Recovery(reqDto.getQuestionnaireId(), reqDto.getUserId());
        Assert.isTrue(result == ONE, CodeEnum.UPDATE_EXCEPTION);
    }

    @Override
    public void pause(MyListPauseReqDto reqDto) {
        int result = qstQuestionnaireInfoService.updateStatus2Pause(reqDto.getQuestionnaireId(), reqDto.getUserId());
        Assert.isTrue(result == ONE, CodeEnum.UPDATE_EXCEPTION);
    }

    @Override
    public String copy(MyListCopyReqDto reqDto) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        String questionnaireId = qstQuestionnaireInfoService.copy(query, reqDto.getNewName());

        QstTitleQuery titleQuery = new QstTitleQuery();
        titleQuery.setNewQuestionnaireId(questionnaireId);
        titleQuery.setQuestionnaireId(reqDto.getQuestionnaireId());
        titleQuery.setUserId(reqDto.getUserId());
        qstTitleService.copy(titleQuery);

        QstOptionQuery optionQuery = new QstOptionQuery();
        optionQuery.setNewQuestionnaireId(questionnaireId);
        optionQuery.setQuestionnaireId(reqDto.getQuestionnaireId());
        optionQuery.setUserId(reqDto.getUserId());
        qstOptionService.copy(optionQuery);
        return questionnaireId;
    }

    @Override
    public void publish(MyListPublishReqDto reqDto) {
        int result = qstQuestionnaireInfoService.updateStatusPause2Publish(reqDto.getQuestionnaireId(), reqDto.getUserId());
        if (result == 0) {
            QstQuestionnaireInfoPo po = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId(), reqDto.getUserId());
            if (isInit(po.getStatus())) {
                throw new ServerException(UN_SEND_QST);
            }
            if (isRun(po.getStatus())) {
                return;
            }
        }
        Assert.isTrue(result == ONE, CodeEnum.UPDATE_EXCEPTION);
    }

    @Override
    public List<ExternalListRepDto> externalList(ExternalListReqDto reqDto) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setUserId(reqDto.getUserId());
        query.setBizId(reqDto.getBizId());
        query.setLikeBizNo(reqDto.getBizNo());
        query.setStatusList(MY_LIST_QUERY_LIST);

        List<QstQuestionnaireInfoPo> questionnaireInfoPoList = qstQuestionnaireInfoService.selectList(query);
        List<ExternalListRepDto> result = ExternalListServerConverter.INSTANCE.to(questionnaireInfoPoList);

        List<String> questionnaireIdList = SafeUtil.of(questionnaireInfoPoList).stream().map(QstQuestionnaireInfoPo::getQuestionnaireId).collect(Collectors.toList());
        QstLimitRuleQuery limitRuleQuery = new QstLimitRuleQuery();
        limitRuleQuery.setQuestionnaireIdList(questionnaireIdList);
        limitRuleQuery.setUserId(reqDto.getUserId());

        List<QstLimitRulePo> qstLimitRulePoList = qstLimitRuleService.selectList(limitRuleQuery);
        Map<String, QstLimitRulePo> qstLimitRulePoMap = SafeUtil.of(qstLimitRulePoList).stream().collect(Collectors.toMap(QstLimitRulePo::getQuestionnaireId, v -> v));

        SafeUtil.of(result).forEach(v -> {
            QstLimitRulePo qstLimitRulePo = qstLimitRulePoMap.get(v.getQuestionnaireId());
            if (qstLimitRulePo != null) {
                v.setBeginTime(qstLimitRulePo.getBeginTime());
                v.setEndTime(qstLimitRulePo.getEndTime());
            }
        });
        return result;
    }

    private void deleteElse(Long userId, String questionnaireId) {
        QstOptionQuery qstOptionQuery = new QstOptionQuery();
        qstOptionQuery.setUserId(userId);
        qstOptionQuery.setQuestionnaireId(questionnaireId);
        qstOptionService.delete(qstOptionQuery);

        QstTitleQuery qstTitleQuery = new QstTitleQuery();
        qstTitleQuery.setUserId(userId);
        qstTitleQuery.setQuestionnaireId(questionnaireId);
        qstTitleService.delete(qstTitleQuery);

        QstEmailQuery qstEmailQuery = new QstEmailQuery();
        qstEmailQuery.setUserId(userId);
        qstEmailQuery.setQuestionnaireId(questionnaireId);
        qstEmailService.delete(qstEmailQuery);

        QstFilterRuleQuery qstFilterRuleQuery = new QstFilterRuleQuery();
        qstFilterRuleQuery.setUserId(userId);
        qstFilterRuleQuery.setQuestionnaireId(questionnaireId);
        qstFilterRuleService.delete(qstFilterRuleQuery);

        QstLimitConditionQuery qstLimitConditionQuery = new QstLimitConditionQuery();
        qstLimitConditionQuery.setUserId(userId);
        qstLimitConditionQuery.setQuestionnaireId(questionnaireId);
        qstLimitConditionService.delete(qstLimitConditionQuery);

        QstLimitRuleQuery qstLimitRuleQuery = new QstLimitRuleQuery();
        qstLimitRuleQuery.setUserId(userId);
        qstLimitRuleQuery.setQuestionnaireId(questionnaireId);
        qstLimitRuleService.delete(qstLimitRuleQuery);

        QstQuotaOptionRuleQuery qstQuotaOptionRuleQuery = new QstQuotaOptionRuleQuery();
        qstQuotaOptionRuleQuery.setUserId(userId);
        qstQuotaOptionRuleQuery.setQuestionnaireId(questionnaireId);
        qstQuotaOptionRuleService.delete(qstQuotaOptionRuleQuery);

        QstQuotaRuleQuery qstQuotaRuleQuery = new QstQuotaRuleQuery();
        qstQuotaRuleQuery.setUserId(userId);
        qstQuotaRuleQuery.setQuestionnaireId(questionnaireId);
        qstQuotaRuleService.delete(qstQuotaRuleQuery);

        QstSmsQuery qstSmsQuery = new QstSmsQuery();
        qstSmsQuery.setUserId(userId);
        qstSmsQuery.setQuestionnaireId(questionnaireId);
        qstSmsService.delete(qstSmsQuery);

        AnswerInfoQuery answerInfoQuery = new AnswerInfoQuery();
        answerInfoQuery.setUserId(userId);
        answerInfoQuery.setQuestionnaireId(questionnaireId);
        answerInfoService.delete(answerInfoQuery);

        AnswerOptionQuery answerOptionQuery = new AnswerOptionQuery();
        answerOptionQuery.setUserId(userId);
        answerOptionQuery.setQuestionnaireId(questionnaireId);
        answerOptionService.delete(answerOptionQuery);
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.LoginauthDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.LoginauthPo">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="userId" jdbcType="BIGINT" property="userId"/>
        <result column="authenId" jdbcType="VARCHAR" property="authenId"/>
        <result column="modifytime" jdbcType="TIMESTAMP" property="modifytime"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="authInfo" jdbcType="VARCHAR" property="authInfo"/>
        <result column="authUserId" jdbcType="VARCHAR" property="authUserId"/>
    </resultMap>
    <sql id="sql_columns">
    id,userId,authenId,modifytime,type,authInfo,authUserId
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.userId and '' != item.userId">and userId = #{item.userId}</if>
            <if test="null != item.authenId and '' != item.authenId">and authenId = #{item.authenId}</if>
            <if test="null != item.modifytime and '' != item.modifytime">and modifytime = #{item.modifytime}</if>
            <if test="null != item.type and '' != item.type">and type = #{item.type}</if>
            <if test="null != item.authInfo and '' != item.authInfo">and authInfo = #{item.authInfo}</if>
            <if test="null != item.authUserId and '' != item.authUserId">and authUserId = #{item.authUserId}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from loginauth
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from loginauth
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from loginauth
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into loginauth
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.userId ">userId,</if>
            <if test="null != item.authenId ">authenId,</if>
            <if test="null != item.modifytime">modifytime,</if>
            <if test="null != item.type ">type,</if>
            <if test="null != item.authInfo">authInfo,</if>
            <if test="null != item.authUserId ">authUserId,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.userId ">#{item.userId},</if>
            <if test="null != item.authenId ">#{item.authenId},</if>
            <if test="null != item.modifytime">#{item.modifytime},</if>
            <if test="null != item.type ">#{item.type},</if>
            <if test="null != item.authInfo">#{item.authInfo},</if>
            <if test="null != item.authUserId ">#{item.authUserId},</if>
        </trim>
    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.userId ">userId = values(userId),</if>
            <if test="null != item.authenId ">authenId = values(authenId),</if>
            <if test="null != item.modifytime">modifytime = values(modifytime),</if>
            <if test="null != item.type ">type = values(type),</if>
            <if test="null != item.authInfo">authInfo = values(authInfo),</if>
            <if test="null != item.authUserId ">authUserId = values(authUserId),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update loginauth
        <set>
            <if test="null != item.userId ">userId =#{item.userId},</if>
            <if test="null != item.authenId ">authenId =#{item.authenId},</if>
            <if test="null != item.modifytime">modifytime = #{item.modifytime},</if>
            <if test="null != item.type ">type = #{item.type},</if>
            <if test="null != item.authInfo">authInfo = #{item.authInfo},</if>
            <if test="null != item.authUserId ">authUserId =#{item.authUserId},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from loginauth
        <include refid="sql_where"/>
    </delete>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstQuestionnaireInfoDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="out_user_id" jdbcType="VARCHAR" property="outUserId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="des" jdbcType="VARCHAR" property="des"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="control_status" jdbcType="INTEGER" property="controlStatus"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="biz_no" jdbcType="VARCHAR" property="bizNo"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="appearance" jdbcType="VARCHAR" property="appearance"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
    </resultMap>
    <sql id="sql_columns">
    id,questionnaire_id,user_id,out_user_id,title,des,begin_time,
    end_time,status,control_status,biz_id,biz_no,update_time,
    create_time,appearance,channel,questionnaire_type
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">and questionnaire_id =
                #{item.questionnaireId}
            </if>
            <if test="null != item.userId and '' != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.outUserId and '' != item.outUserId">and out_user_id = #{item.outUserId}</if>
            <if test="null != item.title and '' != item.title">and title = #{item.title}</if>
            <if test="null != item.des and '' != item.des">and des = #{item.des}</if>
            <if test="null != item.beginTime and '' != item.beginTime">and begin_time = #{item.beginTime}</if>
            <if test="null != item.endTime and '' != item.endTime">and end_time = #{item.endTime}</if>
            <if test="null != item.status and '' != item.status">and status = #{item.status}</if>
            <if test="null != item.controlStatus and '' != item.controlStatus">and control_status =
                #{item.controlStatus}
            </if>
            <if test="null != item.bizId and '' != item.bizId">and biz_id = #{item.bizId}</if>
            <if test="null != item.bizNo and '' != item.bizNo">and biz_no = #{item.bizNo}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
            <if test="null != item.likeBizNo and '' != item.likeBizNo">
                and biz_no like concat(#{item.likeBizNo} ,'%')
            </if>
            <if test="null != item.statusList and item.statusList.size() > 0">
                and status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_questionnaire_info
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_questionnaire_info
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_questionnaire_info
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into qst_questionnaire_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.outUserId">out_user_id,</if>
            <if test="null != item.title">title,</if>
            <if test="null != item.des">des,</if>
            <if test="null != item.beginTime">begin_time,</if>
            <if test="null != item.endTime">end_time,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.controlStatus">control_status,</if>
            <if test="null != item.bizId">biz_id,</if>
            <if test="null != item.bizNo">biz_no,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
            <if test="null != item.channel">channel,</if>
            <if test="null != item.questionnaireType">questionnaire_type,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.outUserId">#{item.outUserId},</if>
            <if test="null != item.title">#{item.title},</if>
            <if test="null != item.des">#{item.des},</if>
            <if test="null != item.beginTime">#{item.beginTime},</if>
            <if test="null != item.endTime">#{item.endTime},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.controlStatus">#{item.controlStatus},</if>
            <if test="null != item.bizId">#{item.bizId},</if>
            <if test="null != item.bizNo">#{item.bizNo},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
            <if test="null != item.channel">#{item.channel},</if>
            <if test="null != item.questionnaireType">#{item.questionnaireType},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.outUserId">out_user_id = values(out_user_id),</if>
            <if test="null != item.title">title = values(title),</if>
            <if test="null != item.des">des = values(des),</if>
            <if test="null != item.beginTime">begin_time = values(begin_time),</if>
            <if test="null != item.endTime">end_time = values(end_time),</if>
            <if test="null != item.status">status = values(status),</if>
            <if test="null != item.controlStatus">control_status = values(control_status),</if>
            <if test="null != item.bizId">biz_id = values(biz_id),</if>
            <if test="null != item.bizNo">biz_no = values(biz_no),</if>
            <if test="null != item.questionnaireType">questionnaire_type = values(questionnaire_type),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update qst_questionnaire_info
        <set>
            <if test="null != item.questionnaireId">questionnaire_id = #{item.questionnaireId},</if>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.outUserId">out_user_id = #{item.outUserId},</if>
            <if test="null != item.title">title = #{item.title},</if>
            <if test="null != item.des">des = #{item.des},</if>
            <if test="null != item.beginTime">begin_time = #{item.beginTime},</if>
            <if test="null != item.endTime">end_time = #{item.endTime},</if>
            <if test="null != item.status">status = #{item.status},</if>
            <if test="null != item.controlStatus">control_status = #{item.controlStatus},</if>
            <if test="null != item.bizId">biz_id = #{item.bizId},</if>
            <if test="null != item.bizNo">biz_no = #{item.bizNo},</if>
            <if test="null != item.appearance">appearance = #{item.appearance},</if>
            <if test="null != item.channel">channel = #{item.channel},</if>
            <if test="null != item.questionnaireType">questionnaire_type = #{item.questionnaireType},</if>
        </set>
        where questionnaire_id = #{item.questionnaireId}
        and user_id = #{item.userId}
        <if test="item.oldStatus != null">
            and status = #{item.oldStatus}
        </if>
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_questionnaire_info
        <include refid="sql_where"/>
    </delete>

    <select id="selectCount" resultType="int">
        select count(1)
        from qst_questionnaire_info
        <where>
            <if test="null != item.userId and '' != item.userId">
                and user_id = #{item.userId}
            </if>
            <if test="null != item.title and '' != item.title">
                and title = #{item.title}
            </if>
            <if test="null != item.likeTitle and '' != item.likeTitle">
                and title like concat('%', #{item.likeTitle} ,'%')
            </if>
            <if test="null != item.status and '' != item.status">
                and status = #{item.status}
            </if>
            <if test="null != item.statusList and item.statusList.size() > 0">
                and status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectMyListByPage" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_questionnaire_info
        <where>
            <if test="null != item.userId and '' != item.userId">
                and user_id = #{item.userId}
            </if>
            <if test="null != item.title and '' != item.title">
                and title = #{item.title}
            </if>
            <if test="null != item.likeTitle and '' != item.likeTitle">
                and title like concat('%', #{item.likeTitle} ,'%')
            </if>
            <if test="null != item.status and '' != item.status">
                and status = #{item.status}
            </if>
            <if test="null != item.statusList and item.statusList.size() > 0">
                and status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
        order by update_time desc
        <choose>
            <when test="null != item.startIndex and null != item.pageSize">
                limit #{item.startIndex}, #{item.pageSize}
            </when>
            <otherwise>
                limit 1
            </otherwise>
        </choose>
    </select>

    <select id="selectQuestionnaireListByPage" resultType="com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireRepDto">
        select
              qqi.questionnaire_id questionnaireId
            , qqi.title
            , qqi.status
            , qqi.begin_time beginTime
            , qqi.end_time endTime
            , qqi.user_id userId
        from qst_questionnaire_info qqi
        <where>
            <if test="null != item.channel">
                and qqi.channel = #{item.channel}
            </if>
            <if test="null != item.userId and '' != item.userId">
                and qqi.user_id = #{item.userId}
            </if>
            <if test="null != item.title and '' != item.title">
                and qqi.title like concat('%', #{item.title} ,'%')
            </if>
            <if test="null != item.status and '' != item.status">
                and qqi.status = #{item.status}
            </if>
            <if test="null != item.statusList and item.statusList.size() > 0">
                and qqi.status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
        order by qqi.update_time desc
    </select>

    <select id="selectQuestionnaireListCount" resultType="int">
        select count(1)
        from qst_questionnaire_info qqi
        inner join user_info ui on qqi.user_id = ui.id
        left join qst_limit_rule qlr on qlr.questionnaire_id = qqi.questionnaire_id
        <where>
            <if test="null != item.userId and '' != item.userId">
                and qqi.user_id = #{item.userId}
            </if>
            <if test="null != item.title and '' != item.title">
                and qqi.title like concat('%', #{item.title} ,'%')
            </if>
            <if test="null != item.status and '' != item.status">
                and qqi.status = #{item.status}
            </if>
            <if test="null != item.statusList and item.statusList.size() > 0">
                and qqi.status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
            <if test="null != item.beginTime and '' != item.beginTime">
                and qlr.begin_time >= #{item.beginTime}
            </if>
            <if test="null != item.endTime and '' != item.endTime">
                and #{item.endTime} >= qlr.end_time
            </if>
            <if test="null != item.name and '' != item.name">
                and ui.name like concat('%', #{item.name} ,'%')
            </if>
            <if test="null != item.channel">
                and qqi.channel = #{item.channel}
            </if>
        </where>
        group by qqi.questionnaire_id
    </select>

    <select id="selectAnswerCount" resultType="int">
        select count(1)
        from answer_info ai
        inner join qst_questionnaire_info qqi on ai.questionnaire_id = qqi.questionnaire_id
        <where>
            and ai.status = 2
            <if test="null != item.userId and '' != item.userId">
                and qqi.user_id = #{item.userId}
            </if>
            <if test="null != item.title and '' != item.title">
                and qqi.title like concat('%', #{item.title} ,'%')
            </if>
            <if test="null != item.status and '' != item.status">
                and qqi.status = #{item.status}
            </if>
            <if test="null != item.statusList and item.statusList.size() > 0">
                and qqi.status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
            <if test="null != item.beginTime and '' != item.beginTime">
                and qqi.begin_time >= #{item.beginTime}
            </if>
            <if test="null != item.endTime and '' != item.endTime">
                and #{item.endTime} >= qqi.end_time
            </if>
            <if test="null != item.name and '' != item.name">
                and ui.name like concat('%', #{item.name} ,'%')
            </if>
            <if test="null != item.channel">
                and qqi.channel = #{item.channel}
            </if>
        </where>
    </select>
</mapper>

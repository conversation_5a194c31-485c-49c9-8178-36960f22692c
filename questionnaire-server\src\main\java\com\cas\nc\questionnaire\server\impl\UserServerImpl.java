package com.cas.nc.questionnaire.server.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.cas.nc.questionnaire.common.dto.user.*;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.UserSourceEnum;
import com.cas.nc.questionnaire.common.enums.UserStatusEnum;
import com.cas.nc.questionnaire.common.enums.UserTypeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.sso.Ticket;
import com.cas.nc.questionnaire.common.to.ExternalUserTo;
import com.cas.nc.questionnaire.common.to.ResetPwdEmailValidTo;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.Base64Utils;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.EmailFormatCheckUtils;
import com.cas.nc.questionnaire.common.utils.EncryptAlgorithmsUtils;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.JedisUtil;
import com.cas.nc.questionnaire.common.utils.Md5Encrypt;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.LoginauthPo;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.LoginauthQuery;
import com.cas.nc.questionnaire.dao.query.UserInfoQuery;
import com.cas.nc.questionnaire.server.UserServer;
import com.cas.nc.questionnaire.server.mapstruct.UserConverter;
import com.cas.nc.questionnaire.server.util.ConvertBeanUtil;
import com.cas.nc.questionnaire.server.util.ESLoginHttpUtil;
import com.cas.nc.questionnaire.service.LoginauthService;
import com.cas.nc.questionnaire.service.UserInfoService;
import com.cas.nc.questionnaire.service.util.SendMailUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.apache.bcel.classfile.Code;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.*;
import static com.cas.nc.questionnaire.common.enums.UserStatusEnum.isInit;
import static com.cas.nc.questionnaire.common.utils.Constants.FORGET_PWD_EMAIL_HTML;
import static com.cas.nc.questionnaire.common.utils.Constants.FORGET_PWD__EMAIL_TITLE;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Constants.PRODUCT_ENV_MARK;
import static com.cas.nc.questionnaire.common.utils.Constants.RESET_PWD_VALID_URL_KEY;
import static com.cas.nc.questionnaire.common.utils.Constants.VALID_EMAIL_HTML;
import static com.cas.nc.questionnaire.common.utils.Constants.VALID_EMAIL_TITLE;
import static com.cas.nc.questionnaire.common.utils.DateUtil.C_TIME_YYYYMMDDHHMMSS;
import static com.cas.nc.questionnaire.common.utils.DateUtil.compareDate;
import static com.cas.nc.questionnaire.common.utils.Md5Encrypt.md5Triple;
import static com.cas.nc.questionnaire.common.utils.RandomValueUtil.getEmail;
import static org.apache.commons.lang3.time.DateUtils.addSeconds;

@Component
public class UserServerImpl implements UserServer {
    @Resource
    private UserInfoService userInfoService;

    @Resource
    private LoginauthService loginauthService;


    @Value("${valid.url}")
    private String validUrl;

    @Value("${valid.encrypt.key}")
    private String encryptKey;

    @Resource
    private SendMailUtil sendMailUtil;
    @Value("${login.error.retry.count}")
    private Integer retryCount;
    @Value("${login.error.retry.interval}")
    private Integer retryInterval;

    @Value("${reset_pwd_valid.url}")
    private String resetPwdValidUrl;
    @Resource
    private JedisUtil jedisUtil;
    @Value("${qst.env}")
    private String env;
    @Resource
    private ESLoginHttpUtil esLoginHttpUtil;

    @Override
    public void register(UserRegisterReqDto reqDto) {
        UserInfoPo validateRegisterPo = validateRegister(reqDto);

        Long id = 0L;
        if (validateRegisterPo == null) {
            UserInfoPo userInfoPo = ConvertBeanUtil.userRegisterDto2Po(reqDto);
            userInfoService.insert(userInfoPo);
            id = userInfoPo.getId();
        } else {
            id = validateRegisterPo.getId();
        }

        String encryptId = EncryptAlgorithmsUtils.encryptByAES(encryptKey, id.toString());
        String url = validUrl + Base64Utils.encodeBase64String(encryptId.getBytes());
        String emailContent = String.format(VALID_EMAIL_HTML, reqDto.getUserName(), url, url);

        boolean sendFlag = sendMailUtil.sendHtmlMail(VALID_EMAIL_TITLE, emailContent, new String[]{reqDto.getEmail()}, new String[]{}, null, 1);
        Assert.isTrue(sendFlag, CodeEnum.SEND_EMAIL_FAIL);
    }
    @Override
    public UserLoginRepDto doEsLogin(UserLoginByESReqDto reqDto) {
        UserLoginRepDto repDto = new UserLoginRepDto();
        ESLoginResult esLoginResult = esLoginHttpUtil.requestESToken(reqDto.getCode(), reqDto.getRedirectUri());
        ESLoginResult.UserInfo esLoginUserInfo = esLoginResult.getUserInfoParse();
        if (esLoginUserInfo == null || StringUtils.isBlank(esLoginUserInfo.getUmtId())) {
            if (ObjectUtils.nullSafeEquals("invalid_grant", esLoginResult.getError())) {
                throw new ServerException(USER_ES_ERROR);
            } else {
                throw new ServerException(USER_ES_NOT_ERROR);
            }
//            esLoginUserInfo = new ESLoginResult.UserInfo();
//            esLoginUserInfo.setUmtId("10867019");
//            esLoginUserInfo.setTruename("李小雄");
//            esLoginUserInfo.setCstnetId("<EMAIL>");
        }
        LoginauthQuery loginQuery = new LoginauthQuery();
        loginQuery.setAuthenId(esLoginUserInfo.getUmtId());
        LoginauthPo oldLoginAuth = loginauthService.selectOne(loginQuery);
        Integer relatedESCount=0;
        UserInfoPo userInfoPo = null;
        if (oldLoginAuth == null) {//没有绑定过科技云账号
            //根据邮箱查询用户信息
            UserInfoQuery userQuery = new UserInfoQuery();
            userQuery.setEmail(esLoginUserInfo.getCstnetId());
            userQuery.setStatus(UserStatusEnum.EFFECTIVE.key());
            userInfoPo = userInfoService.selectOne(userQuery);
            if (userInfoPo == null ) {
                throw new ServerException("科技云登录：code = " + reqDto.getCode() + ";登录失败：未找到对应账户");
            } else {
                LoginauthPo newLoginAuth = new LoginauthPo();
                newLoginAuth.setId(Math.abs(new Long(new Date().getTime()).intValue()));//老id逻辑
                newLoginAuth.setType(0);
                newLoginAuth.setAuthenId(esLoginUserInfo.getUmtId());
                newLoginAuth.setUserId(userInfoPo.getId());
                newLoginAuth.setModifytime(new Date());
                newLoginAuth.setAuthInfo("科技云用户名:" + esLoginUserInfo.getTruename() + ";" + "账号:" + esLoginUserInfo.getCstnetId());
                newLoginAuth.setAuthUserId(esLoginUserInfo.getCstnetId());
                loginauthService.insert(newLoginAuth);
            }
        } else {
            userInfoPo = userInfoService.selectOne(oldLoginAuth.getUserId());
        }
        Assert.notNull(userInfoPo, CodeEnum.USER_PWD_ERROR);

        if (userInfoPo.getPwdErrorCount() >= 5 && compareDate(addSeconds(userInfoPo.getUpdateTime(), retryInterval)) == 1) {
            UserInfoPo finalUserInfoPo = userInfoPo;
            new Thread(() -> {
                userInfoService.resetPwdErrorCount(finalUserInfoPo.getId());
            }).start();
        }
        Ticket ticket = getTicket(userInfoPo.getUserName(), userInfoPo);
        repDto.setTicket(ticket);
        repDto.setUserName(userInfoPo.getUserName());
        if (userInfoPo.getPwdErrorCount() > 0) {
            UserInfoPo finalUserInfoPo1 = userInfoPo;
            new Thread(() -> {
                userInfoService.resetPwdErrorCount(finalUserInfoPo1.getId());
            }).start();
        }
        return repDto;
    }


    @Override
    public UserLoginRepDto doLogin(UserLoginReqDto reqDto) {
        UserLoginRepDto repDto = new UserLoginRepDto();
        String userNameGeneral = reqDto.getUserName();

        UserInfoQuery query = new UserInfoQuery();
        if (EmailFormatCheckUtils.isEmailLegal(userNameGeneral)) {
            query.setEmail(userNameGeneral);
        } else {
            query.setUserName(userNameGeneral);
        }
        query.setStatus(UserStatusEnum.EFFECTIVE.key());

        UserInfoPo userInfoPo = userInfoService.selectOne(query);
        Assert.notNull(userInfoPo, CodeEnum.USER_PWD_ERROR);
        Assert.isTrue(userInfoPo.getPwdErrorCount() < retryCount || compareDate(addSeconds(userInfoPo.getUpdateTime(), retryInterval)) == 1, USER_PWD_RETRY_ERROR);

        if (userInfoPo.getPwdErrorCount() >= 5 && compareDate(addSeconds(userInfoPo.getUpdateTime(), retryInterval)) == 1) {
            new Thread(() -> {
                userInfoService.resetPwdErrorCount(userInfoPo.getId());
            }).start();
        }

        String pwd = md5Triple(reqDto.getPwd());
        if (!userInfoPo.getPwd().equals(pwd)) {
            userInfoService.setPwdErrorCount(userInfoPo.getId(), userInfoPo.getPwdErrorCount() + 1);
            throw new ServerException(USER_PWD_ERROR);
        }

        Ticket ticket = getTicket(userNameGeneral, userInfoPo);

        repDto.setTicket(ticket);

        if (userInfoPo.getPwdErrorCount() > 0) {
            new Thread(() -> {
                userInfoService.resetPwdErrorCount(userInfoPo.getId());
            }).start();
        }
        return repDto;
    }

    @Override
    public UserLoginRepDto doLoginNoPassword(UserLoginNoPasswordReqDto reqDto) {
        Assert.notBlank(reqDto.getEncrypt(), CodeEnum.ENCRYPT_INVALID);
        Assert.notBlank(reqDto.getEmail(), CodeEnum.EMAIL_NOT_NULL);
        Assert.notNull(reqDto.getId(), CodeEnum.USERID_INVALID);

        Map<String, Object> map = MapUtil.builder(new HashMap<String, Object>()).put("id", reqDto.getId()).put("email", reqDto.getEmail()).build();
        String json = JSONObject.toJSONString(map);
        if(!Md5Encrypt.verify(json, reqDto.getEncrypt())) {
            throw new ServerException(CodeEnum.ENCRYPT_INVALID);
        }

        UserLoginRepDto repDto = new UserLoginRepDto();
        String userNameGeneral = reqDto.getEmail();

        UserInfoQuery query = new UserInfoQuery();
        if (EmailFormatCheckUtils.isEmailLegal(userNameGeneral)) {
            query.setEmail(userNameGeneral);
        } else {
            query.setUserName(userNameGeneral);
        }
        query.setStatus(UserStatusEnum.EFFECTIVE.key());
        query.setOutUserId(String.valueOf(reqDto.getId()));

        UserInfoPo userInfoPo = userInfoService.selectOne(query);
        if(userInfoPo == null) {
            throw new ServerException(USER_NONEXISTENT);
        }

        Ticket ticket = getTicket(userNameGeneral, userInfoPo);

        repDto.setTicket(ticket);
        return repDto;
    }

    @Override
    public void emailValid(UserEmailValidReqDto reqDto) {
        UserInfoPo userInfoPo = userInfoService.selectOne(reqDto.getUserId());
        Assert.notNull(userInfoPo, USER_NONEXISTENT);
        Assert.isTrue(!UserStatusEnum.isEffective(userInfoPo.getStatus()), CodeEnum.VALID_DONE);

        int result = userInfoService.updateStatusEffective(reqDto.getUserId());
        Assert.isTrue(result == ONE, CodeEnum.UNKNOWN_RETURN_PAGE);
    }

    @Override
    public UserInfoRespDto getUserInfo(Long userId) {

        UserInfoPo userInfoPo = userInfoService.selectOne(userId);
        Assert.notNull(userInfoPo, CodeEnum.DATA_EXCEPTION);

        UserInfoRespDto result = UserConverter.INSTANCE.to(userInfoPo);
        result.setUserId(String.valueOf(userInfoPo.getId()));

        return result;
    }

    @Override
    public Ticket modifyPwd(UserModifyPwdReqDto reqDto) {
        UserInfoPo userInfoPo = userInfoService.selectOne(reqDto.getUserId());
        Assert.notNull(userInfoPo, USER_NONEXISTENT);

        String pwd = Md5Encrypt.md5Triple(reqDto.getOldPwd());
        Assert.isTrue(userInfoPo.getPwd().equals(pwd), CodeEnum.ANSWER_PWD_ERROR);
        String newPwd = Md5Encrypt.md5Triple(reqDto.getNewPwd());

        int resultCount = userInfoService.updatePwd(reqDto.getUserId(), pwd, newPwd);
        Assert.isTrue(resultCount == 1, PWD_MODIFY_EXCEPTION);

        return getTicket(null, userInfoPo);
    }

    @Override
    public Boolean externalUsersSync(ExternalUserSyncReqDto reqDto) {
        List<UserInfoPo> userInfoPoList = listKxUser(reqDto);

        if (CollectionUtils.isEmpty(userInfoPoList)) {
            try {
                UserInfoQuery insertQuery = new UserInfoQuery();
                setCommonParam(reqDto, insertQuery);
                if (StringUtil.isBlank(insertQuery.getEmail())) {
                    insertQuery.setEmail(getEmail());
                }
                insertQuery.setUserSource(UserSourceEnum.KXPX.key());

                userInfoService.insert(insertQuery);
            } catch (DuplicateKeyException e) {
                return userInfoService.update(getUpdateQuery(reqDto, userInfoPoList)) > 0;
            }
        } else {
            try {
                return userInfoService.update(getUpdateQuery(reqDto, userInfoPoList)) > 0;
            } catch (DuplicateKeyException e) {
                return userInfoService.update(getUpdateQuery(reqDto, null)) > 0;
            }
        }
        return true;
    }

    @Override
    public void resetPwdSendMail(UserRestPwdSendMailReqDto reqDto) {
        UserInfoQuery query = new UserInfoQuery();
        query.setEmail(reqDto.getEmail());

        UserInfoPo userInfoPo = userInfoService.selectOne(query);
        Assert.notNull(userInfoPo, EMAIL_NOT_EXIST);

        ResetPwdEmailValidTo validTo = new ResetPwdEmailValidTo();
        validTo.setUserId(userInfoPo.getId());
        validTo.setBeginTime(new Date());

        String encryptData = EncryptAlgorithmsUtils.encryptByAES(encryptKey, JSONUtil.toJSONString(validTo));
        String url = resetPwdValidUrl + Base64Utils.encodeBase64String(encryptData.getBytes());
        String emailContent = String.format(FORGET_PWD_EMAIL_HTML, url, url);

        boolean sendFlag = sendMailUtil.sendHtmlMail(FORGET_PWD__EMAIL_TITLE, emailContent, new String[]{reqDto.getEmail()}, new String[]{}, null, 2);
        Assert.isTrue(sendFlag, CodeEnum.SEND_EMAIL_FAIL);
    }

    @Override
    public void resetPwdModify(UserRestPwdModifyReqDto reqDto) {
        byte[] bytes = Base64Utils.decodeBase64(reqDto.getValidCode());
        String encryptData = new String(bytes);

        String decryptData = EncryptAlgorithmsUtils.decryptByAES(encryptKey, encryptData);

        ResetPwdEmailValidTo validTo = JSONUtil.parseObject(decryptData, ResetPwdEmailValidTo.class);
        Date date = DateUtil.addMinutes(validTo.getBeginTime(), 30);
        Assert.isTrue(DateUtil.compareDate(date) <= 0, RESET_PWD_URL_INVALIDATE);

        if (PRODUCT_ENV_MARK.equalsIgnoreCase(env)) {
            String validTime = DateUtil.format(validTo.getBeginTime(), C_TIME_YYYYMMDDHHMMSS);
            String key = RESET_PWD_VALID_URL_KEY + validTo.getUserId() + validTime;

            Boolean exists = jedisUtil.exists(key.getBytes());
            Assert.isTrue(exists == false, ONECE_URL_INVALIDATE);

            jedisUtil.set(key.getBytes(), JSONUtil.toJSONString(validTo).getBytes());
            jedisUtil.expire(key.getBytes(), 1800);
        }

        String pwd = md5Triple(reqDto.getNewPwd());
        UserInfoQuery query = new UserInfoQuery();
        query.setId(validTo.getUserId());
        query.setPwd(pwd);

        int update = userInfoService.update(query);

        Assert.isTrue(update == 1, PWD_MODIFY_EXCEPTION);
    }

    @Override
    public void save(UserSaveReqDto reqDto) {
        UserInfoPo userInfoPo = new UserInfoPo();
        userInfoPo.setEmail(reqDto.getEmail());
        userInfoPo.setPwd(Md5Encrypt.md5Triple(reqDto.getPwd()));
        userInfoPo.setUserName(reqDto.getEmail());
        userInfoPo.setUserSource(UserSourceEnum.EL.key());
        userInfoPo.setOutUserId(reqDto.getOutUserId());
        userInfoPo.setType(UserTypeEnum.ORDINARY_USERS.key());
        userInfoPo.setStatus(UserStatusEnum.EFFECTIVE.key());

        userInfoService.insertUpdate(userInfoPo);
    }

    @Override
    public Boolean remove(UserRemoveReqDto reqDto) {
        UserInfoQuery query = new UserInfoQuery();
        query.setEmail(reqDto.getEmail());

        UserInfoPo userInfoPo = userInfoService.selectOne(query);
        if (userInfoPo == null) {
            return false;
        }
        query.setId(userInfoPo.getId());
        query.setStatus(UserStatusEnum.FROZEN.key());

        int update = userInfoService.update(query);

        if (update == 1) {
            return true;
        }

        return false;
    }

    @Override
    public UserInfoRespDto getUserInfo(String email) {
        UserInfoQuery query = new UserInfoQuery();
        query.setEmail(email);
        UserInfoPo userInfoPo = userInfoService.selectOne(query);
        Assert.notNull(userInfoPo, CodeEnum.DATA_EXCEPTION);

        UserInfoRespDto result = UserConverter.INSTANCE.to(userInfoPo);
        result.setUserId(String.valueOf(userInfoPo.getId()));

        return result;
    }

    public List<UserInfoPo> listUser(ExternalUserSyncReqDto reqDto) {
        UserInfoQuery query = getUserInfoQuery(reqDto);

        List<UserInfoPo> userInfoPoList = userInfoService.selectList(query);
        return userInfoPoList;
    }

    public List<UserInfoPo> listKxUser(ExternalUserSyncReqDto reqDto) {
        UserInfoQuery query = getKxUserInfoQuery(reqDto);

        List<UserInfoPo> userInfoPoList = userInfoService.selectList(query);
        return userInfoPoList;
    }

    public UserInfoQuery getUpdateQuery(ExternalUserSyncReqDto reqDto, List<UserInfoPo> userInfoPoList) {
        List<UserInfoPo> userInfoPoListTemp = userInfoPoList;
        if (CollectionUtils.isEmpty(userInfoPoListTemp)) {
            userInfoPoListTemp = listUser(reqDto);
            Assert.notNull(userInfoPoListTemp, UNKNOWN_RETURN_PAGE);
        }

        UserInfoQuery updateQuery = new UserInfoQuery();
        setCommonParam(reqDto, updateQuery);
        if (userInfoPoListTemp.size() == 1) {
            Assert.isTrue(userInfoPoListTemp.get(0).getUserSource().intValue() == UserSourceEnum.KXPX.key(), USER_EXISTENT);
            boolean userNameFlag = userInfoPoListTemp.get(0).getUserName().equals(reqDto.getUser().getUserName());
            Assert.isTrue(userNameFlag, UNKNOWN_RETURN_PAGE);

            updateQuery.setId(userInfoPoListTemp.get(0).getId());
        } else {
            ExternalUserTo user = reqDto.getUser();
            List<UserInfoPo> userNameList = userInfoPoListTemp.stream().filter(filterUser(user)).collect(Collectors.toList());
            updateQuery.setId(userNameList.get(0).getId());
            updateQuery.setEmail(getEmail());
        }
        return updateQuery;
    }

    public Predicate<UserInfoPo> filterUser(ExternalUserTo user) {
        return v -> v.getUserName().equals(user.getUserName()) && v.getUserSource().equals(UserSourceEnum.KXPX.key());
    }

    public UserInfoQuery getKxUserInfoQuery(ExternalUserSyncReqDto reqDto) {
        UserInfoQuery queryTemp = new UserInfoQuery();
        queryTemp.setUserName(reqDto.getUser().getUserName());
        queryTemp.setUserSource(UserSourceEnum.KXPX.key());

        return queryTemp;
    }

    public UserInfoQuery getUserInfoQuery(ExternalUserSyncReqDto reqDto) {
        UserInfoQuery queryTemp = new UserInfoQuery();
        queryTemp.setUserName(reqDto.getUser().getUserName());
        if (StringUtil.isNotBlank(reqDto.getUser().getEmail())) {
            queryTemp.setOrEmail(reqDto.getUser().getEmail());
        }
        return queryTemp;
    }

    public void setCommonParam(ExternalUserSyncReqDto reqDto, UserInfoQuery query) {
        query.setUserName(reqDto.getUser().getUserName());
        query.setEmail(reqDto.getUser().getEmail());
        query.setName(reqDto.getUser().getName());
        if (StringUtil.isNotBlank(reqDto.getUser().getPwd())) {
            query.setPwd(Md5Encrypt.md5Triple(reqDto.getUser().getPwd()));
        }
        query.setOutUserId(reqDto.getUser().getUserId());
        query.setPhone(reqDto.getUser().getPhone());
    }

    private Ticket getTicket(String userNameGeneral, UserInfoPo userInfoPo) {
        Ticket ticket = new Ticket();
        if (StringUtil.isBlank(userNameGeneral)) {
            ticket.setUserName(userInfoPo.getUserName());
        } else {
            ticket.setUserName(userNameGeneral);
        }
        ticket.setUserId(userInfoPo.getId());
        ticket.setExpire(DateUtil.addMinutes(new Date(), 3 * 60));
        ticket.setType(userInfoPo.getType());

        return ticket;
    }

    public UserInfoPo validateRegister(UserRegisterReqDto reqDto) {
        UserInfoQuery query = new UserInfoQuery();
        query.setOrEmail(reqDto.getEmail());
        query.setUserName(reqDto.getUserName());

        UserInfoPo validateUserPo = userInfoService.selectOne(query);
        if (validateUserPo != null && !isInit(validateUserPo.getStatus())) {
            if (validateUserPo.getEmail().equalsIgnoreCase(reqDto.getEmail())) {
                throw new ServerException(EMAIL_REGISTERED);
            }
            if (validateUserPo.getUserName().equalsIgnoreCase(reqDto.getUserName())) {
                throw new ServerException(USER_REGISTERED);
            }
        }
        return validateUserPo;
    }

    public static void main(String[] args) {
        System.out.println(md5Triple("c56d0e9a7ccec67b4ea131655038d604"));
    }
}

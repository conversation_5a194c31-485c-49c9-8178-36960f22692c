package com.cas.nc.questionnaire.server.util;

import com.alibaba.fastjson.JSONObject;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * 甲方给的demo，稍加修改
 */
public class NginxTool {


    /**
     * 本方法不负责执行inputStream的关闭操作，如需关闭流，请调用本方法后自行控制
     *
     * @param inputStream
     * @param fileName
     * @return
     * @throws IOException
     */
    public static Map<String, Object> uploadFile(InputStream inputStream, String fileName) throws IOException {
        if (fileName == null) {
            fileName = "";
        }
        URL serverUrl = new URL( "http://resource.casmooc.cn:8888/upload?uploadType=17");
        //打开和URL之间的连接
        HttpURLConnection conn = (HttpURLConnection) serverUrl.openConnection();
        //发送POST请求必须设置如下两行
        conn.setDoOutput(true);
        conn.setDoInput(true);
        String BOUNDARY = "----WebKitFormBoundaryrmScRANbMuRapDq5";
        conn.setUseCaches(false);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("connection", "Keep-Alive");
        conn.setRequestProperty("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.61 Safari/537.36");
        conn.setRequestProperty("Charsert", "UTF-8");
        conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
        conn.connect();

        DataOutputStream output = null;
        try {
            output = new DataOutputStream(conn.getOutputStream());
            byte[] end_data = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();

            //添加参数
            StringBuffer str1 = new StringBuffer();
            str1.append("--");
            str1.append(BOUNDARY);
            str1.append("\r\n");
            str1.append("Content-Disposition: form-data;name=\"file\"");
            str1.append("\r\n");
            str1.append("\r\n");
            str1.append("***");
            str1.append("\r\n");
            output.write(str1.toString().getBytes("UTF-8"));

            //添加参数file
            StringBuffer sb = new StringBuffer();
            sb.append("--");
            sb.append(BOUNDARY);
            sb.append("\r\n");
            sb.append("Content-Disposition: form-data;name=\"file\";filename=\"" + fileName + "\"");
            sb.append("\r\n");
            sb.append("Content-Type: application/octet-stream");
            sb.append("\r\n");
            sb.append("\r\n");
            output.write(sb.toString().getBytes("UTF-8"));

            int bytes = 0;
            byte[] bufferOut = new byte[1024];
            while ((bytes = inputStream.read(bufferOut)) != -1) {
                output.write(bufferOut, 0, bytes);
            }
            output.write("\r\n".getBytes("UTF-8"));
            output.write(end_data);
            //flush输出流的缓冲
            output.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (output != null) {
                output.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }

        StringBuffer uploadResult = new StringBuffer("");
        //定义BufferedReader输入流来读取URL的响应
        BufferedReader in = null;
        try {
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                uploadResult.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                in.close();
            }
        }
        String url = null;
        String filePath = null;
        String original = null;
        String state = null;
        Boolean status = false;

        try {
            JSONObject jsonObj = JSONObject.parseObject(uploadResult.toString());
            status = jsonObj.getBoolean("status");
            if (status) {
                state = jsonObj.get("state").toString();
                filePath = jsonObj.get("filePath").toString();
                original = jsonObj.get("original").toString();
                url = jsonObj.get("url").toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        Map<String, Object> jsonResult = new HashMap<String, Object>();
        jsonResult.put("status", status);
        if (filePath != null) {
            jsonResult.put("filePath", filePath);
        }
        if (state != null) {
            jsonResult.put("state", state);
        }
        if (original != null) {
            jsonResult.put("original", original);
        }
        if (url != null) {
            jsonResult.put("url", url);
        }
        return jsonResult;
    }

}

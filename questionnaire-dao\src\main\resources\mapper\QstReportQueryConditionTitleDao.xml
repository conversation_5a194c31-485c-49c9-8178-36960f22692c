<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.QstReportQueryConditionTitleDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstReportQueryConditionTitlePo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="condition_id" jdbcType="BIGINT" property="conditionId"/>
        <result column="title_id" jdbcType="BIGINT" property="titleId"/>
        <result column="serial_number" jdbcType="INTEGER" property="serialNumber"/>
        <result column="validate_type" jdbcType="INTEGER" property="validateType"/>
    </resultMap>
    <sql id="sql_columns">
        id, condition_id, title_id, serial_number, validate_type
    </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.conditionId and '' != item.conditionId">and condition_id = #{item.conditionId}</if>
            <if test="null != item.titleId and '' != item.titleId">and title_id = #{item.titleId}</if>
            <if test="null != item.serialNumber and '' != item.serialNumber">and serial_number = #{item.serialNumber}</if>
            <if test="null != item.validateType and '' != item.validateType">and validate_type = #{item.validateType}</if>
        </where>
    </sql>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_report_query_condition_title
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_report_query_condition_title
        <include refid="sql_where"/>
    </select>
    <select id="selectByConditionId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="sql_columns"/>
        from qst_report_query_condition_title
        where condition_id = #{conditionId}
    </select>
    <sql id="sql_insert_columns">
        insert into qst_report_query_condition_title
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.conditionId">condition_id,</if>
            <if test="null != item.titleId">title_id,</if>
            <if test="null != item.serialNumber">serial_number,</if>
            <if test="null != item.validateType">validate_type,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.conditionId">#{item.conditionId},</if>
            <if test="null != item.titleId">#{item.titleId},</if>
            <if test="null != item.serialNumber">#{item.serialNumber},</if>
            <if test="null != item.validateType">#{item.validateType},</if>
        </trim>
    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into qst_report_query_condition_title (condition_id, title_id, serial_number, validate_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.conditionId}, #{item.titleId}, #{item.serialNumber}, #{item.validateType})
        </foreach>
    </insert>
    <sql id="sql_update">
        update qst_report_query_condition_title
        <set>
            <if test="null != item.titleId">title_id = #{item.titleId},</if>
            <if test="null != item.serialNumber">serial_number = #{item.serialNumber},</if>
            <if test="null != item.validateType">validate_type = #{item.validateType},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_report_query_condition_title
        <include refid="sql_where"/>
    </delete>
    <delete id="deleteByConditionId" parameterType="java.lang.Long">
        delete from qst_report_query_condition_title
        where condition_id = #{conditionId}
    </delete>
</mapper> 
package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.user.*;
import com.cas.nc.questionnaire.common.sso.Ticket;

public interface UserServer {

    void register(UserRegisterReqDto reqDto);

    UserLoginRepDto doLogin(UserLoginReqDto reqDto);

    UserLoginRepDto doLoginNoPassword(UserLoginNoPasswordReqDto reqDto);

    /**
     * 邮箱验证
     *
     * @param reqDto
     */
    void emailValid(UserEmailValidReqDto reqDto);

    UserInfoRespDto getUserInfo(Long userId);

    Ticket modifyPwd(UserModifyPwdReqDto reqDto);

    Boolean externalUsersSync(ExternalUserSyncReqDto reqDto);

    void resetPwdSendMail(UserRestPwdSendMailReqDto reqDto);

    void resetPwdModify(UserRestPwdModifyReqDto reqDto);

    void save(UserSaveReqDto reqDto);

    Boolean remove(UserRemoveReqDto reqDto);

	UserLoginRepDto doEsLogin(UserLoginByESReqDto reqDto);

    UserInfoRespDto getUserInfo(String email);
}

package com.cas.nc.questionnaire.common.dto.questionnaire;

public class QuestionnaireListReqDto {

    /*开始时间*/
    private String beginTime;
    /*结束时间*/
    private String endTime;
    /*创建人*/
    private String name;
    /*问卷名称*/
    private String title;
    /*第三方调用问卷列表的用户id*/
    private String outUserId;
    /*每页行数*/
    private Integer pageSize;
    /*导向页*/
    private Integer page;
    /*问卷来源，1：问卷平台；2：继续教育平台*/
    private Integer channel;

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public String getOutUserId() {
        return outUserId;
    }

    public void setOutUserId(String outUserId) {
        this.outUserId = outUserId;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstQuotaOptionRuleDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstQuotaOptionRulePo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="quota_id" jdbcType="BIGINT" property="quotaId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="title_serial_number" jdbcType="INTEGER" property="titleSerialNumber"/>
        <result column="title_type" jdbcType="INTEGER" property="titleType"/>
        <result column="option_serial_number" jdbcType="INTEGER" property="optionSerialNumber"/>
        <result column="quota" jdbcType="INTEGER" property="quota"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,quota_id,user_id,questionnaire_id,title_serial_number,title_type,option_serial_number,
    quota,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.quotaId and '' != item.quotaId">and quota_id = #{item.quotaId}</if>
            <if test="null != item.userId and '' != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">and questionnaire_id =
                #{item.questionnaireId}
            </if>
            <if test="null != item.titleSerialNumber and '' != item.titleSerialNumber">and title_serial_number =
                #{item.titleSerialNumber}
            </if>
            <if test="null != item.titleType and '' != item.titleType">and title_type = #{item.titleType}</if>
            <if test="null != item.optionSerialNumber and '' != item.optionSerialNumber">and option_serial_number =
                #{item.optionSerialNumber}
            </if>
            <if test="null != item.quota and '' != item.quota">and quota = #{item.quota}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_quota_option_rule
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_quota_option_rule
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_quota_option_rule
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into qst_quota_option_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">id,</if>
            <if test="null != item.quotaId">quota_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.titleSerialNumber">title_serial_number,</if>
            <if test="null != item.titleType">title_type,</if>
            <if test="null != item.optionSerialNumber">option_serial_number,</if>
            <if test="null != item.quota">quota,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">#{item.id},</if>
            <if test="null != item.quotaId">#{item.quotaId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.titleSerialNumber">#{item.titleSerialNumber},</if>
            <if test="null != item.titleType">#{item.titleType},</if>
            <if test="null != item.optionSerialNumber">#{item.optionSerialNumber},</if>
            <if test="null != item.quota">#{item.quota},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.id">id = values(id),</if>
            <if test="null != item.quotaId">quota_id = values(quota_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.titleSerialNumber">title_serial_number = values(title_serial_number),</if>
            <if test="null != item.titleType">title_type = values(title_type),</if>
            <if test="null != item.optionSerialNumber">option_serial_number = values(option_serial_number),</if>
            <if test="null != item.quota">quota = values(quota),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update qst_quota_option_rule
        <set>
            <if test="null != item.id">id = #{item.id},</if>
            <if test="null != item.quotaId">quota_id = #{item.quotaId},</if>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.questionnaireId">questionnaire_id = #{item.questionnaireId},</if>
            <if test="null != item.titleSerialNumber">title_serial_number = #{item.titleSerialNumber},</if>
            <if test="null != item.titleType">title_type = #{item.titleType},</if>
            <if test="null != item.optionSerialNumber">option_serial_number = #{item.optionSerialNumber},</if>
            <if test="null != item.quota">quota = #{item.quota},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_quota_option_rule
        <include refid="sql_where"/>
    </delete>
</mapper>
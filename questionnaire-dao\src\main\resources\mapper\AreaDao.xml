<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.AreaDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.AreaPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="area_code" jdbcType="VARCHAR" property="areaCode"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="pass" jdbcType="VARCHAR" property="pass"/>
        <result column="simple_code" jdbcType="VARCHAR" property="simpleCode"/>
        <result column="ranks" jdbcType="INTEGER" property="ranks"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,name,area_code,parent_id,pass,simple_code,ranks,
    status,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.idList and idList.size() > 0">
                and id in
                <foreach collection="item.idList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.name">and name = #{item.name}</if>
            <if test="null != item.areaCode">and area_code = #{item.areaCode}</if>
            <if test="null != item.parentId">and parent_id = #{item.parentId}</if>
            <if test="null != item.pass">and pass = #{item.pass}</if>
            <if test="null != item.simpleCode">and simple_code = #{item.simpleCode}</if>
            <if test="null != item.ranks">and ranks = #{item.ranks}</if>
            <if test="null != item.status">and status = #{item.status}</if>
            <if test="null != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from area
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from area
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from area
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.name">name,</if>
            <if test="null != item.areaCode">area_code,</if>
            <if test="null != item.parentId">parent_id,</if>
            <if test="null != item.pass">pass,</if>
            <if test="null != item.simpleCode">simple_code,</if>
            <if test="null != item.ranks">ranks,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.name">#{item.name},</if>
            <if test="null != item.areaCode">#{item.areaCode},</if>
            <if test="null != item.parentId">#{item.parentId},</if>
            <if test="null != item.pass">#{item.pass},</if>
            <if test="null != item.simpleCode">#{item.simpleCode},</if>
            <if test="null != item.ranks">#{item.ranks},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.name">name = values(name),</if>
            <if test="null != item.areaCode">area_code = values(area_code),</if>
            <if test="null != item.parentId">parent_id = values(parent_id),</if>
            <if test="null != item.pass">pass = values(pass),</if>
            <if test="null != item.simpleCode">simple_code = values(simple_code),</if>
            <if test="null != item.ranks">ranks = values(ranks),</if>
            <if test="null != item.status">status = values(status),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update area
        <set>
            <if test="null != item.name">name = #{item.name},</if>
            <if test="null != item.areaCode">area_code = #{item.areaCode},</if>
            <if test="null != item.parentId">parent_id = #{item.parentId},</if>
            <if test="null != item.pass">pass = #{item.pass},</if>
            <if test="null != item.simpleCode">simple_code = #{item.simpleCode},</if>
            <if test="null != item.ranks">ranks = #{item.ranks},</if>
            <if test="null != item.status">status = #{item.status},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from area
        <include refid="sql_where"/>
    </delete>
</mapper>
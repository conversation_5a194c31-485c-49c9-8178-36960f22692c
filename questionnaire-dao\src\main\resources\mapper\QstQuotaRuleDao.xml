<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstQuotaRuleDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstQuotaRulePo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="quota_rule_id" jdbcType="VARCHAR" property="quotaRuleId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="condition_type" jdbcType="INTEGER" property="conditionType"/>
        <result column="quota" jdbcType="INTEGER" property="quota"/>
        <result column="quota_type" jdbcType="INTEGER" property="quotaType"/>
        <result column="title_serial_number" jdbcType="INTEGER" property="titleSerialNumber"/>
        <result column="title_name" jdbcType="VARCHAR" property="titleName"/>
        <result column="option_json_info" jdbcType="VARCHAR" property="optionJsonInfo"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,name,questionnaire_id,quota_rule_id,user_id,condition_type,quota,
    quota_type,title_serial_number,title_name,option_json_info,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.name">and name = #{item.name}</if>
            <if test="null != item.questionnaireId">and questionnaire_id = #{item.questionnaireId}</if>
            <if test="null != item.quotaRuleId">and quota_rule_id = #{item.quotaRuleId}</if>
            <if test="null != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.conditionType">and condition_type = #{item.conditionType}</if>
            <if test="null != item.quota">and quota = #{item.quota}</if>
            <if test="null != item.quotaType">and quota_type = #{item.quotaType}</if>
            <if test="null != item.titleSerialNumber">and title_serial_number = #{item.titleSerialNumber}</if>
            <if test="null != item.titleName">and title_name = #{item.titleName}</if>
            <if test="null != item.optionJsonInfo">and option_json_info = #{item.optionJsonInfo}</if>
            <if test="null != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_quota_rule
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_quota_rule
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_quota_rule
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into qst_quota_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.name">name,</if>
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.quotaRuleId">quota_rule_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.conditionType">condition_type,</if>
            <if test="null != item.quota">quota,</if>
            <if test="null != item.quotaType">quota_type,</if>
            <if test="null != item.titleSerialNumber">title_serial_number,</if>
            <if test="null != item.titleName">title_name,</if>
            <if test="null != item.optionJsonInfo">option_json_info,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.name">#{item.name},</if>
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.quotaRuleId">#{item.quotaRuleId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.conditionType">#{item.conditionType},</if>
            <if test="null != item.quota">#{item.quota},</if>
            <if test="null != item.quotaType">#{item.quotaType},</if>
            <if test="null != item.titleSerialNumber">#{item.titleSerialNumber},</if>
            <if test="null != item.titleName">#{item.titleName},</if>
            <if test="null != item.optionJsonInfo">#{item.optionJsonInfo},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.name">name = values(name),</if>
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.quotaRuleId">quota_rule_id = values(quota_rule_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.conditionType">condition_type = values(condition_type),</if>
            <if test="null != item.quota">quota = values(quota),</if>
            <if test="null != item.quotaType">quota_type = values(quota_type),</if>
            <if test="null != item.titleSerialNumber">title_serial_number = values(title_serial_number),</if>
            <if test="null != item.titleName">title_name = values(title_name),</if>
            <if test="null != item.optionJsonInfo">option_json_info = values(option_json_info),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update qst_quota_rule
        <set>
            <if test="null != item.name">name = #{item.name},</if>
            <if test="null != item.questionnaireId">questionnaire_id = #{item.questionnaireId},</if>
            <if test="null != item.quotaRuleId">quota_rule_id = #{item.quotaRuleId},</if>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.conditionType">condition_type = #{item.conditionType},</if>
            <if test="null != item.quota">quota = #{item.quota},</if>
            <if test="null != item.quotaType">quota_type = #{item.quotaType},</if>
            <if test="null != item.titleSerialNumber">title_serial_number = #{item.titleSerialNumber},</if>
            <if test="null != item.titleName">title_name = #{item.titleName},</if>
            <if test="null != item.optionJsonInfo">option_json_info = #{item.optionJsonInfo},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_quota_rule
        where user_id = #{item.userId}
        <if test="null != item.quotaRuleId">
            and quota_rule_id = #{item.quotaRuleId}
        </if>
        <if test="null == item.quotaRuleId">
            and questionnaire_id = #{item.questionnaireId}
        </if>
    </delete>
</mapper>
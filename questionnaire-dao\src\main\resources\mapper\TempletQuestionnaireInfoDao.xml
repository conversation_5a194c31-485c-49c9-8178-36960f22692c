<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.TempletQuestionnaireInfoDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.TempletQuestionnaireInfoPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="templet_id" jdbcType="VARCHAR" property="templetId"/>
        <result column="templet_category_id" jdbcType="VARCHAR" property="templetCategoryId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="des" jdbcType="VARCHAR" property="des"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,templet_id,templet_category_id,title,des,status,update_time,
    create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.templetId and '' != item.templetId">and templet_id = #{item.templetId}</if>
            <if test="null != item.templetCategoryId and '' != item.templetCategoryId">and templet_category_id =
                #{item.templetCategoryId}
            </if>
            <if test="null != item.title and '' != item.title">and title = #{item.title}</if>
            <if test="null != item.des and '' != item.des">and des = #{item.des}</if>
            <if test="null != item.status and '' != item.status">and status = #{item.status}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from templet_questionnaire_info
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from templet_questionnaire_info
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from templet_questionnaire_info
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into templet_questionnaire_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.templetId">templet_id,</if>
            <if test="null != item.templetCategoryId">templet_category_id,</if>
            <if test="null != item.title">title,</if>
            <if test="null != item.des">des,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.templetId">#{item.templetId},</if>
            <if test="null != item.templetCategoryId">#{item.templetCategoryId},</if>
            <if test="null != item.title">#{item.title},</if>
            <if test="null != item.des">#{item.des},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.templetId">templet_id = values(templet_id),</if>
            <if test="null != item.templetCategoryId">templet_category_id = values(templet_category_id),</if>
            <if test="null != item.title">title = values(title),</if>
            <if test="null != item.des">des = values(des),</if>
            <if test="null != item.status">status = values(status),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update templet_questionnaire_info
        <set>
            <if test="null != item.templetId">templet_id = #{item.templetId},</if>
            <if test="null != item.templetCategoryId">templet_category_id = #{item.templetCategoryId},</if>
            <if test="null != item.title">title = #{item.title},</if>
            <if test="null != item.des">des = #{item.des},</if>
            <if test="null != item.status">status = #{item.status},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from templet_questionnaire_info
        <include refid="sql_where"/>
    </delete>
</mapper>
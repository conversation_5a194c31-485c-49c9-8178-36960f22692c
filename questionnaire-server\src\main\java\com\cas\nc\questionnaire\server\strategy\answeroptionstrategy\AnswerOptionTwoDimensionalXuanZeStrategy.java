package com.cas.nc.questionnaire.server.strategy.answeroptionstrategy;

import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateReqDto;
import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import com.cas.nc.questionnaire.server.mapstruct.AnswerCreateConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.isBiZhong;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.isJuZhenHuaDongTiao;
import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_TITLE_SPLIT;
import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.constructBaseAnswerOption;

@Component("answerOptionTwoDimensionalXuanZeStrategy")
public class AnswerOptionTwoDimensionalXuanZeStrategy implements AnswerOptionStrategy {

    @Override
    public List<AnswerOptionPo> construct(AnswerCreateReqDto reqDto, String answerId, Map.Entry<String, Object> dataEntry, TitleTypeEnum titleTypeEnum) {
        List<AnswerOptionPo> resultList = new ArrayList<>();
        Object v = dataEntry.getValue();
        String k = dataEntry.getKey();
        String[] titles = k.split(ANSWER_TITLE_SPLIT);

        AnswerOptionPo doubleAnswerOption = constructBaseAnswerOption(reqDto, answerId, titles[0], titleTypeEnum);
        doubleAnswerOption.setRowNumber(Integer.valueOf(titles[1]) + 1);
        if (v instanceof List) {
            List<Object> optionList = (List<Object>) v;
            if (!CollectionUtils.isEmpty(optionList)) {
                optionList.forEach(p -> {
                    AnswerOptionPo tempOptionPo = AnswerCreateConverter.INSTANCE.to(doubleAnswerOption);
                    if (p instanceof Map) {
                        tempOptionPo.setSerialNumber((Integer) ((Map) p).get("serialNumber"));
                        tempOptionPo.setWriteContent((String) ((Map) p).get("blankContent"));
                    } else {
                        tempOptionPo.setSerialNumber((Integer) p + 1);
                    }
                    resultList.add(tempOptionPo);
                });
            }
        } else {
            if (isBiZhong(titleTypeEnum.key())) {
                doubleAnswerOption.setSerialNumber((Integer) v);
            }if (isJuZhenHuaDongTiao(titleTypeEnum.key())){
                if (v instanceof Map) {
                    doubleAnswerOption.setSerialNumber((Integer) ((Map) v).get("serialNumber"));
                } else {
                    doubleAnswerOption.setSerialNumber((Integer) v );
                }
            } else {
                if (v instanceof Map) {
                    doubleAnswerOption.setSerialNumber((Integer) ((Map) v).get("serialNumber"));
                } else {
                    doubleAnswerOption.setSerialNumber((Integer) v + 1);
                }
            }

            resultList.add(doubleAnswerOption);
        }

        return resultList;
    }
}

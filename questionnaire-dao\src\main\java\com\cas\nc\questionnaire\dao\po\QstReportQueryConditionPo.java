package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

/**
 * 测评报告查询条件实体类
 */
public class QstReportQueryConditionPo {
    
    /**
     * 自增主键
     */
    private Long id;
    
    /**
     * 问卷ID
     */
    private String questionnaireId;
    
    /**
     * 设置人用户ID
     */
    private Long userId;
    
    /**
     * 查询开始时间
     */
    private Date beginTime;
    
    /**
     * 查询结束时间
     */
    private Date endTime;
    
    /**
     * 是否启用：1-启用，0-禁用
     */
    private Integer isEnabled;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getQuestionnaireId() {
        return questionnaireId;
    }
    
    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Date getBeginTime() {
        return beginTime;
    }
    
    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }
    
    public Date getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    public Integer getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstLimitConditionDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstLimitConditionPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="limit_condition_id" jdbcType="VARCHAR" property="limitConditionId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="end_hint" jdbcType="VARCHAR" property="endHint"/>
        <result column="return_url" jdbcType="VARCHAR" property="returnUrl"/>
        <result column="return_hint" jdbcType="VARCHAR" property="returnHint"/>
        <result column="email_id" jdbcType="VARCHAR" property="emailId"/>
        <result column="sms_id" jdbcType="VARCHAR" property="smsId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,questionnaire_id,limit_condition_id,user_id,type,end_hint,return_url,
    return_hint,email_id,sms_id,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.questionnaireId">and questionnaire_id = #{item.questionnaireId}</if>
            <if test="null != item.limitConditionId">and limit_condition_id = #{item.limitConditionId}</if>
            <if test="null != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.type">and type = #{item.type}</if>
            <if test="null != item.endHint">and end_hint = #{item.endHint}</if>
            <if test="null != item.returnUrl">and return_url = #{item.returnUrl}</if>
            <if test="null != item.returnHint">and return_hint = #{item.returnHint}</if>
            <if test="null != item.emailId">and email_id = #{item.emailId}</if>
            <if test="null != item.smsId">and sms_id = #{item.smsId}</if>
            <if test="null != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_limit_condition
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_limit_condition
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_limit_condition
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into qst_limit_condition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">id,</if>
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.limitConditionId">limit_condition_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.type">type,</if>
            <if test="null != item.endHint">end_hint,</if>
            <if test="null != item.returnUrl">return_url,</if>
            <if test="null != item.returnHint">return_hint,</if>
            <if test="null != item.emailId">email_id,</if>
            <if test="null != item.smsId">sms_id,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">#{item.id},</if>
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.limitConditionId">#{item.limitConditionId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.type">#{item.type},</if>
            <if test="null != item.endHint">#{item.endHint},</if>
            <if test="null != item.returnUrl">#{item.returnUrl},</if>
            <if test="null != item.returnHint">#{item.returnHint},</if>
            <if test="null != item.emailId">#{item.emailId},</if>
            <if test="null != item.smsId">#{item.smsId},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.id">id = values(id),</if>
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.limitConditionId">limit_condition_id = values(limit_condition_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.type">type = values(type),</if>
            <if test="null != item.endHint">end_hint = values(end_hint),</if>
            <if test="null != item.returnUrl">return_url = values(return_url),</if>
            <if test="null != item.returnHint">return_hint = values(return_hint),</if>
            <if test="null != item.emailId">email_id = values(email_id),</if>
            <if test="null != item.smsId">sms_id = values(sms_id),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update qst_limit_condition
        <set>
            <if test="null != item.type">type = #{item.type},</if>
            <if test="null != item.endHint">end_hint = #{item.endHint},</if>
            <if test="null != item.returnUrl">return_url = #{item.returnUrl},</if>
            <if test="null != item.returnHint">return_hint = #{item.returnHint},</if>
            <if test="null != item.emailId">email_id = #{item.emailId},</if>
            <if test="null != item.smsId">sms_id = #{item.smsId},</if>
        </set>
        where limit_condition_id = #{item.limitConditionId}
        and user_id = #{item.userId}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_limit_condition
        <include refid="sql_where"/>
    </delete>
</mapper>
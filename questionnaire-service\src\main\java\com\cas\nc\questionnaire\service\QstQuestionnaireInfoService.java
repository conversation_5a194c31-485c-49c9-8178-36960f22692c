package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireListRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireRepDto;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.query.QstQuestionnaireInfoQuery;

import java.util.List;


public interface QstQuestionnaireInfoService {
    /**
     * 数据插入
     *
     * @param qstQuestionnaireInfoPo
     * @return
     */
    int insert(QstQuestionnaireInfoPo qstQuestionnaireInfoPo);

    /**
     * 查询符合条件的条数
     *
     * @param query
     * @return
     */
    int selectCount(QstQuestionnaireInfoQuery query);

    /**
     * 我的列表页分页查询
     *
     * @param query
     * @return
     */
    List<QstQuestionnaireInfoPo> selectMyListByPage(QstQuestionnaireInfoQuery query);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstQuestionnaireInfoQuery query);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstQuestionnaireInfoPo> selectList(QstQuestionnaireInfoQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstQuestionnaireInfoPo selectOne(QstQuestionnaireInfoQuery query);

    /**
     * 更新
     *
     * @param query
     * @return
     */
    int update(QstQuestionnaireInfoQuery query);

    /**
     * 复制
     *
     * @param query
     * @param newTitleName
     * @return
     */
    String copy(QstQuestionnaireInfoQuery query, String newTitleName);

    /**
     * 根据问卷ID查询问卷信息
     * @param questionnaireId 问卷ID
     * @return 问卷信息
     */
    QstQuestionnaireInfoPo selectOne(String questionnaireId);

    /**
     * 根据问卷id查询问卷信息
     *
     * @param questionnaireId
     * @return
     */
    QstQuestionnaireInfoPo selectOne(String questionnaireId, Long userId);

    /**
     * 暂停问卷
     *
     * @param questionnaireId
     * @param userId
     */
    int updateStatus2Pause(String questionnaireId, Long userId);

    /**
     * 发布问卷
     *
     * @param questionnaireId
     * @param userId
     * @return
     */
    int updateStatus2Publish(String questionnaireId, Long userId);

    /**
     * 发布问卷
     *
     * @param questionnaireId
     * @param userId
     * @return
     */
    int updateStatusPause2Publish(String questionnaireId, Long userId);

    /**
     * 恢复删除
     *
     * @param questionnaireId
     * @param userId
     * @return
     */
    int updateStatus2Recovery(String questionnaireId, Long userId);

    /**
     * 标记删除
     *
     * @param questionnaireId
     * @param userId
     * @return
     */
    int updateStatus2Delete(String questionnaireId, Long userId);

    /**
     * 问卷列表大小
     *
     * @param query
     * @return
     */
    int selectQuestionnaireListCount(QstQuestionnaireInfoQuery query);

    /**
     * 问卷列表
     *
     * @param query
     * @return
     */
    List<QuestionnaireRepDto> selectQuestionnaireList(QstQuestionnaireInfoQuery query);

    /**
     * 试卷填写数量
     *
     * @param query
     * @return
     */
    int selectAnswerCount(QstQuestionnaireInfoQuery query);

    /**
     * 修改问卷状态
     * @param questionnaireId
     * @return
     */
    int updateStatus(String questionnaireId, Integer status, Long userId);
}

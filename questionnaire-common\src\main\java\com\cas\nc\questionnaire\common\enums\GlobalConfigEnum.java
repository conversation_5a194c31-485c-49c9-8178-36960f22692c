package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.DATA_EXCEPTION;

public enum GlobalConfigEnum {
    ANSWER_LIMIT(1, "收集答卷数量上限"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    GlobalConfigEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static GlobalConfigEnum toEnum(int key) {
        for (GlobalConfigEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(DATA_EXCEPTION);
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

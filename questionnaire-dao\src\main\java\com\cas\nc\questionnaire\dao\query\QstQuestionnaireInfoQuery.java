package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;

import java.util.List;

public class QstQuestionnaireInfoQuery extends QstQuestionnaireInfoPo {
    /*列*/
    private String tableColumns;
    /*开始索引*/
    private int startIndex;
    /*页面大小*/
    private Integer pageSize;
    /*状态集合*/
    private List<Integer> statusList;
    /*模糊标题*/
    private String likeTitle;
    /*新名称, for copy*/
    private String newTitle;
    /*原有状态*/
    private Integer oldStatus;
    /*模糊业务单号*/
    private String likeBizNo;
    /*创建人*/
    private String name;
    /*问卷来源，1：问卷平台；2：继续教育平台*/
    private Integer channel;

    @Override
    public Integer getChannel() {
        return channel;
    }

    @Override
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public String getLikeTitle() {
        return likeTitle;
    }

    public void setLikeTitle(String likeTitle) {
        this.likeTitle = likeTitle;
    }

    public String getNewTitle() {
        return newTitle;
    }

    public void setNewTitle(String newTitle) {
        this.newTitle = newTitle;
    }

    public Integer getOldStatus() {
        return oldStatus;
    }

    public void setOldStatus(Integer oldStatus) {
        this.oldStatus = oldStatus;
    }

    public String getLikeBizNo() {
        return likeBizNo;
    }

    public void setLikeBizNo(String likeBizNo) {
        this.likeBizNo = likeBizNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.user.*;
import com.cas.nc.questionnaire.common.vo.user.*;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserConverter {
    UserConverter INSTANCE = Mappers.getMapper(UserConverter.class);

    UserLoginReqDto to(UserLoginReqVo vo);

    UserRegisterReqDto to(UserRegisterReqVo vo);

    UserInfoRespVo to(UserInfoRespDto userInfo);

    UserInfoRespDto to(UserInfoPo userInfoPo);

    UserModifyPwdReqDto to(UserModifyPwdReqVo vo);

    UserRestPwdSendMailReqDto to(UserRestPwdSendMailReqVo vo);

    UserRestPwdModifyReqDto to(UserRestPwdModifyReqVo vo);

    UserSaveReqDto to(UserSaveReqVo vo);

    UserRemoveReqDto to(UserRemoveReqVo vo);

    UserLoginNoPasswordReqDto to(UserLoginNoPasswordReqVo vo);

    UserLoginByESReqDto to(UserLoginByESVo vo);
}

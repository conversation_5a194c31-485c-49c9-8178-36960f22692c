package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.SendStatusEnum;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.po.QstEmailPo;
import com.cas.nc.questionnaire.dao.query.QstEmailQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstEmailDao;
import com.cas.nc.questionnaire.service.QstEmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.ANSWERED;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.INIT;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.OPEN_NOT_ANSWER;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.SEND;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.isOpened;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.isSended;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;

@Service
public class QstEmailServiceImpl implements QstEmailService {
    private static Logger logger = LoggerFactory.getLogger(QstEmailServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstEmailDao qstEmailDao;

    @Override
    public int insert(QstEmailPo qstEmailPo) {
        return qstEmailDao.insert(qstEmailPo);
    }

    @Override
    public int delete(QstEmailQuery query) {
        validateUserId(query);
        return qstEmailDao.delete(query);
    }

    @Override
    public int delete(String emailId, Long userId) {
        QstEmailQuery query = new QstEmailQuery();
        query.setEmailId(emailId);
        query.setUserId(userId);
        return delete(query);
    }

    @Override
    public List<QstEmailPo> selectList(QstEmailQuery query) {
        validateUserId(query);
        return qstEmailDao.selectList(query);
    }

    @Override
    public QstEmailPo selectOne(QstEmailQuery query) {
        validateUserId(query);
        return qstEmailDao.selectOne(query);
    }

    @Override
    public QstEmailPo selectOne(String emailId) {
        QstEmailQuery query = new QstEmailQuery();
        query.setEmailId(emailId);
        filterCondition(query);
        return selectOne(query);
    }

    @Override
    public QstEmailPo selectOne(String emailId, Long userId) {
        QstEmailQuery query = new QstEmailQuery();
        query.setEmailId(emailId);
        query.setUserId(userId);
//        query.setSendTime(new Date());
        return selectOne(query);
    }

    @Override
    public int update(QstEmailQuery query) {
        validateUserId(query);
        return qstEmailDao.update(query);
    }

    @Override
    public int updateStatus2Send(String emailId) {
        QstEmailQuery query = new QstEmailQuery();
        query.setEmailId(emailId);
        query.setStatus(SEND.key());
        query.setOldStatus(INIT.key());
        query.setSendTime(new Date());
        filterCondition(query);

        int result = update(query);
        if (result != ONE) {
            QstEmailPo qstEmailPo = selectOne(emailId);
            if (qstEmailPo != null && isSended(qstEmailPo.getStatus())) {
                result = ONE;
            }
        }
        return result;
    }

    @Override
    public int selectListCount(QstEmailQuery query) {
        return qstEmailDao.selectCount(query);
    }

    @Override
    public List<QstEmailPo> selectListPage(QstEmailQuery query) {
        return qstEmailDao.selectListPage(query);
    }

    @Override
    public int updateStatus2Open(String emailId) {
        QstEmailQuery query = new QstEmailQuery();
        query.setEmailId(emailId);
        query.setStatus(OPEN_NOT_ANSWER.key());
        query.setOldStatus(SEND.key());
        filterCondition(query);

        int result = update(query);
        if (result != ONE) {
            QstEmailPo qstEmailPo = selectOne(emailId);
            if (qstEmailPo != null && isOpened(qstEmailPo.getStatus())) {
                result = ONE;
            }
        }
        return result;
    }

    @Override
    public int updateStatus2Answered(String emailId) {
        QstEmailQuery query = new QstEmailQuery();
        query.setEmailId(emailId);
        query.setStatus(ANSWERED.key());
        query.setOldStatus(OPEN_NOT_ANSWER.key());
        filterCondition(query);
        return update(query);
    }

    @Override
    public Map<Integer, Integer> analiceMailCount(String questionnaireId, Long userId) {
        SendStatusEnum [] enums = {SendStatusEnum.INIT,SendStatusEnum.SEND,SendStatusEnum.OPEN_NOT_ANSWER,SendStatusEnum.ANSWERED};
        Map<Integer,Integer> counts = new HashMap<>();
        QstEmailQuery qstEmailQuery = new QstEmailQuery();
        qstEmailQuery.setQuestionnaireId(questionnaireId);
        qstEmailQuery.setUserId(userId);
        counts.put(0,qstEmailDao.selectCount(qstEmailQuery));
        Arrays.stream(enums).forEach(e -> {
            qstEmailQuery.setStatus(e.key());
            int i = qstEmailDao.selectCount(qstEmailQuery);
            counts.put(e.key(),i);
        });
        return counts;
    }

    private void validateUserId(QstEmailQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }

    private void filterCondition(QstEmailQuery query) {
        Assert.notNull(query.getEmailId(), "emailId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getEmailId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class QstLimitConditionPo {
    /*自增id*/
    private Long id;

    /*问卷id*/
    private String questionnaireId;

    /*业务主键*/
    private String limitConditionId;

    /*用户id*/
    private Long userId;

    /*类型，1：无条件，2：有条件*/
    private Integer type;

    /*答题结束提示*/
    private String endHint;

    /*跳转url*/
    private String returnUrl;

    /*跳转提示*/
    private String returnHint;

    /*邮件id*/
    private String emailId;

    /*短信id*/
    private String smsId;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public String getLimitConditionId() {
        return limitConditionId;
    }

    public void setLimitConditionId(String limitConditionId) {
        this.limitConditionId = limitConditionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getEndHint() {
        return endHint;
    }

    public void setEndHint(String endHint) {
        this.endHint = endHint;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getReturnHint() {
        return returnHint;
    }

    public void setReturnHint(String returnHint) {
        this.returnHint = returnHint;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public String getSmsId() {
        return smsId;
    }

    public void setSmsId(String smsId) {
        this.smsId = smsId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
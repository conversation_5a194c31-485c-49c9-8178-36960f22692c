package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.answer.ListAnswerRepDto;
import com.cas.nc.questionnaire.common.dto.answer.ListAnswerReqDto;
import com.cas.nc.questionnaire.common.vo.answer.AnswerRepVo;
import com.cas.nc.questionnaire.common.vo.answer.ListAnswerReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ListAnswerConverter {
    ListAnswerConverter INSTANCE = Mappers.getMapper(ListAnswerConverter.class);

    ListAnswerReqDto to(ListAnswerReqVo vo);

    List<AnswerRepVo> to(List<ListAnswerRepDto> data);

    @Mappings({
            @Mapping(source = "commitTime", target = "commitTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
    })
    AnswerRepVo to(ListAnswerRepDto data);
}
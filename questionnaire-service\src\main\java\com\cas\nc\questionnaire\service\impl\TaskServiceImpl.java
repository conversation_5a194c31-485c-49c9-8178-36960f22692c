package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.task.TaskStatusEnum;
import com.cas.nc.questionnaire.common.shard.TaskShardUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.Perfor4jUtil;
import com.cas.nc.questionnaire.dao.nosharddao.TaskDao;
import com.cas.nc.questionnaire.dao.po.TaskPo;
import com.cas.nc.questionnaire.dao.query.TaskQuery;
import com.cas.nc.questionnaire.service.TaskService;
import org.perf4j.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Random;

@Service
public class TaskServiceImpl implements TaskService {
    private static Logger logger = LoggerFactory.getLogger(TaskServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private TaskDao taskDao;

    @Override
    public List<TaskPo> selectTaskList(TaskQuery query) {
        return taskDao.selectTaskList(query);
    }

    @Override
    public int unlockTask(TaskQuery taskQuery) {
        taskQuery.setTaskStatus(TaskStatusEnum.INITIALIZATION.key());
        taskQuery.setOldTaskStatus(TaskStatusEnum.LOCK.key());
        return updateTaskStatus(taskQuery);
    }

    @Override
    public int hangTask(TaskQuery taskQuery) {
        taskQuery.setTaskStatus(TaskStatusEnum.HANG.key());
        taskQuery.setOldTaskStatus(TaskStatusEnum.LOCK.key());
        return updateTaskStatus(taskQuery);
    }

    @Override
    public int insertTask(TaskPo taskPo) {
        return taskDao.insert(taskPo);
    }

    @Override
    public int createTask(long refId, String foreignRefId, int taskType, String content, String url) {
        int result = 0;
        TaskPo taskPo = new TaskPo();
        taskPo.setRefId(refId);
        taskPo.setForeignRefId(foreignRefId);
        taskPo.setTaskType(taskType);
        taskPo.setContent(content);
        taskPo.setUrl(url);
        taskPo.setTaskStatus(TaskStatusEnum.INITIALIZATION.key());
        taskPo.setTaskPartition(new Random().nextInt(4));//分布式任务优化
        taskPo.setRouteId(String.valueOf(TaskShardUtil.getTable(foreignRefId)));
        taskPo.setFailNum(0);
        try {
            result = insertTask(taskPo);
        } catch (DuplicateKeyException e) {
            logger.error("TaskServiceImpl.createTask DuplicateKeyException param[{}]", JSONUtil.toJSONString(taskPo), e);
            result = 1;
        }
        return result;
    }

    @Override
    public int createTask(String foreignRefId, int taskType, String content) {
        return createTask(0L, foreignRefId, taskType, content, "");
    }

    @Override
    public int createTask(long refId, int taskType, String content) {
        return createTask(refId, "", taskType, content, "");
    }

    @Override
    public int lockTask(TaskQuery taskQuery) {
        taskQuery.setTaskStatus(TaskStatusEnum.LOCK.key());
        taskQuery.setOldTaskStatus(TaskStatusEnum.INITIALIZATION.key());
        return updateTaskStatus(taskQuery);
    }

    @Override
    public int updateTaskSuccess(TaskQuery taskQuery) {
        taskQuery.setTaskStatus(TaskStatusEnum.SUCCESS.key());
        taskQuery.setOldTaskStatus(TaskStatusEnum.LOCK.key());
        return updateTaskStatus(taskQuery);
    }

    @Override
    public int updateFailNumAddOne(TaskPo taskPo) {
        TaskQuery query = new TaskQuery();
        query.setId(taskPo.getId());
        query.setFailNum(taskPo.getFailNum() + 1);
        query.setOldFailNum(taskPo.getFailNum());
        return taskDao.updateAddFailNum(query);
    }

    @Override
    public List<TaskPo> selectLockTaskList(TaskQuery query) {
        StopWatch start = Perfor4jUtil.start();
        List<TaskPo> taskList = taskDao.selectLockTaskList(query);
        Perfor4jUtil.stop1("taskDao.selectLockTaskList", start);
        return taskList;
    }

    @Override
    public List<TaskPo> selectExecuteTaskList(TaskQuery query) {
        StopWatch start = Perfor4jUtil.start();
        query.setUpdateTime(new Date());
        List<TaskPo> taskList = taskDao.selectExecuteTaskList(query);
        Perfor4jUtil.stop1("taskDao.selectExecuteTaskList", start);
        return taskList;
    }

    @Override
    public int deleteTask(TaskQuery query) {
        return taskDao.deleteTask(query);
    }

    @Override
    public TaskPo selectLockUpdateTimeAsc(TaskQuery query) {
        StopWatch start = Perfor4jUtil.start();
        TaskPo taskPo = taskDao.selectLockUpdateTimeAsc(query);
        Perfor4jUtil.stop1("taskDao.selectLockUpdateTimeAsc", start);
        return taskPo;
    }

    @Override
    public List<TaskPo> selectDeleteTaskList(TaskQuery query) {
        StopWatch start = Perfor4jUtil.start();
        List<TaskPo> taskList = taskDao.selectDeleteTaskList(query);
        Perfor4jUtil.stop1("taskDao.selectDeleteTaskList", start);
        return taskList;
    }

    @Override
    public int updateTaskStatus(TaskQuery query) {
        StopWatch start = Perfor4jUtil.start();
        int result = taskDao.updateTaskStatus(query);
        Perfor4jUtil.stop1("taskDao.updateTaskStatus", start);
        return result;
    }

    @Override
    public void createTaskBatch(List<TaskPo> taskPoList) {
        for (TaskPo taskPo : taskPoList)
            this.createTask(taskPo.getForeignRefId(),taskPo.getTaskType(),taskPo.getContent());
    }
}

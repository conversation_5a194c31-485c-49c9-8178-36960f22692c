<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.GlobalConfigDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.GlobalConfigPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,type,value,remark,yn,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.type and '' != item.type">and type = #{item.type}</if>
            <if test="null != item.value and '' != item.value">and value = #{item.value}</if>
            <if test="null != item.remark and '' != item.remark">and remark = #{item.remark}</if>
            <if test="null != item.yn and '' != item.yn">and yn = #{item.yn}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from global_config
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from global_config
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into global_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.type">type,</if>
            <if test="null != item.value">value,</if>
            <if test="null != item.remark">remark,</if>
            <if test="null != item.yn">yn,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.type">#{item.type},</if>
            <if test="null != item.value">#{item.value},</if>
            <if test="null != item.remark">#{item.remark},</if>
            <if test="null != item.yn">#{item.yn},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.type">type = values(type),</if>
            <if test="null != item.value">value = values(value),</if>
            <if test="null != item.remark">remark = values(remark),</if>
            <if test="null != item.yn">yn = values(yn),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <insert id="batchInsert">
        insert into global_config(
        type,
        value,
        remark,
        yn
        ) values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.type},
            #{item.value},
            #{item.remark},
            #{item.yn}
            )
        </foreach>
    </insert>
    <sql id="sql_update">
        update global_config
        <set>
            <if test="null != item.type">type = #{item.type},</if>
            <if test="null != item.value">value = #{item.value},</if>
            <if test="null != item.remark">remark = #{item.remark},</if>
            <if test="null != item.yn">yn = #{item.yn},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <update id="batchUpdate">
        <foreach collection="list" index="index" item="item" open="" separator=";" close="">
            <include refid="sql_update"/>
        </foreach>
    </update>
    <delete id="delete">
        delete from global_config
        <include refid="sql_where"/>
    </delete>
</mapper>
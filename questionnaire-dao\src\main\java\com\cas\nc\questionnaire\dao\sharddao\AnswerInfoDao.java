package com.cas.nc.questionnaire.dao.sharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.bo.ProvinceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.SourceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.TimeAnalysisBo;
import com.cas.nc.questionnaire.dao.po.AnswerInfoPo;
import com.cas.nc.questionnaire.dao.query.AnswerInfoQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AnswerInfoDao extends BaseDao<AnswerInfoPo, AnswerInfoQuery> {

    /**
     * 查询条数
     *
     * @param query
     * @return
     */
    int selectCount(@Param("item") AnswerInfoQuery query);

    /**
     * 根据过滤条件查询
     *
     * @param query
     * @return
     */
    List<AnswerInfoPo> selectListByFilterCondition(@Param("item") AnswerInfoQuery query);

    /**
     * 查询最新的一条数据
     *
     * @param query
     * @return
     */
    AnswerInfoPo selectNewest(@Param("item") AnswerInfoQuery query);

    List<AnswerInfoPo> selectListByPage(@Param("item") AnswerInfoQuery query);

    List<SourceAnalysisBo> selectSourceStatistics(@Param("item") AnswerInfoQuery query);

    List<ProvinceAnalysisBo> selectProvinceStatistics(@Param("item") AnswerInfoQuery query);

    List<TimeAnalysisBo> selectDayStatistics(@Param("item") AnswerInfoQuery query);

    List<TimeAnalysisBo> selectWeekDayStatistics(@Param("item") AnswerInfoQuery query);

    List<TimeAnalysisBo> selectMonthStatistics(@Param("item") AnswerInfoQuery query);

    List<Long> selectRuleGroupBy(@Param("item") AnswerInfoQuery query);
}
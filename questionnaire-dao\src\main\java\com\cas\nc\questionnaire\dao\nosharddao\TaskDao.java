package com.cas.nc.questionnaire.dao.nosharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.TaskPo;
import com.cas.nc.questionnaire.dao.query.TaskQuery;

import java.util.List;

public interface TaskDao extends BaseDao<TaskPo, TaskQuery> {
    /**
     * 查询任务
     *
     * @param query
     * @return
     */
    List<TaskPo> selectTaskList(TaskQuery query);

    /**
     * 查询执行任务
     *
     * @param query
     * @return
     */
    List<TaskPo> selectExecuteTaskList(TaskQuery query);

    /**
     * 更新任务状态
     *
     * @param taskQuery
     * @return
     */
    int updateTaskStatus(TaskQuery taskQuery);

    /**
     * 生成任务
     *
     * @param taskPo
     * @return
     */
    int insert(TaskPo taskPo);

    /**
     * 更新失败次数
     *
     * @param query
     * @return
     */
    int updateAddFailNum(TaskQuery query);

    /**
     * 查询锁定任务
     *
     * @return
     */
    List<TaskPo> selectLockTaskList(TaskQuery query);

    /**
     * 删除任务
     *
     * @param query
     * @return
     */
    int deleteTask(TaskQuery query);

    /**
     * 查询最久的一条锁定任务更新时间
     *
     * @param query
     * @return
     */
    TaskPo selectLockUpdateTimeAsc(TaskQuery query);

    /**
     * 查询需要删除的任务
     *
     * @param query
     * @return
     */
    List<TaskPo> selectDeleteTaskList(TaskQuery query);
}
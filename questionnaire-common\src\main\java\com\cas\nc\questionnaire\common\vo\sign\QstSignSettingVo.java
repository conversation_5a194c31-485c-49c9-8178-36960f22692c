package com.cas.nc.questionnaire.common.vo.sign;

import java.util.Date;

public class QstSignSettingVo {
    /*主键ID*/
    private Long id;
    
    /*问卷ID*/
    private String questionnaireId;
    
    /*签到方式 1-微信认证签到*/
    private Integer signType;
    
    /*是否限制签到位置 0-不限制 1-限制*/
    private Integer locationLimit;
    
    /*签到位置纬度*/
    private Double latitude;
    
    /*签到位置经度*/
    private Double longitude;
    
    /*位置范围(米)*/
    private Integer locationRange;
    
    /*位置名称*/
    private String locationName;
    
    /*签到开始时间*/
    private Date beginTime;
    
    /*签到截止时间*/
    private Date endTime;
    
    /*签到频率 1-只签到一次 2-每日签到一次*/
    private Integer signFrequency;
    
    /*签到成功提示信息*/
    private String successTip;
    
    /*签到失败提示信息*/
    private String failTip;
    
    /*状态 1-启用 0-禁用*/
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Integer getSignType() {
        return signType;
    }

    public void setSignType(Integer signType) {
        this.signType = signType;
    }

    public Integer getLocationLimit() {
        return locationLimit;
    }

    public void setLocationLimit(Integer locationLimit) {
        this.locationLimit = locationLimit;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Integer getLocationRange() {
        return locationRange;
    }

    public void setLocationRange(Integer locationRange) {
        this.locationRange = locationRange;
    }
    
    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getSignFrequency() {
        return signFrequency;
    }

    public void setSignFrequency(Integer signFrequency) {
        this.signFrequency = signFrequency;
    }

    public String getSuccessTip() {
        return successTip;
    }

    public void setSuccessTip(String successTip) {
        this.successTip = successTip;
    }

    public String getFailTip() {
        return failTip;
    }

    public void setFailTip(String failTip) {
        this.failTip = failTip;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
} 
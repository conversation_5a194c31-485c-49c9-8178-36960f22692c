package com.cas.nc.questionnaire.dao.bo;

import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;

import java.util.List;

public class QuestionnairePreviewTextBo {
    private QstQuestionnaireInfoPo questionnaireInfoPo;

    private List<QstTitlePo> titlePoList;

    private List<QstOptionPo> optionPoList;

    public QstQuestionnaireInfoPo getQuestionnaireInfoPo() {
        return questionnaireInfoPo;
    }

    public void setQuestionnaireInfoPo(QstQuestionnaireInfoPo questionnaireInfoPo) {
        this.questionnaireInfoPo = questionnaireInfoPo;
    }

    public List<QstTitlePo> getTitlePoList() {
        return titlePoList;
    }

    public void setTitlePoList(List<QstTitlePo> titlePoList) {
        this.titlePoList = titlePoList;
    }

    public List<QstOptionPo> getOptionPoList() {
        return optionPoList;
    }

    public void setOptionPoList(List<QstOptionPo> optionPoList) {
        this.optionPoList = optionPoList;
    }
}

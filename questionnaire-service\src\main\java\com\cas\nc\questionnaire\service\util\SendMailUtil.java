package com.cas.nc.questionnaire.service.util;

import cn.kepu.elearning.jms.mail.MailMessage;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.EmailServerConfigPo;
import com.cas.nc.questionnaire.service.EmailServerConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Component
public class SendMailUtil {
    private static Logger logger = LoggerFactory.getLogger(SendMailUtil.class);

    private static MailSender mailSender;

    @Resource
    private CnicMailSender cnicMailSender;

    @Value("${qst.env}")
    private String env;

    @Resource
    private EmailServerConfigService emailServerConfigService;

    /**
     * 发送简单的文本内容
     *
     * @param title     邮件标题
     * @param content   邮件内容
     * @param receivers 接收人
     * @param ccTo      抄送人
     */
    public boolean sendSimpleMail(String title, String content, String[] receivers, String[] ccTo) {
        if ("product".equalsIgnoreCase(env)) {
            MailMessage mailMessage = new MailMessage();
            mailMessage.setToEmail(receivers);
            mailMessage.setReceiverIDs(new Integer[]{-1});
            mailMessage.setTitle(title);
            mailMessage.setContent(content);
            mailMessage.setSendType(0);
            mailMessage.setMailstamp(title);
            mailMessage.setMsgId(null);
            mailMessage.setIsUrgent(0);
            mailMessage.setAttachment(null);
            try {
                cnicMailSender.send(mailMessage);
                logger.info("cnic邮件发送成功");
                return true;
            } catch (Exception e) {
                logger.error("cnicSendHtmlMail", e);
            }

            return false;
        } else {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setSubject(title);
            message.setTo(receivers);
            message.setFrom(mailSender.getUsername());
            message.setText(content);
            message.setCc(ccTo);
            try {
                mailSender.send(message);
                logger.info("邮件发送成功");
                return true;
            } catch (MailException e) {
                logger.error("sendHtmlMail", e);
            }
            return false;
        }

    }

    /**
     * 发送HTML格式的邮件
     *
     * @param title     邮件标题
     * @param content   邮件内容 html格式字符串
     * @param receivers 接收人
     * @param ccTo      抄送人
     */
    public boolean sendHtmlMail(String title, String content, String[] receivers, String[] ccTo, String replyAddress, Integer isUrgent) {
        if ("product".equalsIgnoreCase(env)) {
            MailMessage mailMessage = new MailMessage();
            mailMessage.setToEmail(receivers);
            mailMessage.setReceiverIDs(new Integer[]{-1});
            mailMessage.setTitle(title);
            mailMessage.setContent(content);
            mailMessage.setSendType(0);
            mailMessage.setMailstamp(title);
            mailMessage.setMsgId(null);
            mailMessage.setIsUrgent(isUrgent);
            mailMessage.setAttachment(null);
            try {
                cnicMailSender.send(mailMessage);
                logger.info("cnic邮件发送成功");
                return true;
            } catch (Exception e) {
                logger.error("cnicSendHtmlMail", e);
            }

            return false;
        } else {
            try {
                MimeMessage mimeMessage = mailSender.createMimeMessage();
                MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage);
                mimeMessageHelper.setSubject(title);
                mimeMessageHelper.setText(content, true);
                mimeMessageHelper.setFrom(mailSender.getUsername());
                mimeMessageHelper.setTo(receivers);
                mimeMessageHelper.setCc(ccTo);
                if (StringUtil.isNotBlank(replyAddress)) {
                    mimeMessageHelper.setReplyTo(replyAddress);
                }
                mailSender.send(mimeMessage);
                logger.info("邮件发送成功");
                return true;
            } catch (Throwable e) {
                logger.error("sendHtmlMail", e);
                initEmail();
            }
            return false;
        }
    }

    @PostConstruct
    private void initEmail() {
        Lock lock = new ReentrantLock();
        try {
            if (lock.tryLock()) {
                List<EmailServerConfigPo> emailServerConfigPoList = emailServerConfigService.selectAll();
                Assert.notNull(emailServerConfigPoList, CodeEnum.DATA_EXCEPTION);
                EmailServerConfigPo configPo = null;

                if (mailSender == null || mailSender.getId() == 0L) {
                    configPo = emailServerConfigPoList.get(0);
                } else {
                    boolean flag = false;
                    long i = 1L;
                    long listSize = emailServerConfigPoList.size();
                    for (EmailServerConfigPo bean : emailServerConfigPoList) {
                        if (flag) {
                            configPo = bean;
                            break;
                        }
                        if (bean.getId() == mailSender.getId()) {
                            if (i == listSize) {
                                configPo = emailServerConfigPoList.get(0);
                                break;
                            }
                            flag = true;
                        }
                        i++;
                    }
                }
                initMailSender(configPo.getId(), configPo.getEmailHost(), configPo.getEmailPort(), configPo.getEmailAddress(), configPo.getEmailPassword());

            }
        } catch (Exception e1) {

        } finally {
            lock.unlock();
        }
    }

    private void initMailSender(Long id, String emailHost, Integer emailPort, String emailAddress, String emailPassword) {
        mailSender = new MailSender();
        mailSender.setId(id);
        mailSender.setHost(emailHost);
        mailSender.setPort(emailPort);
        mailSender.setUsername(emailAddress);
        mailSender.setPassword(emailPassword);
        mailSender.setDefaultEncoding("UTF-8");
        Properties javaMailProperties = new Properties();
        javaMailProperties.setProperty("mail.smtp.auth", "false");
        javaMailProperties.setProperty("mail.smtp.timeout", "25000");
        mailSender.setJavaMailProperties(javaMailProperties);
    }

}

package com.cas.nc.questionnaire.common.to.questionnairetitle;

import com.cas.nc.questionnaire.common.to.questionnaireoption.BaseXuanZeOptionTo;

import java.util.List;
import java.util.Map;

public class BaseXuanZeTitleTo<T extends BaseXuanZeOptionTo> extends BaseTitleTo {
    /*排列方式，1：竖排，2：横排*/
    private Integer arrangementMode;
    private Boolean randomDisorder;
    private Map<String, Integer> jumpOptions;
    private List<T> options;

    public Integer getArrangementMode() {
        return arrangementMode;
    }

    public void setArrangementMode(Integer arrangementMode) {
        this.arrangementMode = arrangementMode;
    }

    public Boolean getRandomDisorder() {
        return randomDisorder;
    }

    public void setRandomDisorder(Boolean randomDisorder) {
        this.randomDisorder = randomDisorder;
    }

    public Map<String, Integer> getJumpOptions() {
        return jumpOptions;
    }

    public void setJumpOptions(Map<String, Integer> jumpOptions) {
        this.jumpOptions = jumpOptions;
    }

    public List<T> getOptions() {
        return options;
    }

    public void setOptions(List<T> options) {
        this.options = options;
    }

}

package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.TimeAnalysisRepDto;
import com.cas.nc.questionnaire.common.dto.analysis.TimeAnalysisReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.TimeAnalysisReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TimeAnalysisConverter {
    TimeAnalysisConverter INSTANCE = Mappers.getMapper(TimeAnalysisConverter.class);

    TimeAnalysisReqDto to(TimeAnalysisReqVo vo);

    TimeAnalysisRepDto to(TimeAnalysisRepDto repDto);
}
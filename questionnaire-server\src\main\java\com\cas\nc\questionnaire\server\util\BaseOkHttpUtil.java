package com.cas.nc.questionnaire.server.util;

import com.alibaba.fastjson.JSON;

import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 基础okhttp请求工具
 */
public abstract class BaseOkHttpUtil {
    protected Logger logger = LoggerFactory.getLogger(this.getClass());
    protected OkHttpClient mOkHttpClient;//连接对象

    public BaseOkHttpUtil() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.connectTimeout(10, TimeUnit.SECONDS);
        builder.writeTimeout(10, TimeUnit.SECONDS);
        builder.readTimeout(10, TimeUnit.SECONDS);
        mOkHttpClient = builder.build();
    }

    /**
     * 打个日志
     *
     * @param str 要打啥
     */
    protected void log(String str) {
        logger.info(str);
    }

    /**
     * 打个日志
     *
     * @param str 要打啥
     * @param e   异常信息
     */
    protected void logE(String str, Throwable e) {
        logger.error(str, e);
    }

    /**
     * 默认header参数对象构建，方便后期增加统一参数
     *
     * @return 默认header参数对象
     */
    protected HashMap<String, Object> defaultHeaderParams() {
        return new HashMap<>();
    }

    /**
     * 默认请求参数对象构建，方便后期增加统一参数
     *
     * @return 默认请求参数对象
     */
    protected HashMap<String, Object> defaultRequestParams() {
        return new HashMap<>();
    }

    /**
     * 参数转化为url后拼接的部分
     *
     * @param params 请求参数
     * @param first  是否是第一个参数，用于判断是?开头还是&开头
     * @return 拼接处理好的参数url
     */
    protected String mapToUrl(HashMap<String, Object> params, boolean first) {
        StringBuilder sb = new StringBuilder();
        if (params != null) {
            int i = 0;
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (i == 0 && first) {
                    sb.append("?");
                } else {
                    sb.append("&");
                }
                sb.append(entry.getKey()).append("=").append(entry.getValue());
                i++;
            }
        }
        String result = sb.toString();
        log("请求参数url：" + (StringUtils.isBlank(result) ? "无" : result));
        return result;
    }

    /**
     * 参数map转化为headers对象
     *
     * @param params header参数
     * @return headers对象
     */
    protected Headers mapToHeaders(HashMap<String, Object> params) {
        Headers.Builder builder = new Headers.Builder();
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                builder.add(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        log("header参数：" + params);
        return builder.build();
    }

    /**
     * 参数map转化为json请求体
     *
     * @param params 请求参数
     * @return json请求体
     */
    protected RequestBody mapToJsonBody(HashMap<String, Object> params) {
        try {
            String json = JSON.toJSONString(params);
            log("请求参数json：" + json);
            return RequestBody.create(MediaType.parse("application/json"), json);
        } catch (Exception e) {
            logE("请求失败 failed", e);
        }
        return null;
    }

    /**
     * 参数map转化为Multipart form请求体
     *
     * @param params 请求参数
     * @return form请求体
     */
    protected RequestBody mapToMultipartFormBody(HashMap<String, Object> params) {
        try {
            MultipartBody.Builder builder = new MultipartBody.Builder();
            builder.setType(MultipartBody.FORM);
            if (params != null) {
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    builder.addFormDataPart(entry.getKey(), String.valueOf(entry.getValue()));
                }
            }
            log("请求参数：" + params);
            return builder.build();
        } catch (Exception e) {
            logE("请求失败 failed", e);
        }
        return null;
    }

    /**
     * 参数map转化为Multipart form请求体
     *
     * @param params 请求参数
     * @return form请求体
     */
    protected RequestBody mapToFormBody(HashMap<String, Object> params) {
        try {
            FormBody.Builder builder = new FormBody.Builder();
            if (params != null) {
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    builder.add(entry.getKey(), String.valueOf(entry.getValue()));
                }
            }
            log("请求参数：" + params);
            return builder.build();
        } catch (Exception e) {
            logE("请求失败 failed", e);
        }
        return null;
    }

    /**
     * 获取基础url前缀，默认是空字符串，可继承重写
     *
     * @return 基础url前缀
     */
    protected String getBaseUrl() {
        return "";
    }

    /**
     * 默认请求构造器
     *
     * @param url          请求完整url地址
     * @param headerParams header参数
     * @return 默认请求构造器
     */
    protected Request.Builder defaultRequestBuilder(String url, HashMap<String, Object> headerParams) {
        log("请求url：" + url);
        return new Request.Builder().url(url).headers(mapToHeaders(headerParams));
    }

    /**
     * post形式 json入参格式请求
     *
     * @param methodName    接口名
     * @param headerParams  header参数
     * @param requestParams 请求参数
     * @return 回文基础对象
     */
    protected Response postParamsInJsonBody(String methodName, HashMap<String, Object> headerParams, HashMap<String, Object> requestParams) {
        try {
            Request.Builder builder = defaultRequestBuilder(getBaseUrl() + methodName, headerParams);
            builder.post(mapToJsonBody(requestParams));
            return mOkHttpClient.newCall(builder.build()).execute();
        } catch (Throwable e) {
            logE("请求失败 failed", e);
            return null;
        }
    }

    /**
     * post形式 Multipart表单入参请求
     *
     * @param methodName    接口名
     * @param headerParams  header参数
     * @param requestParams 请求参数
     * @return 回文基础对象
     */
    protected Response postParamsInMultipartFormBody(String methodName, HashMap<String, Object> headerParams, HashMap<String, Object> requestParams) {
        try {
            Request.Builder builder = defaultRequestBuilder(getBaseUrl() + methodName, headerParams);
            builder.post(mapToMultipartFormBody(requestParams));
            return mOkHttpClient.newCall(builder.build()).execute();
        } catch (Throwable e) {
            logE("请求失败 failed", e);
            return null;
        }
    }

    /**
     * post形式 Form表单入参请求
     *
     * @param methodName    接口名
     * @param headerParams  header参数
     * @param requestParams 请求参数
     * @return 回文基础对象
     */
    protected Response postParamsInFormBody(String methodName, HashMap<String, Object> headerParams, HashMap<String, Object> requestParams) {
        try {
            Request.Builder builder = defaultRequestBuilder(getBaseUrl() + methodName, headerParams);
            builder.post(mapToFormBody(requestParams));
            return mOkHttpClient.newCall(builder.build()).execute();
        } catch (Throwable e) {
            logE("请求失败 failed", e);
            return null;
        }
    }

    /**
     * get形式请求
     *
     * @param methodName    接口名
     * @param headerParams  header参数
     * @param requestParams 请求参数
     * @return 回文基础对象
     */
    protected Response get(String methodName, HashMap<String, Object> headerParams, HashMap<String, Object> requestParams) {
        try {
            Request.Builder builder = defaultRequestBuilder(
                    getBaseUrl() + methodName + mapToUrl(requestParams, !methodName.contains("?")), headerParams);
            builder.get();
            return mOkHttpClient.newCall(builder.build()).execute();
        } catch (Throwable e) {
            logE("请求失败 failed", e);
            return null;
        }
    }

    /**
     * 解析回文
     *
     * @param response  接口response
     * @param analyzers 回文解析器
     * @param <E>       回文中data类型
     * @param <JA>      回文解析器泛型
     * @return 解析后的回文对象
     */
    protected <E, JA extends BaseJsonAnalyzers<E>> E analysis(Response response, JA analyzers) {
        try {
            if (response.code() == 200) {
                String result = response.body().string();
                log("开始解析回文原始数据 : " + result);
                return analyzers.successAnalysis(result);
            } else {
                log("status_code = " + response.code());
                return analyzers.defaultFail(response);
            }
        } catch (Throwable e) {
            logE("请求解析失败 failed", e);
            return analyzers.defaultFail(response);
        }
    }
}

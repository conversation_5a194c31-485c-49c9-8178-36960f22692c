package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.TITLE_TYPE_ERROR;


public enum TitleDimensionalTypeEnum {
    //选择题
    XUANZE(1, "选择题"),
    JUZHEN(2, "矩阵题"),
    ;

    private final static Logger logger = LoggerFactory.getLogger(TitleDimensionalTypeEnum.class);

    private final Integer key;
    private final String value;

    TitleDimensionalTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static TitleDimensionalTypeEnum toEnum(int key) {
        for (TitleDimensionalTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        logger.error("TitleTypeEnum.toEnum exception param[{}]", key);
        throw new ServerException(TITLE_TYPE_ERROR);
    }

    public static TitleDimensionalTypeEnum toEnum(String name) {
        for (TitleDimensionalTypeEnum bean : values()) {
            if (bean.name().equalsIgnoreCase(name)) {
                return bean;
            }
        }
        logger.error("TitleTypeEnum.toEnum exception param[{}]", name);
        throw new ServerException(TITLE_TYPE_ERROR);
    }


    public static boolean isXuanZe(int code) {
        return XUANZE.key == code;
    }

    public static boolean isJuZhen(int code) {
        return JUZHEN.key == code;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

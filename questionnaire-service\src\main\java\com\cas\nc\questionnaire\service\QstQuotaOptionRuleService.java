package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstQuotaOptionRulePo;
import com.cas.nc.questionnaire.dao.query.QstQuotaOptionRuleQuery;

import java.util.List;

public interface QstQuotaOptionRuleService {
    /**
     * 数据插入
     *
     * @param qstQuotaOptionRulePo
     * @return
     */
    int insert(QstQuotaOptionRulePo qstQuotaOptionRulePo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstQuotaOptionRuleQuery query);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstQuotaOptionRulePo> selectList(QstQuotaOptionRuleQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstQuotaOptionRulePo selectOne(QstQuotaOptionRuleQuery query);
}

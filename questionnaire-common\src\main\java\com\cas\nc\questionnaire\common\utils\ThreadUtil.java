package com.cas.nc.questionnaire.common.utils;

import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadUtil {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public static volatile ExecutorService executor;

    public static volatile ExecutorService reportExecutor;

    static {
        initExecutor();
        initReportExecutor();
    }

    /**
     * 异步执行一个任务
     *
     * @param task 任务
     */
    public static Future<?> submit(Runnable task) {
        return executor.submit(task);
    }

    /**
     * 异步执行一个任务
     *
     * @param task 任务
     * @return 任务结果
     */
    public static <V> Future<V> submit(Callable<V> task) {
        return executor.submit(task);
    }


    public static void execute(Runnable task) {
        executor.execute(task);
    }

    /**
     * 批量执行任务
     * 超时间为秒
     *
     * @param callables
     * @param timeout
     * @return
     */
    public static <V> List<V> submit(List<Callable<V>> callables, long timeout) {
        List<Future<V>> futures = new ArrayList<Future<V>>();
        List<V> result = new ArrayList<V>();

        try {
            futures = executor.invokeAll(callables, timeout, TimeUnit.SECONDS);
            for (Future<V> future : futures) {
                V v = future.get();
                if (v != null)
                    result.add(v);
            }
        } catch (Exception e) {
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
        return result;
    }


    /**
     * 关闭服务
     */
    public static void shutdown() {
        if (null != executor) {
            executor.shutdown();
        }
    }

    /**
     * 初始化线程池
     */
    private static void initExecutor() {
        if (executor == null) {
            synchronized (ThreadUtil.class) {
                if (executor == null) {
                    executor = new ThreadPoolExecutor(10, 15, 1,
                            TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(5000), new DefaultThreadFactory());
                }
            }
        }
    }

    /**
     * 初始化线程池
     */
    private static void initReportExecutor() {
        if (reportExecutor == null) {
            synchronized (ThreadUtil.class) {
                if (reportExecutor == null) {
                    reportExecutor = new ThreadPoolExecutor(10, 15, 1,
                            TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(5000), new DefaultThreadFactory());
                }
            }
        }
    }

    /**
     * 线程工厂
     */
    static class DefaultThreadFactory implements ThreadFactory {
        private static final AtomicInteger poolNumber = new AtomicInteger(1);
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final ThreadGroup threadGroup;
        private final String namePrefix;

        public DefaultThreadFactory() {
            SecurityManager sm = System.getSecurityManager();
            this.threadGroup = sm != null ? sm.getThreadGroup() : Thread.currentThread().getThreadGroup();
            this.namePrefix = "pool-" + DefaultThreadFactory.poolNumber.getAndIncrement() + "-thread-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(this.threadGroup, r, this.namePrefix + this.threadNumber.getAndIncrement(), 0);
            if (t.isDaemon())
                t.setDaemon(false);
            if (t.getPriority() != Thread.NORM_PRIORITY)
                t.setPriority(Thread.NORM_PRIORITY);
            return t;
        }

    }
}

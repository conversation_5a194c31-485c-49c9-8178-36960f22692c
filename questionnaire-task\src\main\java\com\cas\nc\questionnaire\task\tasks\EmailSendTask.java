package com.cas.nc.questionnaire.task.tasks;

import com.cas.nc.questionnaire.common.enums.task.TaskTypeEnum;
import com.cas.nc.questionnaire.service.util.SendMailUtil;
import com.cas.nc.questionnaire.dao.po.QstEmailPo;
import com.cas.nc.questionnaire.dao.po.TaskPo;
import com.cas.nc.questionnaire.service.QstEmailService;
import com.cas.nc.questionnaire.task.base.SendQuestionnaireBaseTask;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;



public class EmailSendTask extends SendQuestionnaireBaseTask {

    @Resource
    private QstEmailService qstEmailService;
    @Resource
    private SendMailUtil sendMailUtil;

    @Override
    protected TaskTypeEnum getTaskType() {
        return TaskTypeEnum.SEND_BY_EMAIL_TASK;
    }

    //执行邮件发送逻辑
    @Override
    public boolean process(TaskPo task) {
        Map<String, QstEmailPo> cacheMap = new HashMap<>();
        QstEmailPo qstEmailPo = cacheMap.get(task.getForeignRefId());
        if (qstEmailPo == null) {
            qstEmailPo = qstEmailService.selectOne(task.getForeignRefId());
            cacheMap.put(task.getForeignRefId(), qstEmailPo);
        }
        boolean mailResult = sendMailUtil.sendHtmlMail(qstEmailPo.getEmailTitle(), qstEmailPo.getEmailContent(),
                new String[]{qstEmailPo.getAddressees()}, new String[]{}, qstEmailPo.getReplyAddress(), 2);

        int updateFlag = 0;
        if (mailResult) {
            updateFlag = qstEmailService.updateStatus2Send(qstEmailPo.getEmailId());
        }
        return updateFlag > 0 ? true : false;
    }
}

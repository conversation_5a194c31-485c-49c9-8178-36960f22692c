<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.UrlConfigDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.UrlConfigPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="url_name" jdbcType="VARCHAR" property="urlName"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,url,url_name,type,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.url and '' != item.url">and url = #{item.url}</if>
            <if test="null != item.urlName and '' != item.urlName">and url_name = #{item.urlName}</if>
            <if test="null != item.type and '' != item.type">and type = #{item.type}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from url_config
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from url_config
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from url_config
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into url_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.url">url,</if>
            <if test="null != item.urlName">url_name,</if>
            <if test="null != item.type">type,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.url">#{item.url},</if>
            <if test="null != item.urlName">#{item.urlName},</if>
            <if test="null != item.type">#{item.type},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.url">url = values(url),</if>
            <if test="null != item.urlName">url_name = values(url_name),</if>
            <if test="null != item.type">type = values(type),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update url_config
        <set>
            <if test="null != item.url">url = #{item.url},</if>
            <if test="null != item.urlName">url_name = #{item.urlName},</if>
            <if test="null != item.type">type = #{item.type},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from url_config
        <include refid="sql_where"/>
    </delete>
</mapper>
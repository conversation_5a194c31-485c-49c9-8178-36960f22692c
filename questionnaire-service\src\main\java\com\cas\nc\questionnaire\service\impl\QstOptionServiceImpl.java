package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import com.cas.nc.questionnaire.dao.query.QstOptionQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstOptionDao;
import com.cas.nc.questionnaire.service.QstOptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class QstOptionServiceImpl implements QstOptionService {
    private static Logger logger = LoggerFactory.getLogger(QstOptionServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstOptionDao qstOptionDao;

    @Override
    public int insert(QstOptionPo qstOptionPo) {
        return qstOptionDao.insert(qstOptionPo);
    }

    @Override
    public int delete(QstOptionQuery query) {
        validateUserId(query);
        return qstOptionDao.delete(query);
    }

    @Override
    public int delete(String questionnaireId, Long userId) {
        QstOptionQuery query = new QstOptionQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        return delete(query);
    }

    @Override
    public List<QstOptionPo> selectList(QstOptionQuery query) {
        validateUserId(query);
        return qstOptionDao.selectList(query);
    }

    @Override
    public QstOptionPo selectOne(QstOptionQuery query) {
        validateUserId(query);
        return qstOptionDao.selectOne(query);
    }

    @Override
    public void copy(QstOptionQuery query) {
        List<QstOptionPo> poList = selectList(query);
        if (poList != null) {
            for (QstOptionPo bean : poList) {
                bean.setId(null);
                bean.setQuestionnaireId(query.getNewQuestionnaireId());
                insert(bean);
            }
        }
    }

    @Override
    public List<QstOptionPo> selectList(String questionnaireId) {
        QstOptionQuery query = new QstOptionQuery();
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);
        return selectList(query);
    }

    @Override
    public List<QstOptionPo> selectList(String questionnaireId, Long userId) {
        Assert.notNull(userId, "userId");
        QstOptionQuery query = new QstOptionQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        return selectList(query);
    }

    @Override
    public int insertUpdateList(List<QstOptionPo> poList) {
        return qstOptionDao.insertUpdateList(poList);
    }

    @Override
    public int selectCount(String questionnaireId, Long userId) {
        QstOptionQuery query = new QstOptionQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        filterCondition(query);

        return qstOptionDao.selectCount(query);
    }

    @Override
    public int selectCount(String questionnaireId, Long userId, Integer titleSerialNumber) {
        QstOptionQuery query = new QstOptionQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setTitleSerialNumber(titleSerialNumber);
        filterCondition(query);

        return qstOptionDao.selectCount(query);
    }

    @Override
    public int insertUpdate(QstOptionPo qstOptionPo) {
        Assert.notNull(qstOptionPo.getUserId(), "userId");
        return qstOptionDao.insertUpdate(qstOptionPo);
    }

    private void validateUserId(QstOptionQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }

    private void filterCondition(QstOptionQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class QstQuotaOptionRulePo {
    /*自增id*/
    private Long id;

    /*配额id*/
    private Long quotaId;

    /*用户id*/
    private Long userId;

    /*问卷id*/
    private String questionnaireId;

    /*题目序号*/
    private Integer titleSerialNumber;

    /*题目类型*/
    private Integer titleType;

    /*选项序号*/
    private Integer optionSerialNumber;

    /*配额值*/
    private Integer quota;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Integer getTitleSerialNumber() {
        return titleSerialNumber;
    }

    public void setTitleSerialNumber(Integer titleSerialNumber) {
        this.titleSerialNumber = titleSerialNumber;
    }

    public Integer getTitleType() {
        return titleType;
    }

    public void setTitleType(Integer titleType) {
        this.titleType = titleType;
    }

    public Integer getOptionSerialNumber() {
        return optionSerialNumber;
    }

    public void setOptionSerialNumber(Integer optionSerialNumber) {
        this.optionSerialNumber = optionSerialNumber;
    }

    public Integer getQuota() {
        return quota;
    }

    public void setQuota(Integer quota) {
        this.quota = quota;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
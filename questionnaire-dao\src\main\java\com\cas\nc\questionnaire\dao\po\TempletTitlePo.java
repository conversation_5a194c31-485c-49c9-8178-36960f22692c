package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class TempletTitlePo {
    /*自增id*/
    private Long id;

    /*模板id*/
    private String templetId;

    /*序号*/
    private Integer serialNumber;

    /*题目名称*/
    private String name;

    /*类型*/
    private Integer type;

    /*题目显示序号*/
    private Integer orderNumber;

    /*全局序号*/
    private Integer globalOrder;

    /*关联条件(serial_number)*/
    private Integer associateCondition;

    /*题目跳转类型，1：无条件跳转，2：按选项跳转*/
    private Integer jumpType;

    /*无条件跳转的题目序号*/
    private Integer jumpUncondition;

    /*是否必答，1：必答*/
    private Integer canRequire;

    /*排列方式，1：横排，2：竖排*/
    private Integer arrangementMode;

    /*宽*/
    private Integer width;

    /*高*/
    private Integer height;

    /*默认值*/
    private String defaultValue;

    /*是否允许默认，1：允许，2：不允许*/
    private Integer canDefault;

    /*是否限制字数*/
    private Integer canBounds;

    /*最小字数*/
    private Integer minBoundsValue;

    /*最大限制字数*/
    private Integer maxBoundsValue;

    /*校验类型*/
    private Integer validateType;

    /*至少选择项*/
    private Integer chooseMin;

    /*至多选择项*/
    private Integer chooseMax;

    /*行数*/
    private Integer rowCount;

    /*列数*/
    private Integer columnCount;

    /*垂直列数*/
    private Integer verticalCount;

    /*限制属性json*/
    private LimitAttributeJson limitAttributeJson;

    /*比重总值*/
    private Integer proportionTotal;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTempletId() {
        return templetId;
    }

    public void setTempletId(String templetId) {
        this.templetId = templetId;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getGlobalOrder() {
        return globalOrder;
    }

    public void setGlobalOrder(Integer globalOrder) {
        this.globalOrder = globalOrder;
    }

    public Integer getAssociateCondition() {
        return associateCondition;
    }

    public void setAssociateCondition(Integer associateCondition) {
        this.associateCondition = associateCondition;
    }

    public Integer getJumpType() {
        return jumpType;
    }

    public void setJumpType(Integer jumpType) {
        this.jumpType = jumpType;
    }

    public Integer getJumpUncondition() {
        return jumpUncondition;
    }

    public void setJumpUncondition(Integer jumpUncondition) {
        this.jumpUncondition = jumpUncondition;
    }

    public Integer getCanRequire() {
        return canRequire;
    }

    public void setCanRequire(Integer canRequire) {
        this.canRequire = canRequire;
    }

    public Integer getArrangementMode() {
        return arrangementMode;
    }

    public void setArrangementMode(Integer arrangementMode) {
        this.arrangementMode = arrangementMode;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Integer getCanDefault() {
        return canDefault;
    }

    public void setCanDefault(Integer canDefault) {
        this.canDefault = canDefault;
    }

    public Integer getCanBounds() {
        return canBounds;
    }

    public void setCanBounds(Integer canBounds) {
        this.canBounds = canBounds;
    }

    public Integer getMinBoundsValue() {
        return minBoundsValue;
    }

    public void setMinBoundsValue(Integer minBoundsValue) {
        this.minBoundsValue = minBoundsValue;
    }

    public Integer getMaxBoundsValue() {
        return maxBoundsValue;
    }

    public void setMaxBoundsValue(Integer maxBoundsValue) {
        this.maxBoundsValue = maxBoundsValue;
    }

    public Integer getValidateType() {
        return validateType;
    }

    public void setValidateType(Integer validateType) {
        this.validateType = validateType;
    }

    public Integer getChooseMin() {
        return chooseMin;
    }

    public void setChooseMin(Integer chooseMin) {
        this.chooseMin = chooseMin;
    }

    public Integer getChooseMax() {
        return chooseMax;
    }

    public void setChooseMax(Integer chooseMax) {
        this.chooseMax = chooseMax;
    }

    public Integer getRowCount() {
        return rowCount;
    }

    public void setRowCount(Integer rowCount) {
        this.rowCount = rowCount;
    }

    public Integer getColumnCount() {
        return columnCount;
    }

    public void setColumnCount(Integer columnCount) {
        this.columnCount = columnCount;
    }

    public Integer getVerticalCount() {
        return verticalCount;
    }

    public void setVerticalCount(Integer verticalCount) {
        this.verticalCount = verticalCount;
    }

    public LimitAttributeJson getLimitAttributeJson() {
        return limitAttributeJson;
    }

    public void setLimitAttributeJson(LimitAttributeJson limitAttributeJson) {
        this.limitAttributeJson = limitAttributeJson;
    }

    public Integer getProportionTotal() {
        return proportionTotal;
    }

    public void setProportionTotal(Integer proportionTotal) {
        this.proportionTotal = proportionTotal;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.transmit.QstEmailDto;
import com.cas.nc.questionnaire.common.dto.transmit.QstEmailRepDto;
import com.cas.nc.questionnaire.common.dto.transmit.QstEmailReqDto;
import com.cas.nc.questionnaire.dao.po.QstEmailPo;

import java.util.Map;

public interface MailServer {

    /**
     * 邮件发送任务入库
     * @param qstEmailDto
     */
    void save(QstEmailDto qstEmailDto);


    /**
     * 邮件发送联系人 验重
     * @param questionnaireId
     * @param addressees
     * @param userId
     * @return
     */
    Map<String,String> adressesCheck(String questionnaireId, String addressees,Long userId);

    QstEmailRepDto getEmailSendDetail(QstEmailReqDto qstEmailDto);

    QstEmailPo selectMailByEmailId(String emailId);
}

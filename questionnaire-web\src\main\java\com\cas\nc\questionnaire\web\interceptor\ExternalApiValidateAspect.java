package com.cas.nc.questionnaire.web.interceptor;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.SignGenerateUtil;
import com.cas.nc.questionnaire.common.vo.base.ExternalCommonReqVo;
import com.cas.nc.questionnaire.dao.po.BizConfigPo;
import com.cas.nc.questionnaire.service.BizConfigService;
import com.cas.nc.questionnaire.web.interceptor.annotations.ExternalApiValidate;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

import static com.cas.nc.questionnaire.common.constants.Constants.apiValidateTime;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.BIZ_ID_NOT_EXIST;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.SIGN_EXCEPTION;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.UNKNOWN_RETURN_PAGE;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.VALIDATE_PARAM_EXCEPTION;

@Aspect
public class ExternalApiValidateAspect {

    private static Logger logger = LoggerFactory.getLogger(ExternalApiValidateAspect.class);

    @Resource
    private BizConfigService bizConfigService;

    /**
     * 切点
     */
    @Pointcut("@annotation(com.cas.nc.questionnaire.web.interceptor.annotations.ExternalApiValidate)")
    public void cut() {
    }

    /**
     * 切点
     */
    @Pointcut("@annotation(com.cas.nc.questionnaire.web.interceptor.annotations.ResponseEncrypt)")
    public void responseEncrypt() {
    }

    /**
     * 切点
     */
    @Pointcut("@annotation(com.cas.nc.questionnaire.web.interceptor.annotations.ResponseEncryptAndSign)")
    public void responseEncryptAndSign() {
    }
    
    /**
     * 切点
     */
    @Pointcut("@annotation(com.cas.nc.questionnaire.web.interceptor.annotations.ResponseSign)")
    public void responseSign() {
    }

    /**
     * Around advice
     *
     * @param point
     * @param valid
     * @return
     */
    @Around("cut() && @annotation(valid)")
    public Object around(ProceedingJoinPoint point, ExternalApiValidate valid) {
        Object[] args = point.getArgs();
        if (ArrayUtils.isEmpty(args)) {
            return ApiReturnResult.error(VALIDATE_PARAM_EXCEPTION);
        }
        // 1、检查是否存在ApiParamCO参数
        MethodSignature signature = (MethodSignature) point.getSignature();
        Class[] parameterTypes = signature.getParameterTypes();
        int index = ArrayUtils.indexOf(parameterTypes, ExternalCommonReqVo.class);
        if (index == -1) {
            logger.error("ExternalApiValidateAspect.apiValidated_Error/缺少IntranetApiParamCO类型参数");
            return ApiReturnResult.error(VALIDATE_PARAM_EXCEPTION);
        }
        // 2、参数验签
        ExternalCommonReqVo apiParamCO = (ExternalCommonReqVo) args[index];
        if (validate(apiParamCO)) {
            try {
                return point.proceed(args);
            } catch (ServerException e) {
                return ApiReturnResult.error(e.getCode(), e.getMessage());
            } catch (Throwable throwable) {
                logger.error("ExternalApiValidateAspect.around.proceed_Error/param/[{}]", JSONUtil.toJSONString(apiParamCO), throwable);
                return ApiReturnResult.error(UNKNOWN_RETURN_PAGE);
            }
        }
        logger.error("ExternalApiValidateAspect.apiValidated_Error/param/[{}]", JSONUtil.toJSONString(apiParamCO));
        return ApiReturnResult.error(SIGN_EXCEPTION);
    }

    @AfterReturning(pointcut = "responseEncrypt()", returning = "retVal")
    public void afterReturningEncrypt(JoinPoint jp, Object retVal) {
        ApiReturnResult result = (ApiReturnResult) retVal;

        String encrypt = encryptData(jp, result);
        result.setData(encrypt);
    }

    @AfterReturning(pointcut = "responseEncryptAndSign()", returning = "retVal")
    public void afterReturningEncryptAndSign(JoinPoint jp, Object retVal) {
        ApiReturnResult result = (ApiReturnResult) retVal;
        if (!result.getSuccess() || !CodeEnum.SUCCESS.key().equals(result.getCode())) {
            return;
        }

        encryptDataAndSign(jp, result);
    }

    @AfterReturning(pointcut = "responseSign()", returning = "retVal")
    public void afterReturningSign(JoinPoint jp, Object retVal) {
        ApiReturnResult result = (ApiReturnResult) retVal;
        ExternalCommonReqVo apiParamCO = getParam(jp);
        BizConfigPo bizConfigPo = getBizConfigPo(apiParamCO);
        sign(result, bizConfigPo);
    }

    private String encryptData(JoinPoint jp, ApiReturnResult result) {
        ExternalCommonReqVo apiParamCO = getParam(jp);
        BizConfigPo bizConfigPo = getBizConfigPo(apiParamCO);

        return getEncryptData(result.getData().toString(), bizConfigPo.getAesKey());
    }

    private void encryptDataAndSign(JoinPoint jp, ApiReturnResult result) {
        ExternalCommonReqVo apiParamCO = getParam(jp);
        BizConfigPo bizConfigPo = getBizConfigPo(apiParamCO);

        String encryptData = getEncryptData(result.getData().toString(), bizConfigPo.getAesKey());
        result.setData(encryptData);

        sign(result, bizConfigPo);
    }

    private void sign(ApiReturnResult result, BizConfigPo bizConfigPo) {
        String sign = SignGenerateUtil.generateSignByObject(result, bizConfigPo.getMd5Key());
        result.setSign(sign);
    }

    private String getEncryptData(String data, String key) {
        return SmUtil.sm4(key.getBytes()).encryptBase64(JSONUtil.toJSONString(data));
    }

    private BizConfigPo getBizConfigPo(ExternalCommonReqVo apiParamCO) {
        BizConfigPo bizConfigPo = bizConfigService.selectOne(apiParamCO.getBizId());
        Assert.notNull(bizConfigPo, BIZ_ID_NOT_EXIST);
        return bizConfigPo;
    }

    private ExternalCommonReqVo getParam(JoinPoint jp) {
        Object[] args = jp.getArgs();
        if (ArrayUtils.isEmpty(args)) {
            throw new ServerException(VALIDATE_PARAM_EXCEPTION);
        }
        // 1、检查是否存在ApiParamCO参数
        MethodSignature signature = (MethodSignature) jp.getSignature();
        Class[] parameterTypes = signature.getParameterTypes();
        int index = ArrayUtils.indexOf(parameterTypes, ExternalCommonReqVo.class);
        if (index == -1) {
            logger.error("ExternalApiValidateAspect.afterReturningEncrypt/缺少IntranetApiParamCO类型参数");
            throw new ServerException(VALIDATE_PARAM_EXCEPTION);
        }
        return  (ExternalCommonReqVo) args[index];
    }


    /**
     * 参数验签
     *
     * @param apiParamCO
     */
    private boolean validate(ExternalCommonReqVo apiParamCO) {
        try {

            long requestTime = DateUtil.parseDateTime(apiParamCO.getRequestTime()).getTime();
            long nowTime = System.currentTimeMillis();
            long diffValue = nowTime - requestTime;
            boolean flag = diffValue < apiValidateTime * 1000 && diffValue > 0;
            Assert.isTrue(flag, CodeEnum.REQUEST_INVALID);

            BizConfigPo bizConfigPo = bizConfigService.selectOne(apiParamCO.getBizId());
            Assert.notNull(bizConfigPo, BIZ_ID_NOT_EXIST);
            String sign = SignGenerateUtil.generateSignByObject(apiParamCO, bizConfigPo.getMd5Key());
            Assert.isTrue(apiParamCO.getSign().equalsIgnoreCase(sign), SIGN_EXCEPTION);

            SymmetricCrypto sm4 = SmUtil.sm4(bizConfigPo.getAesKey().getBytes());
            String decryptData = sm4.decryptStr(apiParamCO.getData());

            apiParamCO.setDecryptData(decryptData);
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("ExternalApiValidateAspect.validate exception", e);
            return false;
        }

        return true;
    }

}

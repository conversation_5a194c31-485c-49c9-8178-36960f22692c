package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnairePublishReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireRemoveReqDto;

public interface ApiServer {
    Boolean remove(QuestionnaireRemoveReqDto reqDto);

    Boolean publishQst(QuestionnairePublishReqDto reqDto);

    Boolean pauseQst(QuestionnairePublishReqDto reqDto);
}

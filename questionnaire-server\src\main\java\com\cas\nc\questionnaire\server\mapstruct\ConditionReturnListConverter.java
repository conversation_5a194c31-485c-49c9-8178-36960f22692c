package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.setting.SettingConditionReturnListRepDto;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnListRepVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ConditionReturnListConverter {
    ConditionReturnListConverter INSTANCE = Mappers.getMapper(ConditionReturnListConverter.class);

    List<SettingConditionReturnListRepVo> to(List<SettingConditionReturnListRepDto> repDtoList);
}
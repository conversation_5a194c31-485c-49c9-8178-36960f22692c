package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.TempletTitlePo;
import com.cas.nc.questionnaire.dao.query.TempletTitleQuery;

import java.util.List;

public interface TempletTitleService {
    /**
     * 数据插入
     *
     * @param templetTitlePo
     * @return
     */
    int insert(TempletTitlePo templetTitlePo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(TempletTitleQuery query);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<TempletTitlePo> selectList(TempletTitleQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    TempletTitlePo selectOne(TempletTitleQuery query);

    List<TempletTitlePo> selectList(String templetId);
}

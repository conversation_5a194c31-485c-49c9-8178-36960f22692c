package com.cas.nc.questionnaire.common.dto.questionnaire;

import com.cas.nc.questionnaire.common.dto.base.BaseRequestDto;

public class QuestionnaireCreateReqDto extends BaseRequestDto {
    /*标题*/
    private String title;
    /*描述*/
    private String des;
    /*业务id，提前定义*/
    private Long bizId;
    /*业务单号*/
    private String bizNo;
    /*问卷来源，1：问卷平台；2：继续教育平台*/
    private Integer channel;

    private Integer questionnaireType;


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getQuestionnaireType() {
        return questionnaireType;
    }

    public void setQuestionnaireType(Integer questionnaireType) {
        this.questionnaireType = questionnaireType;
    }
}

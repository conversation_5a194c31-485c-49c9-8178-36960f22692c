package com.cas.nc.questionnaire.common.dto.setting;

import com.cas.nc.questionnaire.common.dto.base.BaseRequestDto;

public class SettingFilterRuleAddReqDto extends BaseRequestDto {
    /*名称*/
    private String name;

    /*规则类型，1：题目，2：省份，3：城市，4：答题时间段，5：来源渠道，6：ip地址*/
    private Integer ruleType;

    /*判断类型，1：是，2：非，3：小于，4：等于，5：大于，6：之间，7：包含，8：不包含*/
    private Integer judgeType;

    /*根据ruleType来判断内容以及格式*/
    private String content;

    /*省*/
    private Long province;

    /*筛选类型，1：自动筛选，2：手动筛选*/
    private Integer filterType;

    /*数据来源，1：质量控制-筛选，2：问卷设置-跳转*/
    private Integer dataSource;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getJudgeType() {
        return judgeType;
    }

    public void setJudgeType(Integer judgeType) {
        this.judgeType = judgeType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getProvince() {
        return province;
    }

    public void setProvince(Long province) {
        this.province = province;
    }

    public Integer getFilterType() {
        return filterType;
    }

    public void setFilterType(Integer filterType) {
        this.filterType = filterType;
    }

    public Integer getDataSource() {
        return dataSource;
    }

    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }
}

package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.transmit.QstEmailDto;
import com.cas.nc.questionnaire.common.dto.transmit.QstEmailReqDto;
import com.cas.nc.questionnaire.common.vo.transmit.QstEmailReqVo;
import com.cas.nc.questionnaire.common.vo.transmit.QstEmailVo;
import com.cas.nc.questionnaire.dao.po.QstEmailPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EmailConverter {
    EmailConverter INSTANCE = Mappers.getMapper(EmailConverter.class);

    QstEmailDto to(QstEmailPo qstEmailPo);

    QstEmailPo to(QstEmailDto qstEmailDto);

    QstEmailDto to(QstEmailVo qstEmailVo);

    QstEmailReqDto to(QstEmailReqVo qstEmailReqVo);
}
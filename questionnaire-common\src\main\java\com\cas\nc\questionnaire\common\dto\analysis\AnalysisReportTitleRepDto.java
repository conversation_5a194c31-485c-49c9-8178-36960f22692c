package com.cas.nc.questionnaire.common.dto.analysis;

import java.math.BigDecimal;
import java.util.List;

public class AnalysisReportTitleRepDto {
    /*题目序号*/
    private Integer hao;
    private Integer order;
    private Integer globalOrder;
    /*题目名称*/
    private String name;
    /*题目类型*/
    private String type;
    private Integer typeInt;
    /*有效答题总数*/
    private Integer total;
    /*总值*/
    private BigDecimal totalValue;
    /*平均值*/
    private BigDecimal averageValue;
    /*选项*/
    private List<AnalysisReportOptionRepDto> optionList;

    public Integer getHao() {
        return hao;
    }

    public void setHao(Integer hao) {
        this.hao = hao;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<AnalysisReportOptionRepDto> getOptionList() {
        return optionList;
    }

    public void setOptionList(List<AnalysisReportOptionRepDto> optionList) {
        this.optionList = optionList;
    }

    public Integer getTypeInt() {
        return typeInt;
    }

    public void setTypeInt(Integer typeInt) {
        this.typeInt = typeInt;
    }

    public BigDecimal getAverageValue() {
        return averageValue;
    }

    public void setAverageValue(BigDecimal averageValue) {
        this.averageValue = averageValue;
    }

    public BigDecimal getTotalValue() {
        return totalValue;
    }

    public void setTotalValue(BigDecimal totalValue) {
        this.totalValue = totalValue;
    }

    public Integer getGlobalOrder() {
        return globalOrder;
    }

    public void setGlobalOrder(Integer globalOrder) {
        this.globalOrder = globalOrder;
    }
}

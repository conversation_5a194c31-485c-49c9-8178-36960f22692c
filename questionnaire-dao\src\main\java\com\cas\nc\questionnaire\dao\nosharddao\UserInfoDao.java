package com.cas.nc.questionnaire.dao.nosharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.UserInfoQuery;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface UserInfoDao extends BaseDao<UserInfoPo, UserInfoQuery> {
    List<UserInfoPo> selectRegister4Check(UserInfoQuery query);

    int selectCount(@Param("item") UserInfoQuery query);

    List<UserInfoPo> selectUserInfoListByPage(@Param("item") UserInfoQuery query);

    @Select("select * from user_info where email = #{email} or user_name = #{userName}")
    UserInfoPo selectUserByEmailOrUserName(@Param("email") String email, @Param("userName") String userName);
}

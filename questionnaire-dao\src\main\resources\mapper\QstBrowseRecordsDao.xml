<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstBrowseRecordsDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstBrowseRecordsPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="province" jdbcType="BIGINT" property="province"/>
        <result column="city" jdbcType="BIGINT" property="city"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,questionnaire_id,user_id,province,city,source,device_id,
    ip,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">and questionnaire_id =
                #{item.questionnaireId}
            </if>
            <if test="null != item.userId and '' != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.province and '' != item.province">and province = #{item.province}</if>
            <if test="null != item.city and '' != item.city">and city = #{item.city}</if>
            <if test="null != item.source and '' != item.source">and source = #{item.source}</if>
            <if test="null != item.deviceId and '' != item.deviceId">and device_id = #{item.deviceId}</if>
            <if test="null != item.ip and '' != item.ip">and ip = #{item.ip}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_browse_records
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_browse_records
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_browse_records
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into qst_browse_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">id,</if>
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.province">province,</if>
            <if test="null != item.city">city,</if>
            <if test="null != item.source">source,</if>
            <if test="null != item.deviceId">device_id,</if>
            <if test="null != item.ip">ip,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">#{item.id},</if>
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.province">#{item.province},</if>
            <if test="null != item.city">#{item.city},</if>
            <if test="null != item.source">#{item.source},</if>
            <if test="null != item.deviceId">#{item.deviceId},</if>
            <if test="null != item.ip">#{item.ip},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.id">id = values(id),</if>
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.province">province = values(province),</if>
            <if test="null != item.city">city = values(city),</if>
            <if test="null != item.source">source = values(source),</if>
            <if test="null != item.deviceId">device_id = values(device_id),</if>
            <if test="null != item.ip">ip = values(ip),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update qst_browse_records
        <set>
            <if test="null != item.id">id = #{item.id},</if>
            <if test="null != item.questionnaireId">questionnaire_id = #{item.questionnaireId},</if>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.province">province = #{item.province},</if>
            <if test="null != item.city">city = #{item.city},</if>
            <if test="null != item.source">source = #{item.source},</if>
            <if test="null != item.deviceId">device_id = #{item.deviceId},</if>
            <if test="null != item.ip">ip = #{item.ip},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_browse_records
        <include refid="sql_where"/>
    </delete>

    <select id="selectSourceStatistics" resultType="SourceAnalysisBo">
        select source, count(source) total
        from qst_browse_records
        <where>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">
                and questionnaire_id = #{item.questionnaireId}
            </if>
            <if test="null != item.userId">
                and user_id = #{item.userId}
            </if>
        </where>
        group by source
    </select>

    <select id="selectProvinceStatistics" resultType="ProvinceAnalysisBo">
        select province, count(province) total
        from qst_browse_records
        <where>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">
                and questionnaire_id = #{item.questionnaireId}
            </if>
            <if test="null != item.userId">
                and user_id = #{item.userId}
            </if>
        </where>
        group by province
    </select>

    <select id="selectDayStatistics" resultType="TimeAnalysisBo">
        select DATE_FORMAT(create_time,'%Y-%m-%d') timeMark, count(1) total
        from qst_browse_records
        where create_time >= #{item.beginCreateTime}
        <![CDATA[and create_time < #{item.endCreateTime}]]>
        <if test="null != item.questionnaireId and '' != item.questionnaireId">
            and questionnaire_id = #{item.questionnaireId}
        </if>
        <if test="null != item.userId">
            and user_id = #{item.userId}
        </if>
        group by DATE_FORMAT(create_time,'%Y-%m-%d')
    </select>

    <select id="selectWeekDayStatistics" resultType="TimeAnalysisBo">
        select DATE_FORMAT(create_time,'%Y%u') timeMark, count(1) total
        from qst_browse_records
        where DATE_FORMAT(create_time,'%Y%u') >= #{item.beginCreateTime}
        <![CDATA[and DATE_FORMAT(create_time,'%Y%u') <= #{item.endCreateTime}]]>
        <if test="null != item.questionnaireId and '' != item.questionnaireId">
            and questionnaire_id = #{item.questionnaireId}
        </if>
        <if test="null != item.userId">
            and user_id = #{item.userId}
        </if>
        group by DATE_FORMAT(create_time,'%Y%u')
    </select>

    <select id="selectMonthStatistics" resultType="TimeAnalysisBo">
        select DATE_FORMAT(create_time,'%Y-%m') timeMark, count(1) total
        from qst_browse_records
        where create_time >= #{item.beginCreateTime}
        <![CDATA[and create_time < #{item.endCreateTime}]]>
        <if test="null != item.questionnaireId and '' != item.questionnaireId">
            and questionnaire_id = #{item.questionnaireId}
        </if>
        <if test="null != item.userId">
            and user_id = #{item.userId}
        </if>
        group by DATE_FORMAT(create_time,'%Y-%m')
    </select>
</mapper>
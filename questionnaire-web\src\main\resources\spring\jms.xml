<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd http://www.dangdang.com/schema/ddframe/rdb http://www.dangdang.com/schema/ddframe/rdb/rdb.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">
<bean id="connectionFactory" class="org.apache.activemq.pool.PooledConnectionFactory">
    <property name="connectionFactory">
        <bean class="org.apache.activemq.ActiveMQConnectionFactory">
        <property name="brokerURL"> <value>tcp://172.16.136.163:61617</value> </property>
        </bean>
    </property>
</bean>
<!-- 和发送邮件有关 explicitQosEnabled属性：服务质量的开关,如果打开，则消息的递送模式、优先级和存活时间的设置就没有作用 -->
   <bean id="mailDestination" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="mailQueue" />
    </bean>

    <bean id="mailTemplate" class="org.springframework.jms.core.JmsTemplate">
        <property name="connectionFactory" ref="connectionFactory" />
        <property name="defaultDestination" ref="mailDestination" />
        <!--<property name="explicitQosEnabled" value="true" />-->
    </bean>

    <bean id="cnicMailSender" class="com.cas.nc.questionnaire.service.util.CnicMailSender">
        <property name="mailTemplate" ref="mailTemplate" />
    </bean>
</beans>

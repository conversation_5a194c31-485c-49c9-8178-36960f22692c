package com.cas.nc.questionnaire.server.strategy.answeroptionstrategy;

import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateReqDto;
import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import com.cas.nc.questionnaire.service.QstOptionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.constructBaseAnswerOption;

@Component("answerOptionPaiXuStrategy")
public class AnswerOptionPaiXuStrategy implements AnswerOptionStrategy {
    @Resource
    private QstOptionService qstOptionService;

    @Override
    public List<AnswerOptionPo> construct(AnswerCreateReqDto reqDto, String answerId, Map.Entry<String, Object> dataEntry, TitleTypeEnum titleTypeEnum) {
        List<AnswerOptionPo> resultList = new ArrayList<>();
        String k = dataEntry.getKey();
        Object v = dataEntry.getValue();
        List<Object> duoXuanOptionList = (List<Object>) v;

        int countOption = qstOptionService.selectCount(reqDto.getId(), null, Integer.valueOf(k));
        AtomicInteger count = new AtomicInteger(countOption);

        duoXuanOptionList.forEach(p -> {
            AnswerOptionPo duoXuanAnswerOption = constructBaseAnswerOption(reqDto, answerId, k, titleTypeEnum);
            duoXuanAnswerOption.setSerialNumber((Integer) p);
            duoXuanAnswerOption.setScore(Long.valueOf(count.getAndDecrement()));

            resultList.add(duoXuanAnswerOption);
        });

        return resultList;
    }
}

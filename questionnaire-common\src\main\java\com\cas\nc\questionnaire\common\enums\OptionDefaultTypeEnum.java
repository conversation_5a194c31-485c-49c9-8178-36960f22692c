package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.DATA_EXCEPTION;


public enum OptionDefaultTypeEnum {
    DEFAULT(1, "默认"),
    NONDEFAULT(2, "非默认"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    OptionDefaultTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static OptionDefaultTypeEnum toEnum(int key) {
        for (OptionDefaultTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(DATA_EXCEPTION);
    }

    public static Boolean convert2Boolean(int key) {
        if (toEnum(key).equals(DEFAULT)) {
            return true;
        }
        return false;
    }

    public static OptionDefaultTypeEnum convert2Enum(Boolean flag) {
        if (flag != null && flag) {
            return DEFAULT;
        }
        return NONDEFAULT;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

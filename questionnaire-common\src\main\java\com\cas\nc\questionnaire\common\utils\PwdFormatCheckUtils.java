package com.cas.nc.questionnaire.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class PwdFormatCheckUtils {

    /**
     * 修改密码检验 密码至少8位，且必须包含数字、字母、下划线
     * @param pwd
     * @return
     */
    public static boolean isPwdLegal(String pwd) {
        if (StringUtil.isBlank(pwd)) {
            return false;
        }
        String regEx1 = "^(?=.*[A-Za-z])(?=.*\\d)(?=.*_)[\\w\\W]{8,}$";
        Pattern p;
        Matcher m;
        p = Pattern.compile(regEx1);
        m = p.matcher(pwd);
        return m.matches();
    }

    public static void main(String[] args) {
        System.out.println(isPwdLegal("123456"));
    }
}

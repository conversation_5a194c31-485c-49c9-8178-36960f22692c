<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.UserInfoDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.UserInfoPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="user_source" jdbcType="INTEGER" property="userSource"/>
        <result column="out_user_id" jdbcType="VARCHAR" property="outUserId"/>
        <result column="out_user_name" jdbcType="VARCHAR" property="outUserName"/>
        <result column="pwd" jdbcType="VARCHAR" property="pwd"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="id_no" jdbcType="VARCHAR" property="idNo"/>
        <result column="pwd_error_count" jdbcType="INTEGER" property="pwdErrorCount"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="vip_end_time" jdbcType="TIMESTAMP" property="vipEndTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,user_name,user_source,out_user_id,out_user_name,pwd,phone,
    status,name,id_no,pwd_error_count,email,type,
    vip_end_time,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.userName and '' != item.userName">and user_name = #{item.userName}</if>
            <if test="null != item.userSource and '' != item.userSource">and user_source = #{item.userSource}</if>
            <if test="null != item.outUserId and '' != item.outUserId">and out_user_id = #{item.outUserId}</if>
            <if test="null != item.outUserName and '' != item.outUserName">and out_user_name = #{item.outUserName}</if>
            <if test="null != item.pwd and '' != item.pwd">and pwd = #{item.pwd}</if>
            <if test="null != item.phone and '' != item.phone">and phone = #{item.phone}</if>
            <if test="null != item.status and '' != item.status">and status = #{item.status}</if>
<!--            <if test="null != item.name and '' != item.name">and name = #{item.name}</if>-->
            <if test="null != item.name and '' != item.name">and name like concat('%', #{item.name}, '%') </if>
            <if test="null != item.idNo and '' != item.idNo">and id_no = #{item.idNo}</if>
            <if test="null != item.pwdErrorCount and '' != item.pwdErrorCount">and pwd_error_count =
                #{item.pwdErrorCount}
            </if>
            <if test="null != item.email and '' != item.email">and email = #{item.email}</if>
            <if test="null != item.type and '' != item.type">and type = #{item.type}</if>
            <if test="null != item.vipEndTime and '' != item.vipEndTime">and vip_end_time = #{item.vipEndTime}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>

            <if test="null != item.orEmail">or email = #{item.orEmail}</if>
            <if test="null != item.outUserIdList and item.outUserIdList.size > 0">
                and out_user_id in
                <foreach collection="item.outUserIdList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
            <if test="null != item.ids and item.ids.size > 0">
                and id in
                <foreach collection="item.ids" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from user_info
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from user_info
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from user_info
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.userName">user_name,</if>
            <if test="null != item.userSource">user_source,</if>
            <if test="null != item.outUserId">out_user_id,</if>
            <if test="null != item.outUserName">out_user_name,</if>
            <if test="null != item.pwd">pwd,</if>
            <if test="null != item.phone">phone,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.name">name,</if>
            <if test="null != item.idNo">id_no,</if>
            <if test="null != item.pwdErrorCount">pwd_error_count,</if>
            <if test="null != item.email">email,</if>
            <if test="null != item.type">type,</if>
            <if test="null != item.vipEndTime">vip_end_time,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.userName">#{item.userName},</if>
            <if test="null != item.userSource">#{item.userSource},</if>
            <if test="null != item.outUserId">#{item.outUserId},</if>
            <if test="null != item.outUserName">#{item.outUserName},</if>
            <if test="null != item.pwd">#{item.pwd},</if>
            <if test="null != item.phone">#{item.phone},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.name">#{item.name},</if>
            <if test="null != item.idNo">#{item.idNo},</if>
            <if test="null != item.pwdErrorCount">#{item.pwdErrorCount},</if>
            <if test="null != item.email">#{item.email},</if>
            <if test="null != item.type">#{item.type},</if>
            <if test="null != item.vipEndTime">#{item.vipEndTime},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.userName">user_name = values(user_name),</if>
            <if test="null != item.userSource">user_source = values(user_source),</if>
            <if test="null != item.outUserId">out_user_id = values(out_user_id),</if>
            <if test="null != item.outUserName">out_user_name = values(out_user_name),</if>
            <if test="null != item.pwd">pwd = values(pwd),</if>
            <if test="null != item.phone">phone = values(phone),</if>
            <if test="null != item.status">status = values(status),</if>
            <if test="null != item.name">name = values(name),</if>
            <if test="null != item.idNo">id_no = values(id_no),</if>
            <if test="null != item.pwdErrorCount">pwd_error_count = values(pwd_error_count),</if>
            <if test="null != item.email">email = values(email),</if>
            <if test="null != item.type">type = values(type),</if>
            <if test="null != item.vipEndTime">vip_end_time = values(vip_end_time),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update user_info
        <set>
            <if test="null != item.userName">user_name = #{item.userName},</if>
            <if test="null != item.userSource">user_source = #{item.userSource},</if>
            <if test="null != item.outUserId">out_user_id = #{item.outUserId},</if>
            <if test="null != item.outUserName">out_user_name = #{item.outUserName},</if>
            <if test="null != item.pwd">pwd = #{item.pwd},</if>
            <if test="null != item.phone">phone = #{item.phone},</if>
            <if test="null != item.status">status = #{item.status},</if>
            <if test="null != item.name">name = #{item.name},</if>
            <if test="null != item.idNo">id_no = #{item.idNo},</if>
            <if test="null != item.pwdErrorCount">pwd_error_count = #{item.pwdErrorCount},</if>
            <if test="null != item.email">email = #{item.email},</if>
            <if test="null != item.type">type = #{item.type},</if>
            <if test="null != item.vipEndTime">vip_end_time = #{item.vipEndTime},</if>
        </set>
        where id = #{item.id}
        <if test="null != item.oldPwd">and pwd = #{item.oldPwd}</if>
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from user_info
        <include refid="sql_where"/>
    </delete>

    <select id="selectCount" resultType="int">
        select count(1)
        from user_info
        <where>
            <if test="null != item.name and '' != item.name">
                and name like concat('%', #{item.name}, '%')
            </if>
            <if test="null != item.userName and '' != item.userName">
                and user_name like concat('%', #{item.userName}, '%')
            </if>
            <if test="null != item.type and '' != item.type">
                and type = #{item.type}
            </if>
            <if test="null != item.status and '' != item.status">
                and status = #{item.status}
            </if>
        </where>
    </select>

    <select id="selectUserInfoListByPage" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from user_info
        <where>
            <if test="null != item.name and '' != item.name">
                and name like concat('%', #{item.name}, '%')
            </if>
            <if test="null != item.userName and '' != item.userName">
                and user_name like concat('%', #{item.userName}, '%')
            </if>
            <if test="null != item.type and '' != item.type">
                and type = #{item.type}
            </if>
            <if test="null != item.status and '' != item.status">
                and status = #{item.status}
            </if>
            <if test="null != item.statusList and item.statusList.size > 0">
                and status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
        order by update_time desc
        <choose>
            <when test="null != item.startIndex and null != item.pageSize">
                limit #{item.startIndex}, #{item.pageSize}
            </when>
            <otherwise>
                limit 1
            </otherwise>
        </choose>
    </select>
</mapper>
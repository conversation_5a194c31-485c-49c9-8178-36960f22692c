package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.questionnaire.RegularQuestionReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.RegularQuestionRepDto;
import com.cas.nc.questionnaire.common.vo.questionnaire.RegularQuestionReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.RegularQuestionRepVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface RegularQuestionConverter {
    RegularQuestionConverter INSTANCE = Mappers.getMapper(RegularQuestionConverter.class);

    RegularQuestionReqDto to(RegularQuestionReqVo vo);
    
    RegularQuestionRepVo to(RegularQuestionRepDto dto);
    
    RegularQuestionRepVo.RegularQuestionItemVo to(RegularQuestionRepDto.RegularQuestionItemDto dto);
    
    List<RegularQuestionRepVo.RegularQuestionItemVo> toItemVoList(List<RegularQuestionRepDto.RegularQuestionItemDto> dtoList);
} 
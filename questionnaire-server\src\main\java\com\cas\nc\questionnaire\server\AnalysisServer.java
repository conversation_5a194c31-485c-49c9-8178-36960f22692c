package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.analysis.*;

import java.util.List;

public interface AnalysisServer {

    /**
     * 答卷分析
     *
     * @param reqDto
     * @return
     */
    AnalysisReportRepDto report(AnalysisReportReqDto reqDto);

    AnalysisReportCrossAnalysisRepDto crossAnalysis(AnalysisReportCrossAnalysisReqDto reqDto);

    AnalysisListFillBlankRepDto listFillBlank(AnalysisListFillBlankReqDto reqDto);

    List<AnalysisReportTitleRepDto> getAnalysisTitleList(AnalysisReportReqDto reqDto);

    SourceAnalysisRepDto sourceAnalysis(SourceAnalysisReqDto reqDto);

    ProvinceAnalysisRepDto provinceAnalysis(ProvinceAnalysisReqDto reqDto);

    TimeAnalysisRepDto timeAnalysis(TimeAnalysisReqDto reqDto);

    ListCheckedRepDto listChecked(ListCheckedReqDto reqDto);

    AnalysisReportRepDto customReport(CustomReportReqDto reqDto);
}

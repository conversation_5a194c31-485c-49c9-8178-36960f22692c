package com.cas.nc.questionnaire.common.to.questionnairetitle;

import com.cas.nc.questionnaire.common.to.questionnaireoption.DuoXiangDanHangTianKongOptionTo;

import java.util.List;

public class DuoXiangDanHangTianKongTitleTo extends BaseTianKongTitleTo {
    /*行标题*/
    private String rowTitle;
    /*行描述*/
    private String rowDescription;

    private List<DuoXiangDanHangTianKongOptionTo> options;

    public String getRowTitle() {
        return rowTitle;
    }

    public void setRowTitle(String rowTitle) {
        this.rowTitle = rowTitle;
    }

    public String getRowDescription() {
        return rowDescription;
    }

    public void setRowDescription(String rowDescription) {
        this.rowDescription = rowDescription;
    }

    public List<DuoXiangDanHangTianKongOptionTo> getOptions() {
        return options;
    }

    public void setOptions(List<DuoXiangDanHangTianKongOptionTo> options) {
        this.options = options;
    }
}

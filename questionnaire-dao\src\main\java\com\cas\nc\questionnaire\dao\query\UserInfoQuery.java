package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.UserInfoPo;

import java.util.List;


public class UserInfoQuery extends UserInfoPo {
    private String tableColumns;
    private String oldPwd;
    private String orEmail;
    private List<String> outUserIdList;
    /*开始索引*/
    private int startIndex;
    /*页面大小*/
    private Integer pageSize;
    private List<Integer> statusList;
    private List<Long> ids;

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public String getOldPwd() {
        return oldPwd;
    }

    public void setOldPwd(String oldPwd) {
        this.oldPwd = oldPwd;
    }

    public String getOrEmail() {
        return orEmail;
    }

    public void setOrEmail(String orEmail) {
        this.orEmail = orEmail;
    }

    public List<String> getOutUserIdList() {
        return outUserIdList;
    }

    public void setOutUserIdList(List<String> outUserIdList) {
        this.outUserIdList = outUserIdList;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
}

package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;


public enum UserTypeEnum {
    ORDINARY_USERS(1, "普通用户"),
    VIP(2, "VIP会员"),
    ADMIN(3, "管理员"),
    SUPER_ADMIN(4, "超级管理员"),
//    SUPERIOR_ADMIN(5, "高级管理员"),
//    REGISTERED_USERS(6, "注册用户"),
    ELSE(99, "其他");

    private final Integer key;
    private final String value;


    UserTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static UserTypeEnum toEnum(int key) {
        for (UserTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.USER_TYPE_EXCEPTION);
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

    public static boolean isAdmin(int key) {
        return ADMIN.key.intValue() == key;
    }

    public static boolean isSuperAdmin(int key) {
        return SUPER_ADMIN.key.intValue() == key;
    }

    public static boolean isVip(int key) {
        return VIP.key.intValue() == key;
    }

//    public static boolean isRegistered(int key) {
//        return REGISTERED_USERS.key.intValue() == key;
//    }

    public static boolean isSuperior(int key) {
        return SUPER_ADMIN.key.intValue() == key;
    }

}

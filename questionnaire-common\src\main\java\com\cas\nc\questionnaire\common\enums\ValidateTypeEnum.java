package com.cas.nc.questionnaire.common.enums;


public enum ValidateTypeEnum {
    CHINESE(1, "中文"),
    ENGLISH(2, "英文"),
    INTEGER(3, "整数"),
    DECIMAL(4, "小数"),
    IDCARD(5, "身份证"),
    PHONE(6, "手机"),
    TELEPHONE(7, "固话"),
    EMAIL(8, "邮箱"),
    URL(9, "网址"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    ValidateTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

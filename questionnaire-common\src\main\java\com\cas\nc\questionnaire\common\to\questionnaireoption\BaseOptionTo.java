package com.cas.nc.questionnaire.common.to.questionnaireoption;

import java.util.Objects;

public class BaseOptionTo {
    /*选项排序*/
    private Integer order;
    /*选项序号*/
    private Integer num;
    /*选项内容*/
    private String text;
    /*说明*/
    private String description;

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseOptionTo that = (BaseOptionTo) o;
        return Objects.equals(order, that.order) &&
                Objects.equals(num, that.num) &&
                Objects.equals(text, that.text) &&
                Objects.equals(description, that.description);
    }

    @Override
    public int hashCode() {
        return Objects.hash(order, num, text, description);
    }
}

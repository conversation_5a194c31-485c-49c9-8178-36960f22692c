package com.cas.nc.questionnaire.common.utils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

public class Constants {

    //user相关
    /*用户状态1：有效*/
    public static final  int USER_STATUS_FORMAL = 1;
    /*用户状态2：冻结*/
    public static final int  USER_STATUS_FROZEN = 2;

    public static final String TITLE_SPLIT_FLAG = "\n";

    public static final int IMAGE_HEIGHT = 40;

    public static final int IMAGE_WIDTH = 120;

    public static final int IMAGE_CODE_COUNT = 4;

    public static final int IMAGE_LINE_COUNT = 120;

    public static final String ANSWER_TITLE_SPLIT = "-";

    public static final String SPLIT_FLAG = "#@#";

    public static final String CITY_CN_NAME = "市";

    public static final String PROVINCE_CN_NAME = "省";

    public static final String IMAGE_ID = "imageId";

    public static final String VALID_EMAIL_HTML = "<html><head></head><body><h1>Hi %s您好,激活请点击以下链接</h1><h3><a href='%s'>%s</href></h3></body></html>";

    public static final String VALID_EMAIL_TITLE = "问卷注册验证";

    public static final String FORGET_PWD_EMAIL_HTML = "<html><head></head><body><h1>Hi 尊敬的用户您好,重置密码请点击以下链接</h1><h3><a href='%s'>%s</href></h3><br>为保证您帐号的安全，该链接有效期为30分钟，并且点击一次后将失效！</body></html>";

    public static final String FORGET_PWD__EMAIL_TITLE = "忘记密码验证";

    public static final int ZERO = 0;

    public static final int ONE = 1;

    public static final int TWO = 2;

    public static final int THREE = 3;

    public static final int FOUR = 4;

    public static final int FIVE = 5;

    public static final String HINT = "答卷已提交，谢谢！";

    public static final String INVALID_ANSWER_HINT = "对不起，您的答卷不符合此次问卷的统计规则，谢谢您的参与！";

    public static final String QST_NAME = "$问卷名称$";

    public static final String QST_URL = "$问卷地址$";

    public static final List<String> SPECIAL_CITY_LIST = Arrays.asList("北京", "上海", "重庆", "天津", "深圳", "台湾", "香港特别行政区");

    public static final String SLASH = "/";

    public static final String PSK = "a93cfa33a6312a34f22e8b85d76eb5d7";

    public static final String PRODUCT_DESC = "会员充值";

    public static final String FILL_BLANK_DOWNLOAD_INDEX = "填空题的数据请前往详细数据页下载";

    public static final String EQUAL_SIGN = "=";

    public static final String UNKNOWN_REGION = "未知";

    public static final String CAESURA_SIGN = "、";

    public static final int TITLE_LIMIT = 2000;

    public static final String PERCENTAGE_MARK = "%";

    public static final String LEFT_CHINESE_BRACKET = "【";

    public static final String RIGHT_CHINESE_BRACKET = "】";

    public static final String TITLE_FLAG = "【标题】";

    /*单位秒*/
    public static final int REQUEST_TIME_VALID_TIME = 30;

    public static final String REDIS_CODE_IMAGE_KEY = "code_image_key_";

    public static final String RESET_PWD_VALID_URL_KEY = "reset_pwd_valid_url_key_";

    public static final String PRODUCT_ENV_MARK = "product";

    public static final String SEPARATOR = " ║ ";

    public static final String RIGHT_ARROW = " » ";

    public static final String DAN_XUAN_REMARK= "○";

    public static final String DUO_XUAN_REMARK= "□";

    public static final String TIAN_KONG_LINE = "_________________________________";

    public static final String PAI_XU_REMARK = "[ ]";

    public static final String PAI_XU_TITLE_DESC = "[请在括号中填写数字]";

    public static final String P1 = "<p>";

    public static final String P2 = "</p>";

    public static final int ANSWER_LIMIT = 500;

    public static final String ANSWER_LIMIT_TIP = "问卷仅可被填写%d次，如需更高权限，请联系技术支持";
}

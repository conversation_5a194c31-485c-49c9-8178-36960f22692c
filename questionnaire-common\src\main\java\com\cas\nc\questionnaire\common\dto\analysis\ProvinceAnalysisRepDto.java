package com.cas.nc.questionnaire.common.dto.analysis;

import java.math.BigDecimal;
import java.util.List;

public class ProvinceAnalysisRepDto {
    private List<ProvinceRepDto> provinceList;
    private Integer answerTotal;
    private Integer browseRecordsTotal;
    private BigDecimal percentage;

    public List<ProvinceRepDto> getProvinceList() {
        return provinceList;
    }

    public void setProvinceList(List<ProvinceRepDto> provinceList) {
        this.provinceList = provinceList;
    }

    public Integer getAnswerTotal() {
        return answerTotal;
    }

    public void setAnswerTotal(Integer answerTotal) {
        this.answerTotal = answerTotal;
    }

    public Integer getBrowseRecordsTotal() {
        return browseRecordsTotal;
    }

    public void setBrowseRecordsTotal(Integer browseRecordsTotal) {
        this.browseRecordsTotal = browseRecordsTotal;
    }

    public BigDecimal getPercentage() {
        return percentage;
    }

    public void setPercentage(BigDecimal percentage) {
        this.percentage = percentage;
    }
}

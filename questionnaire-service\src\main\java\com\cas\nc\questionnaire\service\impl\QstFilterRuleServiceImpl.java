package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.QstFilterRulePo;
import com.cas.nc.questionnaire.dao.query.QstFilterRuleQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstFilterRuleDao;
import com.cas.nc.questionnaire.service.QstFilterRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class QstFilterRuleServiceImpl implements QstFilterRuleService {
    private static Logger logger = LoggerFactory.getLogger(QstFilterRuleServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstFilterRuleDao qstFilterRuleDao;

    @Override
    public int insert(QstFilterRulePo qstFilterRulePo) {
        validateUserId(qstFilterRulePo.getUserId());
        if (StringUtil.isBlank(qstFilterRulePo.getFilterRuleId())) {
            String id = SequenceUtil.getInstance().generateFilterRuleId(qstFilterRulePo.getUserId().toString());
            qstFilterRulePo.setFilterRuleId(id);
        }
        return qstFilterRuleDao.insert(qstFilterRulePo);
    }

    @Override
    public int delete(QstFilterRuleQuery query) {
        validateUserId(query);
        return qstFilterRuleDao.delete(query);
    }

    @Override
    public int delete(String filterRuleId, Long userId) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setFilterRuleId(filterRuleId);
        query.setUserId(userId);
        return delete(query);
    }

    @Override
    public List<QstFilterRulePo> selectList(QstFilterRuleQuery query) {
        validateUserId(query);
        return qstFilterRuleDao.selectList(query);
    }

    @Override
    public List<QstFilterRulePo> selectList(String questionnaireId, int dataSource) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);
        query.setDataSource(dataSource);
        return selectList(query);
    }

    @Override
    public List<QstFilterRulePo> selectList(String questionnaireId, String foreignId, int dataSource) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setForeignId(foreignId);
        query.setDataSource(dataSource);
        filterCondition(query);
        return selectList(query);
    }

    @Override
    public QstFilterRulePo selectOne(QstFilterRuleQuery query) {
        validateUserId(query);
        return qstFilterRuleDao.selectOne(query);
    }

    @Override
    public QstFilterRulePo selectOne(String filterRuleId) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setFilterRuleId(filterRuleId);
        filterCondition(query);
        return selectOne(query);
    }

    @Override
    public QstFilterRulePo selectOne(String filterRuleId, Long userId) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setFilterRuleId(filterRuleId);
        query.setUserId(userId);
        return selectOne(query);
    }

    @Override
    public int update(QstFilterRuleQuery query) {
        validateUserId(query);
        return qstFilterRuleDao.update(query);
    }

    private void validateUserId(QstFilterRuleQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }

    private void validateUserId(Long userId) {
        Assert.notNull(userId, "userId");
    }

    private void filterCondition(QstFilterRuleQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

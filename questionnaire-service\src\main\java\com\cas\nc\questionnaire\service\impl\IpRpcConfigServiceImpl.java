package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.YnEnum;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.dao.nosharddao.IpRpcConfigDao;
import com.cas.nc.questionnaire.dao.po.IpRpcConfigPo;
import com.cas.nc.questionnaire.dao.query.IpRpcConfigQuery;
import com.cas.nc.questionnaire.service.IpRpcConfigService;
import com.cas.nc.questionnaire.service.util.LocalCacheUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Constants.ZERO;

@Service("ipRpcConfigService")
public class IpRpcConfigServiceImpl implements IpRpcConfigService {
    /*下次刷新的时间*/
    private static Date NEXT_UPDATE_TIME = new Date();
    /*缓存刷新时间频率，单位：小时*/
    private static final int UPDATE_FREQUENCY = 1;

    @Resource
    private IpRpcConfigDao ipRpcConfigDao;


    @Override
    public List<IpRpcConfigPo> selectEffective() {
        if (DateUtil.compareDate(NEXT_UPDATE_TIME) == ONE || LocalCacheUtil.IP_RPC_CONFIG_LIST.size() == ZERO) {
            loadLocalCache();
            NEXT_UPDATE_TIME = DateUtil.add(new Date(), ZERO, UPDATE_FREQUENCY, ZERO, ZERO);
        }
        return LocalCacheUtil.IP_RPC_CONFIG_LIST;
    }

    private void loadLocalCache() {
        IpRpcConfigQuery query = new IpRpcConfigQuery();
        query.setYn(YnEnum.Y.key());

        List<IpRpcConfigPo> list = ipRpcConfigDao.selectList(query);
        List<IpRpcConfigPo> result = SafeUtil.of(list).stream()
                .sorted(Comparator.comparing(IpRpcConfigPo::getPriority).reversed())
                .collect(Collectors.toList());

        LocalCacheUtil.IP_RPC_CONFIG_LIST.clear();
        LocalCacheUtil.IP_RPC_CONFIG_LIST.addAll(result);
    }
}

package com.cas.nc.questionnaire.common.dto.analysis;

import com.cas.nc.questionnaire.common.dto.base.BaseRequestDto;

public class CustomReportReqDto extends BaseRequestDto {
    /*规则类型，1：题目，2：省份，3：城市，4：答题时间段，5：来源渠道，6：ip地址*/
    private Integer ruleType;

    /*判断类型，1：是，2：非，3：小于，4：等于，5：大于，6：之间，7：包含，8：不包含*/
    private Integer judgeType;

    /*根据ruleType来判断内容以及格式*/
    private String content;

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getJudgeType() {
        return judgeType;
    }

    public void setJudgeType(Integer judgeType) {
        this.judgeType = judgeType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}

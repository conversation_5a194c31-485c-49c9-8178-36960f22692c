package com.cas.nc.questionnaire.common.dto.WinXin;

import java.util.List;

/**
 * 微信用户信息
 */
public class WeixinUserInfo {
    private String openid;        // 用户的唯一标识
    private String nickname;      // 用户昵称
    private Integer sex;          // 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知
    private String province;      // 用户个人资料填写的省份
    private String city;          // 普通用户个人资料填写的城市
    private String country;       // 国家，如中国为CN
    private String headimgurl;    // 用户头像URL
    private List<String> privilege; // 用户特权信息，json数组
    private String unionid;       // 只有在用户将服务号绑定到微信开放平台账号后，才会出现该字段
    
    public String getOpenid() {
        return openid;
    }
    
    public void setOpenid(String openid) {
        this.openid = openid;
    }
    
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    
    public Integer getSex() {
        return sex;
    }
    
    public void setSex(Integer sex) {
        this.sex = sex;
    }
    
    public String getProvince() {
        return province;
    }
    
    public void setProvince(String province) {
        this.province = province;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public String getHeadimgurl() {
        return headimgurl;
    }
    
    public void setHeadimgurl(String headimgurl) {
        this.headimgurl = headimgurl;
    }
    
    public List<String> getPrivilege() {
        return privilege;
    }
    
    public void setPrivilege(List<String> privilege) {
        this.privilege = privilege;
    }
    
    public String getUnionid() {
        return unionid;
    }
    
    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }
} 
package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.AreaPo;
import com.cas.nc.questionnaire.dao.query.AreaQuery;

import java.util.List;

public interface AreaService {
    /**
     * 查询所有数据
     *
     * @return
     */
    List<AreaPo> selectAll();

    /**
     * 查询所有有效数据
     *
     * @return
     */
    List<AreaPo> selectAllEffective();

    /**
     * 插入数据
     *
     * @param areaPo
     * @return
     */
    int insert(AreaPo areaPo);

    /**
     * 依据传入条件查询数据
     *
     * @param query
     * @return
     */
    List<AreaPo> selectList(AreaQuery query);

    /**
     * 查询单条数据
     *
     * @param query
     * @return
     */
    AreaPo selectOne(AreaQuery query);

    /**
     * 根据名称查省份信息
     *
     * @param name
     * @return
     */
    AreaPo queryProvince(String name);

    /**
     * 根据名称查city信息
     * name(组合) = name + provinceId
     * @param name
     * @return
     */
    AreaPo queryCity(String name);

    /**
     * 查询所有的省
     *
     * @return
     */
    List<AreaPo> selectAllProvince(String columns);

    /**
     * 根据parentId查询
     *
     * @param parentId
     * @return
     */
    List<AreaPo> selectList(Long parentId);

    /**
     * 根据级别查询
     *
     * @param rank
     * @return
     */
    List<AreaPo> selectList(Integer rank);

    /**
     * 根据级别查询
     *
     * @param rank
     * @return
     */
    List<AreaPo> selectList(Integer rank, String columns);

    /**
     * 根据idList查询
     *
     * @param idList
     * @return
     */
    List<AreaPo> selectProvinceList(List<Long> idList);

    AreaPo selectProvince(Long id);

    /**
     * 根据idList查询
     *
     * @param idList
     * @return
     */
    List<AreaPo> selectCityList(List<Long> idList);

    AreaPo selectCity(Long id);

    String getAddress(Long provinceId, Long cityId);
}

package com.cas.nc.questionnaire.common.vo.analysis;

import java.util.List;

public class AnalysisReportCrossAnalysisRepVo {
    private List<CrossAnalysisTitleRepVo> titleList;

    private Boolean independentShowFlag;

    public List<CrossAnalysisTitleRepVo> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<CrossAnalysisTitleRepVo> titleList) {
        this.titleList = titleList;
    }

    public Boolean getIndependentShowFlag() {
        return independentShowFlag;
    }

    public void setIndependentShowFlag(Boolean independentShowFlag) {
        this.independentShowFlag = independentShowFlag;
    }
}

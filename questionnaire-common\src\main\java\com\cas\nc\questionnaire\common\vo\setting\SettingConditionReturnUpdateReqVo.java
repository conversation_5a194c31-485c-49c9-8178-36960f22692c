package com.cas.nc.questionnaire.common.vo.setting;

import com.cas.nc.questionnaire.common.vo.base.BaseRequestVo;

import java.util.List;

public class SettingConditionReturnUpdateReqVo extends BaseRequestVo {
    /*设置条件id*/
    private String limitConditionId;

    /*条件类型，1：无条件，2：有条件*/
    private Integer conditionType;

    /*跳转url*/
    private String returnUrl;

    /*跳转提示*/
    private String returnHint;

    /*答题结束提示*/
    private String endHint;

    private List<SettingConditionReturnFilterRuleVo> filterRuleInfoList;

    private SettingConditionReturnEmailVo emailInfo;

    private SettingConditionReturnSmsVo smsInfo;

    public String getLimitConditionId() {
        return limitConditionId;
    }

    public void setLimitConditionId(String limitConditionId) {
        this.limitConditionId = limitConditionId;
    }

    public Integer getConditionType() {
        return conditionType;
    }

    public void setConditionType(Integer conditionType) {
        this.conditionType = conditionType;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getReturnHint() {
        return returnHint;
    }

    public void setReturnHint(String returnHint) {
        this.returnHint = returnHint;
    }

    public String getEndHint() {
        return endHint;
    }

    public void setEndHint(String endHint) {
        this.endHint = endHint;
    }

    public List<SettingConditionReturnFilterRuleVo> getFilterRuleInfoList() {
        return filterRuleInfoList;
    }

    public void setFilterRuleInfoList(List<SettingConditionReturnFilterRuleVo> filterRuleInfoList) {
        this.filterRuleInfoList = filterRuleInfoList;
    }

    public SettingConditionReturnEmailVo getEmailInfo() {
        return emailInfo;
    }

    public void setEmailInfo(SettingConditionReturnEmailVo emailInfo) {
        this.emailInfo = emailInfo;
    }

    public SettingConditionReturnSmsVo getSmsInfo() {
        return smsInfo;
    }

    public void setSmsInfo(SettingConditionReturnSmsVo smsInfo) {
        this.smsInfo = smsInfo;
    }
}

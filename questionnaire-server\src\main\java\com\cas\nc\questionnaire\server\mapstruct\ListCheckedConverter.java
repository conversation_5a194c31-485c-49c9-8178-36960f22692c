package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.ListCheckedReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.ListCheckedReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ListCheckedConverter {
    ListCheckedConverter INSTANCE = Mappers.getMapper(ListCheckedConverter.class);

    ListCheckedReqDto to(ListCheckedReqVo vo);
}
package com.cas.nc.questionnaire.rpc.wechat;

import com.cas.nc.questionnaire.common.dto.WinXin.WeixinOAuthResult;
import com.cas.nc.questionnaire.common.dto.WinXin.WeixinUserInfo;
import com.cas.nc.questionnaire.rpc.wechat.entity.wechat.UnifiedOrderParam;
import com.cas.nc.questionnaire.rpc.wechat.entity.wechat.UnifiedOrderReturnResult;

import java.util.Map;

public interface WeChatRpc {
    /**
     * 微信统一下单接口
     *
     * @param param
     * @return
     */
    UnifiedOrderReturnResult unifiedOrder(UnifiedOrderParam param);

    /**
     * 微信异步通知接口
     *
     * @param paramXml
     * @return
     */
    Map<String, String> wechatNotify(String paramXml);

    Map<String, String> orderQuery(String orderNo);
    
    /**
     * 微信网页授权获取access_token
     *
     * @param code 微信授权返回的code
     * @return 授权结果
     */
    WeixinOAuthResult getOAuthAccessToken(String code);
    
    /**
     * 获取微信用户信息
     *
     * @param accessToken 网页授权接口调用凭证
     * @param openid 用户的唯一标识
     * @return 用户信息
     */
    WeixinUserInfo getUserInfo(String accessToken, String openid);
}

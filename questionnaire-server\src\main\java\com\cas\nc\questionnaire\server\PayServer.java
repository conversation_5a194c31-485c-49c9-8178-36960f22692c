package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.pay.*;

import java.util.List;
import java.util.Map;


public interface PayServer {
    void weChatNotify(String paramXml, Map<String, String> paramMap);

    List<ListProductRepDto> listProduct(Long userId) throws Exception;

    GetPayUrlRepDto getPayUrl(GetPayUrlReqDto reqDto);

    GetPayStatusRepDto getPayStatus(GetPayStatusReqDto reqDto);

    ListOrderRepDto listOrder(ListOrderReqDto reqDto);
}

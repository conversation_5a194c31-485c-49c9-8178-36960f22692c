package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.po.QstQuotaOptionRulePo;
import com.cas.nc.questionnaire.dao.query.QstQuotaOptionRuleQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstQuotaOptionRuleDao;
import com.cas.nc.questionnaire.service.QstQuotaOptionRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class QstQuotaOptionRuleServiceImpl implements QstQuotaOptionRuleService {
    private static Logger logger = LoggerFactory.getLogger(QstQuotaOptionRuleServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstQuotaOptionRuleDao qstQuotaOptionRuleDao;

    @Override
    public int insert(QstQuotaOptionRulePo qstQuotaOptionRulePo) {
        return 0;
    }

    @Override
    public int delete(QstQuotaOptionRuleQuery query) {
        filterCondition(query);
        return qstQuotaOptionRuleDao.delete(query);
    }

    @Override
    public List<QstQuotaOptionRulePo> selectList(QstQuotaOptionRuleQuery query) {
        filterCondition(query);
        return qstQuotaOptionRuleDao.selectList(query);
    }

    @Override
    public QstQuotaOptionRulePo selectOne(QstQuotaOptionRuleQuery query) {
        filterCondition(query);
        return qstQuotaOptionRuleDao.selectOne(query);
    }

    private void filterCondition(QstQuotaOptionRuleQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.setting.SettingConditionReturnFilterRuleDto;
import com.cas.nc.questionnaire.dao.po.QstFilterRulePo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ConditionReturnListQueryConverter {
    ConditionReturnListQueryConverter INSTANCE = Mappers.getMapper(ConditionReturnListQueryConverter.class);

    List<SettingConditionReturnFilterRuleDto> to(List<QstFilterRulePo> filterRulePoList);
}
package com.cas.nc.questionnaire.common.exception;


import com.cas.nc.questionnaire.common.enums.CodeEnum;


public class ServerException extends RuntimeException {

    private int code;
    private String msg;

    public ServerException(CodeEnum codeEnum) {
        super(codeEnum.value());
        this.code = codeEnum.key();
        this.msg = codeEnum.value();
    }

    public ServerException(String msg) {
        super(msg);
    }

    public ServerException(Throwable throwable) {
        super(throwable);
    }

    public ServerException(String message, Throwable throwable) {
        super(message, throwable);
        this.msg = message;
    }

    public ServerException(CodeEnum codeEnum, Throwable cause) {
        super(cause);
        this.code = codeEnum.key();
        this.msg = codeEnum.value();
    }

    public ServerException(String beforeMsg, CodeEnum codeEnum) {
        super(beforeMsg + codeEnum.value());
        this.code = codeEnum.key();
        this.msg = beforeMsg + codeEnum.value();
    }

    public ServerException(CodeEnum codeEnum, String afterMsg) {
        super(codeEnum.value() + afterMsg);
        this.code = codeEnum.key();
        this.msg = codeEnum.value() + afterMsg;
    }

    public ServerException(int errorCode, String errorMsg) {
        super(errorMsg);
        this.code = errorCode;
        this.msg = errorMsg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

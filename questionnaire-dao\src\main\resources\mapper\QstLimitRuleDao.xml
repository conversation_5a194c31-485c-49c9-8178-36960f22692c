<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstLimitRuleDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstLimitRulePo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="pwd" jdbcType="VARCHAR" property="pwd"/>
        <result column="invalid_answer_hint" jdbcType="VARCHAR" property="invalidAnswerHint"/>
        <result column="break_point" jdbcType="INTEGER" property="breakPoint"/>
        <result column="collect_number" jdbcType="INTEGER" property="collectNumber"/>
        <result column="device_limit_type" jdbcType="INTEGER" property="deviceLimitType"/>
        <result column="device_limit_frequency" jdbcType="INTEGER" property="deviceLimitFrequency"/>
        <result column="ip_limit_type" jdbcType="INTEGER" property="ipLimitType"/>
        <result column="ip_limit_frequency" jdbcType="INTEGER" property="ipLimitFrequency"/>
        <result column="ip_start" jdbcType="VARCHAR" property="ipStart"/>
        <result column="ip_end" jdbcType="VARCHAR" property="ipEnd"/>
        <result column="return_hint" jdbcType="VARCHAR" property="returnHint"/>
        <result column="return_url" jdbcType="VARCHAR" property="returnUrl"/>
        <result column="end_hint" jdbcType="VARCHAR" property="endHint"/>
        <result column="report_share_type" jdbcType="INTEGER" property="reportShareType"/>
        <result column="report_pwd" jdbcType="VARCHAR" property="reportPwd"/>
        <result column="answer_share_type" jdbcType="INTEGER" property="answerShareType"/>
        <result column="answer_pwd" jdbcType="VARCHAR" property="answerPwd"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,questionnaire_id,user_id,begin_time,end_time,pwd,invalid_answer_hint,
    break_point,collect_number,device_limit_type,device_limit_frequency,ip_limit_type,ip_limit_frequency,
    ip_start,ip_end,return_hint,return_url,end_hint,report_share_type,
    report_pwd,answer_share_type,answer_pwd,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">and questionnaire_id =
                #{item.questionnaireId}
            </if>
            <if test="null != item.userId and '' != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.beginTime and '' != item.beginTime">and begin_time >= #{item.beginTime}</if>
            <if test="null != item.endTime and '' != item.endTime">and #{item.endTime} >= end_time</if>
            <if test="null != item.pwd and '' != item.pwd">and pwd = #{item.pwd}</if>
            <if test="null != item.invalidAnswerHint and '' != item.invalidAnswerHint">and invalid_answer_hint =
                #{item.invalidAnswerHint}
            </if>
            <if test="null != item.breakPoint and '' != item.breakPoint">and break_point = #{item.breakPoint}</if>
            <if test="null != item.collectNumber and '' != item.collectNumber">and collect_number =
                #{item.collectNumber}
            </if>
            <if test="null != item.deviceLimitType and '' != item.deviceLimitType">and device_limit_type =
                #{item.deviceLimitType}
            </if>
            <if test="null != item.deviceLimitFrequency and '' != item.deviceLimitFrequency">and device_limit_frequency
                = #{item.deviceLimitFrequency}
            </if>
            <if test="null != item.ipLimitType and '' != item.ipLimitType">and ip_limit_type = #{item.ipLimitType}</if>
            <if test="null != item.ipLimitFrequency and '' != item.ipLimitFrequency">and ip_limit_frequency =
                #{item.ipLimitFrequency}
            </if>
            <if test="null != item.ipStart and '' != item.ipStart">and ip_start = #{item.ipStart}</if>
            <if test="null != item.ipEnd and '' != item.ipEnd">and ip_end = #{item.ipEnd}</if>
            <if test="null != item.returnHint and '' != item.returnHint">and return_hint = #{item.returnHint}</if>
            <if test="null != item.returnUrl and '' != item.returnUrl">and return_url = #{item.returnUrl}</if>
            <if test="null != item.endHint and '' != item.endHint">and end_hint = #{item.endHint}</if>
            <if test="null != item.reportShareType and '' != item.reportShareType">and report_share_type =
                #{item.reportShareType}
            </if>
            <if test="null != item.reportPwd and '' != item.reportPwd">and report_pwd = #{item.reportPwd}</if>
            <if test="null != item.answerShareType and '' != item.answerShareType">and answer_share_type =
                #{item.answerShareType}
            </if>
            <if test="null != item.answerPwd and '' != item.answerPwd">and answer_pwd = #{item.answerPwd}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>

            <if test="null != item.questionnaireIdList">
                and questionnaire_id in
                <foreach collection="item.questionnaireIdList" index="index" item="tag" open="(" separator=","
                         close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_limit_rule
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_limit_rule
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_limit_rule
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into qst_limit_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.beginTime">begin_time,</if>
            <if test="null != item.endTime">end_time,</if>
            <if test="null != item.pwd">pwd,</if>
            <if test="null != item.invalidAnswerHint">invalid_answer_hint,</if>
            <if test="null != item.breakPoint">break_point,</if>
            <if test="null != item.collectNumber">collect_number,</if>
            <if test="null != item.deviceLimitType">device_limit_type,</if>
            <if test="null != item.deviceLimitFrequency">device_limit_frequency,</if>
            <if test="null != item.ipLimitType">ip_limit_type,</if>
            <if test="null != item.ipLimitFrequency">ip_limit_frequency,</if>
            <if test="null != item.ipStart">ip_start,</if>
            <if test="null != item.ipEnd">ip_end,</if>
            <if test="null != item.returnHint">return_hint,</if>
            <if test="null != item.returnUrl">return_url,</if>
            <if test="null != item.endHint">end_hint,</if>
            <if test="null != item.reportShareType">report_share_type,</if>
            <if test="null != item.reportPwd">report_pwd,</if>
            <if test="null != item.answerShareType">answer_share_type,</if>
            <if test="null != item.answerPwd">answer_pwd,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.beginTime">#{item.beginTime},</if>
            <if test="null != item.endTime">#{item.endTime},</if>
            <if test="null != item.pwd">#{item.pwd},</if>
            <if test="null != item.invalidAnswerHint">#{item.invalidAnswerHint},</if>
            <if test="null != item.breakPoint">#{item.breakPoint},</if>
            <if test="null != item.collectNumber">#{item.collectNumber},</if>
            <if test="null != item.deviceLimitType">#{item.deviceLimitType},</if>
            <if test="null != item.deviceLimitFrequency">#{item.deviceLimitFrequency},</if>
            <if test="null != item.ipLimitType">#{item.ipLimitType},</if>
            <if test="null != item.ipLimitFrequency">#{item.ipLimitFrequency},</if>
            <if test="null != item.ipStart">#{item.ipStart},</if>
            <if test="null != item.ipEnd">#{item.ipEnd},</if>
            <if test="null != item.returnHint">#{item.returnHint},</if>
            <if test="null != item.returnUrl">#{item.returnUrl},</if>
            <if test="null != item.endHint">#{item.endHint},</if>
            <if test="null != item.reportShareType">#{item.reportShareType},</if>
            <if test="null != item.reportPwd">#{item.reportPwd},</if>
            <if test="null != item.answerShareType">#{item.answerShareType},</if>
            <if test="null != item.answerPwd">#{item.answerPwd},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.beginTime">begin_time = values(begin_time),</if>
            <if test="null != item.endTime">end_time = values(end_time),</if>
            <if test="null != item.pwd">pwd = values(pwd),</if>
            <if test="null != item.invalidAnswerHint">invalid_answer_hint = values(invalid_answer_hint),</if>
            <if test="null != item.breakPoint">break_point = values(break_point),</if>
            <if test="null != item.collectNumber">collect_number = values(collect_number),</if>
            <if test="null != item.deviceLimitType">device_limit_type = values(device_limit_type),</if>
            <if test="null != item.deviceLimitFrequency">device_limit_frequency = values(device_limit_frequency),</if>
            <if test="null != item.ipLimitType">ip_limit_type = values(ip_limit_type),</if>
            <if test="null != item.ipLimitFrequency">ip_limit_frequency = values(ip_limit_frequency),</if>
            <if test="null != item.ipStart">ip_start = values(ip_start),</if>
            <if test="null != item.ipEnd">ip_end = values(ip_end),</if>
            <if test="null != item.returnHint">return_hint = values(return_hint),</if>
            <if test="null != item.returnUrl">return_url = values(return_url),</if>
            <if test="null != item.endHint">end_hint = values(end_hint),</if>
            <if test="null != item.reportShareType">report_share_type = values(report_share_type),</if>
            <if test="null != item.reportPwd">report_pwd = values(report_pwd),</if>
            <if test="null != item.answerShareType">answer_share_type = values(answer_share_type),</if>
            <if test="null != item.answerPwd">answer_pwd = values(answer_pwd),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update qst_limit_rule
        <set>
            <if test="null != item.questionnaireId">questionnaire_id = #{item.questionnaireId},</if>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.beginTime">begin_time = #{item.beginTime},</if>
            <if test="null != item.endTime">end_time = #{item.endTime},</if>
            <if test="null != item.pwd">pwd = #{item.pwd},</if>
            <if test="null != item.invalidAnswerHint">invalid_answer_hint = #{item.invalidAnswerHint},</if>
            <if test="null != item.breakPoint">break_point = #{item.breakPoint},</if>
            <if test="null != item.collectNumber">collect_number = #{item.collectNumber},</if>
            <if test="null != item.deviceLimitType">device_limit_type = #{item.deviceLimitType},</if>
            <if test="null != item.deviceLimitFrequency">device_limit_frequency = #{item.deviceLimitFrequency},</if>
            <if test="null != item.ipLimitType">ip_limit_type = #{item.ipLimitType},</if>
            <if test="null != item.ipLimitFrequency">ip_limit_frequency = #{item.ipLimitFrequency},</if>
            <if test="null != item.ipStart">ip_start = #{item.ipStart},</if>
            <if test="null != item.ipEnd">ip_end = #{item.ipEnd},</if>
            <if test="null != item.returnHint">return_hint = #{item.returnHint},</if>
            <if test="null != item.returnUrl">return_url = #{item.returnUrl},</if>
            <if test="null != item.endHint">end_hint = #{item.endHint},</if>
            <if test="null != item.reportShareType">report_share_type = #{item.reportShareType},</if>
            <if test="null != item.reportPwd">report_pwd = #{item.reportPwd},</if>
            <if test="null != item.answerShareType">answer_share_type = #{item.answerShareType},</if>
            <if test="null != item.answerPwd">answer_pwd = #{item.answerPwd},</if>
        </set>
        where id = #{item.id}
        and user_id = #{item.userId}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_limit_rule
        <include refid="sql_where"/>
    </delete>

    <update id="updateDealTime" parameterType="QstLimitRuleQuery">
        update qst_limit_rule
        <set>
            <if test="null != item.beginTimeStr">begin_time = #{item.beginTimeStr},</if>
            <if test="null != item.endTimeStr">end_time = #{item.endTimeStr},</if>
            <if test="null != item.pwd">pwd = #{item.pwd},</if>
            <if test="null != item.invalidAnswerHint">invalid_answer_hint = #{item.invalidAnswerHint},</if>
            <if test="null != item.breakPoint">break_point = #{item.breakPoint},</if>
            <if test="null != item.collectNumber">collect_number = #{item.collectNumber},</if>
            <if test="null != item.deviceLimitType">device_limit_type = #{item.deviceLimitType},</if>
            <if test="null != item.deviceLimitFrequency">device_limit_frequency = #{item.deviceLimitFrequency},</if>
            <if test="null != item.ipLimitType">ip_limit_type = #{item.ipLimitType},</if>
            <if test="null != item.ipLimitFrequency">ip_limit_frequency = #{item.ipLimitFrequency},</if>
            <if test="null != item.ipStart">ip_start = #{item.ipStart},</if>
            <if test="null != item.ipEnd">ip_end = #{item.ipEnd},</if>
            <if test="null != item.returnHint">return_hint = #{item.returnHint},</if>
            <if test="null != item.returnUrl">return_url = #{item.returnUrl},</if>
            <if test="null != item.endHint">end_hint = #{item.endHint},</if>
        </set>
        where questionnaire_id = #{item.questionnaireId}
        and user_id = #{item.userId}
    </update>
</mapper>
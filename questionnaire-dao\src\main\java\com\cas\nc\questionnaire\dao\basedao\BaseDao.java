package com.cas.nc.questionnaire.dao.basedao;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BaseDao<T, P> {

    /**
     * 查询单条记录
     *
     * @param obj
     * @return
     */
    T selectOne(@Param("item") P obj);

    /**
     * 查询记录集合
     *
     * @param obj
     * @return
     */
    List<T> selectList(@Param("item") P obj);

    /**
     * 单条插入
     *
     * @param obj
     * @return
     */
    int insert(@Param("item") T obj);

    /**
     * 单条插入失败就更新
     *
     * @param obj
     * @return
     */
    int insertUpdate(@Param("item") T obj);

    /**
     * 单条更新操作
     *
     * @param obj
     * @return
     */
    int update(@Param("item") P obj);

    /**
     * 删除
     *
     * @param obj
     * @return
     */
    int delete(@Param("item") P obj);

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    int insertList(@Param("list") List<T> list);

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    int insertUpdateList(@Param("list") List<T> list);

}

package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class QstLimitRulePo {
    /*自增id*/
    private Long id;

    /*问卷id*/
    private String questionnaireId;

    /*用户id*/
    private Long userId;

    /*开始时间*/
    private Date beginTime;

    /*结束时间*/
    private Date endTime;

    /*答题密码*/
    private String pwd;

    /*无效答卷提示*/
    private String invalidAnswerHint;

    /*断点续答，1：支持，2：不支持*/
    private Integer breakPoint;

    /*问卷回收数量*/
    private Integer collectNumber;

    /*设备限时类型，1：不限时，2：每小时，3：每天，4：每月，5：每年*/
    private Integer deviceLimitType;

    /*设备限制次数*/
    private Integer deviceLimitFrequency;

    /*ip限时类型，1：不限时，2：每小时，3：每天，4：每月，5：每年*/
    private Integer ipLimitType;

    /*ip限制次数*/
    private Integer ipLimitFrequency;

    /*允许答题网段ip开始*/
    private String ipStart;

    /*允许答题网段ip结束*/
    private String ipEnd;

    /*跳转提示*/
    private String returnHint;

    /*跳转url*/
    private String returnUrl;

    /*答题结束提示*/
    private String endHint;

    /*报告分享类型，1：不公开，2：部分公开，3：公开*/
    private Integer reportShareType;

    /*报告密码*/
    private String reportPwd;

    /*问卷答案分享类型，1：不公开，2：部分公开，3：公开*/
    private Integer answerShareType;

    /*答案密码*/
    private String answerPwd;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getInvalidAnswerHint() {
        return invalidAnswerHint;
    }

    public void setInvalidAnswerHint(String invalidAnswerHint) {
        this.invalidAnswerHint = invalidAnswerHint;
    }

    public Integer getBreakPoint() {
        return breakPoint;
    }

    public void setBreakPoint(Integer breakPoint) {
        this.breakPoint = breakPoint;
    }

    public Integer getCollectNumber() {
        return collectNumber;
    }

    public void setCollectNumber(Integer collectNumber) {
        this.collectNumber = collectNumber;
    }

    public Integer getDeviceLimitType() {
        return deviceLimitType;
    }

    public void setDeviceLimitType(Integer deviceLimitType) {
        this.deviceLimitType = deviceLimitType;
    }

    public Integer getDeviceLimitFrequency() {
        return deviceLimitFrequency;
    }

    public void setDeviceLimitFrequency(Integer deviceLimitFrequency) {
        this.deviceLimitFrequency = deviceLimitFrequency;
    }

    public Integer getIpLimitType() {
        return ipLimitType;
    }

    public void setIpLimitType(Integer ipLimitType) {
        this.ipLimitType = ipLimitType;
    }

    public Integer getIpLimitFrequency() {
        return ipLimitFrequency;
    }

    public void setIpLimitFrequency(Integer ipLimitFrequency) {
        this.ipLimitFrequency = ipLimitFrequency;
    }

    public String getIpStart() {
        return ipStart;
    }

    public void setIpStart(String ipStart) {
        this.ipStart = ipStart;
    }

    public String getIpEnd() {
        return ipEnd;
    }

    public void setIpEnd(String ipEnd) {
        this.ipEnd = ipEnd;
    }

    public String getReturnHint() {
        return returnHint;
    }

    public void setReturnHint(String returnHint) {
        this.returnHint = returnHint;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getEndHint() {
        return endHint;
    }

    public void setEndHint(String endHint) {
        this.endHint = endHint;
    }

    public Integer getReportShareType() {
        return reportShareType;
    }

    public void setReportShareType(Integer reportShareType) {
        this.reportShareType = reportShareType;
    }

    public String getReportPwd() {
        return reportPwd;
    }

    public void setReportPwd(String reportPwd) {
        this.reportPwd = reportPwd;
    }

    public Integer getAnswerShareType() {
        return answerShareType;
    }

    public void setAnswerShareType(Integer answerShareType) {
        this.answerShareType = answerShareType;
    }

    public String getAnswerPwd() {
        return answerPwd;
    }

    public void setAnswerPwd(String answerPwd) {
        this.answerPwd = answerPwd;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
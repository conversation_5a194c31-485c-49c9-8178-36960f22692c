package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.TimeTypeEnum;
import com.cas.nc.questionnaire.common.enums.UserStatusEnum;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.dao.nosharddao.UserInfoDao;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.UserInfoQuery;
import com.cas.nc.questionnaire.service.UserInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Date;
import java.util.List;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.DATA_EXCEPTION;
import static com.cas.nc.questionnaire.common.enums.TimeTypeEnum.DAY;
import static com.cas.nc.questionnaire.common.enums.TimeTypeEnum.MONTH;
import static com.cas.nc.questionnaire.common.enums.TimeTypeEnum.WEEK;
import static com.cas.nc.questionnaire.common.enums.TimeTypeEnum.YEAR;
import static com.cas.nc.questionnaire.common.enums.UserTypeEnum.VIP;


@Service
public class UserInfoServiceImpl implements UserInfoService {
    private static Logger logger = LoggerFactory.getLogger(UserInfoServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private UserInfoDao userInfoDao;

    @Override
    public int insert(UserInfoPo userInfoPo) {
        return userInfoDao.insert(userInfoPo);
    }

    @Override
    public UserInfoPo selectOne(UserInfoQuery query) {
        return userInfoDao.selectOne(query);
    }

    @Override
    public Long queryUserId(String outUserId) {
        UserInfoQuery query = new UserInfoQuery();
        query.setOutUserId(outUserId);
        UserInfoPo userInfoPo = selectOne(query);
        Assert.notNull(userInfoPo, DATA_EXCEPTION);
        return userInfoPo.getId();
    }

    @Override
    public int updateStatusEffective(Long id) {
        UserInfoQuery query = new UserInfoQuery();
        query.setId(id);
        query.setStatus(UserStatusEnum.EFFECTIVE.key());
        return userInfoDao.update(query);
    }

    @Override
    public UserInfoPo selectOne(Long id) {
        UserInfoQuery query = new UserInfoQuery();
        query.setId(id);
        return selectOne(query);
    }

    @Override
    public void addMembershipDuration(Long userId, Integer timeCount, TimeTypeEnum timeTypeEnum) {
        UserInfoPo userInfoPo = selectOne(userId);
        Assert.notNull(userInfoPo, DATA_EXCEPTION);
        UserInfoQuery query = new UserInfoQuery();
        query.setId(userId);

        if (userInfoPo.getVipEndTime() != null) {
            query.setVipEndTime(calculation(userInfoPo.getVipEndTime(), timeCount, timeTypeEnum));
        } else {
            query.setVipEndTime(calculation(new Date(), timeCount, timeTypeEnum));
            query.setType(VIP.key());
        }
        int update = userInfoDao.update(query);

        Assert.isTrue(update == 1, DATA_EXCEPTION);
    }

    @Override
    public int updatePwd(Long userId, String oldPwd, String newPwd) {
        UserInfoQuery query = new UserInfoQuery();
        query.setId(userId);
        query.setPwd(newPwd);
        query.setOldPwd(oldPwd);

        return userInfoDao.update(query);
    }

    @Override
    public int resetPwdErrorCount(Long userId) {
        UserInfoQuery query = new UserInfoQuery();
        query.setId(userId);
        query.setPwdErrorCount(0);

        return userInfoDao.update(query);
    }

    @Override
    public int setPwdErrorCount(Long userId, int count) {
        UserInfoQuery query = new UserInfoQuery();
        query.setId(userId);
        query.setPwdErrorCount(count);

        return userInfoDao.update(query);
    }

    @Override
    public int update(UserInfoQuery query) {
        return userInfoDao.update(query);
    }

    @Override
    public List<UserInfoPo> selectList(UserInfoQuery query) {
        return userInfoDao.selectList(query);
    }

    @Override
    public int insertUpdate(UserInfoPo userInfoPo) {
        return userInfoDao.insertUpdate(userInfoPo);
    }

    @Override
    public int selectCount(UserInfoQuery query) {
        return userInfoDao.selectCount(query);
    }

    @Override
    public List<UserInfoPo> selectListByPage(UserInfoQuery query) {
        return userInfoDao.selectUserInfoListByPage(query);
    }

    private Date calculation(Date startDate, Integer timeCount, TimeTypeEnum timeTypeEnum) {
        Date result = new Date();

        if (timeTypeEnum.equals(DAY)) {
            result = DateUtil.addDays(startDate, timeCount);
        } else if (timeTypeEnum.equals(WEEK)) {
            result = DateUtil.addWeeks(startDate, timeCount);
        } else if (timeTypeEnum.equals(MONTH)) {
            result = DateUtil.addMonths(startDate, timeCount);
        } else if (timeTypeEnum.equals(YEAR)) {
            result = DateUtil.addYears(startDate, timeCount);
        }

        return result;
    }

    @Override
    public UserInfoPo selectUserByEmailOrUsername(String email, String userName) {
        return userInfoDao.selectUserByEmailOrUserName(email, userName);
    }
}

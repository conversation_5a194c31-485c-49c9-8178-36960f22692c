package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.nosharddao.TempletTitleDao;
import com.cas.nc.questionnaire.dao.po.TempletTitlePo;
import com.cas.nc.questionnaire.dao.query.TempletTitleQuery;
import com.cas.nc.questionnaire.service.TempletTitleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class TempletTitleServiceImpl implements TempletTitleService {
    private static Logger logger = LoggerFactory.getLogger(TempletTitleServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private TempletTitleDao templetTitleDao;

    @Override
    public int insert(TempletTitlePo templetTitlePo) {
        return templetTitleDao.insert(templetTitlePo);
    }

    @Override
    public int delete(TempletTitleQuery query) {
        filterCondition(query);
        return templetTitleDao.delete(query);
    }

    @Override
    public List<TempletTitlePo> selectList(TempletTitleQuery query) {
        return templetTitleDao.selectList(query);
    }

    @Override
    public TempletTitlePo selectOne(TempletTitleQuery query) {
        return templetTitleDao.selectOne(query);
    }

    @Override
    public List<TempletTitlePo> selectList(String templetId) {
        TempletTitleQuery query = new TempletTitleQuery();
        query.setTempletId(templetId);

        return selectList(query);
    }

    private void filterCondition(TempletTitleQuery query) {
        Assert.notNull(query.getTempletId(), "templetId");
    }
}

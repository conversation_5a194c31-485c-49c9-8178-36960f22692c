package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.dao.po.QstSignRecordPo;
import com.cas.nc.questionnaire.dao.query.QstSignRecordQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstSignRecordDao;
import com.cas.nc.questionnaire.service.QstSignRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 问卷签到记录服务实现
 */
@Service
public class QstSignRecordServiceImpl implements QstSignRecordService {

    @Resource
    private QstSignRecordDao qstSignRecordDao;
    
    @Override
    public int insert(QstSignRecordPo signRecord) {
        return qstSignRecordDao.insert(signRecord);
    }
    
    @Override
    public int update(QstSignRecordPo signRecord) {
        QstSignRecordQuery query = new QstSignRecordQuery();
        query.setId(signRecord.getId());
        
        QstSignRecordPo updatePo = new QstSignRecordPo();
        updatePo.setSignId(signRecord.getSignId());
        updatePo.setQuestionnaireId(signRecord.getQuestionnaireId());
        updatePo.setUserId(signRecord.getUserId());
        updatePo.setOpenid(signRecord.getOpenid());
        updatePo.setNickname(signRecord.getNickname());
        updatePo.setHeadimgurl(signRecord.getHeadimgurl());
        updatePo.setSignTime(signRecord.getSignTime());
        updatePo.setLatitude(signRecord.getLatitude());
        updatePo.setLongitude(signRecord.getLongitude());
        updatePo.setSignDate(signRecord.getSignDate());
        updatePo.setStatus(signRecord.getStatus());
        updatePo.setUpdateTime(signRecord.getUpdateTime());
        
        // 使用自定义SQL更新，因为BaseDao的update方法需要Query类型
        return qstSignRecordDao.update(query);
    }
    
    @Override
    public QstSignRecordPo selectOne(Long id) {
        QstSignRecordQuery query = new QstSignRecordQuery();
        query.setId(id);
        return qstSignRecordDao.selectOne(query);
    }
    
    @Override
    public List<QstSignRecordPo> selectList(QstSignRecordQuery query) {
        return qstSignRecordDao.selectList(query);
    }
    
    @Override
    public QstSignRecordPo selectByQuestionnaireIdAndOpenidAndDate(String questionnaireId, String openid, Date signDate) {
        return qstSignRecordDao.selectByQuestionnaireIdAndOpenidAndDate(questionnaireId, openid, signDate);
    }
    
    @Override
    public List<QstSignRecordPo> selectListByPage(QstSignRecordQuery query) {
        return qstSignRecordDao.selectListByPage(query);
    }
    
    @Override
    public int selectCount(QstSignRecordQuery query) {
        return qstSignRecordDao.selectCount(query);
    }
} 
package com.cas.nc.questionnaire.rpc.wechat.iphandler;

import com.cas.nc.questionnaire.common.utils.HttpClientUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.rpc.wechat.entity.ipentity.IpAreaResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PconlineIpHandler extends IpHandler {
    private static Logger logger = LoggerFactory.getLogger(PconlineIpHandler.class);

    @Override
    protected IpAreaResult getIpArea(String ip) {
        try {
            String url = "http://whois.pconline.com.cn/ipJson.jsp?json=true&ip=" + ip;
            String resultStr = HttpClientUtil.doGet(url, "GBK");
            logger.info("PconlineIpHandler.getIpArea paramUrl[{}] result[{}]", url, resultStr);

            if (StringUtil.isNotBlank(resultStr)) {
                if (resultStr.contains("\"pro\"")) {
                    resultStr = resultStr.replaceAll("\"pro\"", "\"province\"");
                }
                IpAreaResult result = JSONUtil.parseObject(resultStr, IpAreaResult.class);

                return result;
            }

        } catch (Exception e) {
            logger.error("PconlineIpHandler.getIpArea Exception", e);
        }

        return null;
    }
}

package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.answer.DeleteAnswerReqDto;
import com.cas.nc.questionnaire.common.vo.answer.GetAnswerReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DeleteAnswerConverter {
    DeleteAnswerConverter INSTANCE = Mappers.getMapper(DeleteAnswerConverter.class);

    DeleteAnswerReqDto to(GetAnswerReqVo vo);
}
package com.cas.nc.questionnaire.rpc.wechat.entity.ipentity;

import java.io.Serializable;

public class IpResult {
    private Integer code;
    private Address data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Address getData() {
        return data;
    }

    public void setData(Address data) {
        this.data = data;
    }

    public static class Address implements Serializable {
        private String ip;
        private String region;
        private String city;

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }
    }

    public static class Region implements Serializable {
        private String province;
        private String city;

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }
    }
}

package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class TaskConfigPo {
    /*自增id*/
    private Long id;

    /*任务类型*/
    private Integer taskType;

    /*容忍最大失败次数*/
    private Integer failMaxNum;

    /*批量锁定量*/
    private Integer batchNum;

    /*备注*/
    private String remark;

    /*是否有效*/
    private Short yn;

    /*创建时间*/
    private Date createTime;

    /*更新时间*/
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getFailMaxNum() {
        return failMaxNum;
    }

    public void setFailMaxNum(Integer failMaxNum) {
        this.failMaxNum = failMaxNum;
    }

    public Integer getBatchNum() {
        return batchNum;
    }

    public void setBatchNum(Integer batchNum) {
        this.batchNum = batchNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Short getYn() {
        return yn;
    }

    public void setYn(Short yn) {
        this.yn = yn;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
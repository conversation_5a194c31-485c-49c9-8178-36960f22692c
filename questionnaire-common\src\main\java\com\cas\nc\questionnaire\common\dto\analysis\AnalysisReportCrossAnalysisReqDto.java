package com.cas.nc.questionnaire.common.dto.analysis;

import com.cas.nc.questionnaire.common.dto.base.BaseRequestDto;

import java.util.List;

public class AnalysisReportCrossAnalysisReqDto extends BaseRequestDto {
    private List<CrossAnalysisDependentVariableDto> crossAnalysisDependentVariableList;

    private List<Integer> independentVariableTitleList;

    private Boolean hideNull;

    private Boolean hideSkip;

    public List<CrossAnalysisDependentVariableDto> getCrossAnalysisDependentVariableList() {
        return crossAnalysisDependentVariableList;
    }

    public void setCrossAnalysisDependentVariableList(List<CrossAnalysisDependentVariableDto> crossAnalysisDependentVariableList) {
        this.crossAnalysisDependentVariableList = crossAnalysisDependentVariableList;
    }

    public List<Integer> getIndependentVariableTitleList() {
        return independentVariableTitleList;
    }

    public void setIndependentVariableTitleList(List<Integer> independentVariableTitleList) {
        this.independentVariableTitleList = independentVariableTitleList;
    }

    public Boolean getHideNull() {
        return hideNull;
    }

    public void setHideNull(Boolean hideNull) {
        this.hideNull = hideNull;
    }

    public Boolean getHideSkip() {
        return hideSkip;
    }

    public void setHideSkip(Boolean hideSkip) {
        this.hideSkip = hideSkip;
    }
}

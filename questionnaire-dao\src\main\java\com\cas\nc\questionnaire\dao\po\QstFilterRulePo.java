package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class QstFilterRulePo {
    /*主键*/
    private Long id;

    /*用户id*/
    private Long userId;

    /*问卷id*/
    private String questionnaireId;

    /*过滤规则id*/
    private String filterRuleId;

    /*外部id*/
    private String foreignId;

    /*名称*/
    private String name;

    /*规则类型，1：题目，2：省份，3：城市，4：答题时间段，5：来源渠道，6：ip地址，7：提交答卷日期，8：ip包含*/
    private Integer ruleType;

    /*判断类型，1：是，2：非，3：小于，4：等于，5：大于，6：之间，7：包含，8：不包含，9：与，10：或*/
    private Integer judgeType;

    /*根据ruleType来判断内容以及格式*/
    private String content;

    /*省*/
    private Long province;

    /*过滤问卷数*/
    private Integer filterNum;

    /*筛选类型，1：自动筛选，2：手动筛选*/
    private Integer filterType;

    /*数据来源，1：质量控制-筛选，2：问卷设置-跳转*/
    private Integer dataSource;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public String getFilterRuleId() {
        return filterRuleId;
    }

    public void setFilterRuleId(String filterRuleId) {
        this.filterRuleId = filterRuleId;
    }

    public String getForeignId() {
        return foreignId;
    }

    public void setForeignId(String foreignId) {
        this.foreignId = foreignId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getJudgeType() {
        return judgeType;
    }

    public void setJudgeType(Integer judgeType) {
        this.judgeType = judgeType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getProvince() {
        return province;
    }

    public void setProvince(Long province) {
        this.province = province;
    }

    public Integer getFilterNum() {
        return filterNum;
    }

    public void setFilterNum(Integer filterNum) {
        this.filterNum = filterNum;
    }

    public Integer getFilterType() {
        return filterType;
    }

    public void setFilterType(Integer filterType) {
        this.filterType = filterType;
    }

    public Integer getDataSource() {
        return dataSource;
    }

    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
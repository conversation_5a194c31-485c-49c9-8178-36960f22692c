package com.cas.nc.questionnaire.web.interceptor;

import com.cas.nc.questionnaire.common.exception.ServerException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.FILE_TYPE_LIMIT;

public class FileTypeInterceptor extends HandlerInterceptorAdapter {
    private String suffixList;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 判断是否为文件上传请求
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartRequest =
                    (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> files =
                    multipartRequest.getFileMap();
            Iterator<String> iterator = files.keySet().iterator();
            //对多部件请求资源进行遍历
            while (iterator.hasNext()) {
                String formKey = (String) iterator.next();
                MultipartFile multipartFile =
                        multipartRequest.getFile(formKey);
                String filename = multipartFile.getOriginalFilename();
                //判断是否为限制文件类型
                if (!checkFile(filename)) {
                    throw new ServerException(FILE_TYPE_LIMIT, suffixList);
                }
            }
        }
        return true;
    }

    /**
     * 判断是否为允许的上传文件类型,true表示允许
     */
    private boolean checkFile(String fileName) {
        // 获取文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf(".")
                + 1, fileName.length());
        String[] suffixs = suffixList.split(",");
        List<String> suffixTempList = CollectionUtils.arrayToList(suffixs);

        if (suffixTempList.contains(suffix.trim().toLowerCase())) {
            return true;
        }
        return false;
    }

    public void setSuffixList(String suffixList) {
        this.suffixList = suffixList;
    }
}
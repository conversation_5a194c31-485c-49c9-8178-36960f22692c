<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.TempletOptionDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.TempletOptionPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="templet_id" jdbcType="VARCHAR" property="templetId"/>
        <result column="option_id" jdbcType="VARCHAR" property="optionId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="title_serial_number" jdbcType="INTEGER" property="titleSerialNumber"/>
        <result column="serial_number" jdbcType="INTEGER" property="serialNumber"/>
        <result column="order_number" jdbcType="INTEGER" property="orderNumber"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="row_title" jdbcType="VARCHAR" property="rowTitle"/>
        <result column="row_number" jdbcType="INTEGER" property="rowNumber"/>
        <result column="row_des" jdbcType="VARCHAR" property="rowDes"/>
        <result column="column_title" jdbcType="VARCHAR" property="columnTitle"/>
        <result column="column_number" jdbcType="INTEGER" property="columnNumber"/>
        <result column="score" jdbcType="BIGINT" property="score"/>
        <result column="score_type" jdbcType="INTEGER" property="scoreType"/>
        <result column="gap_filling_type" jdbcType="INTEGER" property="gapFillingType"/>
        <result column="gap_filling_content" jdbcType="VARCHAR" property="gapFillingContent"/>
        <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl"/>
        <result column="picture_scaling" jdbcType="INTEGER" property="pictureScaling"/>
        <result column="des" jdbcType="VARCHAR" property="des"/>
        <result column="skip_num" jdbcType="INTEGER" property="skipNum"/>
        <result column="quote_num" jdbcType="INTEGER" property="quoteNum"/>
        <result column="relation_num" jdbcType="INTEGER" property="relationNum"/>
        <result column="can_input" jdbcType="INTEGER" property="canInput"/>
        <result column="default_type" jdbcType="INTEGER" property="defaultType"/>
        <result column="vertical_number" jdbcType="INTEGER" property="verticalNumber"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,templet_id,option_id,user_id,title_serial_number,serial_number,order_number,
    content,row_title,`row_number`,row_des,column_title,column_number,
    score,score_type,gap_filling_type,gap_filling_content,picture_url,picture_scaling,
    des,skip_num,quote_num,relation_num,can_input,default_type,
    vertical_number,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.templetId and '' != item.templetId">and templet_id = #{item.templetId}</if>
            <if test="null != item.optionId and '' != item.optionId">and option_id = #{item.optionId}</if>
            <if test="null != item.userId and '' != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.titleSerialNumber and '' != item.titleSerialNumber">and title_serial_number =
                #{item.titleSerialNumber}
            </if>
            <if test="null != item.serialNumber and '' != item.serialNumber">and serial_number = #{item.serialNumber}
            </if>
            <if test="null != item.orderNumber and '' != item.orderNumber">and order_number = #{item.orderNumber}</if>
            <if test="null != item.content and '' != item.content">and content = #{item.content}</if>
            <if test="null != item.rowTitle and '' != item.rowTitle">and row_title = #{item.rowTitle}</if>
            <if test="null != item.rowNumber and '' != item.rowNumber">and `row_number` = #{item.rowNumber}</if>
            <if test="null != item.rowDes and '' != item.rowDes">and row_des = #{item.rowDes}</if>
            <if test="null != item.columnTitle and '' != item.columnTitle">and column_title = #{item.columnTitle}</if>
            <if test="null != item.columnNumber and '' != item.columnNumber">and column_number = #{item.columnNumber}
            </if>
            <if test="null != item.score and '' != item.score">and score = #{item.score}</if>
            <if test="null != item.scoreType and '' != item.scoreType">and score_type = #{item.scoreType}</if>
            <if test="null != item.gapFillingType and '' != item.gapFillingType">and gap_filling_type =
                #{item.gapFillingType}
            </if>
            <if test="null != item.gapFillingContent and '' != item.gapFillingContent">and gap_filling_content =
                #{item.gapFillingContent}
            </if>
            <if test="null != item.pictureUrl and '' != item.pictureUrl">and picture_url = #{item.pictureUrl}</if>
            <if test="null != item.pictureScaling and '' != item.pictureScaling">and picture_scaling =
                #{item.pictureScaling}
            </if>
            <if test="null != item.des and '' != item.des">and des = #{item.des}</if>
            <if test="null != item.skipNum and '' != item.skipNum">and skip_num = #{item.skipNum}</if>
            <if test="null != item.quoteNum and '' != item.quoteNum">and quote_num = #{item.quoteNum}</if>
            <if test="null != item.relationNum and '' != item.relationNum">and relation_num = #{item.relationNum}</if>
            <if test="null != item.canInput and '' != item.canInput">and can_input = #{item.canInput}</if>
            <if test="null != item.defaultType and '' != item.defaultType">and default_type = #{item.defaultType}</if>
            <if test="null != item.verticalNumber and '' != item.verticalNumber">and vertical_number =
                #{item.verticalNumber}
            </if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from templet_option
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from templet_option
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from templet_option
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into templet_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.templetId">templet_id,</if>
            <if test="null != item.optionId">option_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.titleSerialNumber">title_serial_number,</if>
            <if test="null != item.serialNumber">serial_number,</if>
            <if test="null != item.orderNumber">order_number,</if>
            <if test="null != item.content">content,</if>
            <if test="null != item.rowTitle">row_title,</if>
            <if test="null != item.rowNumber">`row_number`,</if>
            <if test="null != item.rowDes">row_des,</if>
            <if test="null != item.columnTitle">column_title,</if>
            <if test="null != item.columnNumber">column_number,</if>
            <if test="null != item.score">score,</if>
            <if test="null != item.scoreType">score_type,</if>
            <if test="null != item.gapFillingType">gap_filling_type,</if>
            <if test="null != item.gapFillingContent">gap_filling_content,</if>
            <if test="null != item.pictureUrl">picture_url,</if>
            <if test="null != item.pictureScaling">picture_scaling,</if>
            <if test="null != item.des">des,</if>
            <if test="null != item.skipNum">skip_num,</if>
            <if test="null != item.quoteNum">quote_num,</if>
            <if test="null != item.relationNum">relation_num,</if>
            <if test="null != item.canInput">can_input,</if>
            <if test="null != item.defaultType">default_type,</if>
            <if test="null != item.verticalNumber">vertical_number,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.templetId">#{item.templetId},</if>
            <if test="null != item.optionId">#{item.optionId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.titleSerialNumber">#{item.titleSerialNumber},</if>
            <if test="null != item.serialNumber">#{item.serialNumber},</if>
            <if test="null != item.orderNumber">#{item.orderNumber},</if>
            <if test="null != item.content">#{item.content},</if>
            <if test="null != item.rowTitle">#{item.rowTitle},</if>
            <if test="null != item.rowNumber">#{item.rowNumber},</if>
            <if test="null != item.rowDes">#{item.rowDes},</if>
            <if test="null != item.columnTitle">#{item.columnTitle},</if>
            <if test="null != item.columnNumber">#{item.columnNumber},</if>
            <if test="null != item.score">#{item.score},</if>
            <if test="null != item.scoreType">#{item.scoreType},</if>
            <if test="null != item.gapFillingType">#{item.gapFillingType},</if>
            <if test="null != item.gapFillingContent">#{item.gapFillingContent},</if>
            <if test="null != item.pictureUrl">#{item.pictureUrl},</if>
            <if test="null != item.pictureScaling">#{item.pictureScaling},</if>
            <if test="null != item.des">#{item.des},</if>
            <if test="null != item.skipNum">#{item.skipNum},</if>
            <if test="null != item.quoteNum">#{item.quoteNum},</if>
            <if test="null != item.relationNum">#{item.relationNum},</if>
            <if test="null != item.canInput">#{item.canInput},</if>
            <if test="null != item.defaultType">#{item.defaultType},</if>
            <if test="null != item.verticalNumber">#{item.verticalNumber},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.templetId">templet_id = values(templet_id),</if>
            <if test="null != item.optionId">option_id = values(option_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.titleSerialNumber">title_serial_number = values(title_serial_number),</if>
            <if test="null != item.serialNumber">serial_number = values(serial_number),</if>
            <if test="null != item.orderNumber">order_number = values(order_number),</if>
            <if test="null != item.content">content = values(content),</if>
            <if test="null != item.rowTitle">row_title = values(row_title),</if>
            <if test="null != item.rowNumber">`row_number` = values(`row_number`),</if>
            <if test="null != item.rowDes">row_des = values(row_des),</if>
            <if test="null != item.columnTitle">column_title = values(column_title),</if>
            <if test="null != item.columnNumber">column_number = values(column_number),</if>
            <if test="null != item.score">score = values(score),</if>
            <if test="null != item.scoreType">score_type = values(score_type),</if>
            <if test="null != item.gapFillingType">gap_filling_type = values(gap_filling_type),</if>
            <if test="null != item.gapFillingContent">gap_filling_content = values(gap_filling_content),</if>
            <if test="null != item.pictureUrl">picture_url = values(picture_url),</if>
            <if test="null != item.pictureScaling">picture_scaling = values(picture_scaling),</if>
            <if test="null != item.des">des = values(des),</if>
            <if test="null != item.skipNum">skip_num = values(skip_num),</if>
            <if test="null != item.quoteNum">quote_num = values(quote_num),</if>
            <if test="null != item.relationNum">relation_num = values(relation_num),</if>
            <if test="null != item.canInput">can_input = values(can_input),</if>
            <if test="null != item.defaultType">default_type = values(default_type),</if>
            <if test="null != item.verticalNumber">vertical_number = values(vertical_number),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update templet_option
        <set>
            <if test="null != item.templetId">templet_id = #{item.templetId},</if>
            <if test="null != item.optionId">option_id = #{item.optionId},</if>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.titleSerialNumber">title_serial_number = #{item.titleSerialNumber},</if>
            <if test="null != item.serialNumber">serial_number = #{item.serialNumber},</if>
            <if test="null != item.orderNumber">order_number = #{item.orderNumber},</if>
            <if test="null != item.content">content = #{item.content},</if>
            <if test="null != item.rowTitle">row_title = #{item.rowTitle},</if>
            <if test="null != item.rowNumber">`row_number` = #{item.rowNumber},</if>
            <if test="null != item.rowDes">row_des = #{item.rowDes},</if>
            <if test="null != item.columnTitle">column_title = #{item.columnTitle},</if>
            <if test="null != item.columnNumber">column_number = #{item.columnNumber},</if>
            <if test="null != item.score">score = #{item.score},</if>
            <if test="null != item.scoreType">score_type = #{item.scoreType},</if>
            <if test="null != item.gapFillingType">gap_filling_type = #{item.gapFillingType},</if>
            <if test="null != item.gapFillingContent">gap_filling_content = #{item.gapFillingContent},</if>
            <if test="null != item.pictureUrl">picture_url = #{item.pictureUrl},</if>
            <if test="null != item.pictureScaling">picture_scaling = #{item.pictureScaling},</if>
            <if test="null != item.des">des = #{item.des},</if>
            <if test="null != item.skipNum">skip_num = #{item.skipNum},</if>
            <if test="null != item.quoteNum">quote_num = #{item.quoteNum},</if>
            <if test="null != item.relationNum">relation_num = #{item.relationNum},</if>
            <if test="null != item.canInput">can_input = #{item.canInput},</if>
            <if test="null != item.defaultType">default_type = #{item.defaultType},</if>
            <if test="null != item.verticalNumber">vertical_number = #{item.verticalNumber},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from templet_option
        <include refid="sql_where"/>
    </delete>
</mapper>
package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.mylist.ExternalListRepDto;
import com.cas.nc.questionnaire.common.dto.mylist.ExternalListReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListCopyReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListDeleteReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListPauseReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListPublishReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListRecoveryReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListRecycleBinDeleteReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListRecycleBinListRepDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListRecycleBinListReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListRepDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListReqDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.SignGenerateUtil;
import com.cas.nc.questionnaire.common.utils.ValidateUtils;
import com.cas.nc.questionnaire.common.vo.mylist.ExternalListRepVo;
import com.cas.nc.questionnaire.common.vo.mylist.ExternalListReqVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListCopyRepVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListCopyReqVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListDeleteReqVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListPauseReqVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListPublishReqVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListRecoveryReqVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListRecycleBinDeleteReqVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListRecycleBinListRepVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListRecycleBinListReqVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListRepVo;
import com.cas.nc.questionnaire.common.vo.mylist.MyListReqVo;
import com.cas.nc.questionnaire.dao.po.BizConfigPo;
import com.cas.nc.questionnaire.server.MyListServer;
import com.cas.nc.questionnaire.server.UserServer;
import com.cas.nc.questionnaire.server.mapstruct.ExternalListConverter;
import com.cas.nc.questionnaire.server.mapstruct.MyListConverter;
import com.cas.nc.questionnaire.service.BizConfigService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.BIZ_ID_NOT_EXIST;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.REQUEST_INVALID_RETRY;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.SIGN_EXCEPTION;
import static com.cas.nc.questionnaire.common.utils.Constants.REQUEST_TIME_VALID_TIME;

@RestController
@RequestMapping("/questionnaire/mylist")
public class MyListController extends BaseController {

    @Resource
    private MyListServer myListServer;
    @Resource
    private BizConfigService bizConfigService;
    @Resource
    private UserServer userServer;

    @RequestMapping("/list")
    public ApiReturnResult list(@RequestBody MyListReqVo vo) {
        logger.info("MyListController.list param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullContain(vo, "pageSize", "page");

        MyListReqDto reqDto = MyListConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        MyListRepDto repDto = myListServer.list(reqDto);
        MyListRepVo repVo = MyListConverter.INSTANCE.to(repDto);

        repVo.setPage(vo.getPage());
        repVo.setPageSize(vo.getPageSize());

        result.setData(repVo);

        logger.info("MyListController.list result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/recycleBinList")
    public ApiReturnResult recycleBinList(@RequestBody MyListRecycleBinListReqVo vo) {
        logger.info("MyListController.recycleBinList param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        MyListRecycleBinListReqDto reqDto = MyListConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        List<MyListRecycleBinListRepDto> repDtoList = myListServer.recycleBinList(reqDto);
        List<MyListRecycleBinListRepVo> repVoList = MyListConverter.INSTANCE.to(repDtoList);

        result.setData(repVoList);

        logger.info("MyListController.recycleBinList result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/delete")
    public ApiReturnResult delete(@RequestBody MyListDeleteReqVo vo) {
        logger.info("MyListController.delete param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getQuestionnaireIdList(), "questionnaireIdList");

        MyListDeleteReqDto reqDto = MyListConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        myListServer.delete(reqDto);

        logger.info("MyListController.delete result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/pause")
    public ApiReturnResult pause(@RequestBody MyListPauseReqVo vo) {
        logger.info("MyListController.pause param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Assert.notNull(vo, "questionnaireId");

        MyListPauseReqDto reqDto = MyListConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        myListServer.pause(reqDto);

        logger.info("MyListController.pause result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/publish")
    public ApiReturnResult publish(@RequestBody MyListPublishReqVo vo) {
        logger.info("MyListController.publish param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullContain(vo, "questionnaireId");

        MyListPublishReqDto reqDto = MyListConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        myListServer.publish(reqDto);

        logger.info("MyListController.pause result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/recovery")
    public ApiReturnResult recovery(@RequestBody MyListRecoveryReqVo vo) {
        logger.info("MyListController.recovery param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        MyListRecoveryReqDto reqDto = MyListConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        myListServer.recovery(reqDto);

        logger.info("MyListController.recovery result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/recycleBinDelete")
    public ApiReturnResult recycleBinDelete(@RequestBody MyListRecycleBinDeleteReqVo vo) {
        logger.info("MyListController.recycleBinDelete param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        MyListRecycleBinDeleteReqDto reqDto = MyListConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        myListServer.recycleBinDelete(reqDto);

        logger.info("MyListController.recycleBinDelete result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/copy")
    public ApiReturnResult copy(@RequestBody MyListCopyReqVo vo) {
        logger.info("MyListController.copy param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        MyListCopyReqDto reqDto = MyListConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        String questionnaireId = myListServer.copy(reqDto);
        MyListCopyRepVo repVo = new MyListCopyRepVo();
        repVo.setQuestionnaireId(questionnaireId);

        result.setData(repVo);

        logger.info("MyListController.copy result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/externallist")
    public ApiReturnResult externalList(@RequestBody ExternalListReqVo vo) {
        logger.info("MyListController.externalList param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullExclude(vo, "questionnaireId", "userId", "email");
        validateRequestTime(vo.getRequestTime());
        validateSign(vo, vo.getBizId(), vo.getSign());

        if (vo.getBizId() == 2) {
            Assert.notBlank(vo.getEmail(), "email");
            vo.setUserId(Long.valueOf(userServer.getUserInfo(vo.getEmail()).getUserId()));
        } else {
            Assert.notNull(vo.getUserId(), "userId");
        }

        ExternalListReqDto reqDto = MyListConverter.INSTANCE.to(vo);

        List<ExternalListRepDto> repDtoList = myListServer.externalList(reqDto);
        List<ExternalListRepVo> repVoList = ExternalListConverter.INSTANCE.to(repDtoList);

        result.setData(repVoList);

        logger.info("MyListController.externalList result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    public void validateRequestTime(String requestTime) {
        Date validDate = DateUtil.addSeconds(DateUtil.parseDateTime(requestTime), REQUEST_TIME_VALID_TIME);
        int i = DateUtil.compareDate(validDate);
        Assert.isTrue(i == -1, REQUEST_INVALID_RETRY);
    }

    public void validateSign(Object param, Long bizId, String paramSign) {
        BizConfigPo bizConfigPo = bizConfigService.selectOne(bizId);
        Assert.notNull(bizConfigPo, BIZ_ID_NOT_EXIST);
        String sign = SignGenerateUtil.generateSignByObject(param, bizConfigPo.getMd5Key());
        Assert.isTrue(paramSign.equalsIgnoreCase(sign), SIGN_EXCEPTION);
    }

}

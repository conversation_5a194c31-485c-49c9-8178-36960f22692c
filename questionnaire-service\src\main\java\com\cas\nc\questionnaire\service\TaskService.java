package com.cas.nc.questionnaire.service;


import com.cas.nc.questionnaire.dao.po.TaskPo;
import com.cas.nc.questionnaire.dao.query.TaskQuery;

import java.util.List;

public interface TaskService {

    /**
     * 生成任务
     *
     * @param taskPo
     * @return
     */
    int insertTask(TaskPo taskPo);

    /**
     * 查询任务
     *
     * @param query
     * @return
     */
    List<TaskPo> selectTaskList(TaskQuery query);

    /**
     * 查询执行任务
     *
     * @param query
     * @return
     */
    List<TaskPo> selectExecuteTaskList(TaskQuery query);

    /**
     * 解锁任务
     *
     * @param taskQuery
     */
    int unlockTask(TaskQuery taskQuery);

    /**
     * 挂起任务
     *
     * @param taskQuery
     */
    int hangTask(TaskQuery taskQuery);

    /**
     * 创建任务
     *
     * @param refId
     * @param foreignRefId
     * @param taskType
     * @param content
     * @param url
     * @return
     */
    int createTask(long refId, String foreignRefId, int taskType, String content, String url);

    /**
     * 创建任务
     *
     * @param foreignRefId
     * @param taskType
     * @param content
     * @return
     */
    int createTask(String foreignRefId, int taskType, String content);

    /**
     * 创建任务
     *
     * @param refId
     * @param taskType
     * @param content
     * @return
     */
    int createTask(long refId, int taskType, String content);

    /**
     * 锁定任务
     *
     * @param taskQuery
     * @return
     */
    int lockTask(TaskQuery taskQuery);

    /**
     * 更新任务为成功状态
     *
     * @param taskQuery
     * @return
     */
    int updateTaskSuccess(TaskQuery taskQuery);

    /**
     * 更新失败次数
     *
     * @param taskPo
     * @return
     */
    int updateFailNumAddOne(TaskPo taskPo);

    /**
     * 查询锁定任务
     *
     * @param query
     * @return
     */
    List<TaskPo> selectLockTaskList(TaskQuery query);

    /**
     * 删除任务
     *
     * @param query
     * @return
     */
    int deleteTask(TaskQuery query);

    /**
     * 查询最久的一条锁定任务更新时间
     *
     * @param query
     * @return
     */
    TaskPo selectLockUpdateTimeAsc(TaskQuery query);

    /**
     * 查询需要删除的任务
     *
     * @param query
     * @return
     */
    List<TaskPo> selectDeleteTaskList(TaskQuery query);

    /**
     * 更新任务状态
     *
     * @param query
     * @return
     */
    int updateTaskStatus(TaskQuery query);

    /**
     * 批量添加任务
     * @param taskPoList
     */
    void createTaskBatch(List<TaskPo> taskPoList);
}

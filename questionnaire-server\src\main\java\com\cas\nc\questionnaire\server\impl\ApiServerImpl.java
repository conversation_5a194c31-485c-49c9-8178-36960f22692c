package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.mylist.MyListDeleteReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnairePublishReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireRemoveReqDto;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.UserInfoQuery;
import com.cas.nc.questionnaire.server.ApiServer;
import com.cas.nc.questionnaire.server.MyListServer;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import com.cas.nc.questionnaire.service.UserInfoService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("apiServer")
public class ApiServerImpl implements ApiServer {

    @Resource
    private MyListServer myListServer;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;

    @Override
    public Boolean remove(QuestionnaireRemoveReqDto reqDto) {
        UserInfoQuery query = new UserInfoQuery();
        query.setEmail(reqDto.getEmail());

        UserInfoPo userInfoPo = userInfoService.selectOne(query);
        if (userInfoPo == null) {
            return false;
        }

        MyListDeleteReqDto myListDeleteReqDto = new MyListDeleteReqDto();
        myListDeleteReqDto.setUserId(userInfoPo.getId());
        myListDeleteReqDto.setQuestionnaireId(reqDto.getQuestionnaireId());

        myListServer.delete(myListDeleteReqDto);

        return true;
    }

    @Override
    public Boolean publishQst(QuestionnairePublishReqDto reqDto) {
        UserInfoQuery query = new UserInfoQuery();
        query.setEmail(reqDto.getEmail());

        UserInfoPo userInfoPo = userInfoService.selectOne(query);
        if (userInfoPo == null) {
            return false;
        }
        qstQuestionnaireInfoService.updateStatus2Publish(reqDto.getQuestionnaireId(), userInfoPo.getId());
        qstQuestionnaireInfoService.updateStatusPause2Publish(reqDto.getQuestionnaireId(), userInfoPo.getId());

        return true;
    }

    @Override
    public Boolean pauseQst(QuestionnairePublishReqDto reqDto) {
        UserInfoQuery query = new UserInfoQuery();
        query.setEmail(reqDto.getEmail());

        UserInfoPo userInfoPo = userInfoService.selectOne(query);
        if (userInfoPo == null) {
            return false;
        }
        qstQuestionnaireInfoService.updateStatus2Pause(reqDto.getQuestionnaireId(), userInfoPo.getId());

        return true;
    }
}

package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.cas.nc.questionnaire.dao.query.QstTitleQuery;

import java.util.List;

public interface QstTitleService {
    /**
     * 数据插入
     *
     * @param qstTitlePo
     * @return
     */
    int insert(QstTitlePo qstTitlePo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstTitleQuery query);

    /**
     * 删除
     *
     * @param questionnaireId
     * @param userId
     * @return
     */
    int delete(String questionnaireId, Long userId);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstTitlePo> selectList(QstTitleQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstTitlePo selectOne(QstTitleQuery query);

    /**
     * 根据问卷id查询题目
     *
     * @param questionnaireId
     * @return
     */
    List<QstTitlePo> selectList(String questionnaireId);

    /**
     * 根据问卷id查询题目
     *
     * @param questionnaireId
     * @return
     */
    List<QstTitlePo> selectList(String questionnaireId, Long userId);

    /**
     * 查询数量
     *
     * @param query
     * @return
     */
    int selectCount(QstTitleQuery query);

    /**
     * 查询数量
     *
     * @param questionnaireId
     * @param userId
     * @return
     */
    int selectCount(String questionnaireId, Long userId);

    /**
     * 数据插入or更新
     *
     * @param qstTitlePo
     * @return
     */
    int insertUpdate(QstTitlePo qstTitlePo);

    void copy(QstTitleQuery titleQuery);
}

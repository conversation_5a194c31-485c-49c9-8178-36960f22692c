package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.utils.RequestUtil;
import com.cas.nc.questionnaire.server.TestServer;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RequestMapping("/test")
@RestController
public class TestController extends BaseController {

    @Resource
    private TestServer testServer;

    @RequestMapping("/testquest")
    public Object testQuest(HttpServletRequest request) {
        Map<String, String> originalParam = RequestUtil.getParameterMap(request);
        int count = testServer.createTest(originalParam.get("name"));
        return count;
    }

    @RequestMapping("/testquery")
    public Object testQuery(HttpServletRequest request) {
        Map<String, String> originalParam = RequestUtil.getParameterMap(request);
        return testServer.queryTest(originalParam.get("name"));
    }


}

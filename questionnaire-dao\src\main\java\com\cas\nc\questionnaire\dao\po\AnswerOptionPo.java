package com.cas.nc.questionnaire.dao.po;

import java.util.Date;
import java.util.Objects;

public class AnswerOptionPo {
    /*自增id*/
    private Long id;

    /*问卷id*/
    private String questionnaireId;

    /*出卷人用户id*/
    private Long userId;

    /*答案id*/
    private String answerId;

    /*选项id*/
    private String optionId;

    /*选项序号*/
    private Integer serialNumber;

    /*答卷人用户id*/
    private String answerUserId;

    /*题目序号*/
    private Integer titleSerialNumber;

    /*问卷标题*/
    private String title;

    /*类型*/
    private Integer type;

    /*状态，1：初始化，2：完成，3：自动筛选无效，4：手动筛选无效*/
    private Integer status;

    /*列标题，矩阵题*/
    private String columnTitle;

    /*选项内容*/
    private String optionContent;

    /*填写内容*/
    private String writeContent;

    /*分数*/
    private Long score;

    /*行坐标*/
    private Integer rowNumber;

    /*列坐标*/
    private Integer columnNumber;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getAnswerId() {
        return answerId;
    }

    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }

    public String getOptionId() {
        return optionId;
    }

    public void setOptionId(String optionId) {
        this.optionId = optionId;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getAnswerUserId() {
        return answerUserId;
    }

    public void setAnswerUserId(String answerUserId) {
        this.answerUserId = answerUserId;
    }

    public Integer getTitleSerialNumber() {
        return titleSerialNumber;
    }

    public void setTitleSerialNumber(Integer titleSerialNumber) {
        this.titleSerialNumber = titleSerialNumber;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getColumnTitle() {
        return columnTitle;
    }

    public void setColumnTitle(String columnTitle) {
        this.columnTitle = columnTitle;
    }

    public String getOptionContent() {
        return optionContent;
    }

    public void setOptionContent(String optionContent) {
        this.optionContent = optionContent;
    }

    public String getWriteContent() {
        return writeContent;
    }

    public void setWriteContent(String writeContent) {
        this.writeContent = writeContent;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Integer getRowNumber() {
        return rowNumber;
    }

    public void setRowNumber(Integer rowNumber) {
        this.rowNumber = rowNumber;
    }

    public Integer getColumnNumber() {
        return columnNumber;
    }

    public void setColumnNumber(Integer columnNumber) {
        this.columnNumber = columnNumber;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AnswerOptionPo that = (AnswerOptionPo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(questionnaireId, that.questionnaireId) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(answerId, that.answerId) &&
                Objects.equals(optionId, that.optionId) &&
                Objects.equals(serialNumber, that.serialNumber) &&
                Objects.equals(answerUserId, that.answerUserId) &&
                Objects.equals(titleSerialNumber, that.titleSerialNumber) &&
                Objects.equals(title, that.title) &&
                Objects.equals(type, that.type) &&
                Objects.equals(status, that.status) &&
                Objects.equals(columnTitle, that.columnTitle) &&
                Objects.equals(optionContent, that.optionContent) &&
                Objects.equals(writeContent, that.writeContent) &&
                Objects.equals(score, that.score) &&
                Objects.equals(rowNumber, that.rowNumber) &&
                Objects.equals(columnNumber, that.columnNumber) &&
                Objects.equals(updateTime, that.updateTime) &&
                Objects.equals(createTime, that.createTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, questionnaireId, userId, answerId, optionId, serialNumber, answerUserId, titleSerialNumber, title, type, status, columnTitle, optionContent, writeContent, score, rowNumber, columnNumber, updateTime, createTime);
    }
}
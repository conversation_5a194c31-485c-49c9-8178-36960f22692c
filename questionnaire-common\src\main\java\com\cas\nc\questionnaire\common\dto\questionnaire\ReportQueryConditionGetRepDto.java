package com.cas.nc.questionnaire.common.dto.questionnaire;

import java.util.Date;
import java.util.List;

/**
 * 获取测评报告查询条件响应DTO
 */
public class ReportQueryConditionGetRepDto {
    
    /**
     * 是否存在查询条件
     */
    private Boolean exists;
    
    /**
     * 问卷ID
     */
    private String questionnaireId;
    
    /**
     * 查询开始时间
     */
    private Date beginTime;
    
    /**
     * 查询结束时间
     */
    private Date endTime;
    
    /**
     * 常规性问题列表
     */
    private List<RegularQuestionItemDto> regularQuestions;
    
    /**
     * 常规性问题项
     */
    public static class RegularQuestionItemDto {
        
        /**
         * 题目ID
         */
        private Long titleId;
        
        /**
         * 题目序号
         */
        private Integer serialNumber;
        
        /**
         * 题目名称
         */
        private String name;
        
        /**
         * 校验类型：1-姓名，2-邮箱，3-手机，10-身份证号
         */
        private Integer validateType;
        
        /**
         * 校验类型名称
         */
        private String validateTypeName;
        
        public Long getTitleId() {
            return titleId;
        }
        
        public void setTitleId(Long titleId) {
            this.titleId = titleId;
        }
        
        public Integer getSerialNumber() {
            return serialNumber;
        }
        
        public void setSerialNumber(Integer serialNumber) {
            this.serialNumber = serialNumber;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public Integer getValidateType() {
            return validateType;
        }
        
        public void setValidateType(Integer validateType) {
            this.validateType = validateType;
        }
        
        public String getValidateTypeName() {
            return validateTypeName;
        }
        
        public void setValidateTypeName(String validateTypeName) {
            this.validateTypeName = validateTypeName;
        }
    }
    
    public Boolean getExists() {
        return exists;
    }
    
    public void setExists(Boolean exists) {
        this.exists = exists;
    }
    
    public String getQuestionnaireId() {
        return questionnaireId;
    }
    
    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }
    
    public Date getBeginTime() {
        return beginTime;
    }
    
    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }
    
    public Date getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    public List<RegularQuestionItemDto> getRegularQuestions() {
        return regularQuestions;
    }
    
    public void setRegularQuestions(List<RegularQuestionItemDto> regularQuestions) {
        this.regularQuestions = regularQuestions;
    }
} 
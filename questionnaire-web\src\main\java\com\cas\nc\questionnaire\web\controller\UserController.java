package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.user.*;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.UserSourceEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.sso.Ticket;
import com.cas.nc.questionnaire.common.to.ExternalUserTo;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.Base64Utils;
import com.cas.nc.questionnaire.common.utils.Constants;
import com.cas.nc.questionnaire.common.utils.EmailFormatCheckUtils;
import com.cas.nc.questionnaire.common.utils.PwdFormatCheckUtils;
import com.cas.nc.questionnaire.common.utils.EncryptAlgorithmsUtils;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.JedisUtil;
import com.cas.nc.questionnaire.common.utils.SignGenerateUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.common.utils.ValidateCodeUtil;
import com.cas.nc.questionnaire.common.utils.ValidateUtils;
import com.cas.nc.questionnaire.common.vo.user.*;
import com.cas.nc.questionnaire.dao.po.BizConfigPo;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.server.UserServer;
import com.cas.nc.questionnaire.server.mapstruct.UserConverter;
import com.cas.nc.questionnaire.service.BizConfigService;
import com.cas.nc.questionnaire.service.util.LocalCacheUtil;
import com.cas.nc.questionnaire.web.sso.LoginCheck;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.*;
import static com.cas.nc.questionnaire.common.utils.Constants.PRODUCT_ENV_MARK;
import static com.cas.nc.questionnaire.common.utils.Constants.REDIS_CODE_IMAGE_KEY;

@RestController
@RequestMapping("/questionnaire/user")
public class UserController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(UserController.class);

    @Resource
    private UserServer userServer;
    @Resource
    private LoginCheck loginCheck;
    @Resource
    private BizConfigService bizConfigService;
    @Value("${valid.encrypt.key}")
    private String encryptKey;
    @Resource
    private JedisUtil jedisUtil;
    @Value("${qst.env}")
    private String env;

    @RequestMapping(value = "/codeimage")
    public void codeImage(@ModelAttribute UserCodeImageReqVo vo) throws IOException {
        int height = vo.getHeight() == null ? Constants.IMAGE_HEIGHT : vo.getHeight();
        int width = vo.getWidth() == null ? Constants.IMAGE_WIDTH : vo.getWidth();

        String imageId = SequenceUtil.getInstance().generateImageId();
        ValidateCodeUtil code = new ValidateCodeUtil(width, height, Constants.IMAGE_CODE_COUNT, Constants.IMAGE_LINE_COUNT);

        logger.info("UserController.codeImage imageId[{}] codeValue[{}]", imageId, code.getCode());
        if (PRODUCT_ENV_MARK.equalsIgnoreCase(env)) {
            jedisUtil.set((REDIS_CODE_IMAGE_KEY + imageId).getBytes(), code.getCode().getBytes());
            jedisUtil.expire((REDIS_CODE_IMAGE_KEY + imageId).getBytes(), 60);
        } else {
            LocalCacheUtil.IMAGE_CODE_CACHE.put(imageId, code.getCode());
        }

        getHttpServletResponse().setHeader(Constants.IMAGE_ID, imageId);
        getHttpServletResponse().setContentType("image/jpeg");

        ByteArrayOutputStream imageStream = new ByteArrayOutputStream();
        code.write(imageStream);
        OutputStream os = getHttpServletResponse().getOutputStream();
        os.write(imageStream.toByteArray());
        os.flush();
        os.close();
    }

    @RequestMapping(value = "/dologin", method = {RequestMethod.POST})
    public ApiReturnResult doLogin(@RequestBody UserLoginReqVo vo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        validateParam(vo);
        validCode(vo.getImageId(), vo.getValidCode());
        UserLoginReqDto reqDto = UserConverter.INSTANCE.to(vo);
        reqDto.setUserSource(UserSourceEnum.QUESTIONNAIRE.key());

        UserLoginRepDto userLoginRespDto = userServer.doLogin(reqDto);
        String token = loginCheck.addCookie(userLoginRespDto.getTicket(), getHttpServletResponse());

        UserLoginRepVo repVo = new UserLoginRepVo();
        repVo.setToken(token);
        repVo.setType(userLoginRespDto.getTicket().getType());
        result.setData(repVo);
        return result;
    }

    @RequestMapping(value = "/domd5login", method = {RequestMethod.POST})
    public ApiReturnResult doMD5Login(@RequestBody UserLoginNoPasswordReqVo vo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        UserLoginNoPasswordReqDto reqDto = UserConverter.INSTANCE.to(vo);

        UserLoginRepDto userLoginRespDto = userServer.doLoginNoPassword(reqDto);
        String token = loginCheck.addCookie(userLoginRespDto.getTicket(), getHttpServletResponse());

        UserLoginRepVo repVo = new UserLoginRepVo();
        repVo.setToken(token);
        repVo.setType(userLoginRespDto.getTicket().getType());
        result.setData(repVo);
        return result;
    }


    @RequestMapping(value = "/loginByES", method = {RequestMethod.POST})
    public ApiReturnResult loginByES(@RequestBody UserLoginByESVo vo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        UserLoginByESReqDto reqDto = UserConverter.INSTANCE.to(vo);
        reqDto.setUserSource(UserSourceEnum.QUESTIONNAIRE.key());
        UserLoginRepDto userLoginRespDto = userServer.doEsLogin(reqDto);
        String token = loginCheck.addCookie(userLoginRespDto.getTicket(), getHttpServletResponse());
        UserLoginRepVo repVo = new UserLoginRepVo();
        repVo.setToken(token);
        repVo.setUserName(userLoginRespDto.getUserName());
        result.setData(repVo);
        return result;
    }


    @RequestMapping(value = "/modifypwd", method = {RequestMethod.POST})
    public ApiReturnResult modifyPwd(@RequestBody UserModifyPwdReqVo vo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        validateParam(vo);
        UserModifyPwdReqDto reqDto = UserConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        Ticket ticket = userServer.modifyPwd(reqDto);
        String token = loginCheck.addCookie(new Ticket(), getHttpServletResponse());

        UserLoginRepVo repVo = new UserLoginRepVo();
        repVo.setToken(token);
        result.setData(repVo);

        return result;
    }

    @RequestMapping("/register")
    public ApiReturnResult register(@RequestBody UserRegisterReqVo vo) {
        validateParam(vo);
        validCode(vo.getImageId(), vo.getValidCode());
        UserRegisterReqDto reqDto = UserConverter.INSTANCE.to(vo);

        userServer.register(reqDto);
        return new ApiReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.EMAIL_VALID.value());
    }

    @RequestMapping("/emailvalid")
    public ApiReturnResult emailValid(@RequestParam String validCode) {
        Assert.notNull(validCode, "validCode");

        byte[] bytes = Base64Utils.decodeBase64(validCode);
        String encryptData = new String(bytes);
        String userId = EncryptAlgorithmsUtils.decryptByAES(encryptKey, encryptData);

        UserEmailValidReqDto reqDto = new UserEmailValidReqDto();
        reqDto.setUserId(Long.valueOf(userId));

        userServer.emailValid(reqDto);
        return new ApiReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.VALID_SUCCESS.value());
    }

    @RequestMapping("/userinfo")
    public ApiReturnResult getUserInfo() {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        Long userId = getUserId();
        Assert.notNull(userId, CodeEnum.PLEASE_LOGIN);
        UserInfoRespDto userInfo = userServer.getUserInfo(userId);
        UserInfoRespVo respVo = UserConverter.INSTANCE.to(userInfo);

        result.setData(respVo);
        return result;
    }

    @RequestMapping("/externalusersync")
    public ApiReturnResult externalUserSync(@RequestBody ExternalUserSyncReqVo reqVo) {
        logger.info("UserController.externalUserSync param[{}]", JSONUtil.toJSONString(reqVo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        ValidateUtils.validateNotNullExclude(reqVo);
        String aesKey = validateSign(reqVo, reqVo.getBizId(), reqVo.getSign());

        String decryptUser = EncryptAlgorithmsUtils.decryptByAES(aesKey, reqVo.getUser());
        ExternalUserTo userTo = JSONUtil.parseObject(decryptUser, ExternalUserTo.class);
        ValidateUtils.validateNotNullExclude(userTo, "pwd", "email", "phone");
        if (StringUtil.isNotBlank(userTo.getEmail())) {
            Assert.isTrue(EmailFormatCheckUtils.isEmailLegal(userTo.getEmail()), EMAIL_WRONGFUL);
        }

        ExternalUserSyncReqDto reqDto = new ExternalUserSyncReqDto();
        reqDto.setUser(userTo);

        Boolean flag = userServer.externalUsersSync(reqDto);

        if (!flag) {
            result.setCode(CodeEnum.FAIL.key());
            result.setMsg(FAIL.value());
        }

        logger.info("UserController.externalUserSync result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/pwdreset/sendmail")
    public ApiReturnResult resetPwdSendMail(@RequestBody UserRestPwdSendMailReqVo vo) {
        validateParam(vo);
        validCode(vo.getImageId(), vo.getValidCode());
        UserRestPwdSendMailReqDto reqDto = UserConverter.INSTANCE.to(vo);

        userServer.resetPwdSendMail(reqDto);

        return new ApiReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.EMAIL_VALID.value());
    }

    @RequestMapping("/pwdreset/modify")
    public ApiReturnResult resetPwdModify(@RequestBody UserRestPwdModifyReqVo vo) {
        validateParam(vo);
        UserRestPwdModifyReqDto reqDto = UserConverter.INSTANCE.to(vo);

        userServer.resetPwdModify(reqDto);

        return new ApiReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.EMAIL_VALID.value());
    }

    private void validateParam(UserRestPwdModifyReqVo vo) {
        Assert.notBlank(vo.getValidCode(), VALID_CODE_NOT_NULL);
        Assert.notBlank(vo.getNewPwd(), NEW_PWD_IS_NULL);
        Assert.notBlank(vo.getConfirmNewPwd(), CONFIRM_NEW_PWD_IS_ERROR);
        Assert.isTrue(vo.getConfirmNewPwd().equalsIgnoreCase(vo.getNewPwd()), CONFIRM_NEW_PWD_NOT_MATCH);
    }

    private void validateParam(UserRestPwdSendMailReqVo vo) {
        Assert.notBlank(vo.getEmail(), EMAIL_NOT_NULL);
        Assert.notBlank(vo.getImageId(), VALID_CODE_NOT_NULL);
        Assert.notBlank(vo.getImageId(), VALID_CODE_NOT_NULL);
        Assert.isTrue(EmailFormatCheckUtils.isEmailLegal(vo.getEmail()), EMAIL_WRONGFUL);
    }

    private void validateParam(UserModifyPwdReqVo vo) {
        Assert.notBlank(vo.getOldPwd(), OLD_PWD_IS_NULL);
        Assert.notBlank(vo.getNewPwd(), NEW_PWD_IS_NULL);
        Assert.notBlank(vo.getConfirmNewPwd(), CONFIRM_NEW_PWD_IS_ERROR);
        Assert.isTrue(PwdFormatCheckUtils.isPwdLegal(vo.getNewPwdPlain()), PWF_LEGAL_ERROR);
        Assert.isTrue(vo.getConfirmNewPwd().equals(vo.getNewPwd()), CONFIRM_NEW_PWD_NOT_MATCH);
        Assert.isTrue(!vo.getNewPwd().equals(vo.getOldPwd()), OLD_PWD_NEW_PWD_IDENTICAL);
    }

    private void validateParam(UserLoginReqVo vo) {
        Assert.notNull(vo.getUserName(), CodeEnum.USER_NAME_NOT_NULL);
        Assert.notNull(vo.getPwd(), CodeEnum.PWD_NOT_NULL);
        Assert.notNull(vo.getValidCode(), CodeEnum.VALID_CODE_NOT_NULL);
        Assert.notNull(vo.getImageId(), CodeEnum.UNKNOWN_RETURN_PAGE);
    }

    private void validateParam(UserRegisterReqVo vo) {
        Assert.notNull(vo.getEmail(), CodeEnum.EMAIL_NOT_NULL);
        Assert.isTrue(EmailFormatCheckUtils.isEmailLegal(vo.getEmail()), CodeEnum.EMAIL_WRONGFUL);
        Assert.notNull(vo.getPwd(), CodeEnum.PWD_NOT_NULL);
        Assert.notNull(vo.getUserName(), CodeEnum.USER_NAME_NOT_NULL);
        Assert.notNull(vo.getValidCode(), CodeEnum.VALID_CODE_NOT_NULL);
        Assert.notNull(vo.getImageId(), CodeEnum.UNKNOWN_RETURN_PAGE);
    }

    @RequestMapping(
            value = "/subLogin",
            method = RequestMethod.POST,
            produces = "application/json;charset=utf-8")

    public String subLogin(UserInfoPo user) {

        Subject subject = SecurityUtils.getSubject();

        UsernamePasswordToken token = new UsernamePasswordToken(user.getUserName(), user.getPwd());

        try {
//            token.setRememberMe(user.isRememberMe());todo
            subject.login(token);
        } catch (Exception e) {
            return e.getMessage();
        }

        // 编码方式判断是否具有管理员身份
        if (subject.hasRole("admin")) {
            return "有admin权限";
        }

        return "无admin权限";
    }

    //通过注解设置角色权限为admin才可访问
    @RequiresRoles("admin")
    @RequestMapping(value = "/testRole", method = RequestMethod.GET)

    public String testRole() {
        return "test role success";
    }

    /**
     * 通过在service.xml配置访问权限
     */
    @RequestMapping(value = "/testRoles", method = RequestMethod.GET)

    public String testRoles() {
        return "test roles success";
    }

    @RequestMapping(value = "/testRoles1", method = RequestMethod.GET)

    public String testRoles1() {
        return "test roles1 success";
    }

    @RequestMapping(value = "/testPerms", method = RequestMethod.GET)

    public String testPerms() {
        return "test roles success";
    }

    @RequestMapping(value = "/testPerms1", method = RequestMethod.GET)

    public String testPerms1() {
        return "test roles1 success";
    }

    private void validCode(String imageId, String validCode) {
        String validCodeCache = "";
        if (PRODUCT_ENV_MARK.equalsIgnoreCase(env)) {
            Boolean exists = jedisUtil.exists((REDIS_CODE_IMAGE_KEY + imageId).getBytes());
            Assert.isTrue(exists, VALID_CODE_OUT_TIME);
            byte[] bytes = jedisUtil.get((REDIS_CODE_IMAGE_KEY + imageId).getBytes());
            validCodeCache = new String(bytes);
        } else {
            validCodeCache = LocalCacheUtil.IMAGE_CODE_CACHE.getIfPresent(imageId);
        }

        Assert.isTrue(validCode.equalsIgnoreCase(validCodeCache), CodeEnum.VALID_CODE_ERROR);
    }

    public String validateSign(Object param, Long bizId, String paramSign) {
        BizConfigPo bizConfigPo = bizConfigService.selectOne(bizId);
        Assert.notNull(bizConfigPo, BIZ_ID_NOT_EXIST);
        String sign = SignGenerateUtil.generateSignByObject(param, bizConfigPo.getMd5Key());
        Assert.isTrue(paramSign.equalsIgnoreCase(sign), SIGN_EXCEPTION);

        return bizConfigPo.getAesKey();
    }

}

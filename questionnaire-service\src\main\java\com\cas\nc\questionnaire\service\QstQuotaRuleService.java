package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstQuotaRulePo;
import com.cas.nc.questionnaire.dao.query.QstQuotaRuleQuery;

import java.util.List;

public interface QstQuotaRuleService {
    /**
     * 数据插入
     *
     * @param qstQuotaRulePo
     * @return
     */
    int insert(QstQuotaRulePo qstQuotaRulePo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstQuotaRuleQuery query);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstQuotaRulePo> selectList(QstQuotaRuleQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstQuotaRulePo selectOne(QstQuotaRuleQuery query);

    /**
     * 更新数据
     *
     * @param query
     * @return
     */
    int update(QstQuotaRuleQuery query);

    List<QstQuotaRulePo> selectList(String questionnaireId);
}

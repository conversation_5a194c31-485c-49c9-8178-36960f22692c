package com.cas.nc.questionnaire.dao.bo;

public class AnswerAnalysisOptionBo {
    /*题目序号*/
    private Integer firstTitleSerialNumber;

    private Integer firstRowNumber;

    private Integer firstColumnNumber;

    /*选项序号*/
    private Integer firstSerialNumber;

    /*题目序号*/
    private Integer secondTitleSerialNumber;

    /*选项序号*/
    private Integer secondSerialNumber;

    private Integer secondRowNumber;

    private Integer secondColumnNumber;

    /*题目序号*/
    private Integer thirdTitleSerialNumber;

    private Integer thirdRowNumber;

    private Integer thirdColumnNumber;

    /*选项序号*/
    private Integer thirdSerialNumber;

    /*总数*/
    private Integer total;

    public Integer getFirstTitleSerialNumber() {
        return firstTitleSerialNumber;
    }

    public void setFirstTitleSerialNumber(Integer firstTitleSerialNumber) {
        this.firstTitleSerialNumber = firstTitleSerialNumber;
    }

    public Integer getFirstSerialNumber() {
        return firstSerialNumber;
    }

    public void setFirstSerialNumber(Integer firstSerialNumber) {
        this.firstSerialNumber = firstSerialNumber;
    }

    public Integer getSecondTitleSerialNumber() {
        return secondTitleSerialNumber;
    }

    public void setSecondTitleSerialNumber(Integer secondTitleSerialNumber) {
        this.secondTitleSerialNumber = secondTitleSerialNumber;
    }

    public Integer getSecondSerialNumber() {
        return secondSerialNumber;
    }

    public void setSecondSerialNumber(Integer secondSerialNumber) {
        this.secondSerialNumber = secondSerialNumber;
    }

    public Integer getThirdTitleSerialNumber() {
        return thirdTitleSerialNumber;
    }

    public void setThirdTitleSerialNumber(Integer thirdTitleSerialNumber) {
        this.thirdTitleSerialNumber = thirdTitleSerialNumber;
    }

    public Integer getThirdSerialNumber() {
        return thirdSerialNumber;
    }

    public void setThirdSerialNumber(Integer thirdSerialNumber) {
        this.thirdSerialNumber = thirdSerialNumber;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getThirdRowNumber() {
        return thirdRowNumber;
    }

    public void setThirdRowNumber(Integer thirdRowNumber) {
        this.thirdRowNumber = thirdRowNumber;
    }

    public Integer getThirdColumnNumber() {
        return thirdColumnNumber;
    }

    public void setThirdColumnNumber(Integer thirdColumnNumber) {
        this.thirdColumnNumber = thirdColumnNumber;
    }

    public Integer getSecondRowNumber() {
        return secondRowNumber;
    }

    public void setSecondRowNumber(Integer secondRowNumber) {
        this.secondRowNumber = secondRowNumber;
    }

    public Integer getSecondColumnNumber() {
        return secondColumnNumber;
    }

    public void setSecondColumnNumber(Integer secondColumnNumber) {
        this.secondColumnNumber = secondColumnNumber;
    }

    public Integer getFirstRowNumber() {
        return firstRowNumber;
    }

    public void setFirstRowNumber(Integer firstRowNumber) {
        this.firstRowNumber = firstRowNumber;
    }

    public Integer getFirstColumnNumber() {
        return firstColumnNumber;
    }

    public void setFirstColumnNumber(Integer firstColumnNumber) {
        this.firstColumnNumber = firstColumnNumber;
    }
}

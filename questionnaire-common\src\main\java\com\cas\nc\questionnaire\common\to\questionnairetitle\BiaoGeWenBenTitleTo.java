package com.cas.nc.questionnaire.common.to.questionnairetitle;

import java.util.List;

public class BiaoGeWenBenTitleTo extends BaseTitleTo {
    /*行标题*/
    private String rowTitle;

    /*列标题*/
    private String colTitle;

    /*最小字数*/
    private List<String> minBoundsValue;

    /*最大限制字数*/
    private List<String> maxBoundsValue;

    /*校验类型*/
    private List<String> colTitleValidateType;

    /*高度*/
    private Integer height;

    /*宽度*/
    private Integer width;

    public String getRowTitle() {
        return rowTitle;
    }

    public void setRowTitle(String rowTitle) {
        this.rowTitle = rowTitle;
    }

    public String getColTitle() {
        return colTitle;
    }

    public void setColTitle(String colTitle) {
        this.colTitle = colTitle;
    }

    public List<String> getMinBoundsValue() {
        return minBoundsValue;
    }

    public void setMinBoundsValue(List<String> minBoundsValue) {
        this.minBoundsValue = minBoundsValue;
    }

    public List<String> getMaxBoundsValue() {
        return maxBoundsValue;
    }

    public void setMaxBoundsValue(List<String> maxBoundsValue) {
        this.maxBoundsValue = maxBoundsValue;
    }

    public List<String> getColTitleValidateType() {
        return colTitleValidateType;
    }

    public void setColTitleValidateType(List<String> colTitleValidateType) {
        this.colTitleValidateType = colTitleValidateType;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }
}

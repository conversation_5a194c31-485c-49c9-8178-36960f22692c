package com.cas.nc.questionnaire.common.vo.user;

import com.cas.nc.questionnaire.common.dto.user.UserInfoRespDto;

import java.util.List;

public class UserInfoListRespVo {

    private Integer page;

    private Integer pageSize;

    private Integer total;

    private List<UserInfoRespVo> userInfoList;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<UserInfoRespVo> getUserInfoList() {
        return userInfoList;
    }

    public void setUserInfoList(List<UserInfoRespVo> userInfoList) {
        this.userInfoList = userInfoList;
    }
}

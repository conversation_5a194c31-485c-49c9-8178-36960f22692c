package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstSmsPo;
import com.cas.nc.questionnaire.dao.query.QstSmsQuery;

import java.util.List;

public interface QstSmsService {
    /**
     * 数据插入
     *
     * @param qstSmsPo
     * @return
     */
    int insert(QstSmsPo qstSmsPo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstSmsQuery query);

    /**
     * 删除
     *
     * @param smsId
     * @param userId
     * @return
     */
    int delete(String smsId, Long userId);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstSmsPo> selectList(QstSmsQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstSmsPo selectOne(QstSmsQuery query);

    /**
     * 查询单条记录
     *
     * @param smsId
     * @param userId
     * @return
     */
    QstSmsPo selectOne(String smsId, Long userId);

    /**
     * 查询单条记录
     *
     * @param smsId
     * @return
     */
    QstSmsPo selectOne(String smsId);


    /**
     * 更新
     *
     * @param query
     * @return
     */
    int update(QstSmsQuery query);

    /**
     * 更新状态为已发送
     *
     * @param smsId
     * @return
     */
    int updateStatus2Send(String smsId);

    /**
     * 更新状态为已打开未作答
     *
     * @param smsId
     * @return
     */
    int updateStatus2Open(String smsId);

    /**
     * 更新状态为已作答
     *
     * @param smsId
     * @return
     */
    int updateStatus2Answered(String smsId);
}

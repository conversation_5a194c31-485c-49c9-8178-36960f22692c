package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.setting.SettingQuotaRuleListRepDto;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleListRepVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface QueryQuotaListConverter {
    QueryQuotaListConverter INSTANCE = Mappers.getMapper(QueryQuotaListConverter.class);

    List<SettingQuotaRuleListRepVo> to(List<SettingQuotaRuleListRepDto> repDtoList);
}
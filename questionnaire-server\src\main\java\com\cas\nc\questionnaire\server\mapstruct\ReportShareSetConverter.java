package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.setting.ReportShareSetReqDto;
import com.cas.nc.questionnaire.common.vo.setting.ReportShareSetReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ReportShareSetConverter {
    ReportShareSetConverter INSTANCE = Mappers.getMapper(ReportShareSetConverter.class);

    ReportShareSetReqDto to(ReportShareSetReqVo vo);
}
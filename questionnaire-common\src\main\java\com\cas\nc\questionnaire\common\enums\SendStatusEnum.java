package com.cas.nc.questionnaire.common.enums;

import java.lang.annotation.ElementType;


public enum SendStatusEnum {
    INIT(1, "待发送"),
    SEND(2, "已发送"),
    FAIL(3, "失败"),
    OPEN_NOT_ANSWER(4, "打开未作答"),
    ANSWERED(5, "已作答"),
    ELSE(99, "其他");

    private final Integer key;
    private final String value;


    SendStatusEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static SendStatusEnum toEnum(int key) {
        for (SendStatusEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        return ELSE;
    }

    public static boolean isSended(int key) {
        return SEND.key.intValue() == key || isOpened(key);
    }

    public static boolean isOpened(int key) {
        return OPEN_NOT_ANSWER.key.intValue() ==  key || isAnswered(key);
    }

    public static boolean isAnswered(int key) {
        return ANSWERED.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.QstSignSettingDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstSignSettingPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="sign_type" jdbcType="INTEGER" property="signType"/>
        <result column="location_limit" jdbcType="INTEGER" property="locationLimit"/>
        <result column="latitude" jdbcType="DECIMAL" property="latitude"/>
        <result column="longitude" jdbcType="DECIMAL" property="longitude"/>
        <result column="location_range" jdbcType="INTEGER" property="locationRange"/>
        <result column="location_name" jdbcType="VARCHAR" property="locationName"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="sign_frequency" jdbcType="INTEGER" property="signFrequency"/>
        <result column="success_tip" jdbcType="VARCHAR" property="successTip"/>
        <result column="fail_tip" jdbcType="VARCHAR" property="failTip"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="sql_columns">
        id,questionnaire_id,user_id,sign_type,location_limit,latitude,longitude,location_range,location_name,
        begin_time,end_time,sign_frequency,success_tip,fail_tip,status,update_time,create_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from qst_sign_setting
        where id = #{id}
    </select>

    <select id="selectOne" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from qst_sign_setting
        where id = #{item.id}
    </select>

    <select id="selectByQuestionnaireId" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from qst_sign_setting
        where questionnaire_id = #{questionnaireId}
    </select>

    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        insert into qst_sign_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.signType">sign_type,</if>
            <if test="null != item.locationLimit">location_limit,</if>
            <if test="null != item.latitude">latitude,</if>
            <if test="null != item.longitude">longitude,</if>
            <if test="null != item.locationRange">location_range,</if>
            <if test="null != item.locationName">location_name,</if>
            <if test="null != item.beginTime">begin_time,</if>
            <if test="null != item.endTime">end_time,</if>
            <if test="null != item.signFrequency">sign_frequency,</if>
            <if test="null != item.successTip">success_tip,</if>
            <if test="null != item.failTip">fail_tip,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.signType">#{item.signType},</if>
            <if test="null != item.locationLimit">#{item.locationLimit},</if>
            <if test="null != item.latitude">#{item.latitude},</if>
            <if test="null != item.longitude">#{item.longitude},</if>
            <if test="null != item.locationRange">#{item.locationRange},</if>
            <if test="null != item.locationName">#{item.locationName},</if>
            <if test="null != item.beginTime">#{item.beginTime},</if>
            <if test="null != item.endTime">#{item.endTime},</if>
            <if test="null != item.signFrequency">#{item.signFrequency},</if>
            <if test="null != item.successTip">#{item.successTip},</if>
            <if test="null != item.failTip">#{item.failTip},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>
    </insert>

    <update id="update">
        update qst_sign_setting
        <set>
            <if test="null != item.signType">sign_type = #{item.signType},</if>
            <if test="null != item.locationLimit">location_limit = #{item.locationLimit},</if>
            <if test="null != item.latitude">latitude = #{item.latitude},</if>
            <if test="null != item.longitude">longitude = #{item.longitude},</if>
            <if test="null != item.locationRange">location_range = #{item.locationRange},</if>
            <if test="null != item.locationName">location_name = #{item.locationName},</if>
            <if test="null != item.beginTime">begin_time = #{item.beginTime},</if>
            <if test="null != item.endTime">end_time = #{item.endTime},</if>
            <if test="null != item.signFrequency">sign_frequency = #{item.signFrequency},</if>
            <if test="null != item.successTip">success_tip = #{item.successTip},</if>
            <if test="null != item.failTip">fail_tip = #{item.failTip},</if>
            <if test="null != item.status">status = #{item.status},</if>
            <if test="null != item.updateTime">update_time = #{item.updateTime},</if>
        </set>
        where id = #{item.id}
    </update>

    <update id="updateByQuestionnaireId">
        update qst_sign_setting
        <set>
            <if test="null != item.signType">sign_type = #{item.signType},</if>
            <if test="null != item.locationLimit">location_limit = #{item.locationLimit},</if>
            <if test="null != item.latitude">latitude = #{item.latitude},</if>
            <if test="null != item.longitude">longitude = #{item.longitude},</if>
            <if test="null != item.locationRange">location_range = #{item.locationRange},</if>
            <if test="null != item.locationName">location_name = #{item.locationName},</if>
            <if test="null != item.beginTime">begin_time = #{item.beginTime},</if>
            <if test="null != item.endTime">end_time = #{item.endTime},</if>
            <if test="null != item.signFrequency">sign_frequency = #{item.signFrequency},</if>
            <if test="null != item.successTip">success_tip = #{item.successTip},</if>
            <if test="null != item.failTip">fail_tip = #{item.failTip},</if>
            <if test="null != item.status">status = #{item.status},</if>
            <if test="null != item.updateTime">update_time = #{item.updateTime},</if>
        </set>
        where questionnaire_id = #{item.questionnaireId}
    </update>
</mapper> 
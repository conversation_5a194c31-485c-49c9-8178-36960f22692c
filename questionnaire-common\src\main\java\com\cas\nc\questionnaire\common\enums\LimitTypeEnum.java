package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;


public enum LimitTypeEnum {
    NO_TIME_LIMIT(1, "不限时"),
    HOURLY(2, "每小时"),
    DAILY(3, "每天"),
    <PERSON>EE<PERSON>L<PERSON>(4, "每周"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(5, "每月"),
    ANNUALLY(6, "每年"),
    <PERSON>LSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    LimitTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static LimitTypeEnum toEnum(int key) {
        for (LimitTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.DATA_EXCEPTION);
    }

    public static boolean isNoTimeLimit(int key) {
        return NO_TIME_LIMIT.key.intValue() == key;
    }

    public static boolean isHourly(int key) {
        return HOURLY.key.intValue() == key;
    }

    public static boolean isDaily(int key) {
        return DAILY.key.intValue() == key;
    }

    public static boolean isWeekly(int key) {
        return WEEKLY.key.intValue() == key;
    }

    public static boolean isMonthly(int key) {
        return MONTHLY.key.intValue() == key;
    }

    public static boolean isAnnually(int key) {
        return ANNUALLY.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.utils.Assert;

public enum DownloadExcelTypeEnum {
    DOWNLOAD_OPTION(1, "下载答卷选项"),
    DOWNLOAD_TEXT(2, "下载答卷文本"),
    ;
    private final Integer key;
    private final String value;

    DownloadExcelTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static DownloadExcelTypeEnum toEnum(int key) {
        for (DownloadExcelTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.DATA_EXCEPTION);
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

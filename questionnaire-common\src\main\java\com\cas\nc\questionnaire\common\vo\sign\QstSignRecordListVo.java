package com.cas.nc.questionnaire.common.vo.sign;

import java.util.Date;

/**
 * 签到列表项
 */
public class QstSignRecordListVo {

    /**
     * 签到记录ID
     */
    private Long id;

    /**
     * 签到ID
     */
    private String signId;
    
    /**
     * 微信用户唯一标识
     */
    private String openid;
    
    /**
     * 姓名（优先显示问卷中填写的姓名，若没有则显示微信昵称）
     */
    private String name;
    
    /**
     * 邮箱（问卷中填写的邮箱）
     */
    private String email;
    
    /**
     * 手机号（问卷中填写的手机号）
     */
    private String mobile;
    
    /**
     * 签到时间
     */
    private Date signTime;
    
    /**
     * 签到日期
     */
    private Date signDate;
    
    /**
     * 微信昵称
     */
    private String nickname;
    
    /**
     * 微信头像URL
     */
    private String headimgurl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSignId() {
        return signId;
    }

    public void setSignId(String signId) {
        this.signId = signId;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadimgurl() {
        return headimgurl;
    }

    public void setHeadimgurl(String headimgurl) {
        this.headimgurl = headimgurl;
    }
} 
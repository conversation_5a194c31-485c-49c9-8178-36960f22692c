-- 修改answer_info表，添加openid字段和索引
ALTER TABLE answer_info_0 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_0 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_1 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_1 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_2 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_2 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_3 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_3 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_4 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_4 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_5 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_5 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_6 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_6 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_7 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_7 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_8 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_8 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_9 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_9 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_10 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_10 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_11 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_11 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_12 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_12 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_13 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_13 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_14 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_14 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_15 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_15 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_16 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_16 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_17 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_17 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_18 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_18 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_19 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_19 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_20 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_20 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_21 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_21 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_22 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_22 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_23 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_23 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_24 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_24 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_25 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_25 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_26 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_26 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_27 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_27 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_28 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_28 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_29 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_29 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_30 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_30 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_31 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_31 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_32 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_32 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_33 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_33 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_34 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_34 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_35 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_35 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_36 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_36 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_37 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_37 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_38 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_38 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_39 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_39 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_40 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_40 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_41 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_41 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_42 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_42 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_43 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_43 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_44 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_44 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_45 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_45 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_46 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_46 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_47 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_47 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_48 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_48 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_49 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_49 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_50 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_50 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_51 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_51 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_52 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_52 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_53 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_53 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_54 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_54 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_55 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_55 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_56 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_56 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_57 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_57 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_58 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_58 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_59 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_59 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_60 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_60 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_61 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_61 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_62 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_62 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_63 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_63 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_64 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_64 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_65 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_65 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_66 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_66 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_67 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_67 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_68 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_68 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_69 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_69 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_70 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_70 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_71 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_71 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_72 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_72 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_73 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_73 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_74 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_74 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_75 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_75 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_76 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_76 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_77 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_77 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_78 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_78 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_79 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_79 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_80 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_80 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_81 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_81 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_82 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_82 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_83 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_83 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_84 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_84 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_85 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_85 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_86 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_86 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_87 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_87 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_88 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_88 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_89 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_89 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_90 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_90 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_91 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_91 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_92 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_92 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_93 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_93 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_94 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_94 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_95 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_95 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_96 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_96 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_97 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_97 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_98 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_98 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_99 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_99 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_100 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_100 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_101 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_101 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_102 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_102 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_103 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_103 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_104 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_104 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_105 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_105 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_106 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_106 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_107 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_107 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_108 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_108 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_109 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_109 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_110 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_110 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_111 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_111 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_112 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_112 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_113 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_113 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_114 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_114 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_115 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_115 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_116 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_116 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_117 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_117 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_118 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_118 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_119 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_119 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_120 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_120 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_121 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_121 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_122 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_122 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_123 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_123 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_124 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_124 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_125 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_125 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_126 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_126 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';
ALTER TABLE answer_info_127 ADD COLUMN openid varchar(64) DEFAULT NULL COMMENT '微信用户唯一标识' AFTER answer_user_id;
ALTER TABLE answer_info_127 ADD INDEX idx_openid (openid) COMMENT '微信openid索引';

-- 创建签到设置表
CREATE TABLE `qst_sign_setting` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `questionnaire_id` varchar(64) NOT NULL COMMENT '问卷ID',
  `user_id` bigint(20) NOT NULL COMMENT '问卷创建者用户ID',
  `sign_type` tinyint(1) DEFAULT 1 COMMENT '签到方式 1-微信认证签到',
  `location_limit` tinyint(1) DEFAULT 0 COMMENT '是否限制签到位置 0-不限制 1-限制',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '签到位置纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '签到位置经度',
  `location_range` int(11) DEFAULT NULL COMMENT '位置范围(米)',
  `begin_time` datetime DEFAULT NULL COMMENT '签到开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '签到截止时间',
  `sign_frequency` tinyint(1) DEFAULT 1 COMMENT '签到频率 1-只签到一次 2-每日签到一次',
  `success_tip` varchar(255) DEFAULT NULL COMMENT '签到成功提示信息',
  `fail_tip` varchar(255) DEFAULT NULL COMMENT '签到失败提示信息',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_questionnaire_id` (`questionnaire_id`) COMMENT '问卷ID唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问卷签到设置表';



CREATE TABLE `qst_report_query_condition` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `questionnaire_id` VARCHAR(64) NOT NULL COMMENT '问卷ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '设置人用户ID',
  `begin_time` DATETIME DEFAULT NULL COMMENT '查询开始时间',
  `end_time` DATETIME DEFAULT NULL COMMENT '查询结束时间',
  `is_enabled` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  `update_time` DATETIME DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_questionnaire_id` (`questionnaire_id`) COMMENT '问卷ID唯一索引'
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='测评报告查询条件表';



CREATE TABLE `qst_report_query_condition_title` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `condition_id` BIGINT(20) NOT NULL COMMENT '关联的条件ID',
    `title_id` BIGINT(20) NOT NULL COMMENT '题目ID',
    `serial_number` INT(11) NOT NULL COMMENT '题目序号',
    `validate_type` INT(11) NOT NULL COMMENT '校验类型：1-姓名，2-邮箱，3-手机，10-身份证号',
    PRIMARY KEY (`id`),
    KEY `idx_condition_id` (`condition_id`) COMMENT '条件ID索引',
    KEY `idx_title_id` (`title_id`) COMMENT '题目ID索引'
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='测评报告查询条件题目表';






ALTER TABLE `questionnaire`.`answer_info_0` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_1` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_2` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_3` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_4` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_5` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_6` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_7` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_8` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_9` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';

ALTER TABLE `questionnaire`.`answer_info_10` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_11` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_12` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_13` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_14` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_15` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_16` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_17` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_18` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_19` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';


ALTER TABLE `questionnaire`.`answer_info_20` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_21` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_22` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_23` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_24` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_25` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_26` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_27` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_28` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_29` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';

ALTER TABLE `questionnaire`.`answer_info_30` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_31` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_32` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_33` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_34` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_35` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_36` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_37` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_38` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_39` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';

ALTER TABLE `questionnaire`.`answer_info_40` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_41` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_42` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_43` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_44` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_45` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_46` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_47` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_48` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_49` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';


ALTER TABLE `questionnaire`.`answer_info_50` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_51` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_52` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_53` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_54` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_55` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_56` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_57` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_58` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_59` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';

ALTER TABLE `questionnaire`.`answer_info_60` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_61` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_62` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_63` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_64` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_65` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_66` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_67` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_68` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_69` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';

ALTER TABLE `questionnaire`.`answer_info_70` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_71` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_72` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_73` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_74` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_75` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_76` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_77` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_78` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_79` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';


ALTER TABLE `questionnaire`.`answer_info_80` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_81` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_82` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_83` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_84` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_85` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_86` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_87` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_88` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_89` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';

ALTER TABLE `questionnaire`.`answer_info_90` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_91` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_92` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_93` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_94` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_95` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_96` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_97` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_98` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_99` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';

ALTER TABLE `questionnaire`.`answer_info_100` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_101` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_102` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_103` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_104` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_105` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_106` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_107` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_108` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_109` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';


ALTER TABLE `questionnaire`.`answer_info_110` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_111` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_112` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_113` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_114` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_115` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_116` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_117` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_118` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_119` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';



ALTER TABLE `questionnaire`.`answer_info_120` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_121` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_122` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_123` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_124` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_125` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_126` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';
ALTER TABLE `questionnaire`.`answer_info_127` CHANGE `begin_time` `begin_time` DATETIME NULL COMMENT '答题开始时间', CHANGE `end_time` `end_time` DATETIME NULL COMMENT '答题结束时间';


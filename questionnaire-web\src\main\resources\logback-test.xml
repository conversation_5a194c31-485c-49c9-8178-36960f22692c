<?xml version="1.0" encoding="UTF-8"?>
<!-- 根节点，不进行定时扫描 -->
<configuration scan="false">
    <property name="appName" value="questionnaire"/>
    <property name="filePrefix" value="logs"></property>
    <!-- 上下文名字 -->
    <contextName>${appName}</contextName>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%-5level] [%logger.%method-%line] [%msg]%n</pattern>
        </encoder>
    </appender>
    <appender name="daily-info-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${filePrefix}/${appName}-info.log</file>
        <!-- 追加 -->
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${filePrefix}/${appName}-info.%d{yyyyMMdd}.log</fileNamePattern>
        </rollingPolicy>
        <!-- 日志格式化 -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%-5level] [%msg]%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>


    <appender name="daily-error-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${filePrefix}/${appName}-error.log</file>
        <!-- 追加 -->
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${filePrefix}/${appName}-error.%d{yyyyMMdd}.log</fileNamePattern>
        </rollingPolicy>
        <!-- 日志格式化 -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%-5level] [%msg]%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 性能日志appender -->
    <appender name="perf4jFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${filePrefix}/questionnaire-perf4j_%d{yyyyMMdd}.log</FileNamePattern>
            <MaxHistory>365</MaxHistory>
        </rollingPolicy>
        <encoder>
            <Pattern>%msg%n</Pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="CoalescingStatistics" class="org.perf4j.logback.AsyncCoalescingStatisticsAppender">
        <param name="TimeSlice" value="60000"/>
        <appender-ref ref="perf4jFile"/>
    </appender>

    <!-- 性能日志 -->
    <logger name="org.perf4j.TimingLogger" level="debug" additivity="false">
        <appender-ref ref="CoalescingStatistics"/>
    </logger>

    <!-- project default level -->
    <logger name="org.springframework" level="DEBUG"/>
    <logger name="com.cas.nc.questionnaire.dao" level="DEBUG"/>
    <root level="ALL">
        <appender-ref ref="console"/>
        <appender-ref ref="daily-info-log"/>
        <appender-ref ref="daily-error-log"/>
        <appender-ref ref="PERFORM"/>
    </root>
</configuration>
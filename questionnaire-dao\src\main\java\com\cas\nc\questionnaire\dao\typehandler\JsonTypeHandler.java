package com.cas.nc.questionnaire.dao.typehandler;

import com.cas.nc.questionnaire.common.utils.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public abstract class JsonTypeHandler<T> extends BaseTypeHandler<T> {

    private Class<T> type;

    JsonTypeHandler(Class<T> type) {
        if (type == null) throw new IllegalArgumentException("Type argument cannot be null");

        this.type = type;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSONUtil.toJSONString(parameter));
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String valueStr = rs.getString(columnName);
        if (StringUtils.isBlank(valueStr)) {
            return null;
        }

        return JSONUtil.parseObject(valueStr, type);
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String valueStr = rs.getString(columnIndex);

        if (StringUtils.isBlank(valueStr)) {
            return null;
        }

        return JSONUtil.parseObject(valueStr, type);
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String valueStr = cs.getString(columnIndex);

        if (StringUtils.isBlank(valueStr)) {
            return null;
        }

        return JSONUtil.parseObject(valueStr, type);
    }
}
package com.cas.nc.questionnaire.common.utils;

import java.util.List;

public class PaginateUtils<T> {
    /*当前页*/
    private int currentPage = 1;
    /*每页大小*/
    private int pageSize = 10;
    /*总页数*/
    private int pageTotal;
    /*总条数*/
    private int recordTotal = 0;
    /*前一页*/
    private int previousPage;
    /*下一页*/
    private int nextPage;
    /*第一页*/
    private int firstPage = 1;
    /*最后一页*/
    private int lastPage;
    /*每页的内容*/
    private List<T> data;
    /*开始索引*/
    private int startIndex;
    /*结束索引*/
    private int endIndex;

    public int getStartIndex() {
        return (this.currentPage - 1) * this.pageSize;
    }

    public int getEndIndex() {
        int end = this.getStartIndex() + this.getPageSize();  //不包含最后一条记录-1
        if (end > this.getRecordTotal()) {
            end = this.getStartIndex() + (this.getRecordTotal() % this.getPageSize());
        }
        this.endIndex = end;
        return this.endIndex;

    }

    public PaginateUtils() {
    }

    public PaginateUtils(int currentPage, int pageSize) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
    }

    // 以下set方式是需要赋值的

    /**
     * 设置当前页
     *
     * @param currentPage
     */
    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    /**
     * 设置每页大小,也可以不用赋值,默认大小为10条
     *
     * @param pageSize
     */
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 设置总条数,默认为0 <br>
     *
     * @param recordTotal
     */
    public void setRecordTotal(int recordTotal) {
        this.recordTotal = recordTotal;
        otherAttr();
    }

    /**
     * 设置分页内容 <br>
     *
     * @param data
     */
    public void setData(List<T> data) {
        this.data = data;
    }

    /**
     * 设置其他参数
     */
    public void otherAttr() {
        // 总页数
        this.pageTotal = this.recordTotal % this.pageSize > 0 ? this.recordTotal / this.pageSize + 1 : this.recordTotal / this.pageSize;
        // 第一页
        this.firstPage = 1;
        // 最后一页
        this.lastPage = this.pageTotal;
        // 前一页
        if (this.currentPage > 1) {
            this.previousPage = this.currentPage - 1;
        } else {
            this.previousPage = this.firstPage;
        }
        // 下一页
        if (this.currentPage < this.lastPage) {
            this.nextPage = this.currentPage + 1;
        } else {
            this.nextPage = this.lastPage;
        }
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public int getPageSize() {
        return pageSize;
    }

    public int getPageTotal() {
        return pageTotal;
    }

    public int getRecordTotal() {
        return recordTotal;
    }

    public int getPreviousPage() {
        return previousPage;
    }

    public int getNextPage() {
        return nextPage;
    }

    public int getFirstPage() {
        return firstPage;
    }

    public int getLastPage() {
        return lastPage;
    }

    public List<T> getData() {
        return data;
    }
}

package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.GlobalConfigEnum;
import com.cas.nc.questionnaire.common.enums.YnEnum;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.dao.nosharddao.GlobalConfigDao;
import com.cas.nc.questionnaire.dao.po.GlobalConfigPo;
import com.cas.nc.questionnaire.dao.query.GlobalConfigQuery;
import com.cas.nc.questionnaire.service.GlobalConfigService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class GlobalConfigServiceImpl implements GlobalConfigService {
    @Resource
    private GlobalConfigDao globalConfigDao;

    @Override
    public Integer selectAnswerLimit() {
        GlobalConfigQuery query = new GlobalConfigQuery();
        query.setYn(YnEnum.Y.key());
        query.setType(GlobalConfigEnum.ANSWER_LIMIT.key());

        List<GlobalConfigPo> list = globalConfigDao.selectList(query);
        List<GlobalConfigPo> resultList = SafeUtil.of(list).stream().sorted(Comparator.comparing(GlobalConfigPo::getUpdateTime).reversed()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(resultList)) {
            GlobalConfigPo globalConfigPo = resultList.get(0);
            return Integer.valueOf(globalConfigPo.getValue());
        }

        return null;
    }

    @Override
    public void setAnswerLimit(Integer answerLimit) {
        GlobalConfigQuery query = new GlobalConfigQuery();
        query.setYn(YnEnum.Y.key());
        query.setType(GlobalConfigEnum.ANSWER_LIMIT.key());

        List<GlobalConfigPo> list = globalConfigDao.selectList(query);
        List<GlobalConfigPo> resultList = SafeUtil.of(list).stream().sorted(Comparator.comparing(GlobalConfigPo::getUpdateTime).reversed()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(resultList)) {
            GlobalConfigPo globalConfigPo = resultList.get(0);

            GlobalConfigQuery updateQuery = new GlobalConfigQuery();
            updateQuery.setId(globalConfigPo.getId());
            updateQuery.setValue(String.valueOf(answerLimit));
            globalConfigDao.update(updateQuery);
        }
    }
}

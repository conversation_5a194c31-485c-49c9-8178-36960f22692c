package com.cas.nc.questionnaire.server.impl;

import com.alibaba.fastjson.JSONObject;
import com.cas.nc.questionnaire.common.dto.file.FileRepDto;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.vo.file.FileRepVo;
import com.cas.nc.questionnaire.dao.po.FileRelationPo;
import com.cas.nc.questionnaire.server.FileServer;
import com.cas.nc.questionnaire.server.util.NginxTool;
import com.cas.nc.questionnaire.service.FileRelationService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.UNKNOWN_RETURN_PAGE;

@Component
public class FileServerImpl implements FileServer {

    private final static Logger logger = LoggerFactory.getLogger(FileServerImpl.class);

    @Value("${file.path}")
    private String filePatch;
    @Value("${bg.file.path}")
    private String bgFilePath;

    @Resource
    private FileRelationService fileRelationService;

    private FileRelationPo uploadFile(MultipartFile file) {
//        FileRelationPo fileRepDto = new FileRelationPo();
        if (file != null) {
            // 原始文件名
            String originalFileName = file.getOriginalFilename();
            String fileName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
            // 获取图片后缀
            String suffix = originalFileName.substring(originalFileName.lastIndexOf("."));
            String fileId = SequenceUtil.getInstance().generateFileId();
            InputStream inputStream = null;

            try {
                byte[] byteArr = file.getBytes();
                inputStream = new ByteArrayInputStream(byteArr);
                Map<String, Object> fileMap = NginxTool.uploadFile(inputStream, originalFileName);
                logger.info("FileServerImpl.upload fileMap[{}]", fileMap);
                Assert.notNull(fileMap, UNKNOWN_RETURN_PAGE);
                Object state = fileMap.get("status");
                Assert.notNull(state, UNKNOWN_RETURN_PAGE);
                Assert.isTrue((boolean) state, UNKNOWN_RETURN_PAGE);

                String url = (String) fileMap.get("url");
                Assert.notBlank(url, UNKNOWN_RETURN_PAGE);

                FileRelationPo po = new FileRelationPo();
                po.setFileId(fileId);
                po.setFileName(fileName);
                po.setFileSuffix(suffix);
                po.setFileUrl(url);

                return po;
            } catch (IOException e) {
                throw new ServerException(e);
            } finally {
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        throw new ServerException(e);
                    }
                }
            }
        }
        return null;
    }
    @Override
    public FileRepDto upload(MultipartFile file) {
        FileRepDto repDto = new FileRepDto();
        if (file != null) {
            FileRelationPo fileRepDto = uploadFile(file);
            repDto.setFileId(fileRepDto.getFileId());
            repDto.setFileName(fileRepDto.getFileName());
            repDto.setFileSuffix(fileRepDto.getFileSuffix());

            fileRelationService.insert(fileRepDto);
        }
        return repDto;
    }

    @Override
    public FileRepDto uploadBase64(String base64Img) {
        if(StringUtils.isBlank(base64Img)) {
            return null;
        }
        if(StringUtils.contains(base64Img, ",")) {
            base64Img = StringUtils.substring(base64Img, base64Img.indexOf(",") + 1);
        }
        String fileName = UUID.randomUUID().toString().replace("-", "") + ".png";
        String savePath = filePatch + fileName;

        byte[] imageBytes = Base64.getDecoder().decode(base64Img);
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byteArrayInputStream = new ByteArrayInputStream(imageBytes);
            BufferedImage bufferedImage = ImageIO.read(byteArrayInputStream);

            File file = new File(savePath);
            ImageIO.write(bufferedImage, "png", file);
        } catch (IOException e) {
            throw new ServerException(e);
        } finally {
            if(byteArrayInputStream != null) {
                try {
                    byteArrayInputStream.close();
                } catch (IOException e) {
                    throw new ServerException(e);
                }
            }
        }

        FileRepDto fileRepDto = new FileRepDto();
        fileRepDto.setFileName(savePath);
        return fileRepDto;
    }

    @Override
    public List<String> findBackgroundImgList() {
        File bgFolder = new File(bgFilePath);
        if(!bgFolder.exists()) return null;
        File[] fileList = bgFolder.listFiles();
        if(fileList == null || fileList.length == 0) return null;

        List<String> bgFileNames = new ArrayList<>();
        for(File file: fileList) {
            if(file.isFile()) {
                String path = file.getAbsolutePath();
                bgFileNames.add("/bg/" + StringUtils.replace(path, bgFilePath, ""));
            }
        }
        Collections.sort(bgFileNames, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.compareTo(o2);
            }
        });
        return bgFileNames;
    }

    @Override
    public FileRepDto upload(MultipartFile file, String questionnaireId) {
        if(file == null) return null;
        FileRelationPo fileRelationPo = uploadFile(file);
//        fileRelationPo.setQuestionnaireId(questionnaireId);
//        fileRelationService.insert(fileRelationPo);

        FileRepDto repDto = new FileRepDto();
        repDto.setFileId(fileRelationPo.getFileId());
        repDto.setFileName(fileRelationPo.getFileName());
        repDto.setFileSuffix(fileRelationPo.getFileSuffix());
        repDto.setFileUrl(fileRelationPo.getFileUrl());
        return repDto;
    }
}

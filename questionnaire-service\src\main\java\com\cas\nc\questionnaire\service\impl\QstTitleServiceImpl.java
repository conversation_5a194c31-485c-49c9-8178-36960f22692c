package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.cas.nc.questionnaire.dao.query.QstTitleQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstTitleDao;
import com.cas.nc.questionnaire.service.QstTitleService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class QstTitleServiceImpl implements QstTitleService {
    private static Logger logger = LoggerFactory.getLogger(QstTitleServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstTitleDao qstTitleDao;

    @Override
    public int insert(QstTitlePo qstTitlePo) {
        Assert.notNull(qstTitlePo.getUserId(), "userId");
        return qstTitleDao.insert(qstTitlePo);
    }

    @Override
    public int delete(QstTitleQuery query) {
        Assert.notNull(query.getUserId(), "userId");
        return qstTitleDao.delete(query);
    }

    @Override
    public int delete(String questionnaireId, Long userId) {
        Assert.notNull(userId, "userId");
        QstTitleQuery query = new QstTitleQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        return delete(query);
    }

    @Override
    public List<QstTitlePo> selectList(QstTitleQuery query) {
        Assert.notNull(query.getUserId(), "userId");
        return qstTitleDao.selectList(query);
    }

    @Override
    public QstTitlePo selectOne(QstTitleQuery query) {
        Assert.notNull(query.getUserId(), "userId");
        return qstTitleDao.selectOne(query);
    }

    @Override
    public List<QstTitlePo> selectList(String questionnaireId) {
        QstTitleQuery query = new QstTitleQuery();
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);
        return selectList(query);
    }

    @Override
    public List<QstTitlePo> selectList(String questionnaireId, Long userId) {
        Assert.notNull(userId, "userId");
        QstTitleQuery query = new QstTitleQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        return selectList(query);
    }

    @Override
    public int selectCount(QstTitleQuery query) {
        filterCondition(query);
        return qstTitleDao.selectCount(query);
    }

    @Override
    public int selectCount(String questionnaireId, Long userId) {
        QstTitleQuery query = new QstTitleQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);

        return selectCount(query);
    }

    @Override
    public int insertUpdate(QstTitlePo qstTitlePo) {
        Assert.notNull(qstTitlePo.getUserId(), "userId");
        return qstTitleDao.insertUpdate(qstTitlePo);
    }

    @Override
    public void copy(QstTitleQuery titleQuery) {
        List<QstTitlePo> list = selectList(titleQuery);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(v -> {
                v.setId(null);
                v.setQuestionnaireId(titleQuery.getNewQuestionnaireId());
                insert(v);
            });
        }
    }

    private void filterCondition(QstTitleQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

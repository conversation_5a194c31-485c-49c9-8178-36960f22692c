package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.mylist.CreateExternalQuestionnaireRepDto;
import com.cas.nc.questionnaire.common.dto.mylist.CreateExternalQuestionnaireReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.ExternalListRepDto;
import com.cas.nc.questionnaire.common.dto.templet.ConvertTempletReqDto;
import com.cas.nc.questionnaire.common.utils.DateMapper;
import com.cas.nc.questionnaire.common.vo.mylist.CreateExternalQuestionnaireRepVo;
import com.cas.nc.questionnaire.common.vo.mylist.ExternalListRepVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = DateMapper.class)
public interface ExternalListConverter {
    ExternalListConverter INSTANCE = Mappers.getMapper(ExternalListConverter.class);

    List<ExternalListRepVo> to(List<ExternalListRepDto> repDtoList);

    CreateExternalQuestionnaireRepVo to(CreateExternalQuestionnaireRepDto repDto);

    ConvertTempletReqDto to(CreateExternalQuestionnaireReqDto reqDto);
}
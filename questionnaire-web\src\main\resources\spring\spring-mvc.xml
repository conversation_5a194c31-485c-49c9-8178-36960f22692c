<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/mvc
                           http://www.springframework.org/schema/mvc/spring-mvc.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">


    <mvc:annotation-driven/>

    <!-- 扫描controller -->
    <context:component-scan base-package="com.cas.nc.questionnaire"/>
    <context:property-placeholder location="classpath:config/*.properties" ignore-unresolvable="true"/>
    <aop:aspectj-autoproxy proxy-target-class="true" />
    <bean id="externalApiValidateAspect" class="com.cas.nc.questionnaire.web.interceptor.ExternalApiValidateAspect" />
    <!--&lt;!&ndash; 对模型视图名称的解析，即在模型视图名称添加前后缀 &ndash;&gt;-->
    <!--<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver" p:prefix="/WEB-INF/view/" p:suffix=".jsp" />-->
    <!--<mvc:resources mapping="/**" location="/"/>-->

    <bean id="viewResolver"
          class="org.springframework.web.servlet.view.velocity.VelocityViewResolver">
        <property name="cache" value="false"/>
        <property name="prefix" value=""/>
        <property name="suffix" value=".vm"/>
        <property name="contentType" value="text/html;charset=utf-8"/>
        <property name="toolboxConfigLocation" value="/WEB-INF/tool.xml"/>
        <property name="exposeSpringMacroHelpers" value="true"/>
        <property name="exposeRequestAttributes" value="true"/>
        <property name="exposeSessionAttributes" value="true"/>
        <property name="allowSessionOverride" value="true"/>
        <property name="allowRequestOverride" value="true"/>
    </bean>

    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="viewClass"
                  value="org.springframework.web.servlet.view.JstlView"/>
        <property name="prefix" value="/WEB-INF/jsp/"/>
        <property name="suffix" value=".jsp"/>
    </bean>


    <bean id="jsonConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter"/>
    <bean id="stringConverter" class="org.springframework.http.converter.StringHttpMessageConverter">
        <property name="supportedMediaTypes">
            <list>
                <value>text/html;charset=UTF-8</value>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter">
        <property name="messageConverters">
            <list>
                <ref bean="stringConverter"/>
                <ref bean="jsonConverter"/>
            </list>
        </property>
    </bean>
    <mvc:default-servlet-handler/>

    <mvc:interceptors>
        <mvc:interceptor>
            <!--
                /**的意思是所有文件夹及里面的子文件夹
                /*是所有文件夹，不含子文件夹
                /是web项目的根目录
              -->
            <mvc:mapping path="/**" />

            <!-- 首页不进行拦截 -->
            <mvc:exclude-mapping path="index.jsp" />
            <mvc:exclude-mapping path="/js/**" />
            <mvc:exclude-mapping path="/css/**" />
            <!-- 需排除拦截的地址 -->
            <mvc:exclude-mapping path="/questionnaire/user/login"/>
            <mvc:exclude-mapping path="/questionnaire/user/domd5login"/>
            <mvc:exclude-mapping path="/questionnaire/user/register"/>
            <bean id="loginInterceptor" class="com.cas.nc.questionnaire.web.interceptor.LoginInterceptor"/>
        </mvc:interceptor>

        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.cas.nc.questionnaire.web.interceptor.FileUploadInterceptor">
                <!-- 设定限制的文件上传大小 -->
                <property name="maxSize" value="5242880"/>
            </bean>
        </mvc:interceptor>

        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.cas.nc.questionnaire.web.interceptor.FileTypeInterceptor">
                <property name="suffixList" value="jpg,png,gif,tif,bmp,dwg,zip,rar,xls,doc,pdf"/>
            </bean>
        </mvc:interceptor>

        <!-- 当设置多个拦截器时，先按顺序调用preHandle方法，然后逆序调用每个拦截器的postHandle和afterCompletion方法  -->
        <!--        <mvc:interceptor>-->
        <!--            <mvc:mapping path="/**" />-->
        <!--            &lt;!&ndash; 首页不进行拦截 &ndash;&gt;-->
        <!--            <mvc:exclude-mapping path="index.jsp" />-->
        <!--            <mvc:exclude-mapping path="/js/**" />-->
        <!--            <mvc:exclude-mapping path="/css/**" />-->
        <!--            &lt;!&ndash; 需排除拦截的地址 &ndash;&gt;-->
        <!--            <mvc:exclude-mapping path="/questionnaire/user/login"/>-->
        <!--            <mvc:exclude-mapping path="/questionnaire/user/register"/>-->
        <!--            <bean id="gradeIntercept" class="com.cas.nc.questionnaire.web.interceptor.GradeIntercept"/>-->
        <!--        </mvc:interceptor>-->
    </mvc:interceptors>

    <mvc:annotation-driven />
    <!-- 排除静态文件 -->
    <mvc:resources location="file:/opt/local/file/bg/" mapping="/bg/**" />
    <!-- 开启AOP -->
    <aop:config proxy-target-class="true" />
    <!-- 保证 Shiro内部生命周期 -->
    <bean class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>
    <!-- 开启Shiro授权生效 -->
    <bean class="org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor"/>

<!--    &lt;!&ndash; 配置文件上传 &ndash;&gt;-->
<!--    <bean id="multipartResolver"-->
<!--          class="org.springframework.web.multipart.commons.CommonsMultipartResolver">-->
<!--        &lt;!&ndash; 配置文件上传的最大体积 10M &ndash;&gt;-->
<!--        <property name="maxUploadSize" value="10240000"></property>-->
<!--    </bean>-->

    <!-- 配置文件上传类型解析器 multipartResolver-->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver" />

</beans>
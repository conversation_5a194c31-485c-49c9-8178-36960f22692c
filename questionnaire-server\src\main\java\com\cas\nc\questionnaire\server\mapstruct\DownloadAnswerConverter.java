package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.DownloadAnswerReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.DownloadAnswerReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DownloadAnswerConverter {
    DownloadAnswerConverter INSTANCE = Mappers.getMapper(DownloadAnswerConverter.class);

    DownloadAnswerReqDto to(DownloadAnswerReqVo vo);

}
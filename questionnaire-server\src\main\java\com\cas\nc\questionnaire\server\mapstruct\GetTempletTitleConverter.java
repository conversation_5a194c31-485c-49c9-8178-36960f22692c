package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.cas.nc.questionnaire.dao.po.TempletTitlePo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface GetTempletTitleConverter {
    GetTempletTitleConverter INSTANCE = Mappers.getMapper(GetTempletTitleConverter.class);

    List<QstTitlePo> to(List<TempletTitlePo> titlePoList);
}

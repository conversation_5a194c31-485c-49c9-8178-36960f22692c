package com.cas.nc.questionnaire.dao.po;

import java.util.List;

public class LimitAttributeJson {
    /*最小字数*/
    private List<String> minBoundsValue;

    /*最大限制字数*/
    private List<String> maxBoundsValue;

    /*校验类型*/
    private List<String> colTitleValidateType;

    /*高度*/
    private Integer height;

    /*宽度*/
    private Integer width;

    public List<String> getMinBoundsValue() {
        return minBoundsValue;
    }

    public void setMinBoundsValue(List<String> minBoundsValue) {
        this.minBoundsValue = minBoundsValue;
    }

    public List<String> getMaxBoundsValue() {
        return maxBoundsValue;
    }

    public void setMaxBoundsValue(List<String> maxBoundsValue) {
        this.maxBoundsValue = maxBoundsValue;
    }

    public List<String> getColTitleValidateType() {
        return colTitleValidateType;
    }

    public void setColTitleValidateType(List<String> colTitleValidateType) {
        this.colTitleValidateType = colTitleValidateType;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }
}

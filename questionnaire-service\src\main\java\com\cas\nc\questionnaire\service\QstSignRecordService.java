package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstSignRecordPo;
import com.cas.nc.questionnaire.dao.query.QstSignRecordQuery;

import java.util.Date;
import java.util.List;

/**
 * 问卷签到记录服务
 */
public interface QstSignRecordService {
    
    /**
     * 插入签到记录
     *
     * @param signRecord 签到记录
     * @return 影响行数
     */
    int insert(QstSignRecordPo signRecord);
    
    /**
     * 更新签到记录
     *
     * @param signRecord 签到记录
     * @return 影响行数
     */
    int update(QstSignRecordPo signRecord);
    
    /**
     * 查询单条记录
     *
     * @param id ID
     * @return 签到记录
     */
    QstSignRecordPo selectOne(Long id);
    
    /**
     * 查询列表
     *
     * @param query 查询条件
     * @return 签到记录列表
     */
    List<QstSignRecordPo> selectList(QstSignRecordQuery query);
    
    /**
     * 根据问卷ID和openid查询某一天是否已签到
     *
     * @param questionnaireId 问卷ID
     * @param openid 微信openid
     * @param signDate 签到日期
     * @return 签到记录
     */
    QstSignRecordPo selectByQuestionnaireIdAndOpenidAndDate(String questionnaireId, String openid, Date signDate);
    
    /**
     * 查询分页列表
     *
     * @param query 查询条件
     * @return 签到记录列表
     */
    List<QstSignRecordPo> selectListByPage(QstSignRecordQuery query);
    
    /**
     * 查询记录数
     *
     * @param query 查询条件
     * @return 记录数
     */
    int selectCount(QstSignRecordQuery query);
} 
package com.cas.nc.questionnaire.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public enum HeaderCellTitleEnum {
    NUM(1, "序号"),
    SUBMIT_TIME(2, "提交时间"),
    DURATION(3, "所用时长（单位秒）"),
    SOURCE(4, "来源"),
    IP(5, "IP"),
    IP_ADDRESS(6, "IP归属地"),
    NAME(7, "姓名"),
    ;
    private final Integer key;
    private final String value;


    HeaderCellTitleEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static List<HeaderCellTitleEnum> HEADER_CELL_TITLE_LIST = new ArrayList<>();


    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

    public static List<HeaderCellTitleEnum> getHeaderCellTitleList() {

        if (CollectionUtils.isEmpty(HEADER_CELL_TITLE_LIST)) {
            HEADER_CELL_TITLE_LIST.add(NUM);
            HEADER_CELL_TITLE_LIST.add(SUBMIT_TIME);
            HEADER_CELL_TITLE_LIST.add(DURATION);
            HEADER_CELL_TITLE_LIST.add(SOURCE);
            HEADER_CELL_TITLE_LIST.add(IP);
            HEADER_CELL_TITLE_LIST.add(IP_ADDRESS);
        }

        return HEADER_CELL_TITLE_LIST;
    }

}

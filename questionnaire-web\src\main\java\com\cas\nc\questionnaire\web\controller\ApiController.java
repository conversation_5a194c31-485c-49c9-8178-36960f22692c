package com.cas.nc.questionnaire.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.cas.nc.questionnaire.common.dto.analysis.*;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireListRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireListReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnairePublishReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireRemoveReqDto;
import com.cas.nc.questionnaire.common.dto.user.UserLoginRepDto;
import com.cas.nc.questionnaire.common.dto.user.UserLoginReqDto;
import com.cas.nc.questionnaire.common.dto.user.UserRemoveReqDto;
import com.cas.nc.questionnaire.common.dto.user.UserSaveReqDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.TimeTypeEnum;
import com.cas.nc.questionnaire.common.enums.UserSourceEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.to.ReportShareTo;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.ValidateUtils;
import com.cas.nc.questionnaire.common.vo.analysis.*;
import com.cas.nc.questionnaire.common.vo.base.ExternalCommonReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.QuestionnaireListRepVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.QuestionnaireListReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.QuestionnairePublishReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.QuestionnaireRemoveReqVo;
import com.cas.nc.questionnaire.common.vo.user.UserGetTokenRepVo;
import com.cas.nc.questionnaire.common.vo.user.UserGetTokenReqVo;
import com.cas.nc.questionnaire.common.vo.user.UserRemoveReqVo;
import com.cas.nc.questionnaire.common.vo.user.UserSaveReqVo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.server.*;
import com.cas.nc.questionnaire.server.mapstruct.*;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import com.cas.nc.questionnaire.service.QstTitleService;
import com.cas.nc.questionnaire.service.ShareSettingService;
import com.cas.nc.questionnaire.web.interceptor.annotations.ExternalApiValidate;
import com.cas.nc.questionnaire.web.interceptor.annotations.ResponseEncryptAndSign;
import com.cas.nc.questionnaire.web.interceptor.annotations.ResponseSign;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.Date;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.*;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.DATE_FORMAT_EXCEPTION;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.isListChecked;
import static com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum.PAUSE;
import static com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum.RUN;
import static com.cas.nc.questionnaire.common.enums.TimeTypeEnum.*;
import static com.cas.nc.questionnaire.common.utils.DateUtil.*;

@RestController
@RequestMapping("/questionnaire/api")
public class ApiController extends BaseController {

    @Resource
    private UserServer userServer;
    @Resource
    private QuestionnaireServer questionnaireServer;
    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;
    @Resource
    private AnalysisServer analysisServer;
    @Resource
    private ShareSettingService shareSettingService;
    @Resource
    private ApiServer apiServer;

    @ExternalApiValidate
    @ResponseEncryptAndSign
    @RequestMapping(value = "/user/getToken", method = {RequestMethod.POST})
    public ApiReturnResult getToken(@RequestBody ExternalCommonReqVo reqVo) {
        Assert.isTrue(reqVo != null, CodeEnum.USER_PWD_ERROR);
        ApiReturnResult result = ApiReturnResult.success();
        ValidateUtils.validateNotNullExclude(reqVo, "decryptData");
        UserGetTokenReqVo vo = JSONUtil.parseObject(reqVo.getDecryptData(), UserGetTokenReqVo.class);
        ValidateUtils.validateNotNullExclude(vo);

        UserLoginReqDto reqDto = new UserLoginReqDto();
        reqDto.setPwd(vo.getPwd());
        reqDto.setUserName(vo.getEmail());
        reqDto.setUserSource(UserSourceEnum.EL.key());
        UserLoginRepDto userLoginRespDto = userServer.doLogin(reqDto);
        String token = loginCheck.addCookie(userLoginRespDto.getTicket(), getHttpServletResponse());

        UserGetTokenRepVo repVo = new UserGetTokenRepVo();
        repVo.setToken(token);

        result.setData(JSONUtil.toJSONString(repVo));

        return result;
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/user/save", method = {RequestMethod.POST})
    public ApiReturnResult saveUser(@RequestBody ExternalCommonReqVo reqVo) {
        ApiReturnResult result = ApiReturnResult.success();
        ValidateUtils.validateNotNullExclude(reqVo, "decryptData");
        UserSaveReqVo vo = JSONUtil.parseObject(reqVo.getDecryptData(), UserSaveReqVo.class);
        ValidateUtils.validateNotNullExclude(vo);

        UserSaveReqDto reqDto = UserConverter.INSTANCE.to(vo);
        userServer.save(reqDto);

        return result;
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/user/remove", method = {RequestMethod.POST})
    public ApiReturnResult removeUser(@RequestBody ExternalCommonReqVo reqVo) {
        ApiReturnResult result = ApiReturnResult.success();
        ValidateUtils.validateNotNullExclude(reqVo, "decryptData");
        UserRemoveReqVo vo = JSONUtil.parseObject(reqVo.getDecryptData(), UserRemoveReqVo.class);
        ValidateUtils.validateNotNullExclude(vo);

        UserRemoveReqDto reqDto = UserConverter.INSTANCE.to(vo);
        userServer.remove(reqDto);

        return result;
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/remove", method = {RequestMethod.POST})
    public ApiReturnResult removeQst(@RequestBody ExternalCommonReqVo reqVo) {

        ValidateUtils.validateNotNullExclude(reqVo, "decryptData");
        QuestionnaireRemoveReqVo vo = JSONUtil.parseObject(reqVo.getDecryptData(), QuestionnaireRemoveReqVo.class);
        ValidateUtils.validateNotNullExclude(vo);

        QuestionnaireRemoveReqDto reqDto = QuestionnaireEditConverter.INSTANCE.to(vo);
        Boolean flag = apiServer.remove(reqDto);
        if (!flag) {
            return ApiReturnResult.error(CodeEnum.FAIL);
        }

        return ApiReturnResult.success();
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/publish", method = {RequestMethod.POST})
    public ApiReturnResult publishQst(@RequestBody ExternalCommonReqVo reqVo) {
        ValidateUtils.validateNotNullExclude(reqVo, "decryptData");
        QuestionnairePublishReqVo vo = JSONUtil.parseObject(reqVo.getDecryptData(), QuestionnairePublishReqVo.class);
        if(vo.getEmail() != null){
            Assert.notBlank(vo.getEmail(), "email");
            vo.setUserId(Long.valueOf(userServer.getUserInfo(vo.getEmail()).getUserId()));
        }
        vo.setStatus(RUN.key());
        ValidateUtils.validateNotNullExclude(vo);

        QuestionnairePublishReqDto reqDto = QuestionnaireEditConverter.INSTANCE.to(vo);
        Boolean flag = apiServer.publishQst(reqDto);
        if (!flag) {
            return ApiReturnResult.error(CodeEnum.FAIL);
        }

        return ApiReturnResult.success();
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/pause", method = {RequestMethod.POST})
    public ApiReturnResult pauseQst(@RequestBody ExternalCommonReqVo reqVo) {
        ValidateUtils.validateNotNullExclude(reqVo, "decryptData");
        QuestionnairePublishReqVo vo = JSONUtil.parseObject(reqVo.getDecryptData(), QuestionnairePublishReqVo.class);
        if(vo.getEmail() != null){
            Assert.notBlank(vo.getEmail(), "email");
            vo.setUserId(Long.valueOf(userServer.getUserInfo(vo.getEmail()).getUserId()));
        }
        vo.setStatus(PAUSE.key());
        ValidateUtils.validateNotNullExclude(vo);
        QuestionnairePublishReqDto reqDto = QuestionnaireEditConverter.INSTANCE.to(vo);
        Boolean flag = apiServer.pauseQst(reqDto);
        if (!flag) {
            return ApiReturnResult.error(CodeEnum.FAIL);
        }

        return ApiReturnResult.success();
    }

  /*  @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/list", method = {RequestMethod.POST})
    public ApiReturnResult list(@RequestBody QuestionnaireListReqVo vo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullContain(vo, "pageSize", "page");
        QuestionnaireListReqDto reqDto = QuestionnaireQueryConverter.INSTANCE.to(vo);
        QuestionnaireListRepDto repDto = questionnaireServer.list(reqDto);
        QuestionnaireListRepVo repVo = QuestionnaireQueryConverter.INSTANCE.to(repDto);
        repVo.setPage(vo.getPage());
        repVo.setPageSize(vo.getPageSize());
        result.setData(repVo);
        return result;
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/commonsourceanalysis", method = {RequestMethod.POST})
    public ApiReturnResult commonSourceAnalysis(@RequestBody SourceAnalysisReqVo vo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        SourceAnalysisReqDto reqDto = SourceAnalysisConverter.INSTANCE.to(vo);
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            reqDto.setUserId(qstQuestionnaireInfoPo.getUserId());
        }
        SourceAnalysisRepDto repDto = analysisServer.sourceAnalysis(reqDto);
        result.setData(SourceAnalysisConverter.INSTANCE.to(repDto));
        return result;
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/commonListChecked", method = {RequestMethod.POST})
    public ApiReturnResult commonListChecked(@RequestBody ListCheckedReqVo vo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ListCheckedReqDto reqDto = ListCheckedConverter.INSTANCE.to(vo);
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            reqDto.setUserId(qstQuestionnaireInfoPo.getUserId());
        }
        Assert.isTrue(isListChecked(vo.getRuleType()), RULE_TYPE_NOT_EXIST);
        ListCheckedRepDto repDto = analysisServer.listChecked(reqDto);
        result.setData(repDto);
        return result;
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/commontimeanalysis", method = {RequestMethod.POST})
    public ApiReturnResult commonTimeAnalysis(@RequestBody TimeAnalysisReqVo vo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        TimeAnalysisReqDto reqDto = TimeAnalysisConverter.INSTANCE.to(vo);
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            reqDto.setUserId(qstQuestionnaireInfoPo.getUserId());
        }
        TimeTypeEnum.toEnum(vo.getTimeType());
        validateDate(vo);
        TimeAnalysisRepDto repDto = analysisServer.timeAnalysis(reqDto);
        result.setData(TimeAnalysisConverter.INSTANCE.to(repDto));
        return result;
    }

    private void validateDate(TimeAnalysisReqVo vo) {
        if (isDay(vo.getTimeType())) {
            Date beginTime = parseDate(YYYYMMDD, vo.getBeginTime());
            Assert.notNull(beginTime, DATE_FORMAT_EXCEPTION);
            Date endTime = parseDate(YYYYMMDD, vo.getEndTime());
            Assert.notNull(endTime, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(endTime.getTime() >= beginTime.getTime(), BEGIN_END_TIME_COMPARE_ERROR);
            Assert.isTrue(vo.getBeginTime().length() == 10, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(vo.getEndTime().length() == 10, DATE_FORMAT_EXCEPTION);
        } else if (isWeek(vo.getTimeType())) {
            Assert.isTrue(vo.getBeginTime().length() == 6, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(vo.getEndTime().length() == 6, DATE_FORMAT_EXCEPTION);
            Date beginTime = parseDate(YYYY, vo.getBeginTime().substring(0, 4));
            Assert.notNull(beginTime, DATE_FORMAT_EXCEPTION);
            Date endTime = parseDate(YYYY, vo.getEndTime().substring(0, 4));
            Assert.notNull(endTime, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(endTime.getTime() >= beginTime.getTime(), BEGIN_END_TIME_COMPARE_ERROR);
            Integer beginWeek = Integer.valueOf(vo.getBeginTime().substring(4, 6));
            Integer endWeek = Integer.valueOf(vo.getEndTime().substring(4, 6));
            Assert.isTrue(endWeek >= beginWeek, BEGIN_END_TIME_COMPARE_ERROR);

        } else if (isMonth(vo.getTimeType())) {
            Date beginTime = parseDate(YYYYMM, vo.getBeginTime());
            Assert.notNull(beginTime, DATE_FORMAT_EXCEPTION);
            Date endTime = parseDate(YYYYMM, vo.getEndTime());
            Assert.notNull(endTime, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(endTime.getTime() >= beginTime.getTime(), BEGIN_END_TIME_COMPARE_ERROR);
            Assert.isTrue(vo.getBeginTime().length() == 7, DATE_FORMAT_EXCEPTION);
            Assert.isTrue(vo.getEndTime().length() == 7, DATE_FORMAT_EXCEPTION);
        }
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/commonprovinceanalysis", method = {RequestMethod.POST})
    public ApiReturnResult commonProvinceAnalysis(@RequestBody ProvinceAnalysisReqVo vo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ProvinceAnalysisReqDto reqDto = ProvinceAnalysisConverter.INSTANCE.to(vo);
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            reqDto.setUserId(qstQuestionnaireInfoPo.getUserId());
        }
        ProvinceAnalysisRepDto repDto = analysisServer.provinceAnalysis(reqDto);
        result.setData(ProvinceAnalysisConverter.INSTANCE.to(repDto));
        return result;
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/commonReport", method = {RequestMethod.POST})
    public ApiReturnResult commonReport(@RequestBody AnalysisReportReqVo vo) {
        Assert.notNull(vo, "param");
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        AnalysisReportReqDto reqDto = ReportConverter.INSTANCE.to(vo);
        ReportShareTo shareTo = null;
        if(StringUtils.isNotBlank(vo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            Long loginUserId = qstQuestionnaireInfoPo.getUserId();
            shareTo = shareSettingService.getReportShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
            reqDto.setUserId(shareTo.getUserId());
        }
        AnalysisReportRepDto repDto = analysisServer.report(reqDto);
        AnalysisReportRepVo repVo = ReportConverter.INSTANCE.to(repDto);
        if(shareTo != null) {
            repVo.setIndependentShowFlag(shareTo.getIndependentShowFlag());
        }
        result.setData(repVo);
        return result;
    }

    @ExternalApiValidate
    @ResponseSign
    @RequestMapping(value = "/qst/commoncustomreport", method = {RequestMethod.POST})
    public ApiReturnResult commonCustomReport(@RequestBody CustomReportReqVo reqVo) {
        ValidateUtils.validateNotNullExclude(reqVo, "userId", "sharePwd");
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        CustomReportReqDto reqDto = ReportConverter.INSTANCE.to(reqVo);
        ReportShareTo shareTo = null;
        if(StringUtils.isNotBlank(reqVo.getQuestionnaireId())) {
            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(reqVo.getQuestionnaireId());
            Long loginUserId = qstQuestionnaireInfoPo.getUserId();
            shareTo = shareSettingService.getReportShareTo(reqVo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
            reqDto.setUserId(shareTo.getUserId());
        }
        AnalysisReportRepDto repDto = analysisServer.customReport(reqDto);
        AnalysisReportRepVo repVo = ReportConverter.INSTANCE.to(repDto);
        if(shareTo != null) {
            repVo.setIndependentShowFlag(shareTo.getIndependentShowFlag());
        }
        result.setData(repVo);
        return result;
    }
*/
}

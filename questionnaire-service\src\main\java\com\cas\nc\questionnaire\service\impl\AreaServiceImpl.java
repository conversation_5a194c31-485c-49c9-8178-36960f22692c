package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.AreaRankEnum;
import com.cas.nc.questionnaire.common.enums.YnEnum;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.nosharddao.AreaDao;
import com.cas.nc.questionnaire.dao.po.AreaPo;
import com.cas.nc.questionnaire.dao.query.AreaQuery;
import com.cas.nc.questionnaire.service.AreaService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_TITLE_SPLIT;
import static com.cas.nc.questionnaire.common.utils.Constants.SPLIT_FLAG;


@Service
public class AreaServiceImpl implements AreaService {
    private static Logger logger = LoggerFactory.getLogger(AreaServiceImpl.class);

    private static AreaPo defaultAreaPo = new AreaPo();
    static {
        defaultAreaPo.setName("未知");
    }

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private AreaDao areaDao;

    @Override
    public List<AreaPo> selectAll() {
        AreaQuery query = new AreaQuery();
        return selectList(query);
    }

    @Override
    public List<AreaPo> selectAllEffective() {
        AreaQuery query = new AreaQuery();
        query.setStatus(YnEnum.Y.key());
        return selectList(query);
    }

    @Override
    public int insert(AreaPo areaPo) {
        return areaDao.insert(areaPo);
    }

    @Override
    public List<AreaPo> selectList(AreaQuery query) {
        return areaDao.selectList(query);
    }

    @Override
    public AreaPo selectOne(AreaQuery query) {
        return areaDao.selectOne(query);
    }

    @Override
    public AreaPo queryProvince(String name) {
        try {
            return provinceNameCache.get(name);
        } catch (Exception e) {
            logger.error("AreaServiceImpl.queryProvince name[{}]", name, e);
        }
        return null;
    }

    @Override
    public AreaPo queryCity(String name) {
        try {
            return cityNameCache.get(name);
        } catch (Exception e) {
            logger.error("AreaServiceImpl.queryCity name[{}]", name, e);
        }
        return null;
    }

    @Override
    public List<AreaPo> selectAllProvince(String columns) {
        AreaQuery query = new AreaQuery();
        query.setParentId(0L);
        query.setRanks(AreaRankEnum.ONE.key());
        query.setStatus(YnEnum.Y.key());
        if (StringUtil.isNotBlank(columns)) {
            query.setTableColumns(columns);
        }
        return selectList(query);
    }

    @Override
    public List<AreaPo> selectList(Long parentId) {
        AreaQuery query = new AreaQuery();
        query.setParentId(parentId);
        query.setStatus(YnEnum.Y.key());
        return selectList(query);
    }

    @Override
    public List<AreaPo> selectList(Integer rank) {
        AreaQuery query = new AreaQuery();
        query.setRanks(rank);
        query.setStatus(YnEnum.Y.key());
        return selectList(query);
    }

    @Override
    public List<AreaPo> selectList(Integer rank, String columns) {
        AreaQuery query = new AreaQuery();
        query.setRanks(rank);
        query.setStatus(YnEnum.Y.key());
        if (StringUtil.isNotBlank(columns)) {
            query.setTableColumns(columns);
        }
        return selectList(query);
    }

    @Override
    public List<AreaPo> selectProvinceList(List<Long> idList) {
        List<AreaPo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(idList)) {
            return result;
        }
        idList.forEach(v -> {
            try {
                result.add(provinceIdCache.get(v));
            } catch (Exception e) {
                logger.error("AreaServiceImpl.selectProvinceList Exception", e);
            }
        });
        return result;
    }

    @Override
    public AreaPo selectProvince(Long id) {
        try {
            if (id == 0) {
                return defaultAreaPo;
            }
            return provinceIdCache.get(id);
        } catch (Exception e) {
            logger.error("AreaServiceImpl.selectProvinceList Exception", e);
        }
        return new AreaPo();
    }

    @Override
    public List<AreaPo> selectCityList(List<Long> idList) {
        List<AreaPo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(idList)) {
            return result;
        }
        idList.forEach(v -> {
            try {
                result.add(cityIdCache.get(v));
            } catch (Exception e) {
                logger.error("AreaServiceImpl.selectCityList Exception", e);
            }
        });
        return result;
    }

    @Override
    public AreaPo selectCity(Long id) {
        try {
            if (id == 0) {
                return defaultAreaPo;
            }
            return cityIdCache.get(id);
        } catch (Exception e) {
            logger.error("AreaServiceImpl.selectCityList Exception", e);
        }

        return new AreaPo();
    }

    @Override
    public String getAddress(Long provinceId, Long cityId) {
        StringBuffer buffer = new StringBuffer();
        AreaPo province = selectProvince(provinceId);
        AreaPo city = selectCity(cityId);

        buffer.append(province.getName());
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(city.getName());
        return buffer.toString();
    }

    private LoadingCache<String, AreaPo> provinceNameCache = CacheBuilder.newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100)
            .recordStats()
            .build(new CacheLoader<String, AreaPo>() {
                @Override
                public AreaPo load(String key) throws Exception {
                    AreaQuery query = new AreaQuery();
                    query.setParentId(0L);
                    query.setName(key);
                    query.setStatus(1);
                    return selectOne(query);
                }
            });

    private LoadingCache<Long, AreaPo> provinceIdCache = CacheBuilder.newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100)
            .recordStats()
            .build(new CacheLoader<Long, AreaPo>() {
                @Override
                public AreaPo load(Long key) throws Exception {
                    AreaQuery query = new AreaQuery();
                    query.setParentId(0L);
                    query.setId(key);
                    query.setStatus(1);
                    return selectOne(query);
                }
            });

    private LoadingCache<String, AreaPo> cityNameCache = CacheBuilder.newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(2000)
            .recordStats()
            .build(new CacheLoader<String, AreaPo>() {
                @Override
                public AreaPo load(String key) throws Exception {
                    String[] keys = key.split(SPLIT_FLAG);
                    AreaQuery query = new AreaQuery();
                    query.setParentId(Long.valueOf(keys[1]));
                    query.setName(keys[0]);
                    query.setStatus(1);
                    return selectOne(query);
                }
            });

    private LoadingCache<Long, AreaPo> cityIdCache = CacheBuilder.newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(2000)
            .recordStats()
            .build(new CacheLoader<Long, AreaPo>() {
                @Override
                public AreaPo load(Long key) throws Exception {
                    AreaQuery query = new AreaQuery();
                    query.setId(key);
                    query.setRanks(2);
                    query.setStatus(1);
                    return selectOne(query);
                }
            });
}

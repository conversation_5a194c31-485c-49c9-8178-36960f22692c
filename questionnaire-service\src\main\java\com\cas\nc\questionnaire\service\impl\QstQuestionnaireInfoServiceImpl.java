package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireListRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireRepDto;
import com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.query.QstQuestionnaireInfoQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstQuestionnaireInfoDao;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import org.apache.commons.collections.MapUtils;
import org.codehaus.jackson.map.util.BeanUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum.INIT;
import static com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum.MARK_DELETING;
import static com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum.PAUSE;
import static com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum.RUN;


@Service
public class QstQuestionnaireInfoServiceImpl implements QstQuestionnaireInfoService {
    private static Logger logger = LoggerFactory.getLogger(QstQuestionnaireInfoServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstQuestionnaireInfoDao qstQuestionnaireInfoDao;

    @Override
    public int insert(QstQuestionnaireInfoPo qstQuestionnaireInfoPo) {
        return qstQuestionnaireInfoDao.insert(qstQuestionnaireInfoPo);
    }

    @Override
    public int selectCount(QstQuestionnaireInfoQuery query) {
        return qstQuestionnaireInfoDao.selectCount(query);
    }

    @Override
    public List<QstQuestionnaireInfoPo> selectMyListByPage(QstQuestionnaireInfoQuery query) {
        validateUserId(query);
        return qstQuestionnaireInfoDao.selectMyListByPage(query);
    }

    @Override
    public int delete(QstQuestionnaireInfoQuery query) {
        validateUserId(query);
        return qstQuestionnaireInfoDao.delete(query);
    }

    @Override
    public List<QstQuestionnaireInfoPo> selectList(QstQuestionnaireInfoQuery query) {
        validateUserId(query);
        return qstQuestionnaireInfoDao.selectList(query);
    }

    @Override
    public QstQuestionnaireInfoPo selectOne(QstQuestionnaireInfoQuery query) {
        validateUserId(query);
        return qstQuestionnaireInfoDao.selectOne(query);
    }

    @Override
    public int update(QstQuestionnaireInfoQuery query) {
        validateUserId(query);
        return qstQuestionnaireInfoDao.update(query);
    }

    @Override
    public String copy(QstQuestionnaireInfoQuery query, String newTitleName) {
        validateUserId(query);
        QstQuestionnaireInfoPo po = qstQuestionnaireInfoDao.selectOne(query);
        po.setId(null);
        if (StringUtil.isNotBlank(newTitleName)) {
            po.setTitle(newTitleName);
        } else {
            po.setTitle(po.getTitle() + "_1");
        }
        po.setQuestionnaireId(SequenceUtil.getInstance().generateQuestionnaireId(po.getUserId().toString()));
        po.setStatus(INIT.key());
        po.setCreateTime(null);
        po.setUpdateTime(null);
        po.setBizId(null);
        po.setBizNo(null);
        qstQuestionnaireInfoDao.insert(po);
        return po.getQuestionnaireId();
    }

    @Override
    public QstQuestionnaireInfoPo selectOne(String questionnaireId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        
        List<QstQuestionnaireInfoPo> list = selectList(query);
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public QstQuestionnaireInfoPo selectOne(String questionnaireId, Long userId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        return selectOne(query);
    }

    @Override
    public int updateStatus2Pause(String questionnaireId, Long userId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setStatus(PAUSE.key());
        query.setOldStatus(RUN.key());
        validateUserId(query);
        return update(query);
    }

    @Override
    public int updateStatus2Publish(String questionnaireId, Long userId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setStatus(RUN.key());
        query.setOldStatus(INIT.key());
        return update(query);
    }

    @Override
    public int updateStatusPause2Publish(String questionnaireId, Long userId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setStatus(RUN.key());
        query.setOldStatus(PAUSE.key());
        return update(query);
    }

    @Override
    public int updateStatus2Recovery(String questionnaireId, Long userId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setStatus(QuestionnaireStatusEnum.INIT.key());
        query.setOldStatus(MARK_DELETING.key());
        return update(query);
    }

    @Override
    public int updateStatus2Delete(String questionnaireId, Long userId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setStatus(QuestionnaireStatusEnum.MARK_DELETING.key());
        return update(query);
    }

    @Override
    public int selectQuestionnaireListCount(QstQuestionnaireInfoQuery query) {
        return qstQuestionnaireInfoDao.selectQuestionnaireListCount(query);
    }

    @Override
    public List<QuestionnaireRepDto> selectQuestionnaireList(QstQuestionnaireInfoQuery query) {
        return qstQuestionnaireInfoDao.selectQuestionnaireListByPage(query);
    }

    @Override
    public int selectAnswerCount(QstQuestionnaireInfoQuery query) {
        return qstQuestionnaireInfoDao.selectAnswerCount(query);
    }

    @Override
    public int updateStatus(String questionnaireId, Integer status, Long userId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setStatus(status);
        filterCondition(query);
        return update(query);
    }

    private void validateUserId(QstQuestionnaireInfoQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }

    private void filterCondition(QstQuestionnaireInfoQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

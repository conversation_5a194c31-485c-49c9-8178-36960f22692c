package com.cas.nc.questionnaire.web.controller;

import com.alibaba.fastjson.JSON;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionSaveReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionSaveRepDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.vo.questionnaire.ReportQueryConditionSaveReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.ReportQueryConditionSaveRepVo;
import com.cas.nc.questionnaire.server.ReportQueryConditionServer;
import com.cas.nc.questionnaire.server.mapstruct.ReportQueryConditionConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.cas.nc.questionnaire.common.utils.Assert.notNull;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionGetReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionGetRepDto;
import com.cas.nc.questionnaire.common.vo.questionnaire.ReportQueryConditionGetReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.ReportQueryConditionGetRepVo;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionUserReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionUserRepDto;
import com.cas.nc.questionnaire.common.vo.questionnaire.ReportQueryConditionUserReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.ReportQueryConditionUserRepVo;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryAnswerListReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryAnswerListRepDto;
import com.cas.nc.questionnaire.common.vo.questionnaire.ReportQueryAnswerListReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.ReportQueryAnswerListRepVo;

/**
 * 测评报告查询条件控制器
 */
@RestController
@RequestMapping("/questionnaire/report")
public class ReportQueryConditionController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ReportQueryConditionController.class);

    @Resource
    private ReportQueryConditionServer reportQueryConditionServer;

    /**
     * 保存测评报告查询条件
     * @param vo 请求参数
     * @return 响应结果
     */
    @RequestMapping(value = "/save-condition", method = {RequestMethod.POST})
    public ApiReturnResult saveReportQueryCondition(@RequestBody ReportQueryConditionSaveReqVo vo) {
        logger.info("ReportQueryConditionController.saveReportQueryCondition param[{}]", JSON.toJSONString(vo));

        // 校验参数
        notNull(vo, "param");
        notNull(vo.getQuestionnaireId(), "questionnaireId");
        notNull(vo.getRegularQuestions(), "regularQuestions");

        // 转换参数
        ReportQueryConditionSaveReqDto reqDto = ReportQueryConditionConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        // 调用服务
        ReportQueryConditionSaveRepDto repDto = reportQueryConditionServer.saveReportQueryCondition(reqDto);

        // 构建返回结果
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ReportQueryConditionSaveRepVo repVo = ReportQueryConditionConverter.INSTANCE.to(repDto);
        result.setData(repVo);

        logger.info("ReportQueryConditionController.saveReportQueryCondition result[{}]", JSON.toJSONString(result));
        return result;
    }

    /**
     * 获取测评报告查询条件详情
     * @param vo 请求参数
     * @return 响应结果
     */
    @RequestMapping(value = "/get-condition", method = {RequestMethod.POST})
    public ApiReturnResult getReportQueryCondition(@RequestBody ReportQueryConditionGetReqVo vo) {
        logger.info("ReportQueryConditionController.getReportQueryCondition param[{}]", JSON.toJSONString(vo));

        // 校验参数
        notNull(vo, "param");
        notNull(vo.getQuestionnaireId(), "questionnaireId");

        // 转换参数
        ReportQueryConditionGetReqDto reqDto = ReportQueryConditionConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        // 调用服务
        ReportQueryConditionGetRepDto repDto = reportQueryConditionServer.getReportQueryCondition(reqDto);

        // 构建返回结果
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ReportQueryConditionGetRepVo repVo = ReportQueryConditionConverter.INSTANCE.to(repDto);
        result.setData(repVo);

        logger.info("ReportQueryConditionController.getReportQueryCondition result[{}]", JSON.toJSONString(result));
        return result;
    }

    /**
     * 用户获取测评报告查询条件（包含时间校验）
     * @param vo 请求参数
     * @return 响应结果
     */
    @RequestMapping(value = "/user-condition", method = {RequestMethod.POST})
    public ApiReturnResult getUserReportQueryCondition(@RequestBody ReportQueryConditionUserReqVo vo) {
        logger.info("ReportQueryConditionController.getUserReportQueryCondition param[{}]", JSON.toJSONString(vo));

        // 校验参数
        notNull(vo, "param");
        notNull(vo.getQuestionnaireId(), "questionnaireId");

        // 转换参数
        ReportQueryConditionUserReqDto reqDto = ReportQueryConditionConverter.INSTANCE.to(vo);

        // 调用服务
        ReportQueryConditionUserRepDto repDto = reportQueryConditionServer.getUserReportQueryCondition(reqDto);

        // 构建返回结果
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ReportQueryConditionUserRepVo repVo = ReportQueryConditionConverter.INSTANCE.to(repDto);
        result.setData(repVo);

        logger.info("ReportQueryConditionController.getUserReportQueryCondition result[{}]", JSON.toJSONString(result));
        return result;
    }

    /**
     * 查询答题列表
     */
    @RequestMapping(value = "/query-answer-list", method = {RequestMethod.POST})
    public ApiReturnResult queryAnswerList(@RequestBody ReportQueryAnswerListReqVo vo) {
        logger.info("ReportQueryConditionController.queryAnswerList param[{}]", JSON.toJSONString(vo));

        // 校验参数
        notNull(vo, "param");
        notNull(vo.getQuestionnaireId(), "questionnaireId");

        // 转换参数
        ReportQueryAnswerListReqDto reqDto = ReportQueryConditionConverter.INSTANCE.to(vo);

        // 调用服务
        ReportQueryAnswerListRepDto repDto = reportQueryConditionServer.queryAnswerList(reqDto);

        // 构建返回结果
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ReportQueryAnswerListRepVo repVo = ReportQueryConditionConverter.INSTANCE.to(repDto);
        result.setData(repVo);

        logger.info("ReportQueryConditionController.queryAnswerList result[{}]", JSON.toJSONString(result));
        return result;
    }
}

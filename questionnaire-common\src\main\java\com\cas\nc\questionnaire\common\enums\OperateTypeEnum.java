package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;


public enum OperateTypeEnum {
    RETAIN(1, "保留答案"),
    DELETE(2, "删除答案"),
    ADD(3, "新增"),
    ELS<PERSON>(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    OperateTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static OperateTypeEnum toEnum(int key) {
        for (OperateTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.VALIDATE_PARAM_EXCEPTION);
    }

    public static boolean isDelete(int key) {
        return DELETE.key == key;
    }

    public static boolean isAdd(int key) {
        return ADD.key == key;
    }

    public static boolean isRetain(int key) {
        return RETAIN.key == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

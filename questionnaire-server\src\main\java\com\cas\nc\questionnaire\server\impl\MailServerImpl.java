package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.transmit.QstEmailDto;
import com.cas.nc.questionnaire.common.dto.transmit.QstEmailRepDto;
import com.cas.nc.questionnaire.common.dto.transmit.QstEmailReqDto;
import com.cas.nc.questionnaire.common.dto.transmit.QstEmailVoDto;
import com.cas.nc.questionnaire.common.enums.SendStatusEnum;
import com.cas.nc.questionnaire.common.enums.task.TaskTypeEnum;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.PaginateUtils;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.QstEmailPo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.po.TaskPo;
import com.cas.nc.questionnaire.dao.query.QstEmailQuery;
import com.cas.nc.questionnaire.server.MailServer;
import com.cas.nc.questionnaire.server.mapstruct.EmailConverter;
import com.cas.nc.questionnaire.service.QstEmailService;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import com.cas.nc.questionnaire.service.TaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.utils.Constants.QST_NAME;
import static com.cas.nc.questionnaire.common.utils.Constants.QST_URL;

@Component
public class MailServerImpl implements MailServer {

    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;

    @Resource
    private QstEmailService qstEmailService;

    @Resource
    private TaskService taskService;

    @Value("${front.questionnaire.uri}")
    private String frontQuestionnaireUri;

    @Value("${ques.quesPrefix}")
    private String quesPrefix;

    @Transactional
    @Override
    public void save(QstEmailDto qstEmailDto) {
        List<QstEmailPo> qstEmailPoList = installPoList(qstEmailDto);
        List<TaskPo> taskPoList = installTaskPoList(qstEmailPoList);
        taskService.createTaskBatch(taskPoList);
    }

    public List<QstEmailPo> installPoList(QstEmailDto qstEmailDto) {
        QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(qstEmailDto.getQuestionnaireId());

        qstEmailDto.setQuesName(qstQuestionnaireInfoPo == null ? "" : qstQuestionnaireInfoPo.getTitle());
        String addressees = qstEmailDto.getAddressees();
        List<QstEmailPo> toSaveQstEmailPoList = new ArrayList<>();
        if (addressees != null) {
            String[] addresses = addressees.split(",");
            for (String address : addresses) {
                String emailId = SequenceUtil.getInstance().generateEmailId(String.valueOf(qstEmailDto.getUserId()));

                String cellAddress = getUrl(qstEmailDto, emailId);
                String contentCell = getReplaceContent(qstEmailDto, cellAddress);

                QstEmailPo qstEmailPo = EmailConverter.INSTANCE.to(qstEmailDto);
                qstEmailPo.setEmailContent(contentCell);
                qstEmailPo.setCreateTime(new Date());
                qstEmailPo.setAddresseeType(1);
                qstEmailPo.setEmailId(emailId);
                qstEmailPo.setAddressees(address);
                qstEmailPo.setSender(qstEmailDto.getSender());
                qstEmailService.insert(qstEmailPo);
                toSaveQstEmailPoList.add(qstEmailPo);
            }
        }
        return toSaveQstEmailPoList;
    }

    private String getReplaceContent(QstEmailDto qstEmailDto, String cellAddress) {
        String emailContent = qstEmailDto.getEmailContent();
        if (StringUtil.isNotBlank(emailContent)) {
            if (emailContent.contains(QST_NAME)) {
                emailContent = emailContent.replace(QST_NAME, qstEmailDto.getQuesName());
            }

            if (emailContent.contains(QST_URL)) {

                emailContent = emailContent.replace(QST_URL, getHrefAddress(cellAddress));
            }

            if (emailContent.contains("\n")) {
                emailContent = emailContent.replace("\n", "<br>");
            }

            if (emailContent.contains("\t")) {
                emailContent = emailContent.replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;");
            }

        }
        return "<html><body>" + emailContent + "</body></html>";
    }

    @Override
    public Map<String, String> adressesCheck(String questionnaireId, String addressees, Long userId) {
        List<String> repetAddressList = new ArrayList<>();
        Map<String, String> resultMap = new HashMap<>();
        String[] addressList = addressees.split(",");
        QstEmailQuery qstEmailQuery = new QstEmailQuery();
        qstEmailQuery.setQuestionnaireId(questionnaireId);
        qstEmailQuery.setUserId(userId);
        Arrays.stream(addressList).forEach(address -> {
            qstEmailQuery.setAddressees(address);
            List<QstEmailPo> qstEmailPoList = qstEmailService.selectList(qstEmailQuery);
            if (qstEmailPoList != null && qstEmailPoList.size() > 0) {
                repetAddressList.add(address);
            }
        });
        resultMap.put("failSize", String.valueOf(repetAddressList.size()));
        resultMap.put("successSize", String.valueOf(addressList.length - repetAddressList.size()));
        resultMap.put("repetData", String.join(",", repetAddressList));
        return resultMap;
    }

    @Override
    public QstEmailRepDto getEmailSendDetail(QstEmailReqDto qstEmailReqDto) {
        QstEmailQuery query = new QstEmailQuery();
        query.setQuestionnaireId(qstEmailReqDto.getQuestionnaireId());
        query.setUserId(qstEmailReqDto.getUserId());
        if (qstEmailReqDto.getMailType() != null && qstEmailReqDto.getMailType() != 0) {
            query.setStatus(qstEmailReqDto.getMailType());
        }
        if (!StringUtils.isBlank(qstEmailReqDto.getAddress())) {
            query.setAddresseesLike(qstEmailReqDto.getAddress());
        }
        PaginateUtils<QstEmailPo> repDtoPaginate = new PaginateUtils<>(qstEmailReqDto.getPage(), qstEmailReqDto.getPageSize());
        int count = qstEmailService.selectListCount(query);
        repDtoPaginate.setRecordTotal(count);
        query.setPageSize(repDtoPaginate.getPageSize());
        query.setStartIndex(repDtoPaginate.getStartIndex());
        List<QstEmailPo> qstEmailPos = qstEmailService.selectListPage(query);
        List<QstEmailVoDto> qstEmailRepDtos = new ArrayList<>();
        qstEmailPos.forEach(e -> {
            QstEmailVoDto qstEmailDtoTemp = coveryEmailStatus(e);
            qstEmailRepDtos.add(qstEmailDtoTemp);
        });

        QstEmailRepDto qstEmailRepDto = new QstEmailRepDto();
        qstEmailRepDto.setQstEmailList(qstEmailRepDtos);
        qstEmailRepDto.setTotal(count);

        return qstEmailRepDto;
    }

    private QstEmailVoDto coveryEmailStatus(QstEmailPo qstEmailPo) {
        QstEmailVoDto qstEmailVoDto = new QstEmailVoDto();
        qstEmailVoDto.setEmailId(qstEmailPo.getEmailId());
        qstEmailVoDto.setAddressees(qstEmailPo.getAddressees());
        qstEmailVoDto.setReSend(qstEmailPo.getStatus().equals(SendStatusEnum.INIT) ? false : true);
        qstEmailVoDto.setSendTime(DateUtil.formatTime(qstEmailPo.getSendTime()));
        qstEmailVoDto.setUpdateStatusTime(DateUtil.formatTime(qstEmailPo.getUpdateTime()));
        qstEmailVoDto.setStatusDesc(SendStatusEnum.toEnum(qstEmailPo.getStatus()).value());
        return qstEmailVoDto;
    }


    public QstEmailPo selectMailByEmailId(String emailId) {
        return qstEmailService.selectOne(emailId);
    }

    private String getUrl(QstEmailDto qstEmailDto, String emailId) {
        StringBuilder builder = new StringBuilder();
        builder.append(frontQuestionnaireUri);
        builder.append("id=");
        builder.append(qstEmailDto.getQuestionnaireId());
        builder.append("&emailId=");
        builder.append(emailId);
        return builder.toString();
    }

    public List<TaskPo> installTaskPoList(List<QstEmailPo> qstEmailPos) {
        List<TaskPo> taskPoList = new ArrayList<>();
        for (QstEmailPo qstEmailPo : qstEmailPos) {
            TaskPo taskPo = new TaskPo();
            taskPo.setForeignRefId(qstEmailPo.getEmailId());
            taskPo.setTaskType(TaskTypeEnum.SEND_BY_EMAIL_TASK.key());
            taskPo.setContent("mail send");
            taskPoList.add(taskPo);
        }
        return taskPoList;
    }

    private String getHrefAddress(String address) {
        StringBuilder builder = new StringBuilder();
        builder.append("<a href='");
        builder.append(address);
        builder.append("'>");
        builder.append(address);
        builder.append("</a>");
        return builder.toString();
    }
}

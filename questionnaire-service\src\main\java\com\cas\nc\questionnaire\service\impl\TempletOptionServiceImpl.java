package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.nosharddao.TempletOptionDao;
import com.cas.nc.questionnaire.dao.po.TempletOptionPo;
import com.cas.nc.questionnaire.dao.query.TempletOptionQuery;
import com.cas.nc.questionnaire.service.TempletOptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class TempletOptionServiceImpl implements TempletOptionService {
    private static Logger logger = LoggerFactory.getLogger(TempletOptionServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private TempletOptionDao templetOptionDao;

    @Override
    public int insert(TempletOptionPo templetOptionPo) {
        return templetOptionDao.insert(templetOptionPo);
    }

    @Override
    public int delete(TempletOptionQuery query) {
        filterCondition(query);
        return templetOptionDao.delete(query);
    }

    @Override
    public List<TempletOptionPo> selectList(TempletOptionQuery query) {
        return templetOptionDao.selectList(query);
    }

    @Override
    public TempletOptionPo selectOne(TempletOptionQuery query) {
        return templetOptionDao.selectOne(query);
    }

    @Override
    public List<TempletOptionPo> selectList(String templetId) {
        TempletOptionQuery query = new TempletOptionQuery();
        query.setTempletId(templetId);

        return selectList(query);
    }

    private void filterCondition(TempletOptionQuery query) {
        Assert.notNull(query.getTempletId(), "templetId");
    }
}

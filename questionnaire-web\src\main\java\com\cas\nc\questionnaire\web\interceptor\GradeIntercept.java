package com.cas.nc.questionnaire.web.interceptor;

import com.cas.nc.questionnaire.dao.po.UrlConfigPo;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.service.UrlConfigService;
import com.cas.nc.questionnaire.service.UserInfoService;
import com.cas.nc.questionnaire.web.sso.LoginCheck;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

public class GradeIntercept implements HandlerInterceptor {

    @Resource
    private LoginCheck loginCheck;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private UrlConfigService urlConfigService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse httpServletResponse, Object o) throws Exception {
        String url = request.getRequestURI();
        UrlConfigPo urlConfigPo = urlConfigService.selectOne(url);
        Long userId = getUserId(request);
        if (Objects.isNull(userId) || urlConfigPo == null) {
            return false;
        }
        UserInfoPo userInfoPo = userInfoService.selectOne(userId);
        Integer userType = userInfoPo.getType();
//        用户类型不是'其他' 并且 用户等级 >= url等级 检验通过
        return urlConfigPo.getType() != 99 && userType >= urlConfigPo.getType();
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }

    /**
     * 获取登录用户ID
     *
     * @param request
     * @return
     */
    private Long getUserId(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals(LoginCheck.COOKIE_NAME)) {
                return loginCheck.convertToTicket(cookie.getValue()).getUserId();
            }
        }
        return null;
    }
}
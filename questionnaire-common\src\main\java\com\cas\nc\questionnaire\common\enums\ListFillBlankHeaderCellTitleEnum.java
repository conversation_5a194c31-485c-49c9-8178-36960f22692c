package com.cas.nc.questionnaire.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public enum ListFillBlankHeaderCellTitleEnum {
    NUM(1, "序号"),
    SUBMIT_TIME(2, "提交答卷时间"),
    SOURCE(3, "来源渠道"),
    ANSWER_CONTENT(4, "答案文本"),
    ;
    private final Integer key;
    private final String value;


    ListFillBlankHeaderCellTitleEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static List<ListFillBlankHeaderCellTitleEnum> HEADER_CELL_TITLE_LIST = new ArrayList<>();


    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

    public static List<ListFillBlankHeaderCellTitleEnum> getHeaderCellTitleList() {

        if (CollectionUtils.isEmpty(HEADER_CELL_TITLE_LIST)) {
            HEADER_CELL_TITLE_LIST.add(NUM);
            HEADER_CELL_TITLE_LIST.add(SUBMIT_TIME);
            HEADER_CELL_TITLE_LIST.add(SOURCE);
            HEADER_CELL_TITLE_LIST.add(ANSWER_CONTENT);
        }

        return HEADER_CELL_TITLE_LIST;
    }

}

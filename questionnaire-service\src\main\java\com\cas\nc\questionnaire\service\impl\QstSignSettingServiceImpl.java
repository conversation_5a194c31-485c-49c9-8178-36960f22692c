package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.dao.nosharddao.QstSignSettingDao;
import com.cas.nc.questionnaire.dao.po.QstSignSettingPo;
import com.cas.nc.questionnaire.service.QstSignSettingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 签到设置服务实现
 */
@Service
public class QstSignSettingServiceImpl implements QstSignSettingService {

    @Resource
    private QstSignSettingDao qstSignSettingDao;

    @Override
    public int insert(QstSignSettingPo signSetting) {
        return qstSignSettingDao.insert(signSetting);
    }

    @Override
    public int update(QstSignSettingPo signSetting) {
        // 如果问卷ID不为空，则使用问卷ID更新
        if (signSetting.getQuestionnaireId() != null) {
            return qstSignSettingDao.updateByQuestionnaireId(signSetting);
        }
        return qstSignSettingDao.update(signSetting);
    }

    @Override
    public QstSignSettingPo selectByQuestionnaireId(String questionnaireId) {
        return qstSignSettingDao.selectByQuestionnaireId(questionnaireId);
    }

    @Override
    public QstSignSettingPo selectOne(Long id) {
        QstSignSettingPo query = new QstSignSettingPo();
        query.setId(id);
        return qstSignSettingDao.selectOne(query);
    }
} 
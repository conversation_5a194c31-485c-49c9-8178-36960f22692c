package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.WinXin.WeixinUserInfo;
import com.cas.nc.questionnaire.common.dto.sign.QstSignRecordDto;
import com.cas.nc.questionnaire.common.dto.sign.QstSignSettingDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.vo.sign.QstSignRecordListVo;
import com.cas.nc.questionnaire.common.vo.sign.QstSignRecordVo;
import com.cas.nc.questionnaire.common.vo.sign.QstSignSettingVo;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import com.cas.nc.questionnaire.dao.po.QstSignRecordPo;
import com.cas.nc.questionnaire.dao.po.QstSignSettingPo;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.cas.nc.questionnaire.dao.query.AnswerInfoQuery;
import com.cas.nc.questionnaire.dao.query.AnswerOptionQuery;
import com.cas.nc.questionnaire.dao.query.QstSignRecordQuery;
import com.cas.nc.questionnaire.dao.query.QstTitleQuery;
import com.cas.nc.questionnaire.rpc.wechat.WeChatRpc;
import com.cas.nc.questionnaire.server.mapstruct.SignRecordConverter;
import com.cas.nc.questionnaire.server.mapstruct.SignSettingConverter;
import com.cas.nc.questionnaire.service.AnswerInfoService;
import com.cas.nc.questionnaire.service.AnswerOptionService;
import com.cas.nc.questionnaire.service.QstSignRecordService;
import com.cas.nc.questionnaire.service.QstSignSettingService;
import com.cas.nc.questionnaire.service.QstTitleService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 签到相关接口
 */
@RestController
@RequestMapping("/questionnaire/sign")
public class SignController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SignController.class);

    @Resource
    private QstSignSettingService qstSignSettingService;
    
    @Resource
    private AnswerInfoService answerInfoService;
    
    @Resource
    private QstSignRecordService qstSignRecordService;
    
    @Resource
    private WeChatRpc weChatRpc;
    
    @Resource
    private QstTitleService qstTitleService;
    
    @Resource
    private AnswerOptionService answerOptionService;

    /**
     * 创建签到设置
     */
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ApiReturnResult create(@RequestBody QstSignSettingVo vo) {
        logger.info("SignController.create param[{}]", vo);
        
        // 检查参数
        if (vo.getQuestionnaireId() == null) {
            return new ApiReturnResult(CodeEnum.IS_NULL.key(), "问卷ID不能为空");
        }
        
        // 检查是否已存在
        QstSignSettingPo existSetting = qstSignSettingService.selectByQuestionnaireId(vo.getQuestionnaireId());
        if (existSetting != null) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "该问卷已有签到设置，请使用编辑接口");
        }
        
        // 转换并设置默认值
        QstSignSettingDto dto = SignSettingConverter.INSTANCE.voToDto(vo);
        dto.setUserId(getUserId());
        dto.setStatus(1);
        
        // 设置默认值
        if (dto.getSignType() == null) {
            dto.setSignType(1); // 默认微信认证签到
        }
        if (dto.getLocationLimit() == null) {
            dto.setLocationLimit(0); // 默认不限制位置
        }
        if (dto.getSignFrequency() == null) {
            dto.setSignFrequency(1); // 默认只签到一次
        }
        
        // 插入数据
        QstSignSettingPo po = SignSettingConverter.INSTANCE.dtoToPo(dto);
        po.setCreateTime(new Date());
        po.setUpdateTime(new Date());
        
        int result = qstSignSettingService.insert(po);
        
        if (result > 0) {
            return new ApiReturnResult(CodeEnum.SUCCESS.key(), "创建成功");
        } else {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "创建失败");
        }
    }

    /**
     * 编辑签到设置
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ApiReturnResult update(@RequestBody QstSignSettingVo vo) {
        logger.info("SignController.update param[{}]", vo);
        
        // 检查参数
        if (vo.getQuestionnaireId() == null) {
            return new ApiReturnResult(CodeEnum.IS_NULL.key(), "问卷ID不能为空");
        }
        
        // 检查是否存在
        QstSignSettingPo existSetting = qstSignSettingService.selectByQuestionnaireId(vo.getQuestionnaireId());
        if (existSetting == null) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "该问卷无签到设置，请先创建");
        }
        
        // 检查权限
        if (!existSetting.getUserId().equals(getUserId())) {
            return new ApiReturnResult(CodeEnum.PLEASE_LOGIN.key(), "无权限修改此签到设置");
        }
        
        // 转换
        QstSignSettingDto dto = SignSettingConverter.INSTANCE.voToDto(vo);
        dto.setId(existSetting.getId());
        dto.setUserId(getUserId());
        
        // 更新
        QstSignSettingPo po = SignSettingConverter.INSTANCE.dtoToPo(dto);
        po.setUpdateTime(new Date());
        
        int result = qstSignSettingService.update(po);
        
        if (result > 0) {
            return new ApiReturnResult(CodeEnum.SUCCESS.key(), "更新成功");
        } else {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "更新失败");
        }
    }

    /**
     * 根据问卷ID查询签到设置
     */
    @RequestMapping(value = "/getByQuestionnaireId", method = RequestMethod.POST)
    public ApiReturnResult getByQuestionnaireId(@RequestParam("questionnaireId") String questionnaireId) {
        logger.info("SignController.getByQuestionnaireId param[{}]", questionnaireId);
        
        if (questionnaireId == null) {
            return new ApiReturnResult(CodeEnum.IS_NULL.key(), "问卷ID不能为空");
        }
        
        QstSignSettingPo po = qstSignSettingService.selectByQuestionnaireId(questionnaireId);
        
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        if (po != null) {
            QstSignSettingDto dto = SignSettingConverter.INSTANCE.poToDto(po);
            QstSignSettingVo vo = SignSettingConverter.INSTANCE.dtoToVo(dto);
            result.setData(vo);
        }
        
        return result;
    }

    /**
     * 验证用户是否已报名问卷
     */
    @RequestMapping(value = "/checkRegistration", method = RequestMethod.POST)
    public ApiReturnResult checkRegistration(@RequestParam("questionnaireId") String questionnaireId, 
                                            @RequestParam("openid") String openid) {
        logger.info("SignController.checkRegistration questionnaireId[{}], openid[{}]", questionnaireId, openid);
        
        // 检查参数
        if (questionnaireId == null || questionnaireId.trim().isEmpty()) {
            return new ApiReturnResult(CodeEnum.IS_NULL.key(), "问卷ID不能为空");
        }
        
        if (openid == null || openid.trim().isEmpty()) {
            return new ApiReturnResult(CodeEnum.IS_NULL.key(), "微信用户ID不能为空");
        }
        
        // 查询answer_info表，检查是否存在该问卷ID和openid的记录
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setOpenid(openid);
        
        int count = answerInfoService.selectCount(query);
        
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("registered", count > 0);
        
        return new ApiReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.SUCCESS.value(), resultMap);
    }

    /**
     * 用户签到
     */
    @RequestMapping(value = "/sign", method = RequestMethod.POST)
    public ApiReturnResult sign(@RequestBody QstSignRecordVo vo) {
        logger.info("SignController.sign param[{}]", vo);
        
        // 检查参数
        if (vo.getQuestionnaireId() == null) {
            return new ApiReturnResult(CodeEnum.IS_NULL.key(), "问卷ID不能为空");
        }
        
        if (vo.getOpenid() == null) {
            return new ApiReturnResult(CodeEnum.IS_NULL.key(), "微信用户ID不能为空");
        }
        
        // 检查该用户是否已经报名（在answer_info表中有记录）
        AnswerInfoQuery answerQuery = new AnswerInfoQuery();
        answerQuery.setQuestionnaireId(vo.getQuestionnaireId());
        answerQuery.setOpenid(vo.getOpenid());
        
        int answerCount = answerInfoService.selectCount(answerQuery);
        if (answerCount == 0) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "您尚未报名该问卷，请先填写报名问卷");
        }
        
        // 获取签到设置
        QstSignSettingPo settingPo = qstSignSettingService.selectByQuestionnaireId(vo.getQuestionnaireId());
        if (settingPo == null) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "该问卷尚未设置签到功能");
        }
        
        if (settingPo.getStatus() != null && settingPo.getStatus() == 0) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "该问卷的签到功能已关闭");
        }
        
        // 检查签到时间限制
        Date now = new Date();
        if (settingPo.getBeginTime() != null && now.before(settingPo.getBeginTime())) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), 
                    settingPo.getFailTip() != null ? settingPo.getFailTip() : "签到尚未开始");
        }
        
        if (settingPo.getEndTime() != null && now.after(settingPo.getEndTime())) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), 
                    settingPo.getFailTip() != null ? settingPo.getFailTip() : "签到已结束");
        }
        
        // 检查位置限制
        if (settingPo.getLocationLimit() != null && settingPo.getLocationLimit() == 1) {
            // 位置限制开启
            if (vo.getLatitude() == null || vo.getLongitude() == null) {
                return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "请允许获取位置信息");
            }
            
            // 计算距离
            if (settingPo.getLatitude() != null && settingPo.getLongitude() != null && settingPo.getLocationRange() != null) {
                double distance = calculateDistance(
                        vo.getLatitude(), vo.getLongitude(), 
                        settingPo.getLatitude(), settingPo.getLongitude());
                
                if (distance > settingPo.getLocationRange()) {
                    return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), 
                            settingPo.getFailTip() != null ? settingPo.getFailTip() : "不在签到范围内");
                }
            }
        }
        
        // 检查是否今天已经签到过（如果是每日签到一次的设置）
        java.sql.Date today = new java.sql.Date(now.getTime());
        if (settingPo.getSignFrequency() != null && settingPo.getSignFrequency() == 2) {
            // 每日签到一次
            QstSignRecordPo existRecord = qstSignRecordService.selectByQuestionnaireIdAndOpenidAndDate(
                    vo.getQuestionnaireId(), vo.getOpenid(), today);
            
            if (existRecord != null) {
                return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "今日已签到，请勿重复签到");
            }
        } else {
            // 只签到一次
            QstSignRecordQuery recordQuery = new QstSignRecordQuery();
            recordQuery.setQuestionnaireId(vo.getQuestionnaireId());
            recordQuery.setOpenid(vo.getOpenid());
            recordQuery.setStatus(1); // 有效状态
            
            int signCount = qstSignRecordService.selectCount(recordQuery);
            if (signCount > 0) {
                return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "您已签到过，请勿重复签到");
            }
        }
        
        // 尝试获取用户信息（如有）
        WeixinUserInfo userInfo = null;
        if (vo.getAccessToken() != null) {
            try {
                userInfo = weChatRpc.getUserInfo(vo.getAccessToken(), vo.getOpenid());
            } catch (Exception e) {
                logger.error("获取微信用户信息失败", e);
                // 失败不影响流程继续
            }
        }
        
        // 生成签到ID
        String signId = SequenceUtil.getInstance().generateSignId(vo.getQuestionnaireId());
        
        // 插入签到记录
        QstSignRecordPo recordPo = new QstSignRecordPo();
        recordPo.setSignId(signId);
        recordPo.setQuestionnaireId(vo.getQuestionnaireId());
        recordPo.setUserId(settingPo.getUserId()); // 使用问卷创建者ID
        recordPo.setOpenid(vo.getOpenid());
        recordPo.setSignTime(now);
        recordPo.setLatitude(vo.getLatitude());
        recordPo.setLongitude(vo.getLongitude());
        recordPo.setSignDate(today);
        recordPo.setStatus(1); // 有效状态
        recordPo.setCreateTime(now);
        recordPo.setUpdateTime(now);
        
        // 设置用户信息
        if (userInfo != null) {
            recordPo.setNickname(userInfo.getNickname());
            recordPo.setHeadimgurl(userInfo.getHeadimgurl());
        }
        
        int result = qstSignRecordService.insert(recordPo);
        
        if (result > 0) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("signId", signId);
            resultMap.put("signTime", now);
            
            return new ApiReturnResult(CodeEnum.SUCCESS.key(), 
                    settingPo.getSuccessTip() != null ? settingPo.getSuccessTip() : "签到成功", 
                    resultMap);
        } else {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "签到失败，请稍后重试");
        }
    }
    
    /**
     * 计算两个坐标之间的距离（米）
     * 使用Haversine公式计算地球上两点间的距离
     */
    private double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        final int EARTH_RADIUS = 6371000; // 地球半径，单位米
        
        double radLat1 = Math.toRadians(lat1);
        double radLat2 = Math.toRadians(lat2);
        double deltaLat = Math.toRadians(lat2 - lat1);
        double deltaLng = Math.toRadians(lng2 - lng1);
        
        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                   Math.cos(radLat1) * Math.cos(radLat2) *
                   Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        
        return EARTH_RADIUS * c;
    }

    /**
     * 获取签到记录列表
     */
    @RequestMapping(value = "/getSignList", method = RequestMethod.POST)
    public ApiReturnResult getSignList(@RequestParam("questionnaireId") String questionnaireId,
                                       @RequestParam(value = "pageIndex", defaultValue = "0") Integer pageIndex,
                                       @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        logger.info("SignController.getSignList questionnaireId[{}], pageIndex[{}], pageSize[{}]", 
                questionnaireId, pageIndex, pageSize);
        
        // 检查参数
        if (questionnaireId == null) {
            return new ApiReturnResult(CodeEnum.IS_NULL.key(), "问卷ID不能为空");
        }
        
        // 检查是否有签到设置
        QstSignSettingPo settingPo = qstSignSettingService.selectByQuestionnaireId(questionnaireId);
        if (settingPo == null) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "该问卷尚未设置签到功能");
        }
        
        // 检查权限
        if (!settingPo.getUserId().equals(getUserId())) {
            return new ApiReturnResult(CodeEnum.PLEASE_LOGIN.key(), "无权限查看此签到记录");
        }
        
        // 查询问卷中的题目，找出姓名、邮箱、手机号的题目
        List<QstTitlePo> titleList = qstTitleService.selectList(questionnaireId, getUserId());
        Integer nameSerialNumber = null;  // 姓名题目序号
        Integer emailSerialNumber = null; // 邮箱题目序号
        Integer mobileSerialNumber = null; // 手机号题目序号
        
        for (QstTitlePo titlePo : titleList) {
            // validateType: 1-姓名, 2-邮箱, 3-手机号
            if (titlePo.getValidateType() != null) {
                if (titlePo.getValidateType() == 1) {
                    nameSerialNumber = titlePo.getSerialNumber();
                } else if (titlePo.getValidateType() == 2) {
                    emailSerialNumber = titlePo.getSerialNumber();
                } else if (titlePo.getValidateType() == 3) {
                    mobileSerialNumber = titlePo.getSerialNumber();
                }
            }
        }
        
        // 查询签到记录
        QstSignRecordQuery query = new QstSignRecordQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setStatus(1); // 有效状态
        query.setStartIndex(pageIndex * pageSize);
        query.setPageSize(pageSize);
        
        List<QstSignRecordPo> records = qstSignRecordService.selectListByPage(query);
        int total = qstSignRecordService.selectCount(query);
        
        if (records.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("records", new ArrayList<>());
            result.put("total", 0);
            return new ApiReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.SUCCESS.value(), result);
        }
        
        // 提取所有openid
        List<String> openidList = records.stream()
                .map(QstSignRecordPo::getOpenid)
                .collect(Collectors.toList());
        
        // 找出所有签到用户的答案
        Map<String, Map<Integer, String>> userAnswersMap = new HashMap<>();
        if (!openidList.isEmpty() && (nameSerialNumber != null || emailSerialNumber != null || mobileSerialNumber != null)) {
            // 查询所有用户的答案信息
            AnswerInfoQuery answerInfoQuery = new AnswerInfoQuery();
            answerInfoQuery.setQuestionnaireId(questionnaireId);
            
            // 使用Map存储每个用户的答题信息
            List<Integer> titleSerialNumbers = new ArrayList<>();
            if (nameSerialNumber != null) titleSerialNumbers.add(nameSerialNumber);
            if (emailSerialNumber != null) titleSerialNumbers.add(emailSerialNumber);
            if (mobileSerialNumber != null) titleSerialNumbers.add(mobileSerialNumber);
            
            if (!titleSerialNumbers.isEmpty()) {
                // 查询所有签到用户在这些题目上的答案
                AnswerOptionQuery optionQuery = new AnswerOptionQuery();
                optionQuery.setQuestionnaireId(questionnaireId);
                
                // 这里无法直接使用openid，因为AnswerOptionQuery没有openid字段
                // 所以需要先查询answerIds，再查询答案
                
                // 查询所有的openid对应的answerId
                List<String> answerIds = new ArrayList<>();
                for (String openid : openidList) {
                    AnswerInfoQuery answerQuery = new AnswerInfoQuery();
                    answerQuery.setQuestionnaireId(questionnaireId);
                    answerQuery.setOpenid(openid);
                    List<com.cas.nc.questionnaire.dao.po.AnswerInfoPo> answerInfos = answerInfoService.selectList(answerQuery);
                    if (!answerInfos.isEmpty()) {
                        answerIds.add(answerInfos.get(0).getAnswerId());
                    }
                }
                
                // 查询这些answerId的相关答案
                if (!answerIds.isEmpty()) {
                    for (String answerId : answerIds) {
                        // 查询用户的答案
                        List<AnswerOptionPo> answerOptions = answerOptionService.selectList(settingPo.getUserId(), answerId);
                        
                        Map<Integer, String> titleAnswerMap = new HashMap<>();
                        for (AnswerOptionPo answerOption : answerOptions) {
                            if (titleSerialNumbers.contains(answerOption.getTitleSerialNumber())) {
                                // 获取填写内容
                                titleAnswerMap.put(answerOption.getTitleSerialNumber(), answerOption.getWriteContent());
                            }
                        }
                        
                        // 查找这个answerId对应的openid
                        for (QstSignRecordPo record : records) {
                            AnswerInfoQuery infoQuery = new AnswerInfoQuery();
                            infoQuery.setQuestionnaireId(questionnaireId);
                            infoQuery.setOpenid(record.getOpenid());
                            List<com.cas.nc.questionnaire.dao.po.AnswerInfoPo> infos = answerInfoService.selectList(infoQuery);
                            if (!infos.isEmpty() && answerId.equals(infos.get(0).getAnswerId())) {
                                userAnswersMap.put(record.getOpenid(), titleAnswerMap);
                                break;
                            }
                        }
                    }
                }
            }
        }
        
        // 组装返回数据
        List<QstSignRecordListVo> resultRecords = new ArrayList<>();
        for (QstSignRecordPo record : records) {
            QstSignRecordListVo listVo = new QstSignRecordListVo();
            listVo.setId(record.getId());
            listVo.setSignId(record.getSignId());
            listVo.setOpenid(record.getOpenid());
            listVo.setNickname(record.getNickname());
            listVo.setHeadimgurl(record.getHeadimgurl());
            listVo.setSignTime(record.getSignTime());
            listVo.setSignDate(record.getSignDate());
            
            // 设置姓名、邮箱、手机号信息（优先使用问卷中填写的信息）
            Map<Integer, String> userAnswers = userAnswersMap.get(record.getOpenid());
            if (userAnswers != null) {
                if (nameSerialNumber != null && userAnswers.containsKey(nameSerialNumber)) {
                    listVo.setName(userAnswers.get(nameSerialNumber));
                } else {
                    // 没有姓名答案，使用微信昵称
                    listVo.setName(record.getNickname());
                }
                
                if (emailSerialNumber != null && userAnswers.containsKey(emailSerialNumber)) {
                    listVo.setEmail(userAnswers.get(emailSerialNumber));
                }
                
                if (mobileSerialNumber != null && userAnswers.containsKey(mobileSerialNumber)) {
                    listVo.setMobile(userAnswers.get(mobileSerialNumber));
                }
            } else {
                // 没有找到该用户的答案信息，使用微信昵称作为姓名
                listVo.setName(record.getNickname());
            }
            
            resultRecords.add(listVo);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", resultRecords);
        result.put("total", total);
        
        return new ApiReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.SUCCESS.value(), result);
    }

    /**
     * 获取签到设置详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public ApiReturnResult detail(@RequestParam("questionnaireId") String questionnaireId) {
        logger.info("SignController.detail param[{}]", questionnaireId);
        
        if (questionnaireId == null) {
            return new ApiReturnResult(CodeEnum.IS_NULL.key(), "问卷ID不能为空");
        }
        
        QstSignSettingPo po = qstSignSettingService.selectByQuestionnaireId(questionnaireId);
        if (po == null) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION.key(), "未找到该问卷的签到设置");
        }
        
        // 这里不做权限校验，允许所有人查看签到设置详情
        
        QstSignSettingDto dto = SignSettingConverter.INSTANCE.poToDto(po);
        QstSignSettingVo vo = SignSettingConverter.INSTANCE.dtoToVo(dto);
        
        return new ApiReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.SUCCESS.value(), vo);
    }
} 
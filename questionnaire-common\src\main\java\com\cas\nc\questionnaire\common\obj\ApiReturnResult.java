package com.cas.nc.questionnaire.common.obj;


import com.cas.nc.questionnaire.common.enums.CodeEnum;


public class ApiReturnResult<T> {

    private Integer code;
    private String msg;
    private Boolean success;
    private T data;
    private String sign;

    public ApiReturnResult() {

    }

    public ApiReturnResult(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        if (code.equals(CodeEnum.SUCCESS.key())) {
            this.success = true;
        } else {
            this.success = false;
        }
    }

    public ApiReturnResult(CodeEnum codeEnum) {
        this.code = codeEnum.key();
        this.msg = codeEnum.value();
        if (codeEnum.equals(CodeEnum.SUCCESS)) {
            this.success = true;
        } else {
            this.success = false;
        }
    }

    public ApiReturnResult(CodeEnum codeEnum, T data) {
        this.code = codeEnum.key();
        this.msg = codeEnum.value();
        this.data = data;
        if (codeEnum.equals(CodeEnum.SUCCESS)) {
            this.success = true;
        } else {
            this.success = false;
        }
    }

    public ApiReturnResult(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        if (code.equals(CodeEnum.SUCCESS.key())) {
            this.success = true;
        } else {
            this.success = false;
        }
    }

    public static <T> ApiReturnResult<T> success(T data) {
        return new ApiReturnResult<>(CodeEnum.SUCCESS, data);
    }

    public static <T> ApiReturnResult<T> success() {
        return new ApiReturnResult<>(CodeEnum.SUCCESS, null);
    }

    public static <T> ApiReturnResult<T> error(CodeEnum codeEnum) {
        return new ApiReturnResult<>(codeEnum);
    }

    public static <T> ApiReturnResult<T> error(Integer code, String msg) {
        return new ApiReturnResult<>(code, msg);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}

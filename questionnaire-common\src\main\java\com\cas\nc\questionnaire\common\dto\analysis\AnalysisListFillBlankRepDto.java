package com.cas.nc.questionnaire.common.dto.analysis;

import java.util.List;

public class AnalysisListFillBlankRepDto {
    private Integer count;

    private List<FillBlankRepDto> fillBlankRepDtoList;

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<FillBlankRepDto> getFillBlankRepDtoList() {
        return fillBlankRepDtoList;
    }

    public void setFillBlankRepDtoList(List<FillBlankRepDto> fillBlankRepDtoList) {
        this.fillBlankRepDtoList = fillBlankRepDtoList;
    }
}

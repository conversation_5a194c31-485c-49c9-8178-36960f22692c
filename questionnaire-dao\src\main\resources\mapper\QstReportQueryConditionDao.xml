<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.QstReportQueryConditionDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstReportQueryConditionPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="is_enabled" jdbcType="TINYINT" property="isEnabled"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="sql_columns">
        id, questionnaire_id, user_id, begin_time, end_time, is_enabled, create_time, update_time
    </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">and questionnaire_id = #{item.questionnaireId}</if>
            <if test="null != item.userId and '' != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.beginTime">and begin_time = #{item.beginTime}</if>
            <if test="null != item.endTime">and end_time = #{item.endTime}</if>
            <if test="null != item.isEnabled">and is_enabled = #{item.isEnabled}</if>
        </where>
    </sql>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_report_query_condition
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_report_query_condition
        <include refid="sql_where"/>
    </select>
    <select id="selectByQuestionnaireId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="sql_columns"/>
        from qst_report_query_condition
        where questionnaire_id = #{questionnaireId}
    </select>
    <sql id="sql_insert_columns">
        insert into qst_report_query_condition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.beginTime">begin_time,</if>
            <if test="null != item.endTime">end_time,</if>
            <if test="null != item.isEnabled">is_enabled,</if>
            <if test="null != item.createTime">create_time,</if>
            <if test="null != item.updateTime">update_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.beginTime">#{item.beginTime},</if>
            <if test="null != item.endTime">#{item.endTime},</if>
            <if test="null != item.isEnabled">#{item.isEnabled},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
        </trim>
    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.beginTime">begin_time = #{item.beginTime},</if>
            <if test="null != item.endTime">end_time = #{item.endTime},</if>
            <if test="null != item.isEnabled">is_enabled = #{item.isEnabled},</if>
            <if test="null != item.updateTime">update_time = #{item.updateTime},</if>
        </trim>
    </insert>
    <sql id="sql_update">
        update qst_report_query_condition
        <set>
            <if test="null != item.beginTime">begin_time = #{item.beginTime},</if>
            <if test="null != item.endTime">end_time = #{item.endTime},</if>
            <if test="null != item.isEnabled">is_enabled = #{item.isEnabled},</if>
            <if test="null != item.updateTime">update_time = #{item.updateTime},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_report_query_condition
        <include refid="sql_where"/>
    </delete>
    <delete id="deleteByQuestionnaireId" parameterType="java.lang.String">
        delete from qst_report_query_condition
        where questionnaire_id = #{questionnaireId}
    </delete>
</mapper> 
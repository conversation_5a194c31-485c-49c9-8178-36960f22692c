package com.cas.nc.questionnaire.dao.sharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;
import com.cas.nc.questionnaire.dao.query.QstLimitRuleQuery;
import org.apache.ibatis.annotations.Param;

public interface QstLimitRuleDao extends BaseDao<QstLimitRulePo, QstLimitRuleQuery> {
    int updateDealTime(@Param("item") QstLimitRuleQuery query);
}
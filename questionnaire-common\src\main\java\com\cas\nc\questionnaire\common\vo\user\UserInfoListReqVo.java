package com.cas.nc.questionnaire.common.vo.user;

import com.cas.nc.questionnaire.common.vo.base.BaseRequestVo;

public class UserInfoListReqVo extends BaseRequestVo {

    /*账号*/
    private String userName;
    /*姓名*/
    private String name;
    /*账号类型*/
    private Integer type;
    /*账号状态*/
    private Integer status;
    /*每页行数*/
    private Integer pageSize;
    /*导向页*/
    private Integer page;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

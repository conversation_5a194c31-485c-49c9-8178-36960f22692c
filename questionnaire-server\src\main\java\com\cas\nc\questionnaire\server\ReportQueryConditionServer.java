package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionGetReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionGetRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionSaveReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionSaveRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionUserReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionUserRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryAnswerListReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryAnswerListRepDto;

/**
 * 测评报告查询条件服务接口
 */
public interface ReportQueryConditionServer {
    
    /**
     * 保存查询条件
     * @param reqDto 请求参数
     * @return 响应结果
     */
    ReportQueryConditionSaveRepDto saveReportQueryCondition(ReportQueryConditionSaveReqDto reqDto);
    
    /**
     * 获取查询条件详情
     * @param reqDto 请求参数
     * @return 响应结果
     */
    ReportQueryConditionGetRepDto getReportQueryCondition(ReportQueryConditionGetReqDto reqDto);
    
    /**
     * 用户获取查询条件详情（包含时间校验）
     * @param reqDto 请求参数
     * @return 响应结果
     */
    ReportQueryConditionUserRepDto getUserReportQueryCondition(ReportQueryConditionUserReqDto reqDto);
    
    /**
     * 查询答题列表
     * @param reqDto 请求参数
     * @return 答题列表
     */
    ReportQueryAnswerListRepDto queryAnswerList(ReportQueryAnswerListReqDto reqDto);
}
 

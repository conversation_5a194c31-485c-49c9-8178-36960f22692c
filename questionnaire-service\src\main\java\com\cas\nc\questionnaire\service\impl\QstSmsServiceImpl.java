package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.po.QstSmsPo;
import com.cas.nc.questionnaire.dao.query.QstSmsQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstSmsDao;
import com.cas.nc.questionnaire.service.QstSmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.ANSWERED;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.INIT;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.OPEN_NOT_ANSWER;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.SEND;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.isAnswered;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.isOpened;
import static com.cas.nc.questionnaire.common.enums.SendStatusEnum.isSended;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;

@Service
public class QstSmsServiceImpl implements QstSmsService {
    private static Logger logger = LoggerFactory.getLogger(QstSmsServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstSmsDao qstSmsDao;

    @Override
    public int insert(QstSmsPo qstSmsPo) {
        return qstSmsDao.insert(qstSmsPo);
    }

    @Override
    public int delete(QstSmsQuery query) {
        validateUserId(query);
        return qstSmsDao.delete(query);
    }

    @Override
    public int delete(String smsId, Long userId) {
        QstSmsQuery query = new QstSmsQuery();
        query.setSmsId(smsId);
        query.setUserId(userId);
        return delete(query);
    }

    @Override
    public List<QstSmsPo> selectList(QstSmsQuery query) {
        validateUserId(query);
        return qstSmsDao.selectList(query);
    }

    @Override
    public QstSmsPo selectOne(QstSmsQuery query) {
        validateUserId(query);
        return qstSmsDao.selectOne(query);
    }

    @Override
    public QstSmsPo selectOne(String smsId, Long userId) {
        QstSmsQuery query = new QstSmsQuery();
        query.setSmsId(smsId);
        query.setUserId(userId);
        return selectOne(query);
    }

    @Override
    public QstSmsPo selectOne(String smsId) {
        QstSmsQuery query = new QstSmsQuery();
        query.setSmsId(smsId);
        filterCondition(query);
        return selectOne(query);
    }

    @Override
    public int update(QstSmsQuery query) {
        validateUserId(query);
        return qstSmsDao.update(query);
    }

    @Override
    public int updateStatus2Send(String smsId) {
        QstSmsQuery query = new QstSmsQuery();
        query.setSmsId(smsId);
        query.setStatus(SEND.key());
        query.setOldStatus(INIT.key());
        filterCondition(query);

        int result = update(query);
        if (result != ONE) {
            QstSmsPo smsPo = selectOne(smsId);
            if (smsPo != null && isSended(smsPo.getStatus())) {
                result = ONE;
            }
        }
        return result;
    }

    @Override
    public int updateStatus2Open(String smsId) {
        QstSmsQuery query = new QstSmsQuery();
        query.setSmsId(smsId);
        query.setStatus(OPEN_NOT_ANSWER.key());
        query.setOldStatus(SEND.key());
        filterCondition(query);

        int result = update(query);
        if (result != ONE) {
            QstSmsPo smsPo = selectOne(smsId);
            if (smsPo != null && isOpened(smsPo.getStatus())) {
                result = ONE;
            }
        }
        return result;
    }

    @Override
    public int updateStatus2Answered(String smsId) {
        QstSmsQuery query = new QstSmsQuery();
        query.setSmsId(smsId);
        query.setStatus(ANSWERED.key());
        query.setOldStatus(OPEN_NOT_ANSWER.key());
        filterCondition(query);

        int result = update(query);
        if (result != ONE) {
            QstSmsPo smsPo = selectOne(smsId);
            if (smsPo != null && isAnswered(smsPo.getStatus())) {
                result = ONE;
            }
        }
        return result;
    }

    private void validateUserId(QstSmsQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }

    private void filterCondition(QstSmsQuery query) {
        Assert.notNull(query.getSmsId(), "smsId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getSmsId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

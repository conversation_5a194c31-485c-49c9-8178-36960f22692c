<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.TestDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.TestPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="q_id" jdbcType="VARCHAR" property="qId"/>
        <result column="serial_number" jdbcType="INTEGER" property="serialNumber"/>
        <result column="option_number" jdbcType="INTEGER" property="optionNumber"/>
        <result column="test_time" jdbcType="TIMESTAMP" property="testTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,name,update_time,create_time,q_id,serial_number,option_number,
    test_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.name and '' != item.name">and name = #{item.name}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
            <if test="null != item.qId and '' != item.qId">and q_id = #{item.qId}</if>
            <if test="null != item.serialNumber and '' != item.serialNumber">and serial_number = #{item.serialNumber}
            </if>
            <if test="null != item.optionNumber and '' != item.optionNumber">and option_number = #{item.optionNumber}
            </if>
            <if test="null != item.testTime and '' != item.testTime">and test_time = #{item.testTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from test
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from test
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from test
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into test
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.name">name,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
            <if test="null != item.qId">q_id,</if>
            <if test="null != item.serialNumber">serial_number,</if>
            <if test="null != item.optionNumber">option_number,</if>
            <if test="null != item.testTime">test_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.name">#{item.name},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
            <if test="null != item.qId">#{item.qId},</if>
            <if test="null != item.serialNumber">#{item.serialNumber},</if>
            <if test="null != item.optionNumber">#{item.optionNumber},</if>
            <if test="null != item.testTime">#{item.testTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.name">name = values(name),</if>
            <if test="null != item.qId">q_id = values(q_id),</if>
            <if test="null != item.serialNumber">serial_number = values(serial_number),</if>
            <if test="null != item.optionNumber">option_number = values(option_number),</if>
            <if test="null != item.testTime">test_time = values(test_time),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update test
        <set>
            <if test="null != item.name">name = #{item.name},</if>
            <if test="null != item.qId">q_id = #{item.qId},</if>
            <if test="null != item.serialNumber">serial_number = #{item.serialNumber},</if>
            <if test="null != item.optionNumber">option_number = #{item.optionNumber},</if>
            <if test="null != item.testTime">test_time = #{item.testTime},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from test
        <include refid="sql_where"/>
    </delete>
</mapper>
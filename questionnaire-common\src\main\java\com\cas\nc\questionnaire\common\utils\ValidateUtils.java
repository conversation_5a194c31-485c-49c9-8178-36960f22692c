package com.cas.nc.questionnaire.common.utils;

import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ValidateUtils {

    private static Logger logger = LoggerFactory.getLogger(ValidateUtils.class);

    /**
     * 将参数数组中的参数排除，不做校验
     *
     * @param thisObj
     * @param params
     * @throws Exception
     */
    public static void validateNotNullExclude(Object thisObj, String... params) {
        if (thisObj == null) {
            throw new ServerException("param", CodeEnum.IS_NULL);
        }
        try {
            for (Class<?> clazz = thisObj.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
                tag:
                for (Field field : clazz.getDeclaredFields()) {
                    String name = field.getName();
                    if (params != null) {
                        for (String method : params) {
                            if (method.equals(name)) {
                                continue tag;
                            }
                        }
                    }
                    String strGet = "get" + name.substring(0, 1).toUpperCase() + name.substring(1, name.length());
                    Method methodGet = clazz.getDeclaredMethod(strGet);
                    Object object = methodGet.invoke(thisObj);
                    if (object == null || StringUtils.isBlank(object.toString())) {
                        throw new ServerException(name, CodeEnum.IS_NULL);
                    } else {
                        if (!isExclusionType(object)) {
                            validateNotNullExclude(object, params);
                        }
                    }
                }
            }
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("ValidateUtils.validateNotNullExclude Exception", e);
            throw new ServerException(CodeEnum.VALIDATE_PARAM_EXCEPTION);
        }
    }

    public static boolean isExclusionType(Object object) {
        if (isWrapClass(object.getClass())) {
            return true;
        }
        if (object instanceof String
                || object instanceof BigDecimal
                || object instanceof Date) {
            return true;
        }
        return false;
    }

    public static boolean isWrapClass(Class clz) {
        try {
            return ((Class) clz.getField("TYPE").get(null)).isPrimitive();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 仅对参数数据中的参数校验
     *
     * @param thisObj
     * @param params
     * @throws Exception
     */
    public static void validateNotNullContain(Object thisObj, String... params) {
        if (thisObj == null) {
            throw new ServerException("param", CodeEnum.IS_NULL);
        }
        try {
            for (Class<?> clazz = thisObj.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
                for (Field field : clazz.getDeclaredFields()) {
                    String name = field.getName();
                    for (String method : params) {
                        if (method.equals(name)) {
                            String strGet = "get" + name.substring(0, 1).toUpperCase() + name.substring(1, name.length());
                            Method methodGet = clazz.getDeclaredMethod(strGet);
                            Object object = methodGet.invoke(thisObj);
                            if (object == null || StringUtils.isBlank(object.toString())) {
                                throw new ServerException(name, CodeEnum.IS_NULL);
                            }
                        }
                    }
                }
            }
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("ValidateUtils.validateNotNullContain Exception", e);
            throw new ServerException(CodeEnum.VALIDATE_PARAM_EXCEPTION);
        }
    }

    public static Method getDeclaredMethod(Object object, String methodName, Class<?>... parameterTypes) {
        Method method = null;

        for (Class<?> clazz = object.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                method = clazz.getDeclaredMethod(methodName, parameterTypes);
                System.out.println(1);
                return method;
            } catch (Exception e) {
                e.printStackTrace();
                //这里甚么都不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会进入
            }
        }

        return null;
    }


    public static String convertStr(Object obj) {
        if (obj == null) {
            return "";
        }
        return obj.toString();
    }

    public static Map<String, Object> transferMap(Object... params) {
        if (params == null || (params.length & 1) != 0) {
            throw new RuntimeException();
        }

        Map<String, Object> map = new HashMap<String, Object>();
        for (int i = 0; i < params.length; i += 2) {
            map.put(params[i].toString(), params[i + 1]);
        }

        return map;

    }

    /**
     * 前者是否含于后者
     *
     * @param taget
     * @param source
     * @return
     */
    public static boolean isContain(int taget, int source) {
        return (taget & source) == taget;
    }

}

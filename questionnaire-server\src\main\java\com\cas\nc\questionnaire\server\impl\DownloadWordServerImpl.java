package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.analysis.AnalysisReportOptionRepDto;
import com.cas.nc.questionnaire.common.dto.analysis.AnalysisReportTitleRepDto;
import com.cas.nc.questionnaire.common.dto.analysis.DownloadWordReqDto;
import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.cas.nc.questionnaire.dao.query.QstQuestionnaireInfoQuery;
import com.cas.nc.questionnaire.server.AnalysisServer;
import com.cas.nc.questionnaire.server.DownloadWordServer;
import com.cas.nc.questionnaire.server.mapstruct.DownloadWordConverter;
import com.cas.nc.questionnaire.server.util.DocStyleUtils;
import com.cas.nc.questionnaire.service.QstOptionService;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import com.cas.nc.questionnaire.service.QstTitleService;
import com.lowagie.text.*;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.NO_ANSWER;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.QST_NOT_EXIST;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.*;
import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_TITLE_SPLIT;
import static com.cas.nc.questionnaire.common.utils.Constants.DAN_XUAN_REMARK;
import static com.cas.nc.questionnaire.common.utils.Constants.DUO_XUAN_REMARK;
import static com.cas.nc.questionnaire.common.utils.Constants.FILL_BLANK_DOWNLOAD_INDEX;
import static com.cas.nc.questionnaire.common.utils.Constants.P1;
import static com.cas.nc.questionnaire.common.utils.Constants.P2;
import static com.cas.nc.questionnaire.common.utils.Constants.PAI_XU_REMARK;
import static com.cas.nc.questionnaire.common.utils.Constants.PAI_XU_TITLE_DESC;
import static com.cas.nc.questionnaire.common.utils.Constants.PERCENTAGE_MARK;
import static com.cas.nc.questionnaire.common.utils.Constants.TIAN_KONG_LINE;

@Component("downloadWordServer")
public class DownloadWordServerImpl implements DownloadWordServer {

    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;
    @Resource
    private AnalysisServer analysisServer;
    @Resource
    private QstTitleService qstTitleService;
    @Resource
    private QstOptionService qstOptionService;

    @Override
    public QstQuestionnaireInfoPo getQuestionnaire(String questionnaireId, Long userId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setTableColumns("title,biz_id,biz_no");

        QstQuestionnaireInfoPo po = qstQuestionnaireInfoService.selectOne(query);
        Assert.notNull(po, QST_NOT_EXIST);
        return po;
    }

    @Override
    public void writeWordDoc(DownloadWordReqDto reqDto, Document doc) throws DocumentException, IOException {
        List<AnalysisReportTitleRepDto> analysisTitleList = analysisServer.getAnalysisTitleList(DownloadWordConverter.INSTANCE.to(reqDto));
        Assert.notNull(analysisTitleList, NO_ANSWER);

        //设置标题字体样式，粗体、二号、华文中宋
        Font titleFont = DocStyleUtils.setFontStyle("华文中宋", 22f, Font.BOLD);
        //设置正文内容的字体样式，常规、三号、仿宋_GB2312
        Font contentFont = DocStyleUtils.setFontStyle("仿宋_GB2312", 13f, Font.NORMAL);
        //构建标题，居中对齐，12f表示单倍行距
        Paragraph title = DocStyleUtils.setParagraphStyle(reqDto.getTitle(), titleFont, 12f, Paragraph.ALIGN_CENTER);
        Paragraph blankRow = new Paragraph(18f, " ", contentFont);
        doc.add(title);
        doc.add(blankRow);

        Table titleTable = null;
        boolean display = false;
        Map<Integer, String>  imgMap = reqDto.getImg();
        for (AnalysisReportTitleRepDto bean : analysisTitleList) {
            doc.add(getTitleParagraph(bean.getHao(), bean.getName(), bean.getTypeInt(), contentFont));

            if (isFillBlank(bean.getTypeInt())) {
                display = true;
            } else if (isJuZhenHuaDongTiao(bean.getTypeInt())) {
                titleTable = juZhenHuaDongTiaoParagraph(bean, contentFont);
            } else if (isBiZhong(bean.getTypeInt())) {
                titleTable = biZhongParagraph(bean, contentFont, true);
            } else if (isHuaDongTiao(bean.getTypeInt())) {
                doc.add(huaDongTiaoParagraph(bean, contentFont));
                doc.add(blankRow);
                continue;
            } else if (isPaiXu(bean.getTypeInt())) {
                titleTable = oneDimensionalParagraph(bean, contentFont, true);
            } else if (isAnalysisOneDimensional(bean.getTypeInt())) {
                if (isLiangBiao(bean.getTypeInt())) {
                    doc.add(averageValueParagraph(bean, contentFont));
                }
                titleTable = oneDimensionalParagraph(bean, contentFont, false);
            } else if (isAnalysisTwoDimensional(bean.getTypeInt())) {
                titleTable = twoDimensionalParagraph(bean, contentFont);
            } else if (isAnalysisThreeDimensional(bean.getTypeInt())) {
                titleTable = threeDimensionalParagraph(bean, contentFont);
            } else {
                continue;
            }

            if (display) {
                doc.add(getFillBlankIndexParagraph());
                display = false;
            } else {
                doc.add(titleTable);
                doc.add(answerCountParagraph(bean, contentFont));
            }
            doc.add(blankRow);
            String path = imgMap.get(bean.getHao()-1);
            if(StringUtils.isNotBlank(path)) {
                Image image = Image.getInstance(path);
                image.scaleToFit(PageSize.A4.getWidth() - 100f, PageSize.A4.getHeight());
                doc.add(image);
                doc.add(blankRow);
//                String base64Img = img.get("pic");
//                if(StringUtils.isNotBlank(base64Img)) {
//                    base64Img = StringUtils.substring(base64Img, base64Img.indexOf(",") + 1);
//                    byte[] imageBytes = Base64.getDecoder().decode(base64Img);
//                    Image image = Image.getInstance(imageBytes);
//                    image.scaleToFit(PageSize.A4.getWidth() -100f, PageSize.A4.getHeight());
//                    doc.add(image);
//                    doc.add(blankRow);
//                }
            }
        }

        //删除暂存的图片
        for(Map.Entry<Integer, String> entry: imgMap.entrySet()) {
            String fileName = entry.getValue();
            if(StringUtils.isNotBlank(fileName)) {
                File file = new File(fileName);
                file.delete();
            }
        }
    }

    @Override
    public void qstWriteWordDoc(DownloadWordReqDto reqDto, Document doc) throws DocumentException {
        List<QstTitlePo> titlePoList = qstTitleService.selectList(reqDto.getQuestionnaireId(), reqDto.getUserId());
        titlePoList = SafeUtil.of(titlePoList).stream().sorted(Comparator.comparing(QstTitlePo::getGlobalOrder)).collect(Collectors.toList());

        List<QstOptionPo> qstOptionPoList = qstOptionService.selectList(reqDto.getQuestionnaireId(), reqDto.getUserId());
        Map<Integer, List<QstOptionPo>> optionMap = qstOptionPoList.stream().collect(Collectors.groupingBy(QstOptionPo::getTitleSerialNumber));
        //设置标题字体样式，粗体、二号、华文中宋
        Font titleFont = DocStyleUtils.setFontStyle("华文中宋", 22f, Font.BOLD);
        //设置正文内容的字体样式，常规、三号、仿宋_GB2312
        Font contentFont = DocStyleUtils.setFontStyle("仿宋_GB2312", 13f, Font.NORMAL);
        //构建标题，居中对齐，12f表示单倍行距
        Paragraph title = DocStyleUtils.setParagraphStyle(reqDto.getTitle(), titleFont, 12f, Paragraph.ALIGN_CENTER);
        Paragraph blankRow = new Paragraph(18f, " ", contentFont);
        doc.add(title);
        doc.add(blankRow);

        for (QstTitlePo bean : titlePoList) {
            List<QstOptionPo> optionList = optionMap.get(bean.getSerialNumber());
            optionList = SafeUtil.of(optionList).stream().sorted(Comparator.comparing(QstOptionPo::getOrderNumber)).collect(Collectors.toList());
            //题目
            if (isBiaoGeXiaLaXuanZe(bean.getType())) {
                String biaoGeXiaLaTitleExt = getBiaoGeXiaLaXuanZeTitleExt(optionList);
                doc.add(getTitleParagraph(bean.getOrderNumber(), bean.getName(), biaoGeXiaLaTitleExt, contentFont));
            } else if (isMiaoShuShuoMing(bean.getType())) {
                String name = bean.getName();
                name = name.replace(P1, "").replace(P2, "");
                doc.add(new Paragraph(name, contentFont));
            } else if (isXiaLaXuanZe(bean.getType())) {
                doc.add(getTitleParagraph(bean.getOrderNumber(), bean.getName(), DANXUAN.value(), contentFont));
            } else if (isHuaDongTiao(bean.getType())) {
                String huaDongTiaoTitleExt = getHuaDongTiaoTitleExt(bean, optionList);
                doc.add(getTitleParagraph(bean.getOrderNumber(), bean.getName(), huaDongTiaoTitleExt, contentFont));
            } else if (isJuZhenHuaDongTiao(bean.getType())) {
                String juZhenHuaDongTiaoTitleExt = getJuZhenHuaDongTiaoTitleExt(bean);
                doc.add(getTitleParagraph(bean.getOrderNumber(), bean.getName(), juZhenHuaDongTiaoTitleExt, contentFont));
            } else if (isBiZhong(bean.getType())) {
                String titleExt = "请填写数字，所有项和必须等于100";
                doc.add(getTitleParagraph(bean.getOrderNumber(), bean.getName(), titleExt, contentFont));
            } else {
                String name = bean.getName();
                if (isPaiXu(bean.getType())) {
                    name = name + PAI_XU_TITLE_DESC;
                }
                doc.add(getTitleParagraph(bean.getOrderNumber(), name, bean.getType(), contentFont));
            }

            //选项
            if (ONE_DIMENSIONAL_DANXUAN_LIST.contains(toEnum(bean.getType()))) {
                if (isLiangBiao(bean.getType())) {
                    doc.add(getLiangBiaoOptionParagraph(optionList, contentFont));
                } else {
                    for (QstOptionPo qstOptionPo : optionList) {
                        doc.add(getDanXuanOptionParagraph(qstOptionPo.getContent(), contentFont));
                    }
                }
            } else if (ONE_DIMENSIONAL_DUOXUAN_LIST.contains(toEnum(bean.getType()))) {
                for (QstOptionPo qstOptionPo : optionList) {
                    doc.add(getDuoXuanOptionParagraph(qstOptionPo.getContent(), contentFont));
                }
            } else if (isPaiXu(bean.getType())) {
                for (QstOptionPo qstOptionPo : optionList) {
                    doc.add(getPaiXuParagraph(qstOptionPo.getContent(), contentFont));
                }
            } else if (isDuoXiangDanHangTianKong(bean.getType())) {
                doc.add(duoXiangDanHangTianKongParagraph(optionList));
            } else if (ONE_DIMENSIONAL_FILL_BLANK_LIST.contains(toEnum(bean.getType())) || isHuaDongTiao(bean.getType())) {
                doc.add(new Paragraph(TIAN_KONG_LINE, contentFont));
            } else if (isBiaoGeDanXuan(bean.getType())) {
                doc.add(biaoGeDanXuanParagraph(optionList));
            } else if (isBiaoGeDuoXuan(bean.getType())) {
                doc.add(biaoGeDuoXuanParagraph(optionList));
            } else if (TWO_DIMENSIONAL_FILL_BLANK_LIST.contains(toEnum(bean.getType())) || isBiaoGeXiaLaXuanZe(bean.getType())) {
                doc.add(twoDimensionalParagraph(optionList, ""));
            } else if (isBiZhong(bean.getType()) || isJuZhenHuaDongTiao(bean.getType())) {
                doc.add(duoXiangDanHangTianKongParagraph(optionList));
            }

            doc.add(blankRow);
        }
    }

    private String getBiaoGeXiaLaXuanZeTitleExt(List<QstOptionPo> optionList) {
        Set<String> contentTitleSet = new HashSet<>();

        for (QstOptionPo bean : optionList) {
            contentTitleSet.add(bean.getContent());
        }

        StringBuffer buffer = new StringBuffer();
        buffer.append("请填写");
        buffer.append("(");
        int i = 0;
        int size = contentTitleSet.size();
        for (String content : contentTitleSet) {
            i++;
            buffer.append(content);
            if (i != size) {
                buffer.append("、");
            }
        }
        buffer.append(")");

        return buffer.toString();

    }

    private Table biaoGeDanXuanParagraph(List<QstOptionPo> optionList) throws BadElementException {
        return twoDimensionalParagraph(optionList, DAN_XUAN_REMARK, false);
    }

    private Table duoXiangDanHangTianKongParagraph(List<QstOptionPo> optionList) throws DocumentException {
        Table table = new Table(2, optionList.size());
        table.setAlignment(5);
        table.endHeaders();
        table.setWidths(new int[]{20, 80});
        for (QstOptionPo qstOptionPo : optionList) {
            table.addCell(getCell(qstOptionPo.getRowTitle()));
            table.addCell(getCell(""));
        }
        return table;
    }

    private Table biaoGeDuoXuanParagraph(List<QstOptionPo> optionList) throws BadElementException {
        return twoDimensionalParagraph(optionList, DUO_XUAN_REMARK, false);
    }

    private Table twoDimensionalParagraph(List<QstOptionPo> optionList, String remark, boolean isFillBlank) throws BadElementException {
        Map<Integer, List<QstOptionPo>> optionListMap = optionList.stream().collect(Collectors.groupingBy(QstOptionPo::getRowNumber));
        List<QstOptionPo> optionPoList = new ArrayList<>();
        List<String> rowTitleList = new ArrayList<>();

        for (List<QstOptionPo> qstOptionPoList : optionListMap.values()) {
            optionPoList = qstOptionPoList;
            rowTitleList.add(qstOptionPoList.get(0).getRowTitle());
        }

        Table table = new Table(optionPoList.size() + 1, optionListMap.size());
        table.setAlignment(5);
        table.addCell(getCell(""));
        for (QstOptionPo qstOptionPo : optionPoList) {
            if (isFillBlank) {
                table.addCell(getCell(qstOptionPo.getRowTitle()));
            } else {
                table.addCell(getCell(qstOptionPo.getContent()));
            }
        }
        table.endHeaders();

        for (String rowTitle : rowTitleList) {
            table.addCell(getCell(rowTitle));

            for (int i = 0, size = optionPoList.size(); i < size; i++) {
                table.addCell(getCell(remark));
            }
        }

        return table;
    }

    private Table twoDimensionalParagraph(List<QstOptionPo> optionList, String remark) throws BadElementException {
        Set<String> rowTitleSet = new HashSet<>();
        Set<String> columnTitleSet = new HashSet<>();
        Set<String> contentTitleSet = new HashSet<>();

        for (QstOptionPo bean : optionList) {
            rowTitleSet.add(bean.getRowTitle());
            columnTitleSet.add(bean.getColumnTitle());
            contentTitleSet.add(bean.getContent());
        }

        Table table = new Table(columnTitleSet.size() + 1, rowTitleSet.size());
        table.setAlignment(5);
        table.addCell(getCell(""));
        for (String columnTitle : columnTitleSet) {
            table.addCell(getCell(columnTitle));
        }
        table.endHeaders();

        for (String rowTitle : rowTitleSet) {
            table.addCell(getCell(rowTitle));

            for (int i = 0, size = columnTitleSet.size(); i < size; i++) {
                table.addCell(getCell(remark));
            }
        }

        return table;
    }

    public String getHuaDongTiaoTitleExt(QstTitlePo qstTitlePo, List<QstOptionPo> optionList) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("请填写");
        QstOptionPo firstQstOptionPo = optionList.get(0);
        buffer.append(qstTitlePo.getMinBoundsValue());
        buffer.append("(");
        buffer.append(firstQstOptionPo.getRowTitle());
        buffer.append(")到");

        QstOptionPo lastQstOptionPo = optionList.get(optionList.size() - 1);
        buffer.append(qstTitlePo.getMaxBoundsValue());
        buffer.append("(");
        buffer.append(lastQstOptionPo.getColumnTitle());
        buffer.append(")的数字");

        return buffer.toString();
    }

    public String getJuZhenHuaDongTiaoTitleExt(QstTitlePo qstTitlePo) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("请填写");
        buffer.append(qstTitlePo.getMinBoundsValue());
        buffer.append("到");

        buffer.append(qstTitlePo.getMaxBoundsValue());
        buffer.append("的数字");

        return buffer.toString();
    }

    private Table getLiangBiaoOptionParagraph(List<QstOptionPo> optionList, Font contentFont) throws BadElementException {
        int i = 1;
        int optionListSize = optionList.size();

        Table table = new Table(optionListSize + 2, 1);
        table.setAlignment(5);

        for (QstOptionPo qstOptionPo : optionList) {
            if (i == 1) {
                table.addCell(getNoBorderCell(qstOptionPo.getContent()));
            }
            table.addCell(getNoBorderCell(DAN_XUAN_REMARK + qstOptionPo.getSerialNumber()));
            if (i == optionListSize) {
                table.addCell(getNoBorderCell(qstOptionPo.getContent()));
            }
            i++;
        }
        table.endHeaders();

        return table;
    }

    private Paragraph getFillBlankIndexParagraph() {
        Font indexFont = DocStyleUtils.setFontStyle("华文中宋", Color.RED, 11f, Font.BOLD);
        return DocStyleUtils.setParagraphStyle(FILL_BLANK_DOWNLOAD_INDEX, indexFont, 12f, Paragraph.ALIGN_LEFT);
    }

    private Table threeDimensionalParagraph(AnalysisReportTitleRepDto bean, Font contentFont) throws BadElementException {
        int row = bean.getOptionList().size();
        Table table = getHeaderTable(row, false);

        for (AnalysisReportOptionRepDto optionRepDto : SafeUtil.of(bean.getOptionList())) {
            String title = constructTitle(optionRepDto.getRowTitle(), optionRepDto.getColumnTitle(), optionRepDto.getContent());

            table.addCell(getCell(title));
            table.addCell(getCell(optionRepDto.getTotal().toString()));
            table.addCell(getCell(getPercentageStr(optionRepDto.getPercentage())));
        }

        return table;
    }

    private String constructTitle(String rowTitle, String columnTitle, String content) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(rowTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(columnTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(content);

        return buffer.toString();
    }

    private Table twoDimensionalParagraph(AnalysisReportTitleRepDto bean, Font contentFont) throws BadElementException {
        int row = bean.getOptionList().size();
        Table table = getHeaderTable(row, false);

        for (AnalysisReportOptionRepDto optionRepDto : SafeUtil.of(bean.getOptionList())) {
            String title = constructTitle(optionRepDto.getRowTitle(), optionRepDto.getContent());

            table.addCell(getCell(title));
            table.addCell(getCell(optionRepDto.getTotal().toString()));
            table.addCell(getCell(getPercentageStr(optionRepDto.getPercentage())));
        }

        return table;
    }

    public String constructTitle(String rowTitle, String columnTitle) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(rowTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(columnTitle);

        return buffer.toString();
    }

    private Paragraph huaDongTiaoParagraph(AnalysisReportTitleRepDto bean, Font contentFont) throws BadElementException {

        StringBuffer buffer = new StringBuffer();
        buffer.append("本题有效答题次数：");
        buffer.append(bean.getTotal());
        buffer.append(" ");
        buffer.append("总计：");
        buffer.append(bean.getTotalValue());
        buffer.append(" ");
        buffer.append("平均：");
        buffer.append(bean.getAverageValue());

        return DocStyleUtils.setParagraphStyle(buffer.toString(), contentFont, 12f, Paragraph.ALIGN_LEFT);
    }

    private Paragraph averageValueParagraph(AnalysisReportTitleRepDto bean, Font contentFont) throws BadElementException {

        StringBuffer buffer = new StringBuffer();
        buffer.append("本题平均分：");
        buffer.append(bean.getAverageValue());

        return DocStyleUtils.setParagraphStyle(buffer.toString(), contentFont, 12f, Paragraph.ALIGN_LEFT);
    }

    private Paragraph answerCountParagraph(AnalysisReportTitleRepDto bean, Font contentFont) throws BadElementException {


        StringBuffer buffer = new StringBuffer();
        buffer.append("本题有效答题次数：");
        buffer.append(bean.getTotal());

        return DocStyleUtils.setParagraphStyle(buffer.toString(), contentFont, 12f, Paragraph.ALIGN_LEFT);
    }

    private Table biZhongParagraph(AnalysisReportTitleRepDto bean, Font contentFont, boolean score) throws BadElementException {
        int row = bean.getOptionList().size();
        Table table = getHeaderTable(row, score);

        for (AnalysisReportOptionRepDto optionRepDto : SafeUtil.of(bean.getOptionList())) {
            table.addCell(getCell(optionRepDto.getRowTitle()));
            table.addCell(getCell(optionRepDto.getAverageValue().toString()));
            table.addCell(getCell(getPercentageStr(optionRepDto.getPercentage())));
        }

        return table;
    }

    private Table juZhenHuaDongTiaoParagraph(AnalysisReportTitleRepDto bean, Font contentFont) throws BadElementException {
        int row = bean.getOptionList().size();
        Table table = getJuZhenHuaDongTiaoHeaderTable(row);
        if (CollectionUtils.isNotEmpty(bean.getOptionList())) {
            for (AnalysisReportOptionRepDto optionRepDto : bean.getOptionList()) {
                table.addCell(getCell(optionRepDto.getRowTitle()));
                table.addCell(getCell(optionRepDto.getAverageValue().toString()));
            }

            table.addCell("");
            table.addCell("总计：" + bean.getTotalValue() + " 平均值：" + bean.getAverageValue());
        }
        return table;
    }

    private Table oneDimensionalParagraph(AnalysisReportTitleRepDto bean, Font contentFont, boolean score) throws BadElementException {
        int row = bean.getOptionList().size();
        Table table = getHeaderTable(row, score);
        List<AnalysisReportOptionRepDto> repDtoList = SafeUtil.of(bean.getOptionList()).stream().sorted(Comparator.comparing(AnalysisReportOptionRepDto::getOrderNumber)).collect(Collectors.toList());

        for (AnalysisReportOptionRepDto optionRepDto : repDtoList) {
            table.addCell(getCell(optionRepDto.getContent()));
            if (score) {
                table.addCell(getCell(optionRepDto.getAverageValue().toString()));
            } else {
                table.addCell(getCell(optionRepDto.getTotal().toString()));
            }
            table.addCell(getCell(getPercentageStr(optionRepDto.getPercentage())));
        }

        return table;
    }

    private String getPercentageStr(BigDecimal percentage) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(percentage);
        buffer.append(PERCENTAGE_MARK);

        return buffer.toString();
    }

    private Table getHeaderTable(int row, boolean score) throws BadElementException {
        Table table = new Table(3, row);
        table.setAlignment(5);

        table.addCell(getCell("选项"));
        if (score) {
            table.addCell(getCell("平均分"));
        } else {
            table.addCell(getCell("有效答案数量"));
        }
        table.addCell(getCell("比例"));
        table.endHeaders();
        return table;
    }

    private Table getJuZhenHuaDongTiaoHeaderTable(int row) throws BadElementException {
        Table table = new Table(2, row);
        table.setAlignment(5);

        table.addCell(getCell("选项"));
        table.addCell(getCell("平均值"));
        table.endHeaders();
        return table;
    }

    private Paragraph getTitleParagraph(int serialNumber, String name, int titleType, Font contentFont) {
        StringBuffer buffer = new StringBuffer();

        buffer.append("第");
        buffer.append(serialNumber);
        buffer.append("题");
        buffer.append("  ");
        buffer.append(name);
        buffer.append("  ");
        buffer.append("【");
        buffer.append(TitleTypeEnum.toEnum(titleType).value());
        buffer.append("】");

        return new Paragraph(buffer.toString(), contentFont);
    }

    private Paragraph getTitleParagraph(int serialNumber, String name, String titleExt, Font contentFont) {
        StringBuffer buffer = new StringBuffer();

        buffer.append("第");
        buffer.append(serialNumber);
        buffer.append("题");
        buffer.append("  ");
        buffer.append(name);
        buffer.append("  ");
        buffer.append("【");
        buffer.append(titleExt);
        buffer.append("】");

        return new Paragraph(buffer.toString(), contentFont);
    }

    private Paragraph getDanXuanOptionParagraph(String name, Font contentFont) {
        return getOptionParagraph(DAN_XUAN_REMARK, name, contentFont);
    }

    private Paragraph getPaiXuParagraph(String name, Font contentFont) {
        return getOptionParagraph(PAI_XU_REMARK, name, contentFont);
    }

    private Paragraph getDuoXuanOptionParagraph(String name, Font contentFont) {
        return getOptionParagraph(DUO_XUAN_REMARK, name, contentFont);
    }

    private Paragraph getOptionParagraph(String optionRemark, String name, Font contentFont) {
        StringBuffer buffer = new StringBuffer();

        if (StringUtils.isNotBlank(optionRemark)) {
            buffer.append(optionRemark);
        }
        buffer.append(name);
        Paragraph paragraph = new Paragraph(buffer.toString(), contentFont);
        paragraph.setLeading(25);

        return paragraph;
    }

    private Cell getCell(String content) {
        Cell cell = new Cell(content);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        return cell;
    }

    private Cell getNoBorderCell(String content) {
        Cell cell = new Cell(content);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(-1);
        cell.setBorderWidth(-1);
        cell.setBorderColor(Color.white);
        cell.enableBorderSide(-1);

        return cell;
    }
}

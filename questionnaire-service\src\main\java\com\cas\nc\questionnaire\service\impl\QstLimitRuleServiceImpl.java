package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;
import com.cas.nc.questionnaire.dao.query.QstLimitRuleQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstLimitRuleDao;
import com.cas.nc.questionnaire.service.QstLimitRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class QstLimitRuleServiceImpl implements QstLimitRuleService {
    private static Logger logger = LoggerFactory.getLogger(QstLimitRuleServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstLimitRuleDao qstLimitRuleDao;

    @Override
    public int insert(QstLimitRulePo qstLimitRulePo) {
        return qstLimitRuleDao.insert(qstLimitRulePo);
    }

    @Override
    public int delete(QstLimitRuleQuery query) {
        validateUserId(query);
        return qstLimitRuleDao.delete(query);
    }

    @Override
    public List<QstLimitRulePo> selectList(QstLimitRuleQuery query) {
        validateUserId(query);
        return qstLimitRuleDao.selectList(query);
    }

    @Override
    public List<QstLimitRulePo> selectList(List<String> questionnaireList, Date beginDate, Date endDate) {
        QstLimitRuleQuery query = new QstLimitRuleQuery();
        query.setBeginTime(beginDate);
        query.setEndTime(endDate);
        query.setQuestionnaireIdList(questionnaireList);
        return qstLimitRuleDao.selectList(query);
    }

    @Override
    public QstLimitRulePo selectOne(QstLimitRuleQuery query) {
//        validateUserId(query);
        return qstLimitRuleDao.selectOne(query);
    }

    @Override
    public int insertUpdate(QstLimitRulePo qstLimitRulePo) {
        Assert.notNull(qstLimitRulePo.getUserId(), "userId");
        return qstLimitRuleDao.insertUpdate(qstLimitRulePo);
    }

    @Override
    public int update(QstLimitRuleQuery query) {
        validateUserId(query);
        return qstLimitRuleDao.update(query);
    }

    @Override
    public QstLimitRulePo selectOne(String questionnaireId) {
        QstLimitRuleQuery query = new QstLimitRuleQuery();
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);
        return selectOne(query);
    }

    @Override
    public QstLimitRulePo selectOne(String questionnaireId, Long userId) {
        QstLimitRuleQuery query = new QstLimitRuleQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        return selectOne(query);
    }

    @Override
    public int updateDealTime(QstLimitRuleQuery query) {
        filterCondition(query);
        return qstLimitRuleDao.updateDealTime(query);
    }

    private void validateUserId(QstLimitRuleQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }

    private void filterCondition(QstLimitRuleQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
//            query.setUserId(Long.valueOf(userId));
        }
    }
}

package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class TaskPo {
    /*自增id*/
    private Long id;

    /*业务单号*/
    private Long refId;

    /*外联业务单号*/
    private String foreignRefId;

    /*任务内容*/
    private String content;

    /*任务类型*/
    private Integer taskType;

    /*任务状态*/
    private Integer taskStatus;

    /*备注*/
    private String remark;

    /*失败次数*/
    private Integer failNum;

    /*url*/
    private String url;

    /*Ip*/
    private String ip;

    /*路由id*/
    private String routeId;

    /*分区*/
    private Integer taskPartition;

    /*创建时间*/
    private Date createTime;

    /*更新时间*/
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRefId() {
        return refId;
    }

    public void setRefId(Long refId) {
        this.refId = refId;
    }

    public String getForeignRefId() {
        return foreignRefId;
    }

    public void setForeignRefId(String foreignRefId) {
        this.foreignRefId = foreignRefId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getFailNum() {
        return failNum;
    }

    public void setFailNum(Integer failNum) {
        this.failNum = failNum;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getRouteId() {
        return routeId;
    }

    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    public Integer getTaskPartition() {
        return taskPartition;
    }

    public void setTaskPartition(Integer taskPartition) {
        this.taskPartition = taskPartition;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
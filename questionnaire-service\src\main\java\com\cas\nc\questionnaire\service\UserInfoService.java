package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.common.enums.TimeTypeEnum;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.UserInfoQuery;

import java.util.List;

public interface UserInfoService {
    /**
     * 数据插入
     *
     * @param userInfoPo
     * @return
     */
    int insert(UserInfoPo userInfoPo);

    /**
     * 依据条件查询单条数据
     *
     * @param query
     * @return
     */
    UserInfoPo selectOne(UserInfoQuery query);

    /**
     * 通过外部用户id查询问卷用户id
     *
     * @param outUserId
     * @return
     */
    Long queryUserId(String outUserId);

    /**
     * 将用户置为有效状态
     *
     * @param id
     * @return
     */
    int updateStatusEffective(Long id);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    UserInfoPo selectOne(Long id);

    void addMembershipDuration(Long userId, Integer timeCount, TimeTypeEnum toEnum);

    int updatePwd(Long userId, String oldPwd, String newPwd);

    int resetPwdErrorCount(Long userId);

    int setPwdErrorCount(Long userId, int count);

    int update(UserInfoQuery query);

    List<UserInfoPo> selectList(UserInfoQuery query);

    int insertUpdate(UserInfoPo userInfoPo);

    int selectCount(UserInfoQuery query);

    List<UserInfoPo> selectListByPage(UserInfoQuery query);

    UserInfoPo selectUserByEmailOrUsername(String email, String userName);
}

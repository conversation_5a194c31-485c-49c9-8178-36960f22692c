package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.utils.Assert;


public enum ConditionTypeEnum {
    UNCONDITIONAL(1, "无条件"),
    CONDITIONAL(2, "有条件"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    ConditionTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static ConditionTypeEnum toEnum(int key) {
        for (ConditionTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.DATA_EXCEPTION);
    }

    public static boolean filterLegal(int key) {
        for (ConditionTypeEnum bean : values()) {
            if (bean.equals(ELSE)) {
                continue;
            }
            if (bean.key.intValue() == key) {
                return true;
            }
        }
        return false;
    }

    public static boolean filterLegalException(int key) {
        Assert.isTrue(filterLegal(key), "ConditionType", CodeEnum.ILLEGAL);
        return true;
    }

    public static boolean isUnconditional(int key) {
        return UNCONDITIONAL.key.intValue() == key;
    }

    public static boolean isConditional(int key) {
        return CONDITIONAL.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

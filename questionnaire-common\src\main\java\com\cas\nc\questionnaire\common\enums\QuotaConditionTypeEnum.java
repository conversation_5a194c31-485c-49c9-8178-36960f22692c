package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.utils.Assert;


public enum QuotaConditionTypeEnum {
    PROVINCE(1, "省"),
    CITY(2, "市"),
    TITLE(3, "题目"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    QuotaConditionTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static QuotaConditionTypeEnum toEnum(int key) {
        for (QuotaConditionTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.DATA_EXCEPTION);
    }

    public static boolean filterLegal(int key) {
        for (QuotaConditionTypeEnum bean : values()) {
            if (bean.equals(ELSE)) {
                continue;
            }
            if (bean.key.intValue() == key) {
                return true;
            }
        }
        return false;
    }

    public static boolean filterLegalException(int key) {
        Assert.isTrue(filterLegal(key), "ConditionType", CodeEnum.ILLEGAL);
        return true;
    }

    public static boolean isProvince(int key) {
        return PROVINCE.key.intValue() == key;
    }

    public static boolean isCity(int key) {
        return CITY.key.intValue() == key;
    }

    public static boolean isTitle(int key) {
        return TITLE.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

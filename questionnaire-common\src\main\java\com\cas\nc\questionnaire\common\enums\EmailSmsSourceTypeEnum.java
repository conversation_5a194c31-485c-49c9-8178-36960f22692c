package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.utils.Assert;

public enum EmailSmsSourceTypeEnum {
    QST_SET(1, "问卷设置"),
    QST_SEND(2, "发送问卷"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    EmailSmsSourceTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static boolean filterLegal(int key) {
        for (EmailSmsSourceTypeEnum bean : values()) {
            if (bean.equals(ELSE)) {
                continue;
            }
            if (bean.key.intValue() == key) {
                return true;
            }
        }
        return false;
    }

    public static boolean filterLegalException(int key) {
        Assert.isTrue(filterLegal(key), "SourceType", CodeEnum.ILLEGAL);
        return true;
    }


    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.FileRelationDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.FileRelationPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="file_id" jdbcType="VARCHAR" property="fileId"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_suffix" jdbcType="VARCHAR" property="fileSuffix"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,file_id,questionnaire_id,file_name,file_suffix,file_url,create_time,
    update_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.fileId and '' != item.fileId">and file_id = #{item.fileId}</if>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">and questionnaire_id =
                #{item.questionnaireId}
            </if>
            <if test="null != item.fileName and '' != item.fileName">and file_name = #{item.fileName}</if>
            <if test="null != item.fileSuffix and '' != item.fileSuffix">and file_suffix = #{item.fileSuffix}</if>
            <if test="null != item.fileUrl and '' != item.fileUrl">and file_url = #{item.fileUrl}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from file_relation
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from file_relation
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from file_relation
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into file_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">id,</if>
            <if test="null != item.fileId">file_id,</if>
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.fileName">file_name,</if>
            <if test="null != item.fileSuffix">file_suffix,</if>
            <if test="null != item.fileUrl">file_url,</if>
            <if test="null != item.createTime">create_time,</if>
            <if test="null != item.updateTime">update_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">#{item.id},</if>
            <if test="null != item.fileId">#{item.fileId},</if>
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.fileName">#{item.fileName},</if>
            <if test="null != item.fileSuffix">#{item.fileSuffix},</if>
            <if test="null != item.fileUrl">#{item.fileUrl},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.id">id = values(id),</if>
            <if test="null != item.fileId">file_id = values(file_id),</if>
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.fileName">file_name = values(file_name),</if>
            <if test="null != item.fileSuffix">file_suffix = values(file_suffix),</if>
            <if test="null != item.fileUrl">file_url = values(file_url),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update file_relation
        <set>
            <if test="null != item.id">id = #{item.id},</if>
            <if test="null != item.fileId">file_id = #{item.fileId},</if>
            <if test="null != item.questionnaireId">questionnaire_id = #{item.questionnaireId},</if>
            <if test="null != item.fileName">file_name = #{item.fileName},</if>
            <if test="null != item.fileSuffix">file_suffix = #{item.fileSuffix},</if>
            <if test="null != item.fileUrl">file_url = #{item.fileUrl},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from file_relation
        <include refid="sql_where"/>
    </delete>
</mapper>
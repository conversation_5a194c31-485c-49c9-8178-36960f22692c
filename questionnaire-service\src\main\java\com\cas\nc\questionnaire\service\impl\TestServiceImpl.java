package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.dao.nosharddao.TestDao;
import com.cas.nc.questionnaire.dao.po.TestPo;
import com.cas.nc.questionnaire.dao.query.TestQuery;
import com.cas.nc.questionnaire.service.TestService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class TestServiceImpl implements TestService {

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private TestDao testDao;

    @Override
    public int insert(TestPo testPo) {
        return testDao.insert(testPo);
    }

    @Override
    public TestPo selectOne(TestQuery query) {
        return testDao.selectOne(query);
    }
}

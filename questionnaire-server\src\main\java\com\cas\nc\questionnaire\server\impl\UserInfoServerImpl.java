package com.cas.nc.questionnaire.server.impl;

import cn.hutool.core.bean.BeanUtil;
import com.cas.nc.questionnaire.common.dto.user.*;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.UserSourceEnum;
import com.cas.nc.questionnaire.common.enums.UserStatusEnum;
import com.cas.nc.questionnaire.common.enums.UserTypeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.Md5Encrypt;
import com.cas.nc.questionnaire.common.utils.PaginateUtils;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.UserInfoQuery;
import com.cas.nc.questionnaire.server.UserInfoServer;
import com.cas.nc.questionnaire.server.mapstruct.UserInfoConverter;
import com.cas.nc.questionnaire.server.util.ConvertBeanUtil;
import com.cas.nc.questionnaire.service.UserInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class UserInfoServerImpl implements UserInfoServer {
    @Resource
    private UserInfoService userInfoService;

    @Override
    public UserInfoListRespDto list(UserInfoListReqDto reqDto) {
        UserInfoListRespDto respDto = new UserInfoListRespDto();
        PaginateUtils<UserInfoRespDto> repDtoPaginate = new PaginateUtils<UserInfoRespDto>(reqDto.getPage(), reqDto.getPageSize());
        UserInfoQuery query = ConvertBeanUtil.userInfoListReqDto2Query(reqDto);
        query.setStatusList(UserStatusEnum.SHWO_LIST_STATUS);

        int countNum = userInfoService.selectCount(query);
        repDtoPaginate.setRecordTotal(countNum);

        query.setPageSize(repDtoPaginate.getPageSize());
        query.setStartIndex(repDtoPaginate.getStartIndex());
        List<UserInfoPo> queryList = userInfoService.selectListByPage(query);

        List<UserInfoRespDto> userInfoRespDtoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(queryList)) {
            for(UserInfoPo userInfoPo: queryList) {
                UserInfoRespDto userInfoRespDto = UserInfoConverter.INSTANCE.to(userInfoPo);
                userInfoRespDto.setTypeName(UserTypeEnum.toEnum(userInfoPo.getType()).value());
//                userInfoRespDto.setStatusName(UserStatusEnum.toEnum(userInfoPo.getStatus()).value());
                if(UserTypeEnum.isVip(userInfoPo.getType()) ||
                        UserTypeEnum.isSuperAdmin(userInfoPo.getType()) ||
                        UserTypeEnum.isAdmin(userInfoPo.getType())) {
                    userInfoRespDto.setVipEndTimeStr(DateUtil.format(userInfoPo.getVipEndTime(), "yyyy-MM-dd"));
                }
                userInfoRespDto.setUserId(String.valueOf(userInfoPo.getId()));
                userInfoRespDtoList.add(userInfoRespDto);
            }
        }

        respDto.setUserInfoList(userInfoRespDtoList);
        respDto.setTotal(countNum);
        return respDto;
    }

    @Override
    public void save(UserInfoSaveReqDto reqDto) {
        UserInfoPo userByEmail = userInfoService.selectUserByEmailOrUsername(reqDto.getEmail(), reqDto.getUserName());
        if(userByEmail != null) {
            throw new ServerException(CodeEnum.EMAIL_USERNAME_EXIST);
        }
        UserInfoPo userInfoPo = UserInfoConverter.INSTANCE.to(reqDto);
        userInfoPo.setPwd(Md5Encrypt.md5Triple(Md5Encrypt.md5Triple(reqDto.getPwd())));
        userInfoPo.setUserSource(UserSourceEnum.QUESTIONNAIRE.key());
        if(StringUtils.isNotBlank(reqDto.getVipEndTimeStr())) {
            String vipEndTime = StringUtils.join(reqDto.getVipEndTimeStr(), " 00:00:00");
            userInfoPo.setVipEndTime(DateUtil.parseDate(DateUtil.C_TIME_PATTON_DEFAULT, vipEndTime));
        }

        userInfoService.insert(userInfoPo);
    }

    @Override
    public void update(UserInfoUpdateReqDto reqDto) {
        UserInfoQuery query = ConvertBeanUtil.userInfoSaveReqDto2Query(reqDto);
        userInfoService.update(query);
    }

    @Override
    public UserInfoRespDto getUserInfo(Long id) {
        UserInfoPo userInfoPo = userInfoService.selectOne(id);
        UserInfoRespDto userInfoRespDto = UserInfoConverter.INSTANCE.to(userInfoPo);
        if(UserTypeEnum.isVip(userInfoPo.getType())) {
            userInfoRespDto.setVipEndTimeStr(DateUtil.format(userInfoPo.getVipEndTime()));
        } else {
            userInfoRespDto.setVipEndTimeStr(null);
            userInfoRespDto.setVipEndTime(null);
        }
        userInfoRespDto.setUserId(String.valueOf(userInfoPo.getId()));
        return userInfoRespDto;
    }

    @Override
    public void setStatus(UserInfoSetStatusReqDto reqDto) {
        for(Long id: reqDto.getUserIds()) {
            UserInfoQuery query = new UserInfoQuery();
            query.setId(id);
            query.setStatus(reqDto.getStatus());

            userInfoService.update(query);
        }
    }

    @Override
    public void expireVip() {
        UserInfoQuery query = new UserInfoQuery();
        query.setType(UserTypeEnum.VIP.key());
        List<UserInfoPo> userInfoList = userInfoService.selectList(query);

        Date now = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        userInfoList.forEach(x -> {
            Date vipEndTime = x.getVipEndTime();
            if(vipEndTime != null && vipEndTime.getTime() < now.getTime()) {
                UserInfoQuery userInfoQuery = new UserInfoQuery();
                userInfoQuery.setId(x.getId());
                userInfoQuery.setType(UserTypeEnum.ORDINARY_USERS.key());
                userInfoService.update(userInfoQuery);
            }
        });
    }

    public static void main(String[] args) {
        Date now = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        System.out.println(now);
    }
}

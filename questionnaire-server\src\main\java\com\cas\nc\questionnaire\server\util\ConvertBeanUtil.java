package com.cas.nc.questionnaire.server.util;

import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateReqDto;
import com.cas.nc.questionnaire.common.dto.area.AreaCityRepDto;
import com.cas.nc.questionnaire.common.dto.area.AreaCountyRepDto;
import com.cas.nc.questionnaire.common.dto.area.AreaProvinceRepDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireListRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireListReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingSetQueryRepDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingSetReqDto;
import com.cas.nc.questionnaire.common.dto.user.*;
import com.cas.nc.questionnaire.common.enums.*;
import com.cas.nc.questionnaire.common.to.FilterTitleJsonInfoTo;
import com.cas.nc.questionnaire.common.to.FilterTitleJsonStrInfoTo;
import com.cas.nc.questionnaire.common.to.QuestionnaireTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.BaseOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.BaseXuanZeOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.BiaoGeDanXuanOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.BiaoGeDuoXuanOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.BiaoGeShuZhiOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.BiaoGeWenBenOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.BiaoGeXiaLaXuanZeOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.DanXuanOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.DuoXiangDanHangTianKongOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.DuoXuanOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.LiangBiaoOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.PingFenDanXuanOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.PingFenDuoXuanOptionTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.XiaLaXuanZeOptionTo;
import com.cas.nc.questionnaire.common.to.questionnairetitle.*;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.*;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import com.cas.nc.questionnaire.dao.po.AreaPo;
import com.cas.nc.questionnaire.dao.po.LimitAttributeJson;
import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;
import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.QstLimitRuleQuery;
import com.cas.nc.questionnaire.dao.query.QstQuestionnaireInfoQuery;
import com.cas.nc.questionnaire.dao.query.UserInfoQuery;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.isBiaoGeDanXuan;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.isBiaoGeDuoXuan;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Constants.TITLE_SPLIT_FLAG;
import static com.cas.nc.questionnaire.common.utils.Constants.ZERO;
import static com.cas.nc.questionnaire.common.utils.DateUtil.constructTimeDefalut;
import static com.cas.nc.questionnaire.common.utils.Md5Encrypt.md5Triple;
import static com.cas.nc.questionnaire.common.utils.MyRandomUtil.getRandoms;
import static com.cas.nc.questionnaire.common.utils.NumberUtil.null2Default;
import static com.cas.nc.questionnaire.common.utils.StringUtil.default2Null;


public class ConvertBeanUtil {

    public static QstQuestionnaireInfoQuery myListReqDto2Query(MyListReqDto reqDto) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setUserId(reqDto.getUserId());
        query.setLikeTitle(reqDto.getName());
        return query;
    }

    public static UserInfoQuery userInfoListReqDto2Query(UserInfoListReqDto reqDto) {
        UserInfoQuery query = new UserInfoQuery();
        query.setName(reqDto.getName());
        query.setUserName(reqDto.getUserName());
        query.setType(reqDto.getType());
        query.setStatus(reqDto.getStatus());
        return query;
    }

    public static UserInfoQuery userInfoSaveReqDto2Query(UserInfoUpdateReqDto reqDto) {
        UserInfoQuery query = new UserInfoQuery();
        query.setId(reqDto.getUserId());
        query.setType(reqDto.getType());
        query.setStatus(reqDto.getStatus());
        if(StringUtils.isNotBlank(reqDto.getPwd())) {
            query.setPwd(Md5Encrypt.md5Triple(reqDto.getPwd()));
        }
        query.setName(reqDto.getName());
        if(UserTypeEnum.isVip(reqDto.getType()) || UserTypeEnum.isSuperior(reqDto.getType())) {
            String vipEndTime = StringUtils.join(reqDto.getVipEndTimeStr(), " 00:00:00");
            query.setVipEndTime(DateUtil.parseDate(DateUtil.C_TIME_PATTON_DEFAULT, vipEndTime));
        } else {
            query.setVipEndTime(DateUtil.parseDate(DateUtil.C_TIME_PATTON_DEFAULT, DateUtil.ZERO_TIME));
        }
        return query;
    }

    public static UserInfoPo userRegisterDto2Po(UserRegisterReqDto reqDto) {
        UserInfoPo userInfoPo = new UserInfoPo();
        userInfoPo.setUserName(reqDto.getUserName());
        userInfoPo.setPwd(md5Triple(reqDto.getPwd()));
        userInfoPo.setEmail(reqDto.getEmail());
        userInfoPo.setStatus(UserStatusEnum.INIT.key());
        userInfoPo.setType(UserTypeEnum.ORDINARY_USERS.key());
        userInfoPo.setUserSource(UserSourceEnum.KXPX.key());
        return userInfoPo;
    }

    public static QuestionnaireTo convertQuestionnaireInfoPo2QuestionnaireInfoTo(QstQuestionnaireInfoPo qstQuestionnaireInfoPo) {
        QuestionnaireTo questionnaireTo = new QuestionnaireTo();
        questionnaireTo.setId(qstQuestionnaireInfoPo.getQuestionnaireId());
        questionnaireTo.setTitle(qstQuestionnaireInfoPo.getTitle());
        questionnaireTo.setDes(qstQuestionnaireInfoPo.getDes());
        questionnaireTo.setCreatTime(qstQuestionnaireInfoPo.getCreateTime());
        return questionnaireTo;
    }

    public static <T extends BaseXuanZeTitleTo, P extends BaseXuanZeOptionTo> T convertTitlePo2XuanZeTitleTo(T titleTo, List<P> optionToList, QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        convertTitlePo2BaseTitleTo(titleTo, qstTitlePo);
        titleTo.setArrangementMode(qstTitlePo.getArrangementMode());
        Map<String, Integer> jumpOptions = new HashMap<>();
        List<P> optionToListTemp = new ArrayList<>();
        AtomicInteger i = new AtomicInteger();
        int [] randoms = {};
        boolean random = false;
        Map<Integer, Integer> orderMap = new HashMap<>();
        if (qstTitlePo.getRandomDisorder() != null && YnEnum.isY(qstTitlePo.getRandomDisorder())) {
            if (isBiaoGeDanXuan(qstTitlePo.getType()) || isBiaoGeDuoXuan(qstTitlePo.getType())) {
                Map<Integer, List<QstOptionPo>> map = qstOptionPoList.stream().collect(Collectors.groupingBy(QstOptionPo::getSerialNumber));
                int [] randomsTemp = getRandoms(1, map.size(), map.size());
                AtomicInteger j = new AtomicInteger();
                map.forEach((k, v) -> {
                    orderMap.put(k, randomsTemp[j.getAndIncrement()]);
                });
            } else {
                randoms = getRandoms(1, qstOptionPoList.size(), qstOptionPoList.size());
            }
            random = true;
        }
        final int[] finalRandoms = randoms;
        final boolean finalRandom = random;

        qstOptionPoList.forEach(optionPo -> {
            if (optionPo.getSkipNum() != null && optionPo.getSkipNum().intValue() != ZERO) {
                jumpOptions.put(optionPo.getSerialNumber().toString(), optionPo.getSkipNum());
            }
            P p = optionToList.get(i.get());
            optionToListTemp.add(convertOptionPo2XuanZeOptionTo(p, optionPo));
            if (finalRandom) {
                if (isBiaoGeDanXuan(qstTitlePo.getType()) || isBiaoGeDuoXuan(qstTitlePo.getType())) {
                    Integer order = orderMap.get(optionPo.getSerialNumber());
                    p.setOrder(order);
                } else {
                    p.setOrder(finalRandoms[i.get()]);
                }
            }
            i.getAndIncrement();
        });

        Set<P> tempSet = optionToList.stream().collect(Collectors.toSet());
        List<P> tempList = tempSet.stream().collect(Collectors.toList());
        tempList.sort(Comparator.comparingInt(BaseOptionTo::getOrder));
        titleTo.setOptions(tempList);
        titleTo.setJumpOptions(jumpOptions);
        if (qstTitlePo.getRandomDisorder() != null && YnEnum.isY(qstTitlePo.getRandomDisorder())) {
            titleTo.setRandomDisorder(true);
        } else {
            titleTo.setRandomDisorder(false);
        }
        return titleTo;
    }

    public static <T extends BaseTianKongTitleTo> T convertTitlePo2TianKongTitleTo(T titleTo, QstTitlePo qstTitlePo) {
        convertTitlePo2BaseTitleTo(titleTo, qstTitlePo);
        titleTo.setWidth(qstTitlePo.getWidth());
        titleTo.setHeight(qstTitlePo.getHeight());
        return titleTo;
    }

    public static <T extends BaseTitleTo> T convertTitlePo2BaseTitleTo(T titleTo, QstTitlePo qstTitlePo) {
        titleTo.setAssociateCondition(qstTitlePo.getAssociateCondition());
        titleTo.setHao(qstTitlePo.getSerialNumber());
        titleTo.setJumpType(qstTitlePo.getJumpType());
        titleTo.setJumpUncondition(qstTitlePo.getJumpUncondition());
        titleTo.setOrder(qstTitlePo.getOrderNumber());
        titleTo.setRequire(qstTitlePo.getCanRequire());
        titleTo.setStatus(qstTitlePo.getStatus());
        titleTo.setTitle(qstTitlePo.getName());
        titleTo.setType(TitleTypeEnum.toEnum(qstTitlePo.getType()).name().toLowerCase());
        titleTo.setGlobalOrder(qstTitlePo.getGlobalOrder());
        titleTo.setQuoteHao(qstTitlePo.getQuoteNum());
        titleTo.setWidth(qstTitlePo.getWidth());
        titleTo.setHeight(qstTitlePo.getHeight());

        return titleTo;
    }

    public static QstTitlePo convertBaseXuanZeTitleTo2TitlePo(BaseXuanZeTitleTo baseXuanZeTitleTo) {
        QstTitlePo po = convertBaseTitleTo2TitlePo(baseXuanZeTitleTo);
        po.setArrangementMode(baseXuanZeTitleTo.getArrangementMode());
        po.setRandomDisorder((baseXuanZeTitleTo.getRandomDisorder() != null && baseXuanZeTitleTo.getRandomDisorder()) ? YnEnum.Y.key() : YnEnum.N.key());
        return po;
    }

    public static QstTitlePo convertBaseTianKongTitleTo2TitlePo(BaseTianKongTitleTo baseTianKongTitleTo) {
        QstTitlePo po = convertBaseTitleTo2TitlePo(baseTianKongTitleTo);
        po.setHeight(baseTianKongTitleTo.getHeight());
        po.setWidth(baseTianKongTitleTo.getWidth());
        return po;
    }

    public static QstTitlePo convertDanHangTianKongTianKongTitleTo2TitlePo(DanHangTianKongTitleTo titleTo) {
        QstTitlePo po = convertBaseTianKongTitleTo2TitlePo(titleTo);
        po.setDefaultValue(titleTo.getDefaultValue());
        po.setCanDefault(OptionDefaultTypeEnum.convert2Enum(titleTo.getCanDefault()).key());
        po.setCanBounds(OptionDefaultTypeEnum.convert2Enum(titleTo.getCanBounds()).key());
        po.setMaxBoundsValue(titleTo.getMaxBoundsValue());
        po.setMinBoundsValue(titleTo.getMinBoundsValue());
        po.setValidateType(titleTo.getValidateType());
        return po;
    }

    public static QstTitlePo convertDuoXiangDanHangTianKongTitleTo2TitlePo(DuoXiangDanHangTianKongTitleTo duoXiangDanHangTianKongTitleTo) {
        QstTitlePo po = convertBaseTianKongTitleTo2TitlePo(duoXiangDanHangTianKongTitleTo);
        String[] rowTitles = duoXiangDanHangTianKongTitleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        po.setRowCount(rowTitles.length);
        return po;
    }


    public static QstTitlePo convertBaseTitleTo2TitlePo(BaseTitleTo baseTitleTo) {
        QstTitlePo po = new QstTitlePo();
        po.setAssociateCondition(baseTitleTo.getAssociateCondition());
        po.setSerialNumber(baseTitleTo.getHao());
        po.setJumpType(baseTitleTo.getJumpType());
        po.setJumpUncondition(baseTitleTo.getJumpUncondition());
        po.setOrderNumber(baseTitleTo.getOrder());
        po.setCanRequire(baseTitleTo.getRequire());
        po.setName(baseTitleTo.getTitle());
        po.setType(TitleTypeEnum.toEnum(baseTitleTo.getType()).key());
        po.setGlobalOrder(baseTitleTo.getGlobalOrder());
        po.setQuoteNum(baseTitleTo.getQuoteHao());
        po.setWidth(baseTitleTo.getWidth());
        po.setHeight(baseTitleTo.getHeight());

        return po;
    }

    public static QstOptionPo convertXuanZeOptionTo2OptionPo(BaseXuanZeOptionTo xuanZeOptionTo) {
        QstOptionPo po = convertBaseOptionTo2OptionPo(xuanZeOptionTo);
        po.setCanInput(OptionCanInputEnum.convert2Enum(xuanZeOptionTo.getCanInput()).key());
        po.setDes(xuanZeOptionTo.getDescription());
        po.setPictureUrl(xuanZeOptionTo.getImage());
        po.setDefaultType(OptionDefaultTypeEnum.convert2Enum(xuanZeOptionTo.getIsDefault()).key());
        po.setSerialNumber(xuanZeOptionTo.getNum());
        po.setOrderNumber(xuanZeOptionTo.getOrder());
        po.setContent(xuanZeOptionTo.getText());
        return po;
    }

    public static QstOptionPo convertBaseOptionTo2OptionPo(BaseOptionTo optionTo) {
        QstOptionPo po = new QstOptionPo();
        po.setDes(optionTo.getDescription());
        po.setSerialNumber(optionTo.getNum());
        po.setContent(optionTo.getText());
        po.setOrderNumber(optionTo.getOrder());

        return po;
    }

    public static <T extends BaseXuanZeOptionTo> T convertOptionPo2XuanZeOptionTo(T optionTo, QstOptionPo qstOptionPo) {
        convertOptionPo2BaseOptionTo(optionTo, qstOptionPo);
        optionTo.setCanInput(OptionCanInputEnum.convert2Boolean(qstOptionPo.getCanInput()));
        optionTo.setImage(qstOptionPo.getPictureUrl());
        optionTo.setIsDefault(OptionDefaultTypeEnum.convert2Boolean(qstOptionPo.getDefaultType()));
        return optionTo;
    }

    public static <T extends BaseOptionTo> T convertOptionPo2BaseOptionTo(T optionTo, QstOptionPo qstOptionPo) {
        optionTo.setDescription(qstOptionPo.getDes());
        optionTo.setNum(qstOptionPo.getSerialNumber());
        optionTo.setOrder(qstOptionPo.getOrderNumber());
        optionTo.setText(qstOptionPo.getContent());
        return optionTo;
    }

    public static DanXuanTitleTo convertTitlePo2DanXuanTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        List<DanXuanOptionTo> list = new ArrayList<>();
        qstOptionPoList.forEach(v -> list.add(new DanXuanOptionTo()));
        DanXuanTitleTo titleTo = convertTitlePo2XuanZeTitleTo(new DanXuanTitleTo(), list, qstTitlePo, qstOptionPoList);
        return titleTo;
    }

    public static PingFenDuoXuanTitleTo convertTitlePo2PingFenDuoXuanTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        List<PingFenDuoXuanOptionTo> list = new ArrayList<>();
        qstOptionPoList.forEach(v -> list.add(new PingFenDuoXuanOptionTo()));

        PingFenDuoXuanTitleTo titleTo = convertTitlePo2XuanZeTitleTo(new PingFenDuoXuanTitleTo(), list, qstTitlePo, qstOptionPoList);
        Map<Integer, PingFenDuoXuanOptionTo> optionMap = titleTo.getOptions().stream().collect(Collectors.toMap(k -> k.getNum(), v -> v));

        titleTo.setArrangementMode(qstTitlePo.getArrangementMode());
        titleTo.setChooseMax(convertIntegerByZero(qstTitlePo.getChooseMax()));
        titleTo.setChooseMin(convertIntegerByZero(qstTitlePo.getChooseMin()));

        Map<String, Integer> jumpOptions = new HashMap<>();
        List<PingFenDuoXuanOptionTo> optionToList = new ArrayList<>();

        qstOptionPoList.forEach(optionPo -> {
            if (optionPo.getSkipNum() != null && optionPo.getSkipNum().intValue() != ZERO) {
                jumpOptions.put(optionPo.getSerialNumber().toString(), optionPo.getSkipNum());
            }
            PingFenDuoXuanOptionTo pingFenDuoXuanOptionTo = optionMap.get(optionPo.getSerialNumber());

            if (YnEnum.N.key().equals(optionPo.getScoreType())) {
                pingFenDuoXuanOptionTo.setNoScore(true);
            } else {
                pingFenDuoXuanOptionTo.setNoScore(false);
            }
            pingFenDuoXuanOptionTo.setScore(optionPo.getScore());
            optionToList.add(pingFenDuoXuanOptionTo);
        });

        Set<PingFenDuoXuanOptionTo> tempSet = optionToList.stream().collect(Collectors.toSet());
        List<PingFenDuoXuanOptionTo> tempList = tempSet.stream().collect(Collectors.toList());
        tempList.sort(Comparator.comparingInt(BaseOptionTo::getOrder));
        titleTo.setOptions(tempList);
        titleTo.setJumpOptions(jumpOptions);

        return titleTo;
    }

    public static PingFenDanXuanTitleTo convertTitlePo2PingFenDanXuanTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        List<PingFenDanXuanOptionTo> list = new ArrayList<>();
        qstOptionPoList.forEach(v -> list.add(new PingFenDanXuanOptionTo()));

        PingFenDanXuanTitleTo titleTo = convertTitlePo2XuanZeTitleTo(new PingFenDanXuanTitleTo(), list, qstTitlePo, qstOptionPoList);
        Map<Integer, PingFenDanXuanOptionTo> optionMap = titleTo.getOptions().stream().collect(Collectors.toMap(k -> k.getNum(), v -> v));

        Map<String, Integer> jumpOptions = new HashMap<>();
        List<PingFenDanXuanOptionTo> optionToList = new ArrayList<>();

        qstOptionPoList.forEach(optionPo -> {
            if (optionPo.getSkipNum() != null && optionPo.getSkipNum().intValue() != ZERO) {
                jumpOptions.put(optionPo.getSerialNumber().toString(), optionPo.getSkipNum());
            }
            PingFenDanXuanOptionTo pingFenDanXuanOptionTo = optionMap.get(optionPo.getSerialNumber());
            pingFenDanXuanOptionTo.setScore(optionPo.getScore());
            if (YnEnum.N.key().equals(optionPo.getScoreType())) {
                pingFenDanXuanOptionTo.setNoScore(true);
            } else {
                pingFenDanXuanOptionTo.setNoScore(false);
            }
            optionToList.add(pingFenDanXuanOptionTo);
        });

        Set<PingFenDanXuanOptionTo> tempSet = optionToList.stream().collect(Collectors.toSet());
        List<PingFenDanXuanOptionTo> tempList = tempSet.stream().collect(Collectors.toList());
        tempList.sort(Comparator.comparingInt(BaseOptionTo::getOrder));
        titleTo.setOptions(tempList);
        titleTo.setJumpOptions(jumpOptions);

        return titleTo;
    }

    public static DuoXuanTitleTo convertTitlePo2DuoXuanTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        List<DuoXuanOptionTo> list = new ArrayList<>();
        qstOptionPoList.forEach(v -> list.add(new DuoXuanOptionTo()));
        Map<Integer, QstOptionPo> numMap = qstOptionPoList.stream().collect(Collectors.toMap(k -> k.getSerialNumber(), v -> v));

        DuoXuanTitleTo titleTo = convertTitlePo2XuanZeTitleTo(new DuoXuanTitleTo(), list, qstTitlePo, qstOptionPoList);
        titleTo.getOptions().forEach(v -> {
            QstOptionPo qstOptionPo = numMap.get(v.getNum());
            if (qstOptionPo != null) {
                if (qstOptionPo.getCanMutex() == null) {
                    v.setCanMutex(false);
                } else {
                    v.setCanMutex(CanMutexEnum.convert2Boolean(qstOptionPo.getCanMutex()));
                }
            }
        });
        titleTo.setChooseMin(convertIntegerByZero(qstTitlePo.getChooseMin()));
        titleTo.setChooseMax(convertIntegerByZero(qstTitlePo.getChooseMax()));
        return titleTo;
    }

    public static Integer convertIntegerByZero(Integer value) {
        if (value == ZERO) {
            return null;
        }
        return value;
    }

    public static BiaoGeDanXuanTitleTo convertTitlePo2BiaoGeDanXuanTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        List<BiaoGeDanXuanOptionTo> list = new ArrayList<>();
        qstOptionPoList.forEach(v -> list.add(new BiaoGeDanXuanOptionTo()));
        BiaoGeDanXuanTitleTo titleTo = convertTitlePo2XuanZeTitleTo(new BiaoGeDanXuanTitleTo(), list, qstTitlePo, qstOptionPoList);

        titleTo.setRowTitle(getRowTitle(qstOptionPoList));
        return titleTo;
    }

    private static String getRowTitle(List<QstOptionPo> qstOptionPoList) {
        Set<String> rowTitleSet = new HashSet<>();
        StringBuffer buffer = new StringBuffer();
        qstOptionPoList.forEach(v -> {
            boolean addFlag = rowTitleSet.add(v.getRowTitle());
            if (addFlag) {
                buffer.append(v.getRowTitle());
                buffer.append(TITLE_SPLIT_FLAG);
            }
        });
        return buffer.substring(ZERO, buffer.length() - ONE);
    }

    private static String getRowDes(List<QstOptionPo> qstOptionPoList) {
        Set<String> rowDesSet = new HashSet<>();
        StringBuffer buffer = new StringBuffer();
        qstOptionPoList.forEach(v -> {
            boolean addFlag = rowDesSet.add(v.getRowDes());
            if (addFlag) {
                buffer.append(v.getRowDes());
                buffer.append(TITLE_SPLIT_FLAG);
            }
        });
        return buffer.substring(ZERO, buffer.length() - ONE);
    }

    private static Pair<String, String> getRowAndColumnTitle(List<QstOptionPo> qstOptionPoList) {
        Set<String> rowTitleSet = new HashSet<>();
        Set<String> columnTitleSet = new HashSet<>();
        StringBuffer rowTitleBuffer = new StringBuffer();
        StringBuffer columnTitleBuffer = new StringBuffer();

        qstOptionPoList.forEach(v -> {
            boolean addRowFlag = rowTitleSet.add(v.getRowTitle());
            if (addRowFlag) {
                rowTitleBuffer.append(v.getRowTitle());
                rowTitleBuffer.append(TITLE_SPLIT_FLAG);
            }
            boolean addColumnFlag = columnTitleSet.add(v.getColumnTitle());
            if (addColumnFlag) {
                columnTitleBuffer.append(v.getColumnTitle());
                columnTitleBuffer.append(TITLE_SPLIT_FLAG);
            }
        });

        String left = rowTitleBuffer.substring(ZERO, rowTitleBuffer.length() - ONE);
        String right = columnTitleBuffer.substring(ZERO, columnTitleBuffer.length() - ONE);
        return Pair.of(left, right);
    }

    public static BiaoGeDuoXuanTitleTo convertTitlePo2BiaoGeDuoXuanTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        List<BiaoGeDuoXuanOptionTo> list = new ArrayList<>();
        qstOptionPoList.forEach(v -> list.add(new BiaoGeDuoXuanOptionTo()));
        BiaoGeDuoXuanTitleTo titleTo = convertTitlePo2XuanZeTitleTo(new BiaoGeDuoXuanTitleTo(), list, qstTitlePo, qstOptionPoList);
        titleTo.setChooseMax(qstTitlePo.getChooseMax());
        titleTo.setChooseMin(qstTitlePo.getChooseMin());

        titleTo.setRowTitle(getRowTitle(qstOptionPoList));
        return titleTo;
    }

    public static BiaoGeShuZhiTitleTo convertTitlePo2BiaoGeShuZhiTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        List<BiaoGeShuZhiOptionTo> list = new ArrayList<>();
        qstOptionPoList.forEach(v -> list.add(convertOptionPo2BaseOptionTo(new BiaoGeShuZhiOptionTo(), v)));

        BiaoGeShuZhiTitleTo titleTo = new BiaoGeShuZhiTitleTo();
        convertTitlePo2BaseTitleTo(titleTo, qstTitlePo);
        Pair<String, String> pair = getRowAndColumnTitle(qstOptionPoList);
        titleTo.setRowTitle(pair.getLeft());
        titleTo.setColTitle(pair.getRight());
        titleTo.setMaxBoundsValue(qstTitlePo.getMaxBoundsValue());
        titleTo.setHeight(qstTitlePo.getHeight());
        titleTo.setMinBoundsValue(qstTitlePo.getMinBoundsValue());
        titleTo.setValidateType(qstTitlePo.getValidateType());
        titleTo.setWidth(qstTitlePo.getWidth());

        return titleTo;
    }

    public static BiaoGeWenBenTitleTo convertTitlePo2BiaoGeWenBenTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        List<BiaoGeWenBenOptionTo> list = new ArrayList<>();
        qstOptionPoList.forEach(v -> list.add(convertOptionPo2BaseOptionTo(new BiaoGeWenBenOptionTo(), v)));

        BiaoGeWenBenTitleTo titleTo = new BiaoGeWenBenTitleTo();
        convertTitlePo2BaseTitleTo(titleTo, qstTitlePo);
        Pair<String, String> pair = getRowAndColumnTitle(qstOptionPoList);
        titleTo.setRowTitle(pair.getLeft());
        titleTo.setColTitle(pair.getRight());
        titleTo.setHeight(qstTitlePo.getHeight());
        titleTo.setWidth(qstTitlePo.getWidth());

        if (qstTitlePo.getLimitAttributeJson() != null) {
            titleTo.setMaxBoundsValue(qstTitlePo.getLimitAttributeJson().getMaxBoundsValue());
            titleTo.setMinBoundsValue(qstTitlePo.getLimitAttributeJson().getMinBoundsValue());
            titleTo.setColTitleValidateType(qstTitlePo.getLimitAttributeJson().getColTitleValidateType());
        }

        return titleTo;
    }

    public static HuaDongTiaoTitleTo convertTitlePo2HuaDongTiaoTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        QstOptionPo qstOptionPo = qstOptionPoList.get(0);

        HuaDongTiaoTitleTo huaDongTiaoTitleTo = new HuaDongTiaoTitleTo();
        convertTitlePo2BaseTitleTo(huaDongTiaoTitleTo, qstTitlePo);
        huaDongTiaoTitleTo.setMinTitle(qstOptionPo.getRowTitle());
        huaDongTiaoTitleTo.setMaxTitle(qstOptionPo.getColumnTitle());
        huaDongTiaoTitleTo.setMinValue(qstTitlePo.getMinBoundsValue());
        huaDongTiaoTitleTo.setMaxValue(qstTitlePo.getMaxBoundsValue());

        return huaDongTiaoTitleTo;
    }

    public static PageTitleTo convertTitlePo2PageTitleTo(QstTitlePo qstTitlePo) {

        PageTitleTo pageTitleTo = new PageTitleTo();
        convertTitlePo2BaseTitleTo(pageTitleTo, qstTitlePo);
        pageTitleTo.setPageNo(qstTitlePo.getPageNo());
        pageTitleTo.setPageCount(qstTitlePo.getPageCount());

        return pageTitleTo;
    }

    public static WenJianTitleTo convertTitle2WenJianTitleTo(QstTitlePo qstTitlePo) {

        WenJianTitleTo wenJianTitleTo = new WenJianTitleTo();
        convertTitlePo2BaseTitleTo(wenJianTitleTo, qstTitlePo);

        return wenJianTitleTo;
    }

    public static JuZhenHuaDongTiaoTitleTo convertTitlePo2JuZhenHuaDongTiaoTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        JuZhenHuaDongTiaoTitleTo juZhenHuaDongTiaoTitleTo = new JuZhenHuaDongTiaoTitleTo();
        convertTitlePo2BaseTitleTo(juZhenHuaDongTiaoTitleTo, qstTitlePo);
        juZhenHuaDongTiaoTitleTo.setRowTitle(getRowTitle(qstOptionPoList));
        juZhenHuaDongTiaoTitleTo.setRowDescription(getRowDes(qstOptionPoList));
        juZhenHuaDongTiaoTitleTo.setMinValue(qstTitlePo.getMinBoundsValue());
        juZhenHuaDongTiaoTitleTo.setMaxValue(qstTitlePo.getMaxBoundsValue());

        return juZhenHuaDongTiaoTitleTo;
    }

    public static BiZhongTitleTo convertTitlePo2BiZhongTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        BiZhongTitleTo biZhongTitleTo = new BiZhongTitleTo();
        convertTitlePo2BaseTitleTo(biZhongTitleTo, qstTitlePo);
        biZhongTitleTo.setRowTitle(getRowTitle(qstOptionPoList));
        biZhongTitleTo.setScoreCount(qstTitlePo.getProportionTotal());

        return biZhongTitleTo;
    }

    public static LiangBiaoTitleTo convertTitlePo2LiangBiaoTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        LiangBiaoTitleTo liangBiaoTitleTo = convertTitlePo2BaseTitleTo(new LiangBiaoTitleTo(), qstTitlePo);
        liangBiaoTitleTo.setShowStyle(qstTitlePo.getArrangementMode());

        List<LiangBiaoOptionTo> optionTos = new ArrayList<>();
        qstOptionPoList.forEach(v -> {
            LiangBiaoOptionTo optionTo = convertOptionPo2BaseOptionTo(new LiangBiaoOptionTo(), v);

            if (YnEnum.N.key().equals(v.getScoreType())) {
                optionTo.setNoScore(true);
                optionTo.setScore(null);
            } else {
                optionTo.setNoScore(false);
                optionTo.setScore(v.getScore());
            }

            optionTos.add(optionTo);
        });

        liangBiaoTitleTo.setOptions(optionTos);
        return liangBiaoTitleTo;
    }

    public static BiaoGeXiaLaXuanZeTitleTo convertTitlePo2BiaoGeXiaLaXuanZeTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        BiaoGeXiaLaXuanZeTitleTo titleTo = convertTitlePo2BaseTitleTo(new BiaoGeXiaLaXuanZeTitleTo(), qstTitlePo);
        List<BiaoGeXiaLaXuanZeOptionTo> options = new ArrayList<>();
        qstOptionPoList.forEach(optionPo -> options.add(convertOptionPo2XuanZeOptionTo(new BiaoGeXiaLaXuanZeOptionTo(), optionPo))
        );

        Set<BiaoGeXiaLaXuanZeOptionTo> tempSet = options.stream().collect(Collectors.toSet());
        List<BiaoGeXiaLaXuanZeOptionTo> tempList = tempSet.stream().collect(Collectors.toList());
        tempList.sort(Comparator.comparingInt(BaseOptionTo::getOrder));

        titleTo.setOptions(tempList);

        Pair<String, String> pair = getRowAndColumnTitle(qstOptionPoList);
        titleTo.setRowTitle(pair.getLeft());
        titleTo.setColTitle(pair.getRight());
        return titleTo;
    }

    public static DuoXiangDanHangTianKongTitleTo convertTitlePo2DuoXiangDanHangTianKongTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        DuoXiangDanHangTianKongTitleTo titleTo = convertTitlePo2TianKongTitleTo(new DuoXiangDanHangTianKongTitleTo(), qstTitlePo);
        List<DuoXiangDanHangTianKongOptionTo> options = new ArrayList<>();
        qstOptionPoList.forEach(optionPo -> options.add(convertOptionPo2XuanZeOptionTo(new DuoXiangDanHangTianKongOptionTo(), optionPo))
        );
        titleTo.setOptions(options);
        titleTo.setRowTitle(getRowTitle(qstOptionPoList));
        titleTo.setRowDescription(getRowDes(qstOptionPoList));
        return titleTo;
    }

    public static XiaLaXuanZeTitleTo convertTitlePo2XiaLaXuanZeTitleTo(QstTitlePo qstTitlePo, List<QstOptionPo> qstOptionPoList) {
        XiaLaXuanZeTitleTo titleTo = convertTitlePo2BaseTitleTo(new XiaLaXuanZeTitleTo(), qstTitlePo);
        List<XiaLaXuanZeOptionTo> options = new ArrayList<>();
        qstOptionPoList.forEach(optionPo -> options.add(convertOptionPo2BaseOptionTo(new XiaLaXuanZeOptionTo(), optionPo))
        );
        options.sort(Comparator.comparing(XiaLaXuanZeOptionTo::getOrder));
        titleTo.setOptions(options);
        return titleTo;
    }

    public static DanHangTianKongTitleTo convertTitlePo2DanHangTianKongTitleTo(QstTitlePo qstTitlePo) {
        DanHangTianKongTitleTo titleTo = convertTitlePo2TianKongTitleTo(new DanHangTianKongTitleTo(), qstTitlePo);
        titleTo.setCanBounds(YnEnum.isY(qstTitlePo.getCanBounds()));
        titleTo.setCanDefault(YnEnum.isY(qstTitlePo.getCanDefault()));
        titleTo.setDefaultValue(qstTitlePo.getDefaultValue());
        titleTo.setMaxBoundsValue(qstTitlePo.getMaxBoundsValue());
        titleTo.setMinBoundsValue(qstTitlePo.getMinBoundsValue());
        titleTo.setValidateType(qstTitlePo.getValidateType());
        return titleTo;
    }


    public static QstTitlePo convertDuoXuanTitleTo2TitlePo(DuoXuanTitleTo duoXuanTitleTo) {
        QstTitlePo po = convertBaseXuanZeTitleTo2TitlePo(duoXuanTitleTo);
        po.setChooseMin(duoXuanTitleTo.getChooseMin());
        po.setChooseMax(duoXuanTitleTo.getChooseMax());
        return po;
    }

    public static QstTitlePo convertPingFenDuoXuanTitleTo2TitlePo(PingFenDuoXuanTitleTo duoXuanTitleTo) {
        QstTitlePo po = convertBaseXuanZeTitleTo2TitlePo(duoXuanTitleTo);
        po.setChooseMin(duoXuanTitleTo.getChooseMin());
        po.setChooseMax(duoXuanTitleTo.getChooseMax());
        return po;
    }

    public static QstTitlePo convertBiaoGeDanXuanTitleTo2TitlePo(BiaoGeDanXuanTitleTo biaoGeDanXuanTitleTo) {
        QstTitlePo po = convertBaseXuanZeTitleTo2TitlePo(biaoGeDanXuanTitleTo);
        po.setColumnCount(biaoGeDanXuanTitleTo.getOptions().size());
        String[] rowTitles = biaoGeDanXuanTitleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        po.setRowCount(rowTitles.length);
        return po;
    }

    public static QstTitlePo convertBiaoGeXiaLaXuanZeTitleTo2TitlePo(BiaoGeXiaLaXuanZeTitleTo biaoGeXiaLaXuanZeTitleTo) {
        QstTitlePo po = convertBaseTitleTo2TitlePo(biaoGeXiaLaXuanZeTitleTo);
        po.setVerticalCount(biaoGeXiaLaXuanZeTitleTo.getOptions().size());
        String[] rowTitles = biaoGeXiaLaXuanZeTitleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        po.setRowCount(rowTitles.length);
        String[] columnTitles = biaoGeXiaLaXuanZeTitleTo.getColTitle().split(TITLE_SPLIT_FLAG);
        po.setColumnCount(columnTitles.length);
        return po;
    }

    public static QstTitlePo convertBiaoGeDuoXuanTitleTo2TitlePo(BiaoGeDuoXuanTitleTo biaoGeDuoXuanTitleTo) {
        QstTitlePo po = convertBaseXuanZeTitleTo2TitlePo(biaoGeDuoXuanTitleTo);
        po.setChooseMin(biaoGeDuoXuanTitleTo.getChooseMin());
        po.setChooseMax(biaoGeDuoXuanTitleTo.getChooseMax());
        po.setColumnCount(biaoGeDuoXuanTitleTo.getOptions().size());
        String[] rowTitles = biaoGeDuoXuanTitleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        po.setRowCount(rowTitles.length);
        return po;
    }

    public static AreaProvinceRepDto convertAreaPoAreaProvinceRepDto(AreaPo province) {
        AreaProvinceRepDto repDto = new AreaProvinceRepDto();
        repDto.setId(province.getId());
        repDto.setName(province.getName());
        return repDto;
    }

    public static AreaCityRepDto convertAreaPoAreaCityRepDto(AreaPo city) {
        AreaCityRepDto cityRepDto = new AreaCityRepDto();
        cityRepDto.setId(city.getId());
        cityRepDto.setName(city.getName());
        return cityRepDto;
    }

    public static AreaCountyRepDto convertAreaPoAreaCountyRepDto(AreaPo county) {
        AreaCountyRepDto countyRepDto = new AreaCountyRepDto();
        countyRepDto.setId(county.getId());
        countyRepDto.setName(county.getName());
        return countyRepDto;
    }

    public static SettingSetQueryRepDto convert2SettingSetQueryRepDto(QstLimitRulePo limitRulePo) {
        SettingSetQueryRepDto dto = new SettingSetQueryRepDto();
        dto.setQuestionnaireId(limitRulePo.getQuestionnaireId());
        dto.setBeginTime(limitRulePo.getBeginTime());
        dto.setBreakPoint(NumberUtil.default2Null(limitRulePo.getBreakPoint()));
        dto.setCollectNumber(NumberUtil.default2Null(limitRulePo.getCollectNumber()));
        dto.setDeviceLimitFrequency(NumberUtil.default2Null(limitRulePo.getDeviceLimitFrequency()));
        dto.setDeviceLimitType(NumberUtil.default2Null(limitRulePo.getDeviceLimitType()));
        dto.setEndTime(limitRulePo.getEndTime());
        dto.setInvalidAnswerHint(default2Null(limitRulePo.getInvalidAnswerHint()));
        dto.setIpEnd(default2Null(limitRulePo.getIpEnd()));
        dto.setIpLimitFrequency(NumberUtil.default2Null(limitRulePo.getIpLimitFrequency()));
        dto.setIpLimitType(NumberUtil.default2Null(limitRulePo.getIpLimitType()));
        dto.setIpStart(default2Null(limitRulePo.getIpStart()));
        dto.setPwd(default2Null(limitRulePo.getPwd()));
        dto.setEndHint(default2Null(limitRulePo.getEndHint()));
        dto.setReturnHint(default2Null(limitRulePo.getReturnHint()));
        dto.setReturnUrl(default2Null(limitRulePo.getReturnUrl()));
        return dto;
    }

    public static QstLimitRuleQuery convert2QstLimitRulePo(SettingSetReqDto reqDto) {
        QstLimitRuleQuery query = new QstLimitRuleQuery();
        query.setBeginTime(reqDto.getBeginTime());
        query.setBeginTimeStr(constructTimeDefalut(reqDto.getBeginTime()));
        query.setBreakPoint(null2Default(reqDto.getBreakPoint()));
        query.setCollectNumber(null2Default(reqDto.getCollectNumber()));
        query.setDeviceLimitFrequency(null2Default(reqDto.getDeviceLimitFrequency()));
        query.setDeviceLimitType(null2Default(reqDto.getDeviceLimitType()));
        query.setEndTimeStr(constructTimeDefalut(reqDto.getEndTime()));
        query.setEndTime(reqDto.getEndTime());
        query.setInvalidAnswerHint(StringUtil.null2default(reqDto.getInvalidAnswerHint()));
        query.setIpEnd(StringUtil.null2default(reqDto.getIpEnd()));
        query.setIpLimitFrequency(null2Default(reqDto.getIpLimitFrequency()));
        query.setIpLimitType(null2Default(reqDto.getIpLimitType()));
        query.setIpStart(StringUtil.null2default(reqDto.getIpStart()));
        query.setPwd(StringUtil.null2default(reqDto.getPwd()));
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setEndHint(StringUtil.null2default(reqDto.getEndHint()));
        query.setReturnHint(StringUtil.null2default(reqDto.getReturnHint()));
        query.setReturnUrl(StringUtil.default2Null(reqDto.getReturnUrl()));
        return query;
    }

    public static QstTitlePo convertBiaoGeShuZhiTitleTo2TitlePo(BiaoGeShuZhiTitleTo biaoGeShuZhiTitleTo) {
        QstTitlePo po = convertBaseTitleTo2TitlePo(biaoGeShuZhiTitleTo);
        String[] rowTitles = biaoGeShuZhiTitleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        po.setRowCount(rowTitles.length);
        String[] columnTitles = biaoGeShuZhiTitleTo.getColTitle().split(TITLE_SPLIT_FLAG);
        po.setColumnCount(columnTitles.length);
        po.setHeight(biaoGeShuZhiTitleTo.getHeight());
        po.setWidth(biaoGeShuZhiTitleTo.getWidth());
        po.setMinBoundsValue(biaoGeShuZhiTitleTo.getMinBoundsValue());
        po.setMaxBoundsValue(biaoGeShuZhiTitleTo.getMaxBoundsValue());
        return po;
    }

    public static QstTitlePo convertBiaoGeShuZhiTitleTo2TitlePo(BiaoGeWenBenTitleTo biaoGeWenBenTitleTo) {
        QstTitlePo po = convertBaseTitleTo2TitlePo(biaoGeWenBenTitleTo);
        String[] rowTitles = biaoGeWenBenTitleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        po.setRowCount(rowTitles.length);
        String[] columnTitles = biaoGeWenBenTitleTo.getColTitle().split(TITLE_SPLIT_FLAG);
        po.setColumnCount(columnTitles.length);
        po.setWidth(biaoGeWenBenTitleTo.getWidth());
        po.setHeight(biaoGeWenBenTitleTo.getHeight());
        LimitAttributeJson limitAttributeJson = new LimitAttributeJson();
        limitAttributeJson.setColTitleValidateType(biaoGeWenBenTitleTo.getColTitleValidateType());
        limitAttributeJson.setHeight(biaoGeWenBenTitleTo.getHeight());
        limitAttributeJson.setMaxBoundsValue(biaoGeWenBenTitleTo.getMaxBoundsValue());
        limitAttributeJson.setMinBoundsValue(biaoGeWenBenTitleTo.getMinBoundsValue());
        limitAttributeJson.setWidth(biaoGeWenBenTitleTo.getWidth());

        po.setLimitAttributeJson(limitAttributeJson);
        return po;
    }

    public static AnswerOptionPo constructBaseAnswerOption(AnswerCreateReqDto reqDto, String answerId, String titleSerialNumber, TitleTypeEnum titleTypeEnum) {
        AnswerOptionPo po = new AnswerOptionPo();
        po.setAnswerId(answerId);
        po.setQuestionnaireId(reqDto.getId());
        po.setTitleSerialNumber(Integer.valueOf(titleSerialNumber));
        po.setType(titleTypeEnum.key());
        po.setStatus(AnswerStatusEnum.FINISH.key());
        po.setUserId(SequenceUtil.getInstance().parse2UserId4Long(reqDto.getId()));
        return po;
    }

    public static QstTitlePo convertBiZhongTitleTo2TitlePo(BiZhongTitleTo biZhongTitleTo) {
        QstTitlePo po = ConvertBeanUtil.convertBaseTitleTo2TitlePo(biZhongTitleTo);
        po.setProportionTotal(biZhongTitleTo.getScoreCount());

        return po;
    }

    public static QstTitlePo convertLiangBiaoTitleTo2TitlePo(LiangBiaoTitleTo liangBiaoTitleTo) {
        QstTitlePo po = convertBaseTitleTo2TitlePo(liangBiaoTitleTo);
        po.setArrangementMode(liangBiaoTitleTo.getShowStyle());

        return po;
    }

    public static FilterTitleJsonInfoTo getFilterTitleJsonInfoTo(String content) {
        FilterTitleJsonStrInfoTo strInfoTo = JSONUtil.parseObject(content, FilterTitleJsonStrInfoTo.class);
        FilterTitleJsonInfoTo jsonInfoTo = new FilterTitleJsonInfoTo();
        jsonInfoTo.setOptionList(strInfoTo.getOptionList());
        jsonInfoTo.setRowNumber(strInfoTo.getRowNumber());
        jsonInfoTo.setSerialNumber(strInfoTo.getSerialNumber());
        jsonInfoTo.setTextContent(strInfoTo.getTextContent());
        jsonInfoTo.setTitle(strInfoTo.getTitle());
        jsonInfoTo.setTitleType(TitleTypeEnum.toEnum(strInfoTo.getTitleType()).key());

        return jsonInfoTo;
    }

    public static QstQuestionnaireInfoQuery questionnaireListRepDtoToQuery(QuestionnaireListReqDto reqDto) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setTitle(reqDto.getTitle());
        query.setName(reqDto.getName());
        query.setChannel(reqDto.getChannel());
        query.setBeginTime(DateUtil.parseDate(DateUtil.C_TIME_PATTON_DEFAULT, reqDto.getBeginTime() + " 00:00:00"));
        query.setEndTime(DateUtil.parseDate(DateUtil.C_TIME_PATTON_DEFAULT, reqDto.getEndTime() + " 23:59:59"));
        query.setStatusList(QuestionnaireStatusEnum.MY_LIST_QUERY_LIST);
        return query;
    }
}

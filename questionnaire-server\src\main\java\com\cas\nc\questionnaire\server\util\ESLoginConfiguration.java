package com.cas.nc.questionnaire.server.util;

import org.springframework.context.annotation.Configuration;


@Configuration
public class ESLoginConfiguration {
    /**
     * 中国科技云的应用请求token地址
     */
    private String tokenUrl;
    /**
     * 中国科技云的应用App Key，http协议用
     */
    private String clientIdHttp;
    /**
     * 中国科技云的应用App Secret，http协议用
     */
    private String clientSecretHttp;
    /**
     * 中国科技云的应用App Key，https协议用
     */
    private String clientIdHttps;
    /**
     * 中国科技云的应用App Secret，https协议用
     */
    private String clientSecretHttps;

    public String getTokenUrl() {
        return tokenUrl;
    }

    public void setTokenUrl(String tokenUrl) {
        this.tokenUrl = tokenUrl;
    }

    public String getClientIdHttp() {
        return clientIdHttp;
    }

    public void setClientIdHttp(String clientIdHttp) {
        this.clientIdHttp = clientIdHttp;
    }

    public String getClientSecretHttp() {
        return clientSecretHttp;
    }

    public void setClientSecretHttp(String clientSecretHttp) {
        this.clientSecretHttp = clientSecretHttp;
    }

    public String getClientIdHttps() {
        return clientIdHttps;
    }

    public void setClientIdHttps(String clientIdHttps) {
        this.clientIdHttps = clientIdHttps;
    }

    public String getClientSecretHttps() {
        return clientSecretHttps;
    }

    public void setClientSecretHttps(String clientSecretHttps) {
        this.clientSecretHttps = clientSecretHttps;
    }
}

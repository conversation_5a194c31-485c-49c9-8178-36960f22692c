package com.cas.nc.questionnaire.common.dto.questionnaire;

import com.cas.nc.questionnaire.common.dto.base.BaseResponseDto;
import com.cas.nc.questionnaire.common.to.QuestionnaireTo;

import java.util.Date;


public class QuestionnaireQueryRepDto extends BaseResponseDto {
    private QuestionnaireTo questionnairePojo;
    private Date beginTime;

    public QuestionnaireTo getQuestionnairePojo() {
        return questionnairePojo;
    }

    public void setQuestionnairePojo(QuestionnaireTo questionnairePojo) {
        this.questionnairePojo = questionnairePojo;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }
}
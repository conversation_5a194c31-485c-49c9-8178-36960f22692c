package com.cas.nc.questionnaire.web.sso;

import com.alibaba.fastjson.JSON;
import com.cas.nc.questionnaire.common.sso.Ticket;
import com.cas.nc.questionnaire.common.utils.Base64Utils;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.EncryptAlgorithmsUtils;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;


@Component
public class LoginCheck {

    @Value("${COOKIE.ENCRYPT.KEY}")
    public String cookieEncryptKey;
    @Value("${domain.url}")
    public String domain;

    /**
     * Cookie键
     */
    public static final String COOKIE_NAME = "ssoinfo";

    /**
     * 校验Cookie
     *
     * @param cookieName
     * @param cookieValue
     * @return
     */
    public boolean checkCookie(String cookieName, String cookieValue) {
        if (StringUtil.isBlank(cookieName)) {
            return false;
        }
        if (StringUtil.isBlank(cookieValue)) {
            return false;
        }
        if (COOKIE_NAME.equals(cookieName) && validateValue(cookieValue)) {
            return true;
        }
        return false;
    }

    public boolean validateValue(String cookieValue) {
        Ticket ticket = convertToTicket(cookieValue);
        if (ticket != null) {
            if (DateUtil.compareDate(ticket.getExpire()) == 1) {
                return false;
            }
            return true;
        }
        return false;
    }

    public Ticket convertToTicket(String cookieValue) {
        byte[] bytes = Base64Utils.decodeBase64(cookieValue);
        String aesContent = new String(bytes);
        String result = EncryptAlgorithmsUtils.decryptByAES(cookieEncryptKey, aesContent);
        Ticket ticket = JSON.parseObject(result, Ticket.class);
        return ticket;
    }

    public String addCookie(Ticket ticket, HttpServletResponse response) {
        String jsonStr = JSONUtil.toJSONString2(ticket);
        String encryptStr = EncryptAlgorithmsUtils.encryptByAES(cookieEncryptKey, jsonStr);
        String token = Base64Utils.encodeBase64String(encryptStr.getBytes());
        Cookie cookie = new Cookie(COOKIE_NAME, token);
        // 设置在父域下面
        cookie.setDomain(domain);
        // 顶级域名下，所有应用都是可见的
        cookie.setPath("/");
        //12小时的过期时间
        cookie.setMaxAge(43200);
        response.addCookie(cookie);

        return token;
    }

    public void removeCookie(HttpServletResponse response) {
        Cookie cookie = new Cookie(COOKIE_NAME, "");
        // 设置在父域下面
        cookie.setDomain(domain);
        // 顶级域名下，所有应用都是可见的
        cookie.setPath("/");
        cookie.setMaxAge(0);
        response.addCookie(cookie);
    }
}

package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;

public enum AnalysisTypeEnum {
    ANSWER_SOURCE(1, "答案来源"),
    COMPLETION_RATE(2, "完成率"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    AnalysisTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static AnalysisTypeEnum toEnum(int key) {
        for (AnalysisTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.DATA_EXCEPTION);
    }

    public static boolean isAnswerSource(int key) {
        return ANSWER_SOURCE.key.intValue() == key;
    }

    public static boolean isCompletionRate(int key) {
        return COMPLETION_RATE.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

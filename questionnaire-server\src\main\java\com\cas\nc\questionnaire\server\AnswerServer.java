package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.answer.*;
import com.cas.nc.questionnaire.common.utils.PaginateUtils;
import com.cas.nc.questionnaire.dao.query.AnswerOptionQuery;

import java.util.List;

public interface AnswerServer {

    /**
     * 答案提交
     *
     * @param reqDto
     * @return
     */
    AnswerCreateRepDto create(AnswerCreateReqDto reqDto);

    PaginateUtils<ListAnswerRepDto> listAnswer(ListAnswerReqDto reqDto);

    GetAnswerRepDto getAnswer(GetAnswerReqDto reqDto);

    void deleteAnswer(DeleteAnswerReqDto reqDto);

    List<String> listExternalAnswerer(ListExternalAnswererReqDto reqDto);

    Integer countExternalAnswerer(CountExternalAnswererReqDto reqDto);

    Boolean validateExternalAnswered(ValidateExternalAnsweredReqDto reqDto);

    GetAnswerRepDto getExternalAnswer(GetExternalAnswerReqDto reqDto);

    void parseRule(Integer ruleType, Integer judgeType, String content, AnswerOptionQuery query);
}

package com.cas.nc.questionnaire.dao.sharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.bo.AnswerAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.AnswerAnalysisOptionBo;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import com.cas.nc.questionnaire.dao.query.AnswerOptionQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AnswerOptionDao extends BaseDao<AnswerOptionPo, AnswerOptionQuery> {

    /**
     * 过滤答案
     *
     * @param query
     * @return
     */
    List<AnswerOptionPo> selectFilterList(@Param("item") AnswerOptionQuery query);

    /**
     * 题目作答分析
     *
     * @param query
     * @return
     */
    List<AnswerAnalysisBo> selectTitleAnalysis(@Param("item") AnswerOptionQuery query);

    /**
     * 一维选项作答分析
     *
     * @param query
     * @return
     */
    List<AnswerAnalysisBo> selectOneDimensionalAnalysis(@Param("item") AnswerOptionQuery query);

    /**
     * 二维选项作答分析
     *
     * @param query
     * @return
     */
    List<AnswerAnalysisBo> selectTwoDimensionalAnalysis(@Param("item") AnswerOptionQuery query);

    /**
     * 三维选项作答分析
     *
     * @param query
     * @return
     */
    List<AnswerAnalysisBo> selectThreeDimensionalAnalysis(@Param("item") AnswerOptionQuery query);

    List<AnswerAnalysisBo> selectBiZhongAnalysis(@Param("item") AnswerOptionQuery query);

    List<AnswerAnalysisBo> selectPaiXuAnalysis(@Param("item") AnswerOptionQuery query);

    AnswerAnalysisBo selectHuaDongTiaoAnalysis(@Param("item") AnswerOptionQuery query);

    List<AnswerAnalysisBo> selectJuZhenHuaDongTiaoAnalysis(@Param("item") AnswerOptionQuery query);

    List<AnswerAnalysisOptionBo> selectOneDimensionalContainNullCrossAnalysis(@Param("item") AnswerOptionQuery query);

    List<AnswerAnalysisOptionBo> selectOneDimensionalNotNullCrossAnalysis(@Param("item") AnswerOptionQuery query);

    List<AnswerAnalysisOptionBo> selectTwoDimensionalContainNullCrossAnalysis(@Param("item") AnswerOptionQuery query);

    List<AnswerAnalysisOptionBo> selectTwoDimensionalNotNullCrossAnalysis(@Param("item") AnswerOptionQuery query);

    int selectCount(@Param("item") AnswerOptionQuery query);

    List<AnswerAnalysisOptionBo> selectOneDimensionalIndependentVariableCrossAnalysis(@Param("item") AnswerOptionQuery query);

    List<AnswerAnalysisOptionBo> selectTwoDimensionalIndependentVariableCrossAnalysis(@Param("item") AnswerOptionQuery query);

    List<AnswerOptionPo> selectListByPage(@Param("item") AnswerOptionQuery query);

    List<String> selectAnswerIdList(@Param("item") AnswerOptionQuery query);

}
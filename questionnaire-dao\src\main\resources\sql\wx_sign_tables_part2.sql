-- 创建签到记录表(需要分表)
CREATE TABLE `qst_sign_record_0` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sign_id` varchar(64) NOT NULL COMMENT '签到ID',
  `questionnaire_id` varchar(64) NOT NULL COMMENT '问卷ID',
  `user_id` bigint(20) NOT NULL COMMENT '问卷创建者用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信用户唯一标识',
  `nickname` varchar(128) DEFAULT NULL COMMENT '微信用户昵称',
  `headimgurl` varchar(512) DEFAULT NULL COMMENT '头像URL',
  `sign_time` datetime NOT NULL COMMENT '签到时间',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '签到位置纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '签到位置经度',
  `sign_date` date NOT NULL COMMENT '签到日期',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-有效 0-无效',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_openid_questionnaire_date` (`openid`,`questionnaire_id`,`sign_date`) COMMENT '同一用户同一问卷每日签到唯一',
  KEY `idx_questionnaire_id` (`questionnaire_id`) COMMENT '问卷ID索引',
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问卷签到记录表';

CREATE TABLE `qst_sign_record_1` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_2` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_3` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_4` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_5` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_6` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_7` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_8` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_9` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_10` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_11` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_12` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_13` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_14` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_15` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_16` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_17` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_18` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_19` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_20` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_21` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_22` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_23` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_24` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_25` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_26` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_27` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_28` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_29` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_30` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_31` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_32` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_33` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_34` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_35` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_36` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_37` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_38` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_39` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_40` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_41` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_42` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_43` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_44` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_45` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_46` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_47` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_48` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_49` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_50` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_51` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_52` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_53` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_54` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_55` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_56` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_57` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_58` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_59` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_60` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_61` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_62` LIKE `qst_sign_record_0`;
CREATE TABLE `qst_sign_record_63` LIKE `qst_sign_record_0`; 
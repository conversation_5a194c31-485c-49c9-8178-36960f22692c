package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;

import java.util.Arrays;
import java.util.List;


public enum UserStatusEnum {
    INIT(1, "初始化"),
    FROZEN(2, "冻结"),
    EFFECTIVE(3, "有效"),
    DELETE(4, "删除"),
    ELSE(99, "其他");

    private final Integer key;
    private final String value;


    UserStatusEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static UserStatusEnum toEnum(int key) {
        for (UserStatusEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.USER_TYPE_EXCEPTION);
    }

    public static boolean isEffective(int key) {
        return EFFECTIVE.key.intValue() == key;
    }

    public static boolean isInit(int key) {
        return INIT.key.intValue() == key;
    }

    public static List<Integer> SHWO_LIST_STATUS = Arrays.asList(
            FROZEN.key,
            EFFECTIVE.key
    );

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

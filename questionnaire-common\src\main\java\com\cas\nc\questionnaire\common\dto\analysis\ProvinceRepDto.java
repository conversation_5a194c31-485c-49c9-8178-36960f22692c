package com.cas.nc.questionnaire.common.dto.analysis;

import java.math.BigDecimal;

public class ProvinceRepDto {
    private String provinceName;
    private Integer answerTotal;
    private Integer browseRecordsTotal;
    private BigDecimal percentage;

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getAnswerTotal() {
        return answerTotal;
    }

    public void setAnswerTotal(Integer answerTotal) {
        this.answerTotal = answerTotal;
    }

    public Integer getBrowseRecordsTotal() {
        return browseRecordsTotal;
    }

    public void setBrowseRecordsTotal(Integer browseRecordsTotal) {
        this.browseRecordsTotal = browseRecordsTotal;
    }

    public BigDecimal getPercentage() {
        return percentage;
    }

    public void setPercentage(BigDecimal percentage) {
        this.percentage = percentage;
    }
}

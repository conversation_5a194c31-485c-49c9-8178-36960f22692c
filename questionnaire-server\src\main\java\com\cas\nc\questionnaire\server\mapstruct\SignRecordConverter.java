package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.sign.QstSignRecordDto;
import com.cas.nc.questionnaire.common.vo.sign.QstSignRecordVo;
import com.cas.nc.questionnaire.dao.po.QstSignRecordPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 签到记录转换器
 */
@Mapper
public interface SignRecordConverter {

    SignRecordConverter INSTANCE = Mappers.getMapper(SignRecordConverter.class);

    /**
     * VO转DTO
     */
    QstSignRecordDto voToDto(QstSignRecordVo vo);

    /**
     * DTO转VO
     */
    QstSignRecordVo dtoToVo(QstSignRecordDto dto);

    /**
     * PO转DTO
     */
    QstSignRecordDto poToDto(QstSignRecordPo po);

    /**
     * DTO转PO
     */
    QstSignRecordPo dtoToPo(QstSignRecordDto dto);
} 
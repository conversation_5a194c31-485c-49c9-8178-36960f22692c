package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.setting.*;
import com.cas.nc.questionnaire.common.enums.*;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.to.FilterTitleJsonInfoTo;
import com.cas.nc.questionnaire.common.to.FilterTitleJsonStrInfoTo;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.Constants;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.ValidateUtils;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnAddReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnListRepVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnListReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnQueryRepVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnQueryReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnUpdateReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleAddReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleDeleteReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleListRepVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleListReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleUpdateReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleUseReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleAddReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleDeleteReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleListRepVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleListReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleUpdateReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingSetQueryRepVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingSetQueryReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingSetReqVo;
import com.cas.nc.questionnaire.common.vo.setting.*;
import com.cas.nc.questionnaire.server.SettingServer;
import com.cas.nc.questionnaire.server.mapstruct.*;
import com.cas.nc.questionnaire.service.UserInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.*;
import static com.cas.nc.questionnaire.common.enums.ShareTypeEnum.isPartOpen;
import static com.cas.nc.questionnaire.common.utils.JSONUtil.toJSONString;
import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.getFilterTitleJsonInfoTo;


@RestController
@RequestMapping("/questionnaire/setting")
public class SettingController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(SettingController.class);
    @Resource
    private SettingServer settingServer;
    /**
     * 查询过滤规则列表
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/queryfilterrulelist")
    public ApiReturnResult queryFilterRuleList(@RequestBody SettingFilterRuleListReqVo reqVo) {
        logger.info("SettingController.queryFilterRuleList param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        SettingFilterRuleListReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        List<SettingFilterRuleListRepDto> repDtoList = settingServer.queryFilterRuleList(reqDto);
        List<SettingFilterRuleListRepVo> repVoList = SettingConverter.INSTANCE.to(repDtoList);

        result.setData(repVoList);

        logger.info("SettingController.queryFilterRuleList result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 新增过滤条件
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/addfilterrule")
    public ApiReturnResult addFilterRule(@RequestBody SettingFilterRuleAddReqVo reqVo) {
        logger.info("SettingController.addFilterRule param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        SettingFilterRuleAddReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
        if (FilterRuleTypeEnum.isTitle(reqVo.getRuleType())) {
            FilterTitleJsonInfoTo jsonInfoTo = getFilterTitleJsonInfoTo(reqVo.getContent());
            reqDto.setContent(JSONUtil.toJSONString(jsonInfoTo));
        }
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.addFilterRule(reqDto);

        logger.info("SettingController.addFilterRule result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 删除过滤条件
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/dltfilterrule")
    public ApiReturnResult deleteFilterRule(@RequestBody SettingFilterRuleDeleteReqVo reqVo) {
        logger.info("SettingController.deleteFilterRule param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        validateParam(reqVo);
        SettingFilterRuleDeleteReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.deleteFilterRule(reqDto);

        logger.info("SettingController.deleteFilterRule result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 更新过滤条件
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/updatefilterrule")
    public ApiReturnResult updateFilterRule(@RequestBody SettingFilterRuleUpdateReqVo reqVo) {
        logger.info("SettingController.updateFilterRule param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        validateParam(reqVo);

        SettingFilterRuleUpdateReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.updateFilterRule(reqDto);

        logger.info("SettingController.updateFilterRule result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 过滤条件应用(自动应用 or 手动应用)
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/filteruse")
    public ApiReturnResult filterUse(@RequestBody SettingFilterRuleUseReqVo reqVo) {
        logger.info("SettingController.filterUse param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        SettingFilterRuleUseReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.filterUse(reqDto);

        logger.info("SettingController.filterUse result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 查询配额列表
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/queryquotalist")
    public ApiReturnResult queryQuotaList(@RequestBody SettingQuotaRuleListReqVo reqVo) {
        logger.info("SettingController.queryQuotaList param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        SettingQuotaRuleListReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        List<SettingQuotaRuleListRepDto> repDtoList = settingServer.queryQuotaList(reqDto);
        List<SettingQuotaRuleListRepVo> repVoList = QueryQuotaListConverter.INSTANCE.to(repDtoList);

        result.setData(repVoList);

        logger.info("SettingController.queryQuotaList result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 新增配额规则
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/addquotarule")
    public ApiReturnResult addQuotaRule(@RequestBody SettingQuotaRuleAddReqVo reqVo) {
        logger.info("SettingController.addQuotaRule param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        SettingQuotaRuleAddReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);

//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.addQuotaRule(reqDto);

        logger.info("SettingController.addQuotaRule result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 删除配额规则
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/dltquotarule")
    public ApiReturnResult deleteQuotaRule(@RequestBody SettingQuotaRuleDeleteReqVo reqVo) {
        logger.info("SettingController.deleteQuotaRule param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        ValidateUtils.validateNotNullExclude(reqVo, "userId");
        SettingQuotaRuleDeleteReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.deleteQuotaRule(reqDto);

        logger.info("SettingController.deleteQuotaRule result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 更新配额规则
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/updatequotarule")
    public ApiReturnResult updateQuotaRule(@RequestBody SettingQuotaRuleUpdateReqVo reqVo) {
        logger.info("SettingController.updateQuotaRule param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        SettingQuotaRuleUpdateReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.updateQuotaRule(reqDto);

        logger.info("SettingController.updateQuotaRule result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 问卷设置提交 or 更新
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/set")
    public ApiReturnResult set(@RequestBody SettingSetReqVo reqVo) {
        logger.info("SettingController.set param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        validateParam(reqVo);
        SettingSetReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.set(reqDto);

        logger.info("SettingController.set result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 问卷设置查询
     *
     * @param reqVo
     * @return
     */

    @RequestMapping("/query")
    public ApiReturnResult query(@RequestBody SettingSetQueryReqVo reqVo) {
        logger.info("SettingController.query param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullContain(reqVo, "questionnaireId");

        SettingSetQueryReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
        reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));
//        reqDto.setUserId(getUserId());

        SettingSetQueryRepDto repDto = settingServer.query(reqDto);
        SettingSetQueryRepVo repVo = SettingConverter.INSTANCE.to(repDto);

        result.setData(repVo);

        logger.info("SettingController.query result[{}]", toJSONString(result));
        return result;
    }

    /**
     * 条件跳转
     *
     * @param reqVo
     * @return
     */
    @RequestMapping("/conditionreturnlist")
    public ApiReturnResult conditionReturnList(@RequestBody SettingConditionReturnListReqVo reqVo) {
        logger.info("SettingController.conditionReturnList param[{}]", toJSONString(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        SettingConditionReturnListReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        List<SettingConditionReturnListRepDto> repDtoList = settingServer.conditionReturnList(reqDto);
        List<SettingConditionReturnListRepVo> repVoList = ConditionReturnListConverter.INSTANCE.to(repDtoList);
        result.setData(repVoList);

        logger.info("SettingController.conditionReturnList result[{}]", toJSONString(result));
        return result;
    }

    @RequestMapping("/conditionreturnadd")
    public ApiReturnResult conditionReturnListAdd(@RequestBody SettingConditionReturnAddReqVo reqVo) {
        logger.info("SettingController.conditionReturnListAdd param[{}]", toJSONString(reqVo));
        Assert.notNull(reqVo);
        Assert.notNull(reqVo.getConditionType(), "conditionType");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        SettingConditionReturnAddReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.conditionReturnListAdd(reqDto);

        logger.info("SettingController.conditionReturnListAdd result[{}]", toJSONString(result));
        return result;
    }

    @RequestMapping("/conditionreturnupdate")
    public ApiReturnResult conditionReturnListUpdate(@RequestBody SettingConditionReturnUpdateReqVo reqVo) {
        logger.info("SettingController.conditionReturnListUpdate param[{}]", toJSONString(reqVo));
        Assert.notNull(reqVo);
        Assert.notNull(reqVo.getConditionType(), "conditionType");
        Assert.notNull(reqVo.getLimitConditionId(), "limitConditionId");
        ConditionTypeEnum.filterLegalException(reqVo.getConditionType());

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        SettingConditionReturnUpdateReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.conditionReturnListUpdate(reqDto);

        logger.info("SettingController.conditionReturnListUpdate result[{}]", toJSONString(result));
        return result;
    }

    @RequestMapping("/conditionreturnquery")
    public ApiReturnResult conditionReturnListQuery(@RequestBody SettingConditionReturnQueryReqVo reqVo) {
        logger.info("SettingController.conditionReturnListQuery param[{}]", toJSONString(reqVo));
        Assert.notNull(reqVo);
        Assert.notNull(reqVo.getLimitConditionId(), "limitConditionId");
        Assert.notNull(reqVo.getQuestionnaireId(), "questionnaireId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        SettingConditionReturnQueryReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        SettingConditionReturnQueryRepDto repDto = settingServer.conditionReturnListQuery(reqDto);
        SettingConditionReturnQueryRepVo repVo = SettingConverter.INSTANCE.to(repDto);

        result.setData(repVo);

        logger.info("SettingController.conditionReturnListQuery result[{}]", toJSONString(result));
        return result;
    }

    @RequestMapping("/dltconditionreturn")
    public ApiReturnResult deleteConditionReturn(@RequestBody SettingDeleteConditionReturnReqVo reqVo) {
        logger.info("SettingController.deleteConditionReturn param[{}]", toJSONString(reqVo));
        ValidateUtils.validateNotNullExclude(reqVo, "userId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        SettingDeleteConditionReturnReqDto reqDto = SettingConverter.INSTANCE.to(reqVo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));

        settingServer.deleteConditionReturn(reqDto);

        logger.info("SettingController.deleteConditionReturn result[{}]", toJSONString(result));
        return result;
    }

    @RequestMapping("/getreportshare")
    public ApiReturnResult getReportShare(@RequestBody GetReportShareReqVo vo) {
        logger.info("SettingController.getReportShare param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullExclude(vo, "userId");
        GetReportShareReqDto reqDto = GetReportShareConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());
        ShareRequestTypeEnum.toEnum(vo.getShareRequestType());

        GetReportShareRepDto repDto = settingServer.getReportShare(reqDto);
        GetReportShareRepVo repVo = GetReportShareConverter.INSTANCE.to(repDto);

        result.setData(repVo);

        logger.info("SettingController.getReportShare result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/reportshareset")
    public ApiReturnResult reportShareSet(@RequestBody ReportShareSetReqVo vo) {
        logger.info("SettingController.reportShareSet param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullExclude(vo, "userId", "pwd");
        ReportShareSetReqDto reqDto = ReportShareSetConverter.INSTANCE.to(vo);
//        reqDto.setUserId(getUserId());
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId());
        reqDto.setUserId(Long.valueOf(userId));
        ShareRequestTypeEnum.toEnum(vo.getShareRequestType());
        ShareTypeEnum.toEnum(vo.getShareType());
        validatePwd(vo);

        settingServer.reportShareSet(reqDto);

        logger.info("SettingController.reportShareSet result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/gettip")
    public ApiReturnResult getTip() {
        logger.info("SettingController.getTip start");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        int limit = settingServer.getTip(getUserId());
        String tip = String.format(Constants.ANSWER_LIMIT_TIP, limit);
        SettingTipRepVo vo = new SettingTipRepVo();
        vo.setLimit(limit);
        vo.setTip(tip);
        result.setData(vo);

        logger.info("SettingController.getTip result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/settip")
    public ApiReturnResult setTip(@RequestBody SettingAnswerLimitReqVo vo) {
        logger.info("SettingController.setTip param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        settingServer.setTip(vo.getAnswerLimit());
        return result;

    }

    private void validatePwd(ReportShareSetReqVo vo) {
        if (isPartOpen(vo.getShareType())) {
            Assert.notNull(vo.getPwd(), "pwd");
            Assert.isTrue(vo.getPwd().length() <= 30 && vo.getPwd().length() >= 6, PWD_LONG_EXCEPTION, "为6-30位");
        }
    }

    private void validateParam(SettingSetReqVo reqVo) {
        Assert.notNull(reqVo.getQuestionnaireId(), "questionnaireId");
        if (reqVo.getBeginTime() != null && reqVo.getEndTime() != null) {
            boolean flag = reqVo.getBeginTime().getTime() <= reqVo.getEndTime().getTime();
            Assert.isTrue(flag, BEGIN_END_TIME_COMPARE_ERROR);
        }
    }

    private void validateParam(SettingFilterRuleDeleteReqVo reqVo) {
        Assert.notNull(reqVo, IS_NULL);
        Assert.notNull(reqVo.getFilterRuleId(), "id");
        Assert.notNull(reqVo.getQuestionnaireId(), "questionnaireId");
    }

    private void validateParam(SettingFilterRuleUpdateReqVo reqVo) {
        Assert.notNull(reqVo, IS_NULL);
        Assert.notNull(reqVo.getQuestionnaireId(), "questionnaireId");
        Assert.notNull(reqVo.getId(), "id");
    }
}

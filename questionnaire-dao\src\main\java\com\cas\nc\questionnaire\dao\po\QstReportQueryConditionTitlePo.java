package com.cas.nc.questionnaire.dao.po;

/**
 * 测评报告查询条件题目实体类
 */
public class QstReportQueryConditionTitlePo {
    
    /**
     * 自增主键
     */
    private Long id;
    
    /**
     * 关联的条件ID
     */
    private Long conditionId;
    
    /**
     * 题目ID
     */
    private Long titleId;
    
    /**
     * 题目序号
     */
    private Integer serialNumber;
    
    /**
     * 校验类型：1-姓名，2-邮箱，3-手机，10-身份证号
     */
    private Integer validateType;
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getConditionId() {
        return conditionId;
    }
    
    public void setConditionId(Long conditionId) {
        this.conditionId = conditionId;
    }
    
    public Long getTitleId() {
        return titleId;
    }
    
    public void setTitleId(Long titleId) {
        this.titleId = titleId;
    }
    
    public Integer getSerialNumber() {
        return serialNumber;
    }
    
    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }
    
    public Integer getValidateType() {
        return validateType;
    }
    
    public void setValidateType(Integer validateType) {
        this.validateType = validateType;
    }
} 
package com.cas.nc.questionnaire.common.to.questionnairetitle;

public class BiaoGeShuZhiTitleTo extends BaseTitleTo {
    /*行标题*/
    private String rowTitle;

    /*列标题*/
    private String colTitle;

    /*最小字数*/
    private Integer minBoundsValue;

    /*最大限制字数*/
    private Integer maxBoundsValue;

    /*校验类型*/
    private Integer validateType;

    /*高度*/
    private Integer height;

    /*宽度*/
    private Integer width;

    public String getRowTitle() {
        return rowTitle;
    }

    public void setRowTitle(String rowTitle) {
        this.rowTitle = rowTitle;
    }

    public String getColTitle() {
        return colTitle;
    }

    public void setColTitle(String colTitle) {
        this.colTitle = colTitle;
    }

    public Integer getMinBoundsValue() {
        return minBoundsValue;
    }

    public void setMinBoundsValue(Integer minBoundsValue) {
        this.minBoundsValue = minBoundsValue;
    }

    public Integer getMaxBoundsValue() {
        return maxBoundsValue;
    }

    public void setMaxBoundsValue(Integer maxBoundsValue) {
        this.maxBoundsValue = maxBoundsValue;
    }

    public Integer getValidateType() {
        return validateType;
    }

    public void setValidateType(Integer validateType) {
        this.validateType = validateType;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }
}

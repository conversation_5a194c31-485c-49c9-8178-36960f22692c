package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.questionnaire.GetTitleReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireEditRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireEditReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnairePublishReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireRemoveReqDto;
import com.cas.nc.questionnaire.common.vo.questionnaire.GetTitleReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.QuestionnaireEditRepVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.QuestionnaireEditReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.QuestionnairePublishReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.QuestionnaireRemoveReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface QuestionnaireEditConverter {
    QuestionnaireEditConverter INSTANCE = Mappers.getMapper(QuestionnaireEditConverter.class);

    QuestionnaireEditReqDto to(QuestionnaireEditReqVo vo);

    QuestionnaireEditRepVo to(QuestionnaireEditRepDto repDto);

    GetTitleReqDto to(GetTitleReqVo vo);

    QuestionnaireRemoveReqDto to(QuestionnaireRemoveReqVo vo);

    QuestionnairePublishReqDto to(QuestionnairePublishReqVo vo);
}
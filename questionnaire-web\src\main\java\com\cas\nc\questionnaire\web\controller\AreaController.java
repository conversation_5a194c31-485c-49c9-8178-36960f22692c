package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.area.AreaQueryProvince2CountyRepDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.vo.area.AreaQueryProvince2CountyRepVo;
import com.cas.nc.questionnaire.server.AreaServer;
import com.cas.nc.questionnaire.server.mapstruct.AreaConverter;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RequestMapping("/questionnaire/area")
@RestController
public class AreaController extends BaseController {

    @Resource
    private AreaServer areaServer;

    @RequestMapping("/queryprovince2county")
    public ApiReturnResult queryProvince2County() {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        AreaQueryProvince2CountyRepDto repDto = areaServer.queryProvince2County();
        AreaQueryProvince2CountyRepVo repVo = AreaConverter.INSTANCE.to(repDto);

        result.setData(repVo);
        return result;
    }

    @RequestMapping("/queryprovince2city")
    public ApiReturnResult queryProvince2city() {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        AreaQueryProvince2CountyRepDto repDto = areaServer.queryProvince2City();
        AreaQueryProvince2CountyRepVo repVo = AreaConverter.INSTANCE.to(repDto);

        result.setData(repVo);
        return result;
    }
}

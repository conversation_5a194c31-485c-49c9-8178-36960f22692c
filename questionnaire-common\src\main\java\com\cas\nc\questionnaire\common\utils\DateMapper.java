package com.cas.nc.questionnaire.common.utils;

import java.util.Date;

import static com.cas.nc.questionnaire.common.utils.DateUtil.C_TIME_PATTON_DEFAULT;

public class DateMapper {

    public String asString(Date date) {
        return date != null ? DateUtil.format(date, C_TIME_PATTON_DEFAULT) : null;
    }

    public Date asDate(String date) {
        return StringUtil.isNotBlank(date) ? DateUtil.parseDate(C_TIME_PATTON_DEFAULT, date) : null;

    }

}

package com.cas.nc.questionnaire.common.vo.answer;

import java.util.List;

public class ListAnswerRepVo {
    /*每页行数*/
    private Integer pageSize;
    /*导向页*/
    private Integer page;

    private Integer count;

    private List<AnswerRepVo> answerList;

    private Boolean independentShowFlag;

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<AnswerRepVo> getAnswerList() {
        return answerList;
    }

    public void setAnswerList(List<AnswerRepVo> answerList) {
        this.answerList = answerList;
    }

    public void setIndependentShowFlag(Boolean independentShowFlag) {
        this.independentShowFlag = independentShowFlag;
    }

    public Boolean getIndependentShowFlag() {
        return independentShowFlag;
    }
}

package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.setting.*;

import java.util.List;


public interface SettingServer {

    /**
     * 查询过滤列表
     *
     * @param reqDto
     * @return
     */
    List<SettingFilterRuleListRepDto> queryFilterRuleList(SettingFilterRuleListReqDto reqDto);

    /**
     * 添加过滤规则
     *
     * @param reqDto
     */
    void addFilterRule(SettingFilterRuleAddReqDto reqDto);

    /**
     * 更新过滤规则
     *
     * @param reqDto
     */
    void updateFilterRule(SettingFilterRuleUpdateReqDto reqDto);

    /**
     * 删除过滤规则
     *
     * @param reqDto
     */
    void deleteFilterRule(SettingFilterRuleDeleteReqDto reqDto);

    /**
     * 查询配额列表
     *
     * @param reqDto
     * @return
     */
    List<SettingQuotaRuleListRepDto> queryQuotaList(SettingQuotaRuleListReqDto reqDto);

    /**
     * 添加配额规则
     *
     * @param reqDto
     */
    void addQuotaRule(SettingQuotaRuleAddReqDto reqDto);

    /**
     * 删除配额规则
     *
     * @param reqDto
     */
    void deleteQuotaRule(SettingQuotaRuleDeleteReqDto reqDto);

    /**
     * 更新配额规则
     *
     * @param reqDto
     */
    void updateQuotaRule(SettingQuotaRuleUpdateReqDto reqDto);

    /**
     * 配置设置
     *
     * @param reqDto
     */
    void set(SettingSetReqDto reqDto);

    /**
     * 手动应用过滤规则
     *
     * @param reqDto
     */
    void filterUse(SettingFilterRuleUseReqDto reqDto);

    /**
     * 条件跳转查询
     *
     * @param reqDto
     * @return
     */
    List<SettingConditionReturnListRepDto> conditionReturnList(SettingConditionReturnListReqDto reqDto);

    /**
     * 条件跳转新增
     *
     * @param reqDto
     */
    void conditionReturnListAdd(SettingConditionReturnAddReqDto reqDto);

    /**
     * 条件跳转更新
     *
     * @param reqDto
     */
    void conditionReturnListUpdate(SettingConditionReturnUpdateReqDto reqDto);

    /**
     * 条件编辑查询
     *
     * @param reqDto
     * @return
     */
    SettingConditionReturnQueryRepDto conditionReturnListQuery(SettingConditionReturnQueryReqDto reqDto);

    /**
     * 问卷设置查询
     *
     * @param reqDto
     * @return
     */
    SettingSetQueryRepDto query(SettingSetQueryReqDto reqDto);

    void deleteConditionReturn(SettingDeleteConditionReturnReqDto reqDto);

    GetReportShareRepDto getReportShare(GetReportShareReqDto reqDto);

    void reportShareSet(ReportShareSetReqDto reqDto);

    int getTip(Long userId);

    void setTip(Integer answerLimit);
}

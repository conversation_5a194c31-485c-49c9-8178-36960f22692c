<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.AnswerInfoDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.AnswerInfoPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="answer_id" jdbcType="VARCHAR" property="answerId"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="answer_user_id" jdbcType="VARCHAR" property="answerUserId"/>
        <result column="openid" jdbcType="VARCHAR" property="openid"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="duration" jdbcType="BIGINT" property="duration"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="ip_long" jdbcType="BIGINT" property="ipLong"/>
        <result column="province" jdbcType="BIGINT" property="province"/>
        <result column="city" jdbcType="BIGINT" property="city"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="filter_rule_id" jdbcType="BIGINT" property="filterRuleId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,answer_id,questionnaire_id,user_id,answer_user_id,openid,begin_time,end_time,
    duration,status,ip,ip_long,province,city,
    device_id,source,filter_rule_id,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.answerId">and answer_id = #{item.answerId}</if>
            <if test="null != item.questionnaireId">and questionnaire_id = #{item.questionnaireId}</if>
            <if test="null != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.answerUserId">and answer_user_id = #{item.answerUserId}</if>
            <if test="null != item.openid">and openid = #{item.openid}</if>
            <if test="null != item.beginTime">and begin_time = #{item.beginTime}</if>
            <if test="null != item.endTime">and end_time = #{item.endTime}</if>
            <if test="null != item.duration">and duration = #{item.duration}</if>
            <if test="null != item.status">and status = #{item.status}</if>
            <if test="null != item.ip">and ip = #{item.ip}</if>
            <if test="null != item.ipLong">and ip_long = #{item.ipLong}</if>
            <if test="null != item.province">and province = #{item.province}</if>
            <if test="null != item.city">and city = #{item.city}</if>
            <if test="null != item.deviceId">and device_id = #{item.deviceId}</if>
            <if test="null != item.source">and source = #{item.source}</if>
            <if test="null != item.filterRuleId">and filter_rule_id = #{item.filterRuleId}</if>
            <if test="null != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime">and create_time = #{item.createTime}</if>


            <if test="null != item.answerIdList and item.answerIdList.size() > 0">
                and answer_id in
                <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>

            <if test="null != item.statusList and item.statusList.size() > 0">
                and status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>

            <if test="null != item.questionnaireIdList and item.questionnaireIdList.size() > 0">
                and questionnaire_id in
                <foreach collection="item.questionnaireIdList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>

            <if test="item.beginCreateTime != null and item.endCreateTime != null">
                <![CDATA[and create_time >= #{item.beginCreateTime} and create_time < #{item.endCreateTime}]]>
            </if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_info
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_info
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_info
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into answer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.answerId">answer_id,</if>
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.answerUserId">answer_user_id,</if>
            <if test="null != item.openid">openid,</if>
            <if test="null != item.beginTime">begin_time,</if>
            <if test="null != item.endTime">end_time,</if>
            <if test="null != item.duration">duration,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.ip">ip,</if>
            <if test="null != item.ipLong">ip_long,</if>
            <if test="null != item.province">province,</if>
            <if test="null != item.city">city,</if>
            <if test="null != item.deviceId">device_id,</if>
            <if test="null != item.source">source,</if>
            <if test="null != item.filterRuleId">filter_rule_id,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.answerId">#{item.answerId},</if>
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.answerUserId">#{item.answerUserId},</if>
            <if test="null != item.openid">#{item.openid},</if>
            <if test="null != item.beginTime">#{item.beginTime},</if>
            <if test="null != item.endTime">#{item.endTime},</if>
            <if test="null != item.duration">#{item.duration},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.ip">#{item.ip},</if>
            <if test="null != item.ipLong">#{item.ipLong},</if>
            <if test="null != item.province">#{item.province},</if>
            <if test="null != item.city">#{item.city},</if>
            <if test="null != item.deviceId">#{item.deviceId},</if>
            <if test="null != item.source">#{item.source},</if>
            <if test="null != item.filterRuleId">#{item.filterRuleId},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.answerId">answer_id = values(answer_id),</if>
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.answerUserId">answer_user_id = values(answer_user_id),</if>
            <if test="null != item.openid">openid = values(openid),</if>
            <if test="null != item.beginTime">begin_time = values(begin_time),</if>
            <if test="null != item.endTime">end_time = values(end_time),</if>
            <if test="null != item.duration">duration = values(duration),</if>
            <if test="null != item.status">status = values(status),</if>
            <if test="null != item.ip">ip = values(ip),</if>
            <if test="null != item.ipLong">ip_long = values(ip_long),</if>
            <if test="null != item.province">province = values(province),</if>
            <if test="null != item.city">city = values(city),</if>
            <if test="null != item.deviceId">device_id = values(device_id),</if>
            <if test="null != item.source">source = values(source),</if>
            <if test="null != item.filterRuleId">filter_rule_id = values(filter_rule_id),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update answer_info
        <set>
            <if test="null != item.beginTime">begin_time = #{item.beginTime},</if>
            <if test="null != item.endTime">end_time = #{item.endTime},</if>
            <if test="null != item.duration">duration = #{item.duration},</if>
            <if test="null != item.status">status = #{item.status},</if>
            <if test="null != item.ip">ip = #{item.ip},</if>
            <if test="null != item.ipLong">ip_long = #{item.ipLong},</if>
            <if test="null != item.province">province = #{item.province},</if>
            <if test="null != item.city">city = #{item.city},</if>
            <if test="null != item.deviceId">device_id = #{item.deviceId},</if>
            <if test="null != item.source">source = #{item.source},</if>
            <if test="null != item.filterRuleId">filter_rule_id = #{item.filterRuleId},</if>
            <if test="null != item.openid">openid = #{item.openid},</if>
        </set>
        where questionnaire_id = #{item.questionnaireId}
        and user_id = #{item.userId}
        and answer_id = #{item.answerId}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from answer_info
        <include refid="sql_where"/>
    </delete>

    <select id="selectCount" resultType="int">
        select count(1)
        from answer_info
        <include refid="sql_where"/>
    </select>

    <select id="selectListByFilterCondition" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_info
        <include refid="sql_where"/>
        <if test="null != item.provinceList and item.provinceList.size() > 0">
            and province
            <if test="item.judgeCondition == 1">
                in
            </if>
            <if test="item.judgeCondition == 2">
                not in
            </if>
            <foreach collection="item.provinceList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        <if test="null != item.cityList and item.cityList.size() > 0">
            and city
            <if test="item.judgeCondition == 7">
                in
            </if>
            <if test="item.judgeCondition == 8">
                not in
            </if>
            <foreach collection="item.cityList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        <if test="null != item.sourceList and item.sourceList.size() > 0">
            and source
            <if test="item.judgeCondition == 7">
                in
            </if>
            <if test="item.judgeCondition == 8">
                not in
            </if>
            <foreach collection="item.sourceList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        <if test="null != item.durationStart">
            and duration > #{item.durationStart}
        </if>
        <if test="null != item.durationEnd">
            <![CDATA[ and duration < #{item.durationEnd}]]>
        </if>
    </select>

    <select id="selectNewest" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_info
        <include refid="sql_where"/>
        order by create_time desc, id desc
        limit 1;
    </select>

    <select id="selectListByPage" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_info
        <include refid="sql_where"/>

        order by create_time desc, id desc
        limit #{item.startIndex}, #{item.pageSize}
    </select>

    <select id="selectSourceStatistics" resultType="SourceAnalysisBo">
        select source, count(source) total
        from answer_info
        <where>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">
                and questionnaire_id = #{item.questionnaireId}
            </if>
            <if test="null != item.userId">
                and user_id = #{item.userId}
            </if>
        </where>
        group by source
    </select>

    <select id="selectProvinceStatistics" resultType="ProvinceAnalysisBo">
        select province, count(province) total
        from answer_info
        <where>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">
                and questionnaire_id = #{item.questionnaireId}
            </if>
            <if test="null != item.userId">
                and user_id = #{item.userId}
            </if>
        </where>
        group by province
    </select>

    <select id="selectDayStatistics" resultType="TimeAnalysisBo">
        select DATE_FORMAT(create_time,'%Y-%m-%d') timeMark, count(1) total
        from answer_info
        where create_time >= #{item.beginCreateTime}
        <![CDATA[and create_time < #{item.endCreateTime}]]>
        <if test="null != item.questionnaireId and '' != item.questionnaireId">
            and questionnaire_id = #{item.questionnaireId}
        </if>
        <if test="null != item.userId">
            and user_id = #{item.userId}
        </if>
        group by DATE_FORMAT(create_time,'%Y-%m-%d')
    </select>

    <select id="selectWeekDayStatistics" resultType="TimeAnalysisBo">
        select DATE_FORMAT(create_time,'%Y%u') timeMark, count(1) total
        from answer_info
        where DATE_FORMAT(create_time,'%Y%u') >= #{item.beginCreateTime}
        <![CDATA[and DATE_FORMAT(create_time,'%Y%u') <= #{item.endCreateTime}]]>
        <if test="null != item.questionnaireId and '' != item.questionnaireId">
            and questionnaire_id = #{item.questionnaireId}
        </if>
        <if test="null != item.userId">
            and user_id = #{item.userId}
        </if>
        group by DATE_FORMAT(create_time,'%Y%u')
    </select>

    <select id="selectMonthStatistics" resultType="TimeAnalysisBo">
        select DATE_FORMAT(create_time,'%Y-%m') timeMark, count(1) total
        from answer_info
        where create_time >= #{item.beginCreateTime}
        <![CDATA[and create_time < #{item.endCreateTime}]]>
        <if test="null != item.questionnaireId and '' != item.questionnaireId">
            and questionnaire_id = #{item.questionnaireId}
        </if>
        <if test="null != item.userId">
            and user_id = #{item.userId}
        </if>
        group by DATE_FORMAT(create_time,'%Y-%m')
    </select>

    <select id="selectRuleGroupBy" resultType="java.lang.Long">
        select
        <if test="item.provinceGroupBy != null">
            province
        </if>
        <if test="item.cityGroupBy != null">
            city
        </if>
        <if test="item.sourceGroupBy">
            source
        </if>
        from answer_info
        <where>
            <if test="null != item.questionnaireId and '' != item.questionnaireId">
                and questionnaire_id = #{item.questionnaireId}
            </if>
            <if test="null != item.userId">
                and user_id = #{item.userId}
            </if>
        </where>
        <if test="item.provinceGroupBy != null">
            group by province
        </if>
        <if test="item.cityGroupBy != null">
            group by city
        </if>
        <if test="item.sourceGroupBy">
            group by source
        </if>
    </select>

</mapper>




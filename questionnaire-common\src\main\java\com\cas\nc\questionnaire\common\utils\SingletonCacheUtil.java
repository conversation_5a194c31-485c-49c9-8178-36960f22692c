package com.cas.nc.questionnaire.common.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class SingletonCacheUtil {

    private Map<String, Object> localCacheStore = new ConcurrentHashMap<String, Object>();

    //内部类实现懒加载
    private static class SingletonMapCacheUtilHandler {
        private static SingletonCacheUtil instance = new SingletonCacheUtil();
    }

    private SingletonCacheUtil() {
    }

    //静态方法，外部获得实例对象
    public static SingletonCacheUtil getInstance() {
        return SingletonMapCacheUtilHandler.instance;
    }

    //获得缓存中的数据
    public Object getValueByKey(String key) {
        return localCacheStore.get(key);
    }

    //向缓存中添加数据
    public void putValue(String key, Object value) {
        localCacheStore.put(key, value);
    }
}

package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class QstEmailPo {
    /*自增id*/
    private Long id;

    /*邮件id*/
    private String emailId;

    /*问卷id*/
    private String questionnaireId;

    /*用户id*/
    private Long userId;

    private String foreignId;

    /*邮件主题*/
    private String emailTitle;

    /*收件人*/
    private String sender;

    /*状态：1，初始化，2：发送完成，3：发送失败',  4 已打开 未作答     5 已打开  已作答*/
    private Integer status;

    /*来源类型，1：问卷设置，2：发送问卷*/
    private Integer sourceType;

    /*回复地址*/
    private String replyAddress;

    /*收件人类型，1：指定收件人，2：问卷中填写的收件人*/
    private Integer addresseeType;

    /*题目序号*/
    private Integer serialNumber;

    /*发送时间*/
    private Date sendTime;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    /*邮件正文*/
    private String emailContent;

    /*收件人，用分号分隔*/
    private String addressees;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getForeignId() {
        return foreignId;
    }

    public void setForeignId(String foreignId) {
        this.foreignId = foreignId;
    }

    public String getEmailTitle() {
        return emailTitle;
    }

    public void setEmailTitle(String emailTitle) {
        this.emailTitle = emailTitle;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public String getReplyAddress() {
        return replyAddress;
    }

    public void setReplyAddress(String replyAddress) {
        this.replyAddress = replyAddress;
    }

    public Integer getAddresseeType() {
        return addresseeType;
    }

    public void setAddresseeType(Integer addresseeType) {
        this.addresseeType = addresseeType;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getEmailContent() {
        return emailContent;
    }

    public void setEmailContent(String emailContent) {
        this.emailContent = emailContent;
    }

    public String getAddressees() {
        return addressees;
    }

    public void setAddressees(String addressees) {
        this.addressees = addressees;
    }
}
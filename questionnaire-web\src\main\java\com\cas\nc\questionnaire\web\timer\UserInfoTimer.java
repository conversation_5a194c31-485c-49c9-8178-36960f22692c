package com.cas.nc.questionnaire.web.timer;

import com.cas.nc.questionnaire.server.UserInfoServer;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class UserInfoTimer {

    @Resource
    private UserInfoServer userInfoServer;

    @Scheduled(cron = "0 10 0 ? * *")
    public void expiredUserInfo() {
        userInfoServer.expireVip();
    }
}

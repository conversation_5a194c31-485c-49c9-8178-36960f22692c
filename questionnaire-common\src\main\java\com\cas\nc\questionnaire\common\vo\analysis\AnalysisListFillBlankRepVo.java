package com.cas.nc.questionnaire.common.vo.analysis;

import java.util.List;

public class AnalysisListFillBlankRepVo {
    private Integer pageSize;

    private Integer page;

    private Integer count;

    private List<FillBlankRepVo> fillBlankRepVoList;

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<FillBlankRepVo> getFillBlankRepVoList() {
        return fillBlankRepVoList;
    }

    public void setFillBlankRepVoList(List<FillBlankRepVo> fillBlankRepVoList) {
        this.fillBlankRepVoList = fillBlankRepVoList;
    }
}

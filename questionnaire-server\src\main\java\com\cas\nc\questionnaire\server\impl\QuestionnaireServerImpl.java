package com.cas.nc.questionnaire.server.impl;

import cn.hutool.system.UserInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cas.nc.questionnaire.common.dto.mylist.CreateExternalQuestionnaireRepDto;
import com.cas.nc.questionnaire.common.dto.mylist.CreateExternalQuestionnaireReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListQuestionnaireRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.*;
import com.cas.nc.questionnaire.common.dto.templet.ConvertTempletRepDto;
import com.cas.nc.questionnaire.common.dto.templet.ConvertTempletReqDto;
import com.cas.nc.questionnaire.common.enums.*;
import com.cas.nc.questionnaire.common.to.QuestionnaireTo;
import com.cas.nc.questionnaire.common.to.questionnaireoption.*;
import com.cas.nc.questionnaire.common.to.questionnairetitle.*;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.*;
import com.cas.nc.questionnaire.dao.bo.QuestionnairePreviewTextBo;
import com.cas.nc.questionnaire.dao.po.*;
import com.cas.nc.questionnaire.dao.query.*;
import com.cas.nc.questionnaire.rpc.wechat.entity.ipentity.IpAreaResult;
import com.cas.nc.questionnaire.server.IpServer;
import com.cas.nc.questionnaire.server.QuestionnaireServer;
import com.cas.nc.questionnaire.server.TempletServer;
import com.cas.nc.questionnaire.server.ValidateQstLimitRuleServer;
import com.cas.nc.questionnaire.server.mapstruct.ExternalListConverter;
import com.cas.nc.questionnaire.server.mapstruct.TextCreateConverter;
import com.cas.nc.questionnaire.server.util.ConvertBeanUtil;
import com.cas.nc.questionnaire.service.*;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.*;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.*;
import static com.cas.nc.questionnaire.common.utils.Constants.*;
import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.*;

@Component
public class QuestionnaireServerImpl implements QuestionnaireServer {

    protected final static Logger logger = LoggerFactory.getLogger(QuestionnaireServer.class);
    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;
    @Resource
    private QstTitleService qstTitleService;
    @Resource
    private QstOptionService qstOptionService;
    @Resource
    private AnswerInfoService answerInfoService;
    @Resource
    private QstEmailService qstEmailService;
    @Resource
    private QstSmsService qstSmsService;
    @Resource
    private AnswerOptionService answerOptionService;
    @Resource
    private TempletServer templetServer;
    @Resource
    private IpServer ipServer;
    @Resource
    private AreaService areaService;
    @Resource
    private QstBrowseRecordsService qstBrowseRecordsService;
    @Resource
    private ValidateQstLimitRuleServer validateQstLimitRuleServer;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private QstLimitRuleService qstLimitRuleService;

    @Value("${text.templet.title.json}")
    private String titleJson;
    @Value("${text.templet.option.json}")
    private String optionJson;
    @Value("${text.templet.questionnaire.json}")
    private String questionnaireJson;

    @Override
    public QuestionnaireQueryRepDto query(QuestionnaireQueryReqDto reqDto) {

        QuestionnaireQueryRepDto result = new QuestionnaireQueryRepDto();

        QstQuestionnaireInfoPo infoPo = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId(), reqDto.getUserId());
        Assert.notNull(infoPo, QST_NOT_EXIST);
        if (StringUtil.isNotBlank(reqDto.getBizNo()) && reqDto.getBizId() != null) {
            Assert.notNull(reqDto.getBizNo().equals(infoPo.getBizNo()) && reqDto.getBizId().equals(infoPo.getBizId()), QST_NOT_EXIST);
        }
        Assert.isTrue(QuestionnaireStatusEnum.isPublished(infoPo.getStatus()), CodeEnum.QST_UPDATING);

        validateQstLimitRuleServer.validateQstLimitRule(reqDto.getQuestionnaireId(), reqDto.getIp(), reqDto.getDeviceCode(), reqDto.getPwd());

        QuestionnaireTo questionnaireTo = ConvertBeanUtil.convertQuestionnaireInfoPo2QuestionnaireInfoTo(infoPo);
        result.setQuestionnairePojo(questionnaireTo);

        if (selectTitleOption(questionnaireTo, reqDto.getQuestionnaireId(), reqDto.getUserId(), reqDto.getPageNo(), false)) {
            return result;
        }

        updateEmailOrSms(reqDto);
        insertBrowseRecords(reqDto);
        return result;
    }

    @Override
    public QuestionnaireQueryRepDto preview(QuestionnaireQueryReqDto reqDto) {
        QuestionnaireQueryRepDto result = new QuestionnaireQueryRepDto();

        QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId(), reqDto.getUserId());
        Assert.notNull(qstQuestionnaireInfoPo, QST_NOT_EXIST);

        QuestionnaireTo questionnaireTo = ConvertBeanUtil.convertQuestionnaireInfoPo2QuestionnaireInfoTo(qstQuestionnaireInfoPo);
        result.setQuestionnairePojo(questionnaireTo);

        selectTitleOption(questionnaireTo, reqDto.getQuestionnaireId(), reqDto.getUserId(), reqDto.getPageNo(), false);
        return result;
    }

    @Override
    public QuestionnaireCreateRepDto create(QuestionnaireCreateReqDto reqDto) {
        logger.info("创建问卷入参:{}", JSONObject.toJSONString(reqDto));
        QuestionnaireCreateRepDto repDto = new QuestionnaireCreateRepDto();
        QstQuestionnaireInfoPo po = new QstQuestionnaireInfoPo();
        po.setTitle(reqDto.getTitle());
        po.setDes(reqDto.getDes());
        po.setUserId(reqDto.getUserId());
        po.setStatus(QuestionnaireStatusEnum.INIT.key());
        String questionnaireId = SequenceUtil.getInstance().generateQuestionnaireId(reqDto.getUserId().toString());
        po.setQuestionnaireId(questionnaireId);
        po.setBizId(reqDto.getBizId());
        po.setBizNo(reqDto.getBizNo());
        po.setChannel(reqDto.getChannel());
        po.setQuestionnaireType(reqDto.getQuestionnaireType());

        qstQuestionnaireInfoService.insert(po);
        repDto.setQuestionnaireId(questionnaireId);
        return repDto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createDetail(QuestionnaireCreateDetailReqDto reqDto) {
        JSONObject jsonObject = JSON.parseObject(reqDto.getRequestJson());
        String questionnaireId = jsonObject.getString("id");
        int operateType = jsonObject.getIntValue("operateType");
        Assert.notNull(questionnaireId, CodeEnum.VALIDATE_PARAM_EXCEPTION);

        reqDto.setQuestionnaireId(questionnaireId);
        validateUpdateQuestionnaireAndStatus(constructQuestionnaireByJson(jsonObject, reqDto.getUserId()));

        List<QstTitlePo> titlePoList = new ArrayList<>();
        List<QstOptionPo> optionPoList = new ArrayList<>();
        JSONArray jsonArray = (JSONArray) jsonObject.get("content");
        Iterator<Object> iterator = jsonArray.iterator();

        while (iterator.hasNext()) {
            JSONObject object = (JSONObject) iterator.next();
            constructTitleAndOption(object, reqDto, titlePoList, optionPoList);
        }
        validateTitleLimit(titlePoList);

        if (OperateTypeEnum.isDelete(operateType)) {
            validateAnswerDelete(reqDto);
            deleteTitleAndOption(reqDto);

            Map<Integer, List<QstOptionPo>> optionMap = optionPoList.stream().collect(Collectors.groupingBy(QstOptionPo::getTitleSerialNumber));
            titlePoList.forEach(v -> {
                if (v.getSerialNumber() != null && v.getSerialNumber() != 0) {
                    List<QstOptionPo> qstOptionPoList = optionMap.get(v.getSerialNumber());
                    SafeUtil.of(qstOptionPoList).forEach(p -> {
                        p.setTitleSerialNumber(v.getOrderNumber());
                    });
                    v.setSerialNumber(v.getOrderNumber());
                }
            });

        } else if (OperateTypeEnum.isRetain(operateType)) {
            validateTitleAndOption(titlePoList, optionPoList, questionnaireId, reqDto.getUserId());
        }

        titlePoList.forEach(v -> qstTitleService.insertUpdate(v));
        optionPoList.forEach(v -> qstOptionService.insertUpdate(v));
    }

    @Override
    public QuestionnaireEditRepDto edit(QuestionnaireEditReqDto reqDto) {
        QuestionnaireEditRepDto result = new QuestionnaireEditRepDto();
        QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId());
        Assert.notNull(qstQuestionnaireInfoPo, QST_NOT_EXIST);
        QuestionnaireTo questionnaireTo = ConvertBeanUtil.convertQuestionnaireInfoPo2QuestionnaireInfoTo(qstQuestionnaireInfoPo);
        result.setQuestionnairePojo(questionnaireTo);

        selectTitleOption(questionnaireTo, reqDto.getQuestionnaireId(), reqDto.getUserId(), null, true);
        return result;
    }

    @Override
    public List<Object> constructTitles(List<QstTitlePo> titlePoList, ListMultimap<Integer, QstOptionPo> optionListMultiMap) {
        List<Object> list = new ArrayList<>();
        titlePoList.forEach(v -> {
            List<QstOptionPo> qstOptionPoList = optionListMultiMap.get(v.getSerialNumber());
            switch (TitleTypeEnum.toEnum(v.getType())) {
                case DANXUAN:
                case XINGBIE:
                case NIANLIANG:
                case GONGZUONIANXIN:
                case JIAOYUCHENGDU:
                case HUNYINZHUANGKUANG:
                    list.add(convertTitlePo2DanXuanTitleTo(v, qstOptionPoList));
                    break;
                case DUOXUAN:
                case PAIXU:
                    list.add(convertTitlePo2DuoXuanTitleTo(v, qstOptionPoList));
                    break;
                case BIAOGEDANXUAN:
                    list.add(convertTitlePo2BiaoGeDanXuanTitleTo(v, qstOptionPoList));
                    break;
                case BIAOGEDUOXUAN:
                    list.add(convertTitlePo2BiaoGeDuoXuanTitleTo(v, qstOptionPoList));
                    break;
                case LIANGBIAO:
                    list.add(convertTitlePo2LiangBiaoTitleTo(v, qstOptionPoList));
                    break;
                case BIAOGEXIALAXUANZE:
                    list.add(convertTitlePo2BiaoGeXiaLaXuanZeTitleTo(v, qstOptionPoList));
                    break;
                case DUOXIANGDANHANGTIANKONG:
                    list.add(convertTitlePo2DuoXiangDanHangTianKongTitleTo(v, qstOptionPoList));
                    break;
                case XIALAXUANZE:
                case ZHIYE:
                case BIYEYUANXIAO:
                case CHENGSHIDIZHI:
                    list.add(convertTitlePo2XiaLaXuanZeTitleTo(v, qstOptionPoList));
                    break;
                case DANHANGTIANKONG:
                case XINGMING:
                case SHOUJI:
                case YOUXIANG:
                case TONGXUNDIZHI:
                    list.add(convertTitlePo2DanHangTianKongTitleTo(v));
                    break;
                case BIAOGESHUZHI:
                    list.add(convertTitlePo2BiaoGeShuZhiTitleTo(v, qstOptionPoList));
                    break;
                case BIAOGEWENBEN:
                    list.add(convertTitlePo2BiaoGeWenBenTitleTo(v, qstOptionPoList));
                    break;
                case MIAOSHUSHUOMING:
                    list.add(convertTitlePo2BaseTitleTo(new BaseTitleTo(), v));
                    break;
                case HUADONGTIAO:
                    list.add(convertTitlePo2HuaDongTiaoTitleTo(v, qstOptionPoList));
                    break;
                case JUZHENHUADONGTIAO:
                    list.add(convertTitlePo2JuZhenHuaDongTiaoTitleTo(v, qstOptionPoList));
                    break;
                case BIZHONG:
                    list.add(convertTitlePo2BiZhongTitleTo(v, qstOptionPoList));
                    break;
                case PINGFENDANXUAN:
                    list.add(convertTitlePo2PingFenDanXuanTitleTo(v, qstOptionPoList));
                    break;
                case PINGFENDUOXUAN:
                    list.add(convertTitlePo2PingFenDuoXuanTitleTo(v, qstOptionPoList));
                    break;
                case FENYEFU:
                    list.add(convertTitlePo2PageTitleTo(v));
                    break;
                case WENJIAN:
                    list.add(convertTitle2WenJianTitleTo(v));
                    break;
            }
        });
        return list;
    }

    @Override
    public CreateExternalQuestionnaireRepDto createExternalQuestionnaire(CreateExternalQuestionnaireReqDto reqDto) {
        ConvertTempletReqDto templetReqDto = ExternalListConverter.INSTANCE.to(reqDto);

        ConvertTempletRepDto convertTempletRepDto = templetServer.convertTemplet(templetReqDto);

        CreateExternalQuestionnaireRepDto repDto = new CreateExternalQuestionnaireRepDto();
        repDto.setQuestionnaireId(convertTempletRepDto.getQuestionnaireId());

        return repDto;
    }

    @Override
    public TextCreateRepDto textCreate(Long userId, String text) {
        TextCreateRepDto result = new TextCreateRepDto();

        List<QstTitlePo> qstTitlePoList = new ArrayList<>();
        List<QstOptionPo> qstOptionPoList = new ArrayList<>();

        String questionnaireId = parseText(userId, text, qstTitlePoList, qstOptionPoList, false);

        qstTitlePoList.forEach(v -> qstTitleService.insert(v));
        qstOptionPoList.forEach(v -> qstOptionService.insert(v));

        result.setQuestionnaireId(questionnaireId);
        return result;
    }

    @Override
    public QuestionnaireQueryRepDto previewText(Long userId, String text) {
        QuestionnaireQueryRepDto result = new QuestionnaireQueryRepDto();

        List<QstTitlePo> qstTitlePoList = new ArrayList<>();
        List<QstOptionPo> qstOptionPoList = new ArrayList<>();


        QuestionnairePreviewTextBo bo = new QuestionnairePreviewTextBo();
        bo.setQuestionnaireInfoPo(JSONUtil.parseObject(questionnaireJson, QstQuestionnaireInfoPo.class));
        parseText(userId, text, qstTitlePoList, qstOptionPoList, true);

        bo.setTitlePoList(qstTitlePoList);
        bo.setOptionPoList(qstOptionPoList);
        QuestionnaireTo questionnaireTo = ConvertBeanUtil.convertQuestionnaireInfoPo2QuestionnaireInfoTo(bo.getQuestionnaireInfoPo());
        String title = text.substring(0, text.indexOf("【标题】"));
        questionnaireTo.setTitle(title);
        ListMultimap<Integer, QstOptionPo> optionListMultiMap = ArrayListMultimap.create();
        SafeUtil.of(bo.getOptionPoList()).forEach(v -> optionListMultiMap.put(v.getTitleSerialNumber(), v));

        List<Object> titleList = constructTitles(bo.getTitlePoList(), optionListMultiMap);
        questionnaireTo.setContent(titleList);

        result.setQuestionnairePojo(questionnaireTo);

        return result;
    }

    @Override
    public String getTitle(GetTitleReqDto reqDto) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setTableColumns("title");
        query.setUserId(reqDto.getUserId());
        query.setQuestionnaireId(reqDto.getQuestionnaireId());

        QstQuestionnaireInfoPo questionnaireInfoPo = qstQuestionnaireInfoService.selectOne(query);
        if (questionnaireInfoPo != null) {
            return questionnaireInfoPo.getTitle();
        }

        return null;
    }

    private void validateTitleAndOption(List<QstTitlePo> paramTitlePoList, List<QstOptionPo> paramOptionPoList, String questionnaireId, Long userId) {
        List<QstTitlePo> qstTitlePoList = qstTitleService.selectList(questionnaireId, userId);
        List<Integer> paramTitleSerialNumberList = paramTitlePoList.stream()
                .filter(v -> v.getSerialNumber() != null && v.getSerialNumber() > 0)
                .map(QstTitlePo::getSerialNumber)
                .collect(Collectors.toList());

        List<Integer> titleSerialNumberList = qstTitlePoList.stream()
                .filter(v -> v.getSerialNumber() != null && v.getSerialNumber() > 0)
                .map(QstTitlePo::getSerialNumber)
                .collect(Collectors.toList());

        Assert.isTrue(paramTitleSerialNumberList.containsAll(titleSerialNumberList), RETAIN_ANSWER_TITLE_LIMIT);

        List<QstOptionPo> qstOptionPoList = qstOptionService.selectList(questionnaireId, userId);
        Map<Integer, List<QstOptionPo>> qstOptionListMap = qstOptionPoList.stream().collect(Collectors.groupingBy(QstOptionPo::getTitleSerialNumber));
        Map<Integer, List<QstOptionPo>> paramQstOptionListMap = paramOptionPoList.stream().collect(Collectors.groupingBy(QstOptionPo::getTitleSerialNumber));

        titleSerialNumberList.forEach(p -> {
            List<String> optionKeyList = qstOptionListMap.get(p).stream().map(v -> constructOptionKey(v)).collect(Collectors.toList());
            List<String> paramOptionKeyList = paramQstOptionListMap.get(p).stream().map(v -> constructOptionKey(v)).collect(Collectors.toList());
            Assert.isTrue(optionKeyList.containsAll(paramOptionKeyList), RETAIN_ANSWER_OPTION_LIMIT);
        });
    }

    private String constructOptionKey(QstOptionPo v) {
        StringBuilder builder = new StringBuilder();
        builder.append(v.getTitleSerialNumber());
        if (v.getSerialNumber() == null) {
            builder.append(0);
        } else {
            builder.append(v.getSerialNumber());
        }

        if (v.getRowNumber() == null) {
            builder.append(0);
        } else {
            builder.append(v.getRowNumber());
        }

        if (v.getColumnNumber() == null) {
            builder.append(0);
        } else {
            builder.append(v.getColumnNumber());
        }

        if (v.getVerticalNumber() == null) {
            builder.append(0);
        } else {
            builder.append(v.getVerticalNumber());
        }

        return builder.toString();
    }

    private void deleteTitleAndOption(QuestionnaireCreateDetailReqDto reqDto) {
        int deleteResult = qstTitleService.delete(reqDto.getQuestionnaireId(), reqDto.getUserId());
        if (deleteResult == 0) {
            int count = qstTitleService.selectCount(reqDto.getQuestionnaireId(), reqDto.getUserId());
            Assert.isTrue(count == 0, CodeEnum.UPDATE_EXCEPTION);
        }
        int deleteOptionResult = qstOptionService.delete(reqDto.getQuestionnaireId(), reqDto.getUserId());
        if (deleteOptionResult == 0) {
            int count = qstOptionService.selectCount(reqDto.getQuestionnaireId(), reqDto.getUserId());
            Assert.isTrue(count == 0, CodeEnum.UPDATE_EXCEPTION);
        }
    }

    private void validateAnswerDelete(QuestionnaireCreateDetailReqDto reqDto) {
        int result = answerInfoService.selectCount(reqDto.getUserId(), reqDto.getQuestionnaireId());
        if (result > 0) {
            AnswerInfoQuery query = new AnswerInfoQuery();
            query.setUserId(reqDto.getUserId());
            query.setQuestionnaireId(reqDto.getQuestionnaireId());
            int deleteResult = answerInfoService.delete(query);
            Assert.isTrue(deleteResult > 0, CodeEnum.UPDATE_EXCEPTION);

            AnswerOptionQuery optionQuery = new AnswerOptionQuery();
            optionQuery.setQuestionnaireId(reqDto.getQuestionnaireId());
            optionQuery.setUserId(reqDto.getUserId());
            answerOptionService.delete(optionQuery);
            Assert.isTrue(deleteResult > 0, CodeEnum.UPDATE_EXCEPTION);
        }
    }

    private void constructTitleAndOption(JSONObject object, QuestionnaireCreateDetailReqDto reqDto, List<QstTitlePo> titlePoList, List<QstOptionPo> optionPoList) {
        String type = object.getString("type");
        switch (TitleTypeEnum.toEnum(type)) {
            case DANXUAN:
            case XINGBIE:
            case NIANLIANG:
            case GONGZUONIANXIN:
            case JIAOYUCHENGDU:
            case HUNYINZHUANGKUANG:
                DanXuanTitleTo danXuanTitleTo = JSON.toJavaObject(object, DanXuanTitleTo.class);
                QstTitlePo qstTitlePo = ConvertBeanUtil.convertBaseXuanZeTitleTo2TitlePo(danXuanTitleTo);
                qstTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                qstTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(qstTitlePo);
                constructDanXuanOptionList(danXuanTitleTo, qstTitlePo, optionPoList, reqDto);
                break;
            case DUOXUAN:
            case PAIXU:
                DuoXuanTitleTo duoXuanTitleTo = JSON.toJavaObject(object, DuoXuanTitleTo.class);
                QstTitlePo duoXuanTitlePo = ConvertBeanUtil.convertDuoXuanTitleTo2TitlePo(duoXuanTitleTo);
                duoXuanTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                duoXuanTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(duoXuanTitlePo);
                constructDuoXuanOptionList(duoXuanTitleTo, duoXuanTitlePo, optionPoList, reqDto);
                break;
            case BIAOGEDANXUAN:
                BiaoGeDanXuanTitleTo biaoGeDanXuanTitleTo = JSON.toJavaObject(object, BiaoGeDanXuanTitleTo.class);
                QstTitlePo biaoGeDanXuanTitlePo = ConvertBeanUtil.convertBiaoGeDanXuanTitleTo2TitlePo(biaoGeDanXuanTitleTo);
                biaoGeDanXuanTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                biaoGeDanXuanTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(biaoGeDanXuanTitlePo);
                constructBiaoGeDanXuanOptionList(biaoGeDanXuanTitleTo, biaoGeDanXuanTitlePo, optionPoList, reqDto);
                break;
            case BIAOGEDUOXUAN:
                BiaoGeDuoXuanTitleTo biaoGeDuoXuanTitleTo = JSON.toJavaObject(object, BiaoGeDuoXuanTitleTo.class);
                QstTitlePo biaoGeDuoXuanTitlePo = ConvertBeanUtil.convertBiaoGeDuoXuanTitleTo2TitlePo(biaoGeDuoXuanTitleTo);
                biaoGeDuoXuanTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                biaoGeDuoXuanTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(biaoGeDuoXuanTitlePo);
                constructBiaoGeDuoXuanOptionList(biaoGeDuoXuanTitleTo, biaoGeDuoXuanTitlePo, optionPoList, reqDto);
                break;
            case LIANGBIAO:
                LiangBiaoTitleTo liangBiaoTitleTo = JSON.toJavaObject(object, LiangBiaoTitleTo.class);
                QstTitlePo liangBiaoTitlePo = ConvertBeanUtil.convertLiangBiaoTitleTo2TitlePo(liangBiaoTitleTo);
                liangBiaoTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                liangBiaoTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(liangBiaoTitlePo);
                constructLiangBiaoOptionList(liangBiaoTitleTo, liangBiaoTitlePo, optionPoList, reqDto);
                break;
            case BIAOGEXIALAXUANZE:
                BiaoGeXiaLaXuanZeTitleTo biaoGeXiaLaXuanZeTitleTo = JSON.toJavaObject(object, BiaoGeXiaLaXuanZeTitleTo.class);
                QstTitlePo biaoGeXiaLaTitlePo = ConvertBeanUtil.convertBiaoGeXiaLaXuanZeTitleTo2TitlePo(biaoGeXiaLaXuanZeTitleTo);
                biaoGeXiaLaTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                biaoGeXiaLaTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(biaoGeXiaLaTitlePo);
                constructBiaoGeXiaLaOptionList(biaoGeXiaLaXuanZeTitleTo, biaoGeXiaLaTitlePo, optionPoList, reqDto);
                break;
            case DUOXIANGDANHANGTIANKONG:
                DuoXiangDanHangTianKongTitleTo duoXiangDanHangTianKongTitleTo = JSON.toJavaObject(object, DuoXiangDanHangTianKongTitleTo.class);
                QstTitlePo duoXiangDanHangTianKongTitlePo = ConvertBeanUtil.convertDuoXiangDanHangTianKongTitleTo2TitlePo(duoXiangDanHangTianKongTitleTo);
                duoXiangDanHangTianKongTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                duoXiangDanHangTianKongTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(duoXiangDanHangTianKongTitlePo);
                constructDuoXiangDanHangTianKongOptionList(duoXiangDanHangTianKongTitleTo, duoXiangDanHangTianKongTitlePo, optionPoList, reqDto);
                break;
            case XIALAXUANZE:
            case ZHIYE:
            case BIYEYUANXIAO:
            case CHENGSHIDIZHI:
                XiaLaXuanZeTitleTo xiaLaXuanZeTitleTo = JSON.toJavaObject(object, XiaLaXuanZeTitleTo.class);
                QstTitlePo xiaLaXuanZeTitlePo = ConvertBeanUtil.convertBaseTitleTo2TitlePo(xiaLaXuanZeTitleTo);
                xiaLaXuanZeTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                xiaLaXuanZeTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(xiaLaXuanZeTitlePo);
                constructXiaLaXuanZeOptionList(xiaLaXuanZeTitleTo, xiaLaXuanZeTitlePo, optionPoList, reqDto);
                break;
            case DANHANGTIANKONG:
            case XINGMING:
            case SHOUJI:
            case YOUXIANG:
            case TONGXUNDIZHI:
                DanHangTianKongTitleTo danHangTianKongTitleTo = JSON.toJavaObject(object, DanHangTianKongTitleTo.class);
                QstTitlePo danHangTianKongTitlePo = ConvertBeanUtil.convertDanHangTianKongTianKongTitleTo2TitlePo(danHangTianKongTitleTo);
                danHangTianKongTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                danHangTianKongTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(danHangTianKongTitlePo);
                constructDanHangTianKongOptionList(reqDto.getQuestionnaireId(), danHangTianKongTitlePo.getSerialNumber(), optionPoList, reqDto);
                break;
            case BIAOGESHUZHI:
                BiaoGeShuZhiTitleTo biaoGeShuZhiTitleTo = JSON.toJavaObject(object, BiaoGeShuZhiTitleTo.class);
                QstTitlePo biaoGeShuZhiTitlePo = ConvertBeanUtil.convertBiaoGeShuZhiTitleTo2TitlePo(biaoGeShuZhiTitleTo);
                biaoGeShuZhiTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                biaoGeShuZhiTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(biaoGeShuZhiTitlePo);
                constructBiaoGeShuZhiOptionList(biaoGeShuZhiTitleTo, biaoGeShuZhiTitlePo, optionPoList, reqDto);
                break;
            case BIAOGEWENBEN:
                BiaoGeWenBenTitleTo biaoGeWenBenTitleTo = JSON.toJavaObject(object, BiaoGeWenBenTitleTo.class);
                QstTitlePo biaoGeWenBenTitlePo = ConvertBeanUtil.convertBiaoGeShuZhiTitleTo2TitlePo(biaoGeWenBenTitleTo);
                biaoGeWenBenTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                biaoGeWenBenTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(biaoGeWenBenTitlePo);
                constructBiaoGeWenBenOptionList(biaoGeWenBenTitleTo, biaoGeWenBenTitlePo, optionPoList, reqDto);
                break;
            case MIAOSHUSHUOMING:
                BaseTitleTo miaoShuShuoMingTitleTo = JSON.toJavaObject(object, BaseTitleTo.class);
                QstTitlePo miaoShuShuoMingTitlePo = ConvertBeanUtil.convertBaseTitleTo2TitlePo(miaoShuShuoMingTitleTo);
                miaoShuShuoMingTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                miaoShuShuoMingTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(miaoShuShuoMingTitlePo);
                break;
            case HUADONGTIAO:
                HuaDongTiaoTitleTo huaDongTiaoTitleTo = JSON.toJavaObject(object, HuaDongTiaoTitleTo.class);
                QstTitlePo huaDongTiaoTitlePo = ConvertBeanUtil.convertBaseTitleTo2TitlePo(huaDongTiaoTitleTo);
                huaDongTiaoTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                huaDongTiaoTitlePo.setUserId(reqDto.getUserId());
                huaDongTiaoTitlePo.setMinBoundsValue(huaDongTiaoTitleTo.getMinValue());
                huaDongTiaoTitlePo.setMaxBoundsValue(huaDongTiaoTitleTo.getMaxValue());
                titlePoList.add(huaDongTiaoTitlePo);
                constructHuaDongTiaoOptionList(huaDongTiaoTitleTo, huaDongTiaoTitlePo, optionPoList, reqDto);
                break;
            case JUZHENHUADONGTIAO:
                JuZhenHuaDongTiaoTitleTo juZhenHuaDongTiaoTitleTo = JSON.toJavaObject(object, JuZhenHuaDongTiaoTitleTo.class);
                QstTitlePo juZhenHuaDongTiaoTitlePo = ConvertBeanUtil.convertBaseTitleTo2TitlePo(juZhenHuaDongTiaoTitleTo);
                juZhenHuaDongTiaoTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                juZhenHuaDongTiaoTitlePo.setUserId(reqDto.getUserId());
                juZhenHuaDongTiaoTitlePo.setMinBoundsValue(juZhenHuaDongTiaoTitleTo.getMinValue());
                juZhenHuaDongTiaoTitlePo.setMaxBoundsValue(juZhenHuaDongTiaoTitleTo.getMaxValue());
                titlePoList.add(juZhenHuaDongTiaoTitlePo);
                constructJuZhenHuaDongTiaoOptionList(juZhenHuaDongTiaoTitleTo, juZhenHuaDongTiaoTitlePo, optionPoList, reqDto);
                break;
            case BIZHONG:
                BiZhongTitleTo biZhongTitleTo = JSON.toJavaObject(object, BiZhongTitleTo.class);
                QstTitlePo biZhongTitlePo = ConvertBeanUtil.convertBiZhongTitleTo2TitlePo(biZhongTitleTo);
                biZhongTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                biZhongTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(biZhongTitlePo);
                constructBiZhongOptionList(biZhongTitleTo, biZhongTitlePo, optionPoList, reqDto);
                break;
            case PINGFENDANXUAN:
                PingFenDanXuanTitleTo pingFenDanXuanTitleTo = JSON.toJavaObject(object, PingFenDanXuanTitleTo.class);
                QstTitlePo pingFenDanXuanTitlePo = ConvertBeanUtil.convertBaseXuanZeTitleTo2TitlePo(pingFenDanXuanTitleTo);
                pingFenDanXuanTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                pingFenDanXuanTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(pingFenDanXuanTitlePo);
                constructPingFenDanXuanOptionList(pingFenDanXuanTitleTo, pingFenDanXuanTitlePo, optionPoList, reqDto);
                break;
            case PINGFENDUOXUAN:
                PingFenDuoXuanTitleTo pingFenDuoXuanTitleTo = JSON.toJavaObject(object, PingFenDuoXuanTitleTo.class);
                QstTitlePo pingFenDuoXuanTitlePo = ConvertBeanUtil.convertPingFenDuoXuanTitleTo2TitlePo(pingFenDuoXuanTitleTo);
                pingFenDuoXuanTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                pingFenDuoXuanTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(pingFenDuoXuanTitlePo);
                constructPingFenDuoXuanOptionList(pingFenDuoXuanTitleTo, pingFenDuoXuanTitlePo, optionPoList, reqDto);
                break;
            case FENYEFU:
                PageTitleTo pageTitleTo = JSON.toJavaObject(object, PageTitleTo.class);
                QstTitlePo pageQstTitle = ConvertBeanUtil.convertBaseTitleTo2TitlePo(pageTitleTo);
                pageQstTitle.setQuestionnaireId(reqDto.getQuestionnaireId());
                pageQstTitle.setUserId(reqDto.getUserId());
                pageQstTitle.setPageCount(pageTitleTo.getPageCount());
                pageQstTitle.setPageNo(pageTitleTo.getPageNo());
                titlePoList.add(pageQstTitle);
                break;
            case WENJIAN:
                WenJianTitleTo wenJianTitleTo = JSON.toJavaObject(object, WenJianTitleTo.class);
                QstTitlePo wenJianTitlePo = convertBaseTitleTo2TitlePo(wenJianTitleTo);
                wenJianTitlePo.setQuestionnaireId(reqDto.getQuestionnaireId());
                wenJianTitlePo.setUserId(reqDto.getUserId());
                titlePoList.add(wenJianTitlePo);
                constructWenJianOptionList(reqDto.getQuestionnaireId(), wenJianTitlePo.getSerialNumber(), optionPoList, reqDto);
                break;
        }
    }

    private void constructBiZhongOptionList(BiZhongTitleTo biZhongTitleTo, QstTitlePo biZhongTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        String[] rowTitles = biZhongTitleTo.getRowTitle().split(TITLE_SPLIT_FLAG);

        for (int i = 0, rowTitleCount = rowTitles.length; i < rowTitleCount; i++) {
            QstOptionPo po = new QstOptionPo();
            po.setUserId(reqDto.getUserId());
            po.setQuestionnaireId(reqDto.getQuestionnaireId());
            po.setTitleSerialNumber(biZhongTitlePo.getSerialNumber());
            po.setSerialNumber(i + 1);
            po.setRowTitle(rowTitles[i]);
            optionPoList.add(po);
        }
    }

    private void constructJuZhenHuaDongTiaoOptionList(JuZhenHuaDongTiaoTitleTo juZhenHuaDongTiaoTitleTo, QstTitlePo juZhenHuaDongTiaoTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        String[] rowTitles = juZhenHuaDongTiaoTitleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        String[] rowDescs = SafeUtil.of(juZhenHuaDongTiaoTitleTo.getRowDescription()).split(TITLE_SPLIT_FLAG);
        int rowDescLength = rowDescs.length;

        for (int i = 0, rowTitleCount = rowTitles.length; i < rowTitleCount; i++) {
            QstOptionPo po = new QstOptionPo();
            po.setUserId(reqDto.getUserId());
            po.setQuestionnaireId(reqDto.getQuestionnaireId());
            po.setTitleSerialNumber(juZhenHuaDongTiaoTitlePo.getSerialNumber());
            po.setSerialNumber(i + 1);
            po.setRowTitle(rowTitles[i]);
            if (i < rowDescLength) {
                po.setRowDes(rowDescs[i]);
            }
            optionPoList.add(po);
        }
    }

    private void constructHuaDongTiaoOptionList(HuaDongTiaoTitleTo huaDongTiaoTitleTo, QstTitlePo huaDongTiaoTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        QstOptionPo po = new QstOptionPo();
        po.setQuestionnaireId(reqDto.getQuestionnaireId());
        po.setSerialNumber(1);
        po.setTitleSerialNumber(huaDongTiaoTitlePo.getSerialNumber());
        po.setGapFillingType(YnEnum.N.key());
        po.setUserId(reqDto.getUserId());
        po.setRowTitle(huaDongTiaoTitleTo.getMinTitle());
        po.setColumnTitle(huaDongTiaoTitleTo.getMaxTitle());
        optionPoList.add(po);
    }

    private void constructDanHangTianKongOptionList(String questionnaireId, Integer titleSerialNumber, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        QstOptionPo po = new QstOptionPo();
        po.setQuestionnaireId(questionnaireId);
        po.setSerialNumber(1);
        po.setTitleSerialNumber(titleSerialNumber);
        po.setGapFillingType(YnEnum.Y.key());
        po.setUserId(reqDto.getUserId());
        optionPoList.add(po);
    }

    private void constructWenJianOptionList(String questionnaireId, Integer titleSerialNumber, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        QstOptionPo po = new QstOptionPo();
        po.setQuestionnaireId(questionnaireId);
        po.setSerialNumber(1);
        po.setTitleSerialNumber(titleSerialNumber);
        po.setGapFillingType(YnEnum.N.key());
        po.setUserId(reqDto.getUserId());
        optionPoList.add(po);
    }

    private void constructXiaLaXuanZeOptionList(XiaLaXuanZeTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        titleTo.getOptions().forEach(v -> {
            QstOptionPo po = constructXiaLaXuanZeOption(qstTitlePo, reqDto, v);
            optionPoList.add(po);
        });
    }

    private QstOptionPo constructXiaLaXuanZeOption(QstTitlePo qstTitlePo, QuestionnaireCreateDetailReqDto reqDto, BaseOptionTo optionTo) {
        QstOptionPo po = convertBaseOptionTo2OptionPo(optionTo);
        po.setUserId(reqDto.getUserId());
        po.setQuestionnaireId(reqDto.getQuestionnaireId());
        po.setTitleSerialNumber(qstTitlePo.getSerialNumber());
        return po;
    }

    private void constructLiangBiaoOptionList(LiangBiaoTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        titleTo.getOptions().forEach(v -> {
            QstOptionPo po = constructLiangBiaoOption(qstTitlePo, reqDto, v);
            optionPoList.add(po);
        });
    }

    private QstOptionPo constructLiangBiaoOption(QstTitlePo qstTitlePo, QuestionnaireCreateDetailReqDto reqDto, LiangBiaoOptionTo optionTo) {
        QstOptionPo po = convertBaseOptionTo2OptionPo(optionTo);
        po.setScore(optionTo.getScore());

        if (optionTo.getNoScore() == null || optionTo.getNoScore()) {
            po.setScoreType(YnEnum.N.key());
        } else {
            po.setScoreType(YnEnum.Y.key());
        }
        po.setUserId(qstTitlePo.getUserId());
        po.setQuestionnaireId(qstTitlePo.getQuestionnaireId());
        po.setTitleSerialNumber(qstTitlePo.getSerialNumber());

        return po;
    }

    private void constructDanXuanOptionList(DanXuanTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        for (Object obj : titleTo.getOptions()) {
            DanXuanOptionTo optionTo = null;
            if (obj instanceof JSONObject) {
                optionTo = JSON.toJavaObject((JSON) obj, DanXuanOptionTo.class);
            } else {
                optionTo = (DanXuanOptionTo) obj;
            }
            QstOptionPo po = constructBaseXuanZeOption(titleTo.getJumpOptions(), qstTitlePo, reqDto, optionTo);
            optionPoList.add(po);
        }
    }

    private void constructPingFenDanXuanOptionList(PingFenDanXuanTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        for (Object obj : titleTo.getOptions()) {
            PingFenDanXuanOptionTo optionTo = null;
            if (obj instanceof JSONObject) {
                optionTo = JSON.toJavaObject((JSON) obj, PingFenDanXuanOptionTo.class);
            } else {
                optionTo = (PingFenDanXuanOptionTo) obj;
            }
            QstOptionPo po = constructBaseXuanZeOption(titleTo.getJumpOptions(), qstTitlePo, reqDto, optionTo);

            Assert.notNull(optionTo.getNoScore());
            if (optionTo.getNoScore()) {
                po.setScoreType(YnEnum.N.key());
            } else {
                po.setScoreType(YnEnum.Y.key());
            }
            po.setScore(optionTo.getScore());
            optionPoList.add(po);
        }
    }

    private void constructDuoXuanOptionList(DuoXuanTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        for (Object obj : titleTo.getOptions()) {
            DuoXuanOptionTo optionTo = null;
            if (obj instanceof JSONObject) {
                optionTo = JSON.toJavaObject((JSON) obj, DuoXuanOptionTo.class);
            } else {
                optionTo = (DuoXuanOptionTo) obj;
            }
            QstOptionPo po = constructBaseXuanZeOption(titleTo.getJumpOptions(), qstTitlePo, reqDto, optionTo);
            po.setCanMutex(CanMutexEnum.convert2Enum(optionTo.getCanMutex()).key());

            optionPoList.add(po);
        }
    }

    private void constructPingFenDuoXuanOptionList(PingFenDuoXuanTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        for (Object obj : titleTo.getOptions()) {
            PingFenDuoXuanOptionTo optionTo = null;
            if (obj instanceof JSONObject) {
                optionTo = JSON.toJavaObject((JSON) obj, PingFenDuoXuanOptionTo.class);
            } else {
                optionTo = (PingFenDuoXuanOptionTo) obj;
            }
            QstOptionPo po = constructBaseXuanZeOption(titleTo.getJumpOptions(), qstTitlePo, reqDto, optionTo);

            po.setScore(optionTo.getScore());
            Assert.notNull(optionTo.getNoScore());
            if (optionTo.getNoScore()) {
                po.setScoreType(YnEnum.N.key());
            } else {
                po.setScoreType(YnEnum.Y.key());
            }

            optionPoList.add(po);
        }
    }

    private void constructBiaoGeDanXuanOptionList(BiaoGeDanXuanTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        String[] rowTitles = titleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        AtomicInteger row_num = new AtomicInteger(1);

        for (String rowTile : rowTitles) {
            AtomicInteger column_num = new AtomicInteger(1);
            for (Object obj : titleTo.getOptions()) {
                BiaoGeDanXuanOptionTo optionTo = null;
                if (obj instanceof JSONObject) {
                    optionTo = JSON.toJavaObject((JSON) obj, BiaoGeDanXuanOptionTo.class);
                } else {
                    optionTo = (BiaoGeDanXuanOptionTo) obj;
                }
                QstOptionPo po = constructBaseXuanZeOption(titleTo.getJumpOptions(), qstTitlePo, reqDto, optionTo);
                po.setRowTitle(rowTile);
                po.setRowNumber(row_num.get());
                po.setColumnNumber(column_num.get());
                optionPoList.add(po);
                column_num.getAndIncrement();
            }
            row_num.getAndIncrement();
        }
    }

    private void constructBiaoGeDuoXuanOptionList(BiaoGeDuoXuanTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        String[] rowTitles = titleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        AtomicInteger row_num = new AtomicInteger(1);

        for (String rowTile : rowTitles) {
            AtomicInteger column_num = new AtomicInteger(1);
            for (Object obj : titleTo.getOptions()) {
                BiaoGeDuoXuanOptionTo optionTo = null;
                if (obj instanceof JSONObject) {
                    optionTo = JSON.toJavaObject((JSON) obj, BiaoGeDuoXuanOptionTo.class);
                } else {
                    optionTo = (BiaoGeDuoXuanOptionTo) obj;
                }
                QstOptionPo po = constructBaseXuanZeOption(titleTo.getJumpOptions(), qstTitlePo, reqDto, optionTo);
                po.setRowTitle(rowTile);
                po.setColumnNumber(column_num.get());
                po.setRowNumber(row_num.get());
                optionPoList.add(po);
                column_num.getAndIncrement();
            }
            row_num.getAndIncrement();
        }
    }

    private void constructBiaoGeXiaLaOptionList(BiaoGeXiaLaXuanZeTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        String[] columnTitles = titleTo.getColTitle().split(TITLE_SPLIT_FLAG);
        String[] rowTitles = titleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        AtomicInteger row_num = new AtomicInteger(1);

        for (String rowTile : rowTitles) {
            AtomicInteger column_num = new AtomicInteger(1);
            for (String columnTitle : columnTitles) {
                AtomicInteger vertical_num = new AtomicInteger(1);
                titleTo.getOptions().forEach(v -> {
                    QstOptionPo po = constructBaseXuanZeOption(qstTitlePo, reqDto, v);
                    po.setRowTitle(rowTile);
                    po.setRowNumber(row_num.get());
                    po.setColumnTitle(columnTitle);
                    po.setColumnNumber(column_num.get());
                    po.setVerticalNumber(vertical_num.get());
                    optionPoList.add(po);
                    vertical_num.getAndIncrement();
                });
                column_num.getAndIncrement();
            }
            row_num.getAndIncrement();
        }
    }

    private void constructBiaoGeShuZhiOptionList(BiaoGeShuZhiTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        String[] columnTitles = titleTo.getColTitle().split(TITLE_SPLIT_FLAG);
        String[] rowTitles = titleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        AtomicInteger row_num = new AtomicInteger(1);

        int i = 1;
        for (String rowTile : rowTitles) {
            AtomicInteger column_num = new AtomicInteger(1);
            for (String columnTitle : columnTitles) {
                BiaoGeShuZhiOptionTo option = new BiaoGeShuZhiOptionTo();
                option.setNum(i);
                option.setOrder(i);

                QstOptionPo po = constructBiaoGeShuZhiOption(qstTitlePo, reqDto, option);
                po.setRowTitle(rowTile);
                po.setRowNumber(row_num.get());
                po.setColumnNumber(column_num.get());
                po.setColumnTitle(columnTitle);
                optionPoList.add(po);
                column_num.getAndIncrement();
                i++;
            }
            row_num.getAndIncrement();
        }
    }

    private void constructBiaoGeWenBenOptionList(BiaoGeWenBenTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {
        String[] columnTitles = titleTo.getColTitle().split(TITLE_SPLIT_FLAG);
        String[] rowTitles = titleTo.getRowTitle().split(TITLE_SPLIT_FLAG);
        AtomicInteger row_num = new AtomicInteger(1);

        int i = 1;
        for (String rowTile : rowTitles) {
            AtomicInteger column_num = new AtomicInteger(1);
            for (String columnTitle : columnTitles) {
                BiaoGeShuZhiOptionTo option = new BiaoGeShuZhiOptionTo();
                option.setNum(i);
                option.setOrder(i);

                QstOptionPo po = constructBiaoGeShuZhiOption(qstTitlePo, reqDto, option);
                po.setRowTitle(rowTile);
                po.setColumnNumber(column_num.get());
                po.setRowNumber(row_num.get());
                po.setColumnTitle(columnTitle);
                optionPoList.add(po);
                column_num.getAndIncrement();
                i++;
            }
            row_num.getAndIncrement();
        }
    }

    private void constructDuoXiangDanHangTianKongOptionList(DuoXiangDanHangTianKongTitleTo titleTo, QstTitlePo qstTitlePo, List<QstOptionPo> optionPoList, QuestionnaireCreateDetailReqDto reqDto) {

        AtomicInteger row_num = new AtomicInteger(1);
        titleTo.getOptions().forEach(v -> {
            QstOptionPo po = constructBaseXuanZeOption(qstTitlePo, reqDto, v);
            po.setRowTitle(v.getText());
            po.setRowDes(v.getDescription());
            po.setRowNumber(row_num.get());
            optionPoList.add(po);
            row_num.getAndIncrement();
        });
    }

    private QstOptionPo constructBaseXuanZeOption(Map<String, Integer> jumpOptions, QstTitlePo qstTitlePo, QuestionnaireCreateDetailReqDto reqDto, BaseXuanZeOptionTo v) {
        QstOptionPo po = constructBaseXuanZeOption(qstTitlePo, reqDto, v);
        po.setSkipNum(SafeUtil.of(jumpOptions).get(po.getSerialNumber().toString()));
        return po;
    }

    private QstOptionPo constructBaseXuanZeOption(QstTitlePo qstTitlePo, QuestionnaireCreateDetailReqDto reqDto, BaseXuanZeOptionTo v) {
        QstOptionPo po = ConvertBeanUtil.convertXuanZeOptionTo2OptionPo(v);
        po.setUserId(reqDto.getUserId());
        po.setQuestionnaireId(reqDto.getQuestionnaireId());
        po.setTitleSerialNumber(qstTitlePo.getSerialNumber());

        return po;
    }

    private QstOptionPo constructBiaoGeShuZhiOption(QstTitlePo qstTitlePo, QuestionnaireCreateDetailReqDto reqDto, BaseOptionTo v) {
        QstOptionPo po = convertBaseOptionTo2OptionPo(v);
        po.setUserId(reqDto.getUserId());
        po.setQuestionnaireId(reqDto.getQuestionnaireId());
        po.setTitleSerialNumber(qstTitlePo.getSerialNumber());
        return po;
    }

    private void validateUpdateQuestionnaireAndStatus(QstQuestionnaireInfoQuery query) {

        if (StringUtil.isNotBlank(query.getDes())) {
            int length = query.getDes().length();
            Assert.isTrue(length <= TITLE_LIMIT, QST_SHUOMING_LIMIT, TITLE_LIMIT + "，当前长度为：" + length);
        }

//        QstQuestionnaireInfoQuery infoQuery = new QstQuestionnaireInfoQuery();
//        infoQuery.setUserId(query.getUserId());
//        infoQuery.setQuestionnaireId(query.getQuestionnaireId());
        QstQuestionnaireInfoPo po = qstQuestionnaireInfoService.selectOne(query.getQuestionnaireId());

        boolean titleFlag = query.getTitle().equals(po.getTitle());
        boolean decFlag = (StringUtil.isNotBlank(query.getDes())
                && StringUtil.isNotBlank(po.getDes())
                && query.getDes().equals(po.getDes()))
                || (StringUtil.isBlank(query.getDes())
                && StringUtil.isBlank(po.getDes()));
        if (titleFlag && decFlag) {
            return;
        }
        if (query.getDes() == null) {
            query.setDes("");
        }
        int result = qstQuestionnaireInfoService.update(query);
        Assert.isTrue(result == ONE, CodeEnum.UPDATE_EXCEPTION);
        Assert.isTrue(QuestionnaireStatusEnum.isUnpublished(po.getStatus()), CodeEnum.STATUS_EXCEPTION);
    }

    private QstQuestionnaireInfoQuery constructQuestionnaireByJson(JSONObject jsonObject, Long userId) {
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setQuestionnaireId(jsonObject.getString("id"));
        query.setTitle(jsonObject.getString("title"));
        query.setDes(jsonObject.getString("des"));
        query.setUserId(userId);
        return query;
    }

    private boolean selectTitleOption(QuestionnaireTo questionnaireTo, String questionnaireId, Long userId, Integer pageNo, Boolean editFlag) {
        List<QstTitlePo> titlePoList = qstTitleService.selectList(questionnaireId, userId);
        if (CollectionUtils.isEmpty(titlePoList)) {
            return true;
        }
        if (editFlag == null || !editFlag) {
            QstTitlePo qstTitlePo = titlePoList.stream().filter(v -> v.getPageNo() > 0).findAny().orElse(null);
            if (qstTitlePo != null) {
                if (pageNo == null || pageNo == 0) {
                    questionnaireTo.setPageNo(1);
                } else if (pageNo >= qstTitlePo.getPageCount()) {
                    questionnaireTo.setPageNo(qstTitlePo.getPageCount());
                } else {
                    questionnaireTo.setPageNo(pageNo);
                }
                questionnaireTo.setPageCount(qstTitlePo.getPageCount());
            }
        }

        List<QstOptionPo> optionPoList = qstOptionService.selectList(questionnaireId, userId);
//        if (CollectionUtils.isEmpty(optionPoList)) {
//            if (titlePoList.stream().map(v -> toEnum(v.getType())).anyMatch(titleTypeEnum -> !NO_OPTION_LIST.contains(titleTypeEnum))) {
//                return false;
//            }
//            return true;
//        }

        ListMultimap<Integer, QstOptionPo> optionListMultiMap = ArrayListMultimap.create();
        SafeUtil.of(optionPoList).forEach(v -> optionListMultiMap.put(v.getTitleSerialNumber(), v));

        List<Object> titleList = constructTitles(titlePoList, optionListMultiMap);
        questionnaireTo.setContent(titleList);

        return false;
    }

    private void updateEmailOrSms(QuestionnaireQueryReqDto reqDto) {
        if (StringUtil.isNotBlank(reqDto.getEmailId())) {
            qstEmailService.updateStatus2Open(reqDto.getEmailId());
        } else if (StringUtil.isNotBlank(reqDto.getSmsId())) {
            qstSmsService.updateStatus2Open(reqDto.getSmsId());
        }
    }

    private void validateTitleLimit(List<QstTitlePo> titlePoList) {
        SafeUtil.of(titlePoList).forEach(v -> {
            Assert.notNull(v.getName(), TITLE_NAME_NOT_NULL);
            int length = v.getName().length();
            if (TitleTypeEnum.MIAOSHUSHUOMING.key().intValue() == v.getType()) {
                Assert.isTrue(length <= TITLE_LIMIT, MIAO_SHU_OUT_LIMIT, TITLE_LIMIT + ", 当前长度为：" + length);
            } else {
                Assert.isTrue(length <= TITLE_LIMIT, TITLE_NAME_OUT_LIMIT, TITLE_LIMIT + ", 当前长度为：" + length);
            }
        });
    }

    private static TitleTypeEnum contains(String tempStr) {
        for (String s : listManualTitle().keySet()) {
            if (tempStr.contains(s)) {
                return listManualTitle().get(s);
            }
        }

        return null;
    }

    public String createQuestionnaire(Long userId, String title) {
        QuestionnaireCreateReqDto reqDto = new QuestionnaireCreateReqDto();
        reqDto.setTitle(title);
        reqDto.setUserId(userId);
        QuestionnaireCreateRepDto questionnaireCreateRepDto = create(reqDto);
        String questionnaireId = questionnaireCreateRepDto.getQuestionnaireId();
        return questionnaireId;
    }

    public String parseText(Long userId, String text, List<QstTitlePo> qstTitlePoList, List<QstOptionPo> qstOptionPoList, boolean previewFlag) {
        List<String> splitMarkList = Arrays.asList("\\r\\n", "\\\\r\\\\n", "\\\\n", "\\n");
        String[] strGroup = {};
        for (String mark : splitMarkList) {
            strGroup = text.split(mark);
            if (strGroup.length > 1) {
                break;
            }
        }

        List<QstTitlePo> titlePoList = JSONUtil.parseArray(titleJson, QstTitlePo.class);
        List<QstOptionPo> optionPoList = JSONUtil.parseArray(optionJson, QstOptionPo.class);

        Map<String, List<String>> titleMap = new LinkedHashMap<>();
        Map<Integer, QstTitlePo> titlePoMap = titlePoList.stream().collect(Collectors.toMap(QstTitlePo::getType, v -> v));
        Map<Integer, List<QstOptionPo>> optionPoMap = optionPoList.stream().collect(Collectors.groupingBy(QstOptionPo::getTitleSerialNumber));
        List<String> tempList = null;
        String title = "";
        for (int i = 0; i < strGroup.length; i++) {
            String tempStr = strGroup[i];

            if (i == 0) {
                if (!tempStr.contains(TITLE_FLAG)) {
                    throw new RuntimeException();
                } else {
                    title = tempStr.replace(TITLE_FLAG, "");
                    continue;
                }
            }

            if (StringUtil.isBlank(tempStr)) {
                continue;
            }

            TitleTypeEnum titleTypeEnum = contains(tempStr);
            if (i == 1 && titleTypeEnum == null) {
                throw new RuntimeException();
            }

            if (titleTypeEnum != null) {
                tempList = new ArrayList<>();
                titleMap.put(titleTypeEnum.key() + "-" + i, tempList);
                tempStr = tempStr.replace(constructChineseBracket(titleTypeEnum), "");
            }

            tempList.add(tempStr);
        }

        String questionnaireId = "";
        if (!previewFlag) {
            questionnaireId = createQuestionnaire(userId, title);
        } else {
            questionnaireId = SequenceUtil.getInstance().generateQuestionnaireId(String.valueOf(userId));
        }
        AtomicInteger i = new AtomicInteger(1);
        String finalQuestionnaireId = questionnaireId;
        titleMap.forEach((k, v) -> {
            Integer key = Integer.valueOf(k.split("-")[0]);
            QstTitlePo qstTitlePo1 = titlePoMap.get(key);
            QstTitlePo qstTitlePo = TextCreateConverter.INSTANCE.to(qstTitlePo1);
            Integer templetSerialNumber = qstTitlePo.getSerialNumber();
            qstTitlePo.setQuestionnaireId(finalQuestionnaireId);
            qstTitlePo.setUserId(userId);
            qstTitlePo.setType(key);
            qstTitlePo.setId(null);
            qstTitlePo.setSerialNumber(i.get());
            qstTitlePo.setName(v.get(0));
            qstTitlePo.setOrderNumber(i.get());
            qstTitlePo.setGlobalOrder(i.get());
            qstTitlePoList.add(qstTitlePo);

            List<QstOptionPo> qstOptionPoTempList = optionPoMap.get(templetSerialNumber);
            if (isDanXuan(key) || isDuoXuan(key) || isLiangBiao(key)) {
                QstOptionPo qstOptionPoTemp = qstOptionPoTempList.get(0);
                for (int j = 1, l = v.size(); j < l; j++) {
                    QstOptionPo qstOptionPo = TextCreateConverter.INSTANCE.to(qstOptionPoTemp);
                    qstOptionPo.setQuestionnaireId(finalQuestionnaireId);
                    qstOptionPo.setTitleSerialNumber(i.get());
                    qstOptionPo.setContent(v.get(j));
                    qstOptionPo.setId(null);
                    qstOptionPo.setOrderNumber(j);
                    qstOptionPo.setSerialNumber(j);
                    qstOptionPo.setUserId(userId);
                    qstOptionPo.setScoreType(YnEnum.N.key());
                    qstOptionPo.setScore(Long.valueOf(j));
                    qstOptionPo.setCanMutex(CanMutexEnum.CANNOT.key());

                    qstOptionPoList.add(qstOptionPo);
                }
            } else if (isBiaoGeDuoXuan(key) || isBiaoGeDanXuan(key)) {

                String columnTitleStr = v.get(1);
                String[] columnTitles = columnTitleStr.split(" ");

                QstOptionPo qstOptionPoTemp = qstOptionPoTempList.get(0);

                for (int j = 2, l = v.size(); j < l; j++) {
                    for (int m = 0, size = columnTitles.length; m < size; m++) {
                        QstOptionPo qstOptionPo = TextCreateConverter.INSTANCE.to(qstOptionPoTemp);
                        qstOptionPo.setQuestionnaireId(finalQuestionnaireId);
                        qstOptionPo.setTitleSerialNumber(i.get());
                        qstOptionPo.setContent(columnTitles[m]);
                        qstOptionPo.setId(null);
                        qstOptionPo.setOrderNumber(m + 1);
                        qstOptionPo.setSerialNumber(m + 1);
                        qstOptionPo.setUserId(userId);
                        qstOptionPo.setRowNumber(j - 1);
                        qstOptionPo.setColumnNumber(m + 1);
                        qstOptionPo.setRowTitle(v.get(j));

                        qstOptionPoList.add(qstOptionPo);
                    }
                }
            } else if (isDanHangTianKong(key)) {
                QstOptionPo qstOptionPoTemp = qstOptionPoTempList.get(0);
                QstOptionPo qstOptionPo = TextCreateConverter.INSTANCE.to(qstOptionPoTemp);
                qstOptionPo.setQuestionnaireId(finalQuestionnaireId);
                qstOptionPo.setTitleSerialNumber(i.get());
                qstOptionPo.setId(null);
                qstOptionPo.setUserId(userId);
                qstOptionPoList.add(qstOptionPo);

            }
            i.getAndIncrement();
        });
        return questionnaireId;
    }

    private void insertBrowseRecords(QuestionnaireQueryReqDto reqDto) {
        ThreadUtil.execute(() -> {
            QstBrowseRecordsPo po = new QstBrowseRecordsQuery();
            po.setQuestionnaireId(reqDto.getQuestionnaireId());
            po.setUserId(SequenceUtil.getInstance().parse2UserId4Long(reqDto.getQuestionnaireId()));
            po.setIp(reqDto.getIp());
            po.setSource(reqDto.getSource());
            IpAreaResult ipInfo = ipServer.getIpInfo(reqDto.getIp());
            if (ipInfo != null) {
                AreaPo province = areaService.queryProvince(ipInfo.getProvince());
                po.setProvince(province == null ? null : province.getId());
                String key = ipInfo.getCity() + SPLIT_FLAG + (province == null ? "" : province.getId());
                AreaPo city = areaService.queryCity(key);
                po.setCity(city == null ? null : city.getId());
            }

            qstBrowseRecordsService.insert(po);
        });
    }

    @Override
    public QuestionnaireListRepDto list(QuestionnaireListReqDto reqDto) {
        QuestionnaireListRepDto repDto = new QuestionnaireListRepDto();

        QstQuestionnaireInfoQuery query = ConvertBeanUtil.questionnaireListRepDtoToQuery(reqDto);
        if(StringUtil.isNotEmpty(reqDto.getOutUserId())) {
            UserInfoQuery userInfoQuery = new UserInfoQuery();
            userInfoQuery.setOutUserId(reqDto.getOutUserId());
            UserInfoPo userInfo = userInfoService.selectOne(userInfoQuery);
            query.setUserId(userInfo.getId());
        }
        logger.info("问卷分页列表入参:{}", JSONObject.toJSONString(query));
        List<QuestionnaireRepDto> repDtoList = qstQuestionnaireInfoService.selectQuestionnaireList(query);

        //根据创建人搜索
        if(!CollectionUtils.isEmpty(repDtoList) && !StringUtils.isEmpty(reqDto.getName())) {
            UserInfoQuery searchUserInfoQuery = new UserInfoQuery();
            searchUserInfoQuery.setName(reqDto.getName());
            List<UserInfoPo> userByNameList = userInfoService.selectList(searchUserInfoQuery);
            List<Long> ids = userByNameList.stream().map(UserInfoPo::getId).collect(Collectors.toList());
            repDtoList = repDtoList.stream().filter(x -> ids.contains(x.getUserId())).collect(Collectors.toList());
        }
        //根据开始时间搜索
        if(!CollectionUtils.isEmpty(repDtoList) && !StringUtils.isEmpty(reqDto.getBeginTime())) {
            Date beginDate = query.getBeginTime();
            List<String> ids = repDtoList.stream().map(QuestionnaireRepDto::getQuestionnaireId).collect(Collectors.toList());
            List<QstLimitRulePo> qstLimitRulePoList = qstLimitRuleService.selectList(ids, beginDate, null);
            List<String> questionnaireIds = qstLimitRulePoList.stream().map(QstLimitRulePo::getQuestionnaireId).collect(Collectors.toList());
            repDtoList = repDtoList.stream().filter(x -> questionnaireIds.contains(x.getQuestionnaireId())).collect(Collectors.toList());
        }
        //根据结束时间搜索
        if(!CollectionUtils.isEmpty(repDtoList) && !StringUtils.isEmpty(reqDto.getEndTime())) {
            Date endDate = query.getEndTime();
            List<String> ids = repDtoList.stream().map(QuestionnaireRepDto::getQuestionnaireId).collect(Collectors.toList());
            List<QstLimitRulePo> qstLimitRulePoList = qstLimitRuleService.selectList(ids, null, endDate);
            List<String> questionnaireIds = qstLimitRulePoList.stream().map(QstLimitRulePo::getQuestionnaireId).collect(Collectors.toList());
            repDtoList = repDtoList.stream().filter(x -> questionnaireIds.contains(x.getQuestionnaireId())).collect(Collectors.toList());
        }

        List<String> questionnaireRepDtoIdList = repDtoList.stream().map(QuestionnaireRepDto::getQuestionnaireId).collect(Collectors.toList());
        repDto.setTotal(org.apache.commons.collections.CollectionUtils.isEmpty(repDtoList)? 0: repDtoList.size());
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(repDtoList)) {
            //进行分页
            int pageSize = reqDto.getPageSize();
            int current = reqDto.getPage();
            int liseSize = repDtoList.size();
            int start = (current - 1) * pageSize;
            int end = current * pageSize > liseSize? liseSize: current * pageSize;
            repDtoList = repDtoList.subList(start, end);

            for (QuestionnaireRepDto item : repDtoList) {
                if(item.getUserId() != null) {
                    UserInfoPo userInfoPo = userInfoService.selectOne(item.getUserId());
                    if(userInfoPo != null) {
                        item.setUserName(userInfoPo.getUserName());
                        item.setName(userInfoPo.getName());
                    }
                }

                if(QuestionnaireStatusEnum.isUnpublished(item.getStatus())) {
                    item.setTime("--");
                    item.setAnswerCountStr("-");
                    item.setStatus(1);
                } else {
                    QstLimitRulePo qstLimitRulePo = qstLimitRuleService.selectOne(item.getQuestionnaireId());
                    if(qstLimitRulePo == null) {
                        item.setTime("--");
                    } else {
                        if(qstLimitRulePo.getBeginTime() != null) {
                            StringBuilder sb = new StringBuilder();
                            String beginTime = DateUtil.format(qstLimitRulePo.getBeginTime(), DateUtil.C_TIME_PATTON_DEFAULT);
                            sb.append(beginTime);
                            sb.append(" 至 ");
                            if(qstLimitRulePo.getEndTime() != null) {
                                String endTime = DateUtil.format(qstLimitRulePo.getEndTime(), DateUtil.C_TIME_PATTON_DEFAULT);
                                sb.append(endTime);
                            }

                            item.setTime(sb.toString());
                        }
                    }
                    //答卷人数
                    int count = answerInfoService.selectCount(item.getUserId(), item.getQuestionnaireId());
                    item.setAnswerCount(count);
                    item.setAnswerCountStr(String.valueOf(count));
                }
            }
        }

        repDto.setQuestionnaireRepDtoList(repDtoList);
        if(CollectionUtils.isEmpty(questionnaireRepDtoIdList)) {
            repDto.setAllAnswerCount(0);
        } else {
            int answerCount = answerInfoService.selectCount(questionnaireRepDtoIdList, 2);
            repDto.setAllAnswerCount(answerCount);
        }
        return repDto;
    }

    @Override
    public String getQuestionnaireAppearance(String questionnaireId) {
        QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(questionnaireId);
        return qstQuestionnaireInfoPo.getAppearance();
    }

    @Override
    public void setQuestionnaireAppearance(String questionnaireId, String appearance) {
        QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(questionnaireId);
        QstQuestionnaireInfoQuery query = new QstQuestionnaireInfoQuery();
        query.setId(qstQuestionnaireInfoPo.getId());
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(qstQuestionnaireInfoPo.getUserId());
        query.setAppearance(appearance);
        qstQuestionnaireInfoService.update(query);
    }

    @Override
    public void deleteQuestionnaire(List<String> questionnaireIdList) {
        if(CollectionUtils.isEmpty(questionnaireIdList)) return;

        for (String bean : questionnaireIdList) {
            String userId = SequenceUtil.getInstance().parse2UserId(bean);
            if(StringUtil.isNotEmpty(userId)) {
                qstQuestionnaireInfoService.updateStatus2Delete(bean, Long.valueOf(userId));
            }
        }
    }

    @Override
    public void publishQuestionnaire(String questionnaireId, Integer status) {
        // 根据status值选择合适的方法
        if (status == 2) { // 发布状态
            qstQuestionnaireInfoService.updateStatusPause2Publish(questionnaireId, null);
        } else if (status == 1) { // 暂停状态
            qstQuestionnaireInfoService.updateStatus2Pause(questionnaireId, null);
        }
    }
    
    @Override
    public RegularQuestionRepDto getRegularQuestions(RegularQuestionReqDto reqDto) {
        RegularQuestionRepDto result = new RegularQuestionRepDto();
        List<RegularQuestionRepDto.RegularQuestionItemDto> regularQuestions = new ArrayList<>();
        
        // 查询问卷中的题目，获取validateType值为1、2、3、10的题目
        List<QstTitlePo> titlePoList = qstTitleService.selectList(reqDto.getQuestionnaireId(), reqDto.getUserId());
        
        if (CollectionUtils.isEmpty(titlePoList)) {
            result.setRegularQuestions(regularQuestions);
            return result;
        }
        
        for (QstTitlePo titlePo : titlePoList) {
            Integer validateType = titlePo.getValidateType();
            if (validateType != null && (validateType == 1 || validateType == 2 || validateType == 3 || validateType == 10)) {
                RegularQuestionRepDto.RegularQuestionItemDto itemDto = new RegularQuestionRepDto.RegularQuestionItemDto();
                itemDto.setId(titlePo.getId());
                itemDto.setSerialNumber(titlePo.getSerialNumber());
                itemDto.setName(titlePo.getName());
                itemDto.setValidateType(validateType);
                
                // 设置校验类型名称
                switch (validateType) {
                    case 1:
                        itemDto.setValidateTypeName("姓名");
                        break;
                    case 2:
                        itemDto.setValidateTypeName("邮箱");
                        break;
                    case 3:
                        itemDto.setValidateTypeName("手机");
                        break;
                    case 10:
                        itemDto.setValidateTypeName("身份证号");
                        break;
                    default:
                        break;
                }
                
                regularQuestions.add(itemDto);
            }
        }
        
        result.setRegularQuestions(regularQuestions);
        return result;
    }
}

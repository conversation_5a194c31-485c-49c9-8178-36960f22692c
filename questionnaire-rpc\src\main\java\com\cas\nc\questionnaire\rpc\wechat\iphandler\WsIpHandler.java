package com.cas.nc.questionnaire.rpc.wechat.iphandler;

import com.cas.nc.questionnaire.common.utils.HttpClientUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.rpc.wechat.entity.ipentity.IpAreaResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.cas.nc.questionnaire.common.utils.Constants.EQUAL_SIGN;

public class WsIpHandler extends IpHandler {
    private static Logger logger = LoggerFactory.getLogger(WsIpHandler.class);

    @Override
    protected IpAreaResult getIpArea(String ip) {
        try {
            String url = "http://ip.ws.126.net/ipquery?ip=" + ip;
            String resultStr = HttpClientUtil.doGet(url, "GBK");
            logger.info("WsIpHandler.getIpArea paramUrl[{}] result[{}]", url, resultStr);

            if (StringUtil.isNotBlank(resultStr)) {
                String[] split = resultStr.split(EQUAL_SIGN);
                String prsJson = split[split.length - 1];
                if (prsJson.contains("city")) {
                    prsJson = prsJson.replaceAll("city", "\"city\"");
                }
                if (prsJson.contains("province")) {
                    prsJson = prsJson.replaceAll("province", "\"province\"");
                }
                IpAreaResult result = JSONUtil.parseObject(prsJson, IpAreaResult.class);

                return result;
            }
        } catch (Exception e) {
            logger.error("WsIpHandler.getIpArea Exception", e);
        }

        return null;
    }
}

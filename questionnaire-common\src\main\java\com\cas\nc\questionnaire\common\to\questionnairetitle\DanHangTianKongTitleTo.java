package com.cas.nc.questionnaire.common.to.questionnairetitle;

public class DanHangTianKongTitleTo extends BaseTianKongTitleTo {
    /*默认值*/
    private String defaultValue;

    /*是否允许默认*/
    private Boolean canDefault;

    /*是否限制字数*/
    private Boolean canBounds;

    /*最小字数*/
    private Integer minBoundsValue;

    /*最大限制字数*/
    private Integer maxBoundsValue;

    /*校验类型*/
    private Integer validateType;

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Boolean getCanDefault() {
        return canDefault;
    }

    public void setCanDefault(Boolean canDefault) {
        this.canDefault = canDefault;
    }

    public Boolean getCanBounds() {
        return canBounds;
    }

    public void setCanBounds(Boolean canBounds) {
        this.canBounds = canBounds;
    }

    public Integer getMinBoundsValue() {
        return minBoundsValue;
    }

    public void setMinBoundsValue(Integer minBoundsValue) {
        this.minBoundsValue = minBoundsValue;
    }

    public Integer getMaxBoundsValue() {
        return maxBoundsValue;
    }

    public void setMaxBoundsValue(Integer maxBoundsValue) {
        this.maxBoundsValue = maxBoundsValue;
    }

    public Integer getValidateType() {
        return validateType;
    }

    public void setValidateType(Integer validateType) {
        this.validateType = validateType;
    }
}

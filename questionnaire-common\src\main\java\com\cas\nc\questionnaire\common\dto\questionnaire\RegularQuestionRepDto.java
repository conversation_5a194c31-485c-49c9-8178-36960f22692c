package com.cas.nc.questionnaire.common.dto.questionnaire;

import java.util.List;

/**
 * 获取问卷常规性问题响应DTO
 */
public class RegularQuestionRepDto {
    
    /**
     * 常规题目列表
     */
    private List<RegularQuestionItemDto> regularQuestions;

    public List<RegularQuestionItemDto> getRegularQuestions() {
        return regularQuestions;
    }

    public void setRegularQuestions(List<RegularQuestionItemDto> regularQuestions) {
        this.regularQuestions = regularQuestions;
    }
    
    /**
     * 常规性问题项
     */
    public static class RegularQuestionItemDto {
        /**
         * 题目ID
         */
        private Long id;
        
        /**
         * 题目序号
         */
        private Integer serialNumber;
        
        /**
         * 题目名称
         */
        private String name;
        
        /**
         * 校验类型 1:姓名; 2:邮箱; 3:手机; 10:身份证号
         */
        private Integer validateType;
        
        /**
         * 校验类型名称
         */
        private String validateTypeName;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Integer getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(Integer serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getValidateType() {
            return validateType;
        }

        public void setValidateType(Integer validateType) {
            this.validateType = validateType;
        }

        public String getValidateTypeName() {
            return validateTypeName;
        }

        public void setValidateTypeName(String validateTypeName) {
            this.validateTypeName = validateTypeName;
        }
    }
} 
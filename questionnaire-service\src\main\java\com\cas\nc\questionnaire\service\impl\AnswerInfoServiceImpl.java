package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.AnswerStatusEnum;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.dao.bo.ProvinceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.SourceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.TimeAnalysisBo;
import com.cas.nc.questionnaire.dao.po.AnswerInfoPo;
import com.cas.nc.questionnaire.dao.query.AnswerInfoQuery;
import com.cas.nc.questionnaire.dao.query.QstBrowseRecordsQuery;
import com.cas.nc.questionnaire.dao.sharddao.AnswerInfoDao;
import com.cas.nc.questionnaire.service.AnswerInfoService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.cas.nc.questionnaire.common.utils.Constants.ONE;


@Service
public class AnswerInfoServiceImpl implements AnswerInfoService {
    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private AnswerInfoDao answerInfoDao;

    @Override
    public int insert(AnswerInfoPo answerInfoPo) {
        return answerInfoDao.insert(answerInfoPo);
    }

    @Override
    public int delete(AnswerInfoQuery query) {
        filterCondition(query);
        return answerInfoDao.delete(query);
    }

    @Override
    public List<AnswerInfoPo> selectList(AnswerInfoQuery query) {
        filterCondition(query);
        return answerInfoDao.selectList(query);
    }

    @Override
    public AnswerInfoPo selectOne(AnswerInfoQuery query) {
        filterCondition(query);
        return answerInfoDao.selectOne(query);
    }

    @Override
    public AnswerInfoPo selectNewestByDevice(String deviceId, String questionnaireId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);
        query.setDeviceId(deviceId);
        return null;
    }

    @Override
    public int selectCountByDeviceHourly(String deviceId, String questionnaireId) {
        AnswerInfoQuery query = getDeviceCommonAnswerInfoQuery(deviceId, questionnaireId);
        setHourly(query);
        return selectCount(query);
    }

    @Override
    public int selectCountByDeviceDaily(String deviceId, String questionnaireId) {
        AnswerInfoQuery query = getDeviceCommonAnswerInfoQuery(deviceId, questionnaireId);
        setDaily(query);
        return selectCount(query);
    }

    @Override
    public int selectCountByDeviceWeekly(String deviceId, String questionnaireId) {
        AnswerInfoQuery query = getDeviceCommonAnswerInfoQuery(deviceId, questionnaireId);
        setWeekly(query);
        return selectCount(query);
    }

    @Override
    public int selectCountByDeviceMonthly(String deviceId, String questionnaireId) {
        AnswerInfoQuery query = getDeviceCommonAnswerInfoQuery(deviceId, questionnaireId);
        setMonthly(query);
        return selectCount(query);
    }

    private void setMonthly(AnswerInfoQuery query) {
        Date date = new Date();
        query.setBeginCreateTime(DateUtil.format(date, DateUtil.YYYYMM));
        query.setEndCreateTime(DateUtil.format(DateUtil.addMonths(date, 1), DateUtil.YYYYMM));
    }

    @Override
    public int selectCountByDeviceAnnually(String deviceId, String questionnaireId) {
        AnswerInfoQuery query = getDeviceCommonAnswerInfoQuery(deviceId, questionnaireId);
        setAnnually(query);
        return selectCount(query);
    }

    @Override
    public int selectCountByIpHourly(String ip, String questionnaireId) {
        AnswerInfoQuery query = getIpCommonAnswerInfoQuery(ip, questionnaireId);
        setHourly(query);
        return selectCount(query);
    }

    @Override
    public int selectCountByIpDaily(String ip, String questionnaireId) {
        AnswerInfoQuery query = getIpCommonAnswerInfoQuery(ip, questionnaireId);
        setDaily(query);
        return selectCount(query);
    }

    @Override
    public int selectCountByIpWeekly(String ip, String questionnaireId) {
        AnswerInfoQuery query = getIpCommonAnswerInfoQuery(ip, questionnaireId);
        setWeekly(query);
        return selectCount(query);
    }

    @Override
    public int selectCountByIpMonthly(String ip, String questionnaireId) {
        AnswerInfoQuery query = getIpCommonAnswerInfoQuery(ip, questionnaireId);
        setMonthly(query);
        return selectCount(query);
    }

    @Override
    public int selectCountByIpAnnually(String ip, String questionnaireId) {
        AnswerInfoQuery query = getIpCommonAnswerInfoQuery(ip, questionnaireId);
        setAnnually(query);
        return selectCount(query);
    }

    @Override
    public AnswerInfoPo selectNewestByIp(String ip, String questionnaireId) {
        return null;
    }

    @Override
    public int selectCount(AnswerInfoQuery query) {
        return answerInfoDao.selectCount(query);
    }

    @Override
    public int selectCount(Long userId, String questionnaireId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setUserId(userId);
        query.setQuestionnaireId(questionnaireId);
        query.setStatus(AnswerStatusEnum.FINISH.key());

        return selectCount(query);
    }

    @Override
    public int selectCount(String deviceId, String questionnaireId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setDeviceId(deviceId);
        filterCondition(query);
        return selectCount(query);
    }

    @Override
    public int selectCount(String questionnaireId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);
        return selectCount(query);
    }

    @Override
    public int selectCount(List<String> questionnaireIdList, int status) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireIdList(questionnaireIdList);
        query.setStatus(status);
        return selectCount(query);
    }

    @Override
    public int update(AnswerInfoQuery query) {
        return answerInfoDao.update(query);
    }

    @Override
    public void updateStatusList(AnswerInfoQuery query) {
        if (CollectionUtils.isEmpty(query.getIdList())) {
            return;
        }
        for (Long bean : query.getIdList()) {
            query.setId(bean);
            Assert.isTrue(update(query) == ONE, CodeEnum.UPDATE_EXCEPTION);
        }
    }

    @Override
    public List<AnswerInfoPo> selectListByFilterCondition(AnswerInfoQuery query) {
        return answerInfoDao.selectListByFilterCondition(query);
    }

    @Override
    public int selectProvinceCount(String questionnaireId, Long provinceId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setProvince(provinceId);
        filterCondition(query);

        return selectCount(query);
    }

    @Override
    public int selectCityCount(String questionnaireId, Long cityId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setCity(cityId);
        filterCondition(query);

        return selectCount(query);
    }

    @Override
    public List<AnswerInfoPo> selectList(String questionnaireId, List<String> answerIdList) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setAnswerIdList(answerIdList);
        query.setQuestionnaireId(questionnaireId);

        return selectList(query);
    }

    @Override
    public List<AnswerInfoPo> selectList(String questionnaireId, Long userId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);

        return selectList(query);
    }

    @Override
    public List<AnswerInfoPo> selectListByPage(AnswerInfoQuery query) {
        return answerInfoDao.selectListByPage(query);
    }

    @Override
    public AnswerInfoPo selectEffective(String answerId, Long userId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setAnswerId(answerId);
        query.setUserId(userId);
        query.setStatus(AnswerStatusEnum.FINISH.key());

        return answerInfoDao.selectOne(query);
    }

    @Override
    public List<SourceAnalysisBo> selectSourceStatistics(String questionnaireId, Long userId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
//        filterCondition(query);

        return answerInfoDao.selectSourceStatistics(query);
    }

    @Override
    public List<ProvinceAnalysisBo> selectProvinceStatistics(String questionnaireId, Long userId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
//        filterCondition(query);

        return answerInfoDao.selectProvinceStatistics(query);
    }

    @Override
    public List<TimeAnalysisBo> selectDayStatistics(String questionnaireId, String beginTime, String endTime) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setBeginCreateTime(beginTime);
        query.setEndCreateTime(endTime);

//        filterCondition(query);

        return answerInfoDao.selectDayStatistics(query);
    }

    @Override
    public List<TimeAnalysisBo> selectWeekDayStatistics(String questionnaireId, String beginTime, String endTime) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setBeginCreateTime(beginTime);
        query.setEndCreateTime(endTime);

        filterCondition(query);

        return answerInfoDao.selectWeekDayStatistics(query);
    }

    @Override
    public List<TimeAnalysisBo> selectMonthStatistics(String questionnaireId, String beginTime, String endTime) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setBeginCreateTime(beginTime);
        query.setEndCreateTime(endTime);

        filterCondition(query);

        return answerInfoDao.selectMonthStatistics(query);
    }

    @Override
    public List<Long> selectProvinceGroupBy(String questionnaireId, Long userId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setProvinceGroupBy(true);
//        Assert.notNull(userId, "userId");

        return answerInfoDao.selectRuleGroupBy(query);
    }

    @Override
    public List<Long> selectCityGroupBy(String questionnaireId, Long userId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setCityGroupBy(true);
//        Assert.notNull(userId, "userId");

        return answerInfoDao.selectRuleGroupBy(query);
    }

    @Override
    public List<Long> selectSourceGroupBy(String questionnaireId, Long userId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setSourceGroupBy(true);
//        Assert.notNull(userId, "userId");

        return answerInfoDao.selectRuleGroupBy(query);
    }

    private void filterCondition(AnswerInfoQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }

    private AnswerInfoQuery getDeviceCommonAnswerInfoQuery(String deviceId, String questionnaireId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setDeviceId(deviceId);
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);
        return query;
    }

    private void setHourly(AnswerInfoQuery query) {
        Date date = new Date();
        query.setBeginCreateTime(DateUtil.format(date, DateUtil.YYYYMMDDHH));
        query.setEndCreateTime(DateUtil.format(DateUtil.addHours(date, 1), DateUtil.YYYYMMDDHH));
    }

    private void setDaily(AnswerInfoQuery query) {
        Date date = new Date();
        query.setBeginCreateTime(DateUtil.format(date, DateUtil.YYYYMMDD));
        query.setEndCreateTime(DateUtil.format(DateUtil.addDays(date, 1), DateUtil.YYYYMMDD));
    }

    private void setWeekly(AnswerInfoQuery query) {
        Date date = new Date();
        try {
            query.setBeginCreateTime(DateUtil.getFirstOfWeek(DateUtil.format(date, DateUtil.YYYYMMDD)));
            String lastOfWeek = DateUtil.getLastOfWeek(DateUtil.format(date, DateUtil.YYYYMMDD));
            Date lastOfWeekDate = DateUtil.addDays(DateUtil.parseDate(lastOfWeek, DateUtil.YYYYMMDD), 1);
            query.setEndCreateTime(DateUtil.format(lastOfWeekDate, DateUtil.YYYYMMDD));
        } catch (ParseException e) {
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
    }

    private void setAnnually(AnswerInfoQuery query) {
        Date date = new Date();
        query.setBeginCreateTime(DateUtil.format(date, DateUtil.YYYY));
        query.setEndCreateTime(DateUtil.format(DateUtil.addYears(date, 1), DateUtil.YYYY));
    }

    private AnswerInfoQuery getIpCommonAnswerInfoQuery(String ip, String questionnaireId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);
        query.setIp(ip);
        return query;
    }

    @Override
    public List<String> selectAnswerIdsByQuestionnaireId(String questionnaireId) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setStatus(AnswerStatusEnum.FINISH.key());
        
        List<AnswerInfoPo> answerInfoList = selectList(query);
        List<String> answerIdList = new ArrayList<>();
        for (AnswerInfoPo answerInfo : answerInfoList) {
            answerIdList.add(answerInfo.getAnswerId());
        }
        return answerIdList;
    }

    @Override
    public List<AnswerInfoPo> selectByAnswerIds(String questionnaireId, List<String> answerIdList) {
        if (answerIdList == null || answerIdList.isEmpty()) {
            return new ArrayList<>();
        }
        
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setAnswerIdList(answerIdList);
        query.setStatus(AnswerStatusEnum.FINISH.key());
        
        return selectList(query);
    }
}

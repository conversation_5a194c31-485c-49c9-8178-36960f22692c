package com.cas.nc.questionnaire.common.to.questionnairetitle;

import com.cas.nc.questionnaire.common.to.questionnaireoption.LiangBiaoOptionTo;
import com.cas.nc.questionnaire.common.utils.SafeUtil;

import java.util.List;


public class LiangBiaoTitleTo extends BaseTitleTo {
    private Integer showStyle;
    private List<LiangBiaoOptionTo> options;
    private Integer levelCount;

    public Integer getShowStyle() {
        return showStyle;
    }

    public void setShowStyle(Integer showStyle) {
        this.showStyle = showStyle;
    }

    public List<LiangBiaoOptionTo> getOptions() {
        return options;
    }

    public void setOptions(List<LiangBiaoOptionTo> options) {
        this.options = options;
    }

    public Integer getLevelCount() {
        return SafeUtil.of(options).size();
    }

    public void setLevelCount(Integer levelCount) {
        this.levelCount = levelCount;
    }
}

package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.pay.*;
import com.cas.nc.questionnaire.common.vo.pay.GetPayStatusRepVo;
import com.cas.nc.questionnaire.common.vo.pay.GetPayStatusReqVo;
import com.cas.nc.questionnaire.common.vo.pay.GetPayUrlRepVo;
import com.cas.nc.questionnaire.common.vo.pay.GetPayUrlReqVo;
import com.cas.nc.questionnaire.dao.po.OrderInfoPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PayConverter {
    PayConverter INSTANCE = Mappers.getMapper(PayConverter.class);

    GetPayUrlReqDto to(GetPayUrlReqVo reqVo);

    GetPayUrlRepVo to(GetPayUrlRepDto repDto);

    GetPayStatusReqDto to(GetPayStatusReqVo reqVo);

    GetPayStatusRepVo to(GetPayStatusRepDto repDto);

    List<OrderRepTo> toListOrder(List<OrderInfoPo> orderInfoPoList);
}
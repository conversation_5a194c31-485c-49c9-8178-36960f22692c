package com.cas.nc.questionnaire.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


public enum AnswerStatusEnum {
    INIT(1, "初始化"),
    FINISH(2, "完成"),
    AUTO_FILTER_INVALID(3, "自动筛选无效"),
    MANUAL_FILTER_INVALID(4, "手动筛选无效"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    AnswerStatusEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static AnswerStatusEnum filterTypeConvert(int key) {
        FilterTypeEnum.filterLegalException(key);
        if (FilterTypeEnum.isAuto(key)) {
            return AUTO_FILTER_INVALID;
        } else if (FilterTypeEnum.isManual(key)) {
            return MANUAL_FILTER_INVALID;
        }
        return ELSE;
    }

    public static List<Integer> INVALID_STATUS_LIST = new ArrayList<>();

    public static List<Integer> getInvalidStatusList() {
        if (CollectionUtils.isEmpty(INVALID_STATUS_LIST)) {
            INVALID_STATUS_LIST.add(AUTO_FILTER_INVALID.key);
            INVALID_STATUS_LIST.add(MANUAL_FILTER_INVALID.key);
        }
        return INVALID_STATUS_LIST;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

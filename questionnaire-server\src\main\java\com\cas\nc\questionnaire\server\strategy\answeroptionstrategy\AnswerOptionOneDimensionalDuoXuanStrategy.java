package com.cas.nc.questionnaire.server.strategy.answeroptionstrategy;

import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateReqDto;
import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.constructBaseAnswerOption;

@Component("answerOptionOneDimensionalDuoXuanStrategy")
public class AnswerOptionOneDimensionalDuoXuanStrategy implements AnswerOptionStrategy {
    @Override
    public List<AnswerOptionPo> construct(AnswerCreateReqDto reqDto, String answerId, Map.Entry<String, Object> dataEntry, TitleTypeEnum titleTypeEnum) {
        List<AnswerOptionPo> resultList = new ArrayList<>();
        String k = dataEntry.getKey();
        Object v = dataEntry.getValue();
        List<Object> duoXuanOptionList = (List<Object>) v;
        duoXuanOptionList.forEach(p -> {
            AnswerOptionPo duoXuanAnswerOption = constructBaseAnswerOption(reqDto, answerId, k, titleTypeEnum);
            if (p instanceof Map) {
                duoXuanAnswerOption.setSerialNumber((Integer) ((Map) p).get("serialNumber"));
                duoXuanAnswerOption.setWriteContent((String) ((Map) p).get("blankContent"));
            } else {
                duoXuanAnswerOption.setSerialNumber((Integer) p);
            }
            resultList.add(duoXuanAnswerOption);
        });

        return resultList;
    }
}

package com.cas.nc.questionnaire.common.vo.analysis;

import java.math.BigDecimal;

public class CrossAnalysisColumnOptionRepVo {
    /*选项序号*/
    private Integer serialNumber;
    /*选项名称*/
    private String name;
    /*有效答题数量*/
    private Integer total;
    /*百分比*/
    private BigDecimal percentage;

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public BigDecimal getPercentage() {
        return percentage;
    }

    public void setPercentage(BigDecimal percentage) {
        this.percentage = percentage;
    }
}

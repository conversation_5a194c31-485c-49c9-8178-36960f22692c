package com.cas.nc.questionnaire.common.vo.questionnaire;

import java.util.Date;
import java.util.List;

/**
 * 保存测评报告查询条件请求VO
 */
public class ReportQueryConditionSaveReqVo {
    
    /**
     * 问卷ID
     */
    private String questionnaireId;
    
    /**
     * 查询开始时间
     */
    private Date beginTime;
    
    /**
     * 查询结束时间
     */
    private Date endTime;
    
    /**
     * 常规性问题列表
     */
    private List<RegularQuestionItemVo> regularQuestions;
    
    /**
     * 常规性问题项
     */
    public static class RegularQuestionItemVo {
        
        /**
         * 题目ID
         */
        private Long titleId;
        
        /**
         * 题目序号
         */
        private Integer serialNumber;
        
        /**
         * 校验类型：1-姓名，2-邮箱，3-手机，10-身份证号
         */
        private Integer validateType;
        
        public Long getTitleId() {
            return titleId;
        }
        
        public void setTitleId(Long titleId) {
            this.titleId = titleId;
        }
        
        public Integer getSerialNumber() {
            return serialNumber;
        }
        
        public void setSerialNumber(Integer serialNumber) {
            this.serialNumber = serialNumber;
        }
        
        public Integer getValidateType() {
            return validateType;
        }
        
        public void setValidateType(Integer validateType) {
            this.validateType = validateType;
        }
    }
    
    public String getQuestionnaireId() {
        return questionnaireId;
    }
    
    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }
    
    public Date getBeginTime() {
        return beginTime;
    }
    
    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }
    
    public Date getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    public List<RegularQuestionItemVo> getRegularQuestions() {
        return regularQuestions;
    }
    
    public void setRegularQuestions(List<RegularQuestionItemVo> regularQuestions) {
        this.regularQuestions = regularQuestions;
    }
} 
package com.cas.nc.questionnaire.web.interceptor;

import com.cas.nc.questionnaire.web.sso.LoginCheck;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class LoginInterceptor implements HandlerInterceptor {

    private static String loginUri = "/questionnaire/user/login";

    @Resource
    private LoginCheck loginCheck;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        String contextPath = request.getContextPath();
//        String cookieValue = HttpClientUtil.getCookieValue(request.getCookies(), LoginCheck.COOKIE_NAME);
//        boolean login = loginCheck.checkCookie(LoginCheck.COOKIE_NAME, cookieValue);
//        if (login) {
//            return true;
//        }
//        HttpSession session = request.getSession(true);
//        String uri = request.getRequestURI();//拿到上一个页面地址
//        String path = uri.substring(request.getContextPath().length());//去掉项目地址长度的字符（因为我的默认项目地址是给出的）
//        String query = request.getQueryString();//得到参数
//        if(query == null) {
//            query="";
//        }
//        else {
//            query="?"+query;
//        }
//        String beforePath = path+query;
//        session.setAttribute("beforePath", beforePath);
//        session.setAttribute("method", request.getMethod());
//        response.sendRedirect(contextPath + loginUri);
//        return false;
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

    }
}

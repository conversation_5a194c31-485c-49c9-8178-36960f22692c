<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.TempletTitleDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.TempletTitlePo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="templet_id" jdbcType="VARCHAR" property="templetId"/>
        <result column="serial_number" jdbcType="INTEGER" property="serialNumber"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="order_number" jdbcType="INTEGER" property="orderNumber"/>
        <result column="global_order" jdbcType="INTEGER" property="globalOrder"/>
        <result column="associate_condition" jdbcType="INTEGER" property="associateCondition"/>
        <result column="jump_type" jdbcType="INTEGER" property="jumpType"/>
        <result column="jump_uncondition" jdbcType="INTEGER" property="jumpUncondition"/>
        <result column="can_require" jdbcType="INTEGER" property="canRequire"/>
        <result column="arrangement_mode" jdbcType="INTEGER" property="arrangementMode"/>
        <result column="width" jdbcType="INTEGER" property="width"/>
        <result column="height" jdbcType="INTEGER" property="height"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="can_default" jdbcType="INTEGER" property="canDefault"/>
        <result column="can_bounds" jdbcType="INTEGER" property="canBounds"/>
        <result column="min_bounds_value" jdbcType="INTEGER" property="minBoundsValue"/>
        <result column="max_bounds_value" jdbcType="INTEGER" property="maxBoundsValue"/>
        <result column="validate_type" jdbcType="INTEGER" property="validateType"/>
        <result column="choose_min" jdbcType="INTEGER" property="chooseMin"/>
        <result column="choose_max" jdbcType="INTEGER" property="chooseMax"/>
        <result column="row_count" jdbcType="INTEGER" property="rowCount"/>
        <result column="column_count" jdbcType="INTEGER" property="columnCount"/>
        <result column="vertical_count" jdbcType="INTEGER" property="verticalCount"/>
        <result column="limit_attribute_json" jdbcType="OTHER" property="limitAttributeJson"/>
        <result column="proportion_total" jdbcType="INTEGER" property="proportionTotal"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,templet_id,serial_number,name,type,order_number,global_order,
    associate_condition,jump_type,jump_uncondition,can_require,arrangement_mode,width,
    height,default_value,can_default,can_bounds,min_bounds_value,max_bounds_value,
    validate_type,choose_min,choose_max,row_count,column_count,vertical_count,
    limit_attribute_json,proportion_total,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.templetId and '' != item.templetId">and templet_id = #{item.templetId}</if>
            <if test="null != item.serialNumber and '' != item.serialNumber">and serial_number = #{item.serialNumber}
            </if>
            <if test="null != item.name and '' != item.name">and name = #{item.name}</if>
            <if test="null != item.type and '' != item.type">and type = #{item.type}</if>
            <if test="null != item.orderNumber and '' != item.orderNumber">and order_number = #{item.orderNumber}</if>
            <if test="null != item.globalOrder and '' != item.globalOrder">and global_order = #{item.globalOrder}</if>
            <if test="null != item.associateCondition and '' != item.associateCondition">and associate_condition =
                #{item.associateCondition}
            </if>
            <if test="null != item.jumpType and '' != item.jumpType">and jump_type = #{item.jumpType}</if>
            <if test="null != item.jumpUncondition and '' != item.jumpUncondition">and jump_uncondition =
                #{item.jumpUncondition}
            </if>
            <if test="null != item.canRequire and '' != item.canRequire">and can_require = #{item.canRequire}</if>
            <if test="null != item.arrangementMode and '' != item.arrangementMode">and arrangement_mode =
                #{item.arrangementMode}
            </if>
            <if test="null != item.width and '' != item.width">and width = #{item.width}</if>
            <if test="null != item.height and '' != item.height">and height = #{item.height}</if>
            <if test="null != item.defaultValue and '' != item.defaultValue">and default_value = #{item.defaultValue}
            </if>
            <if test="null != item.canDefault and '' != item.canDefault">and can_default = #{item.canDefault}</if>
            <if test="null != item.canBounds and '' != item.canBounds">and can_bounds = #{item.canBounds}</if>
            <if test="null != item.minBoundsValue and '' != item.minBoundsValue">and min_bounds_value =
                #{item.minBoundsValue}
            </if>
            <if test="null != item.maxBoundsValue and '' != item.maxBoundsValue">and max_bounds_value =
                #{item.maxBoundsValue}
            </if>
            <if test="null != item.validateType and '' != item.validateType">and validate_type = #{item.validateType}
            </if>
            <if test="null != item.chooseMin and '' != item.chooseMin">and choose_min = #{item.chooseMin}</if>
            <if test="null != item.chooseMax and '' != item.chooseMax">and choose_max = #{item.chooseMax}</if>
            <if test="null != item.rowCount and '' != item.rowCount">and row_count = #{item.rowCount}</if>
            <if test="null != item.columnCount and '' != item.columnCount">and column_count = #{item.columnCount}</if>
            <if test="null != item.verticalCount and '' != item.verticalCount">and vertical_count =
                #{item.verticalCount}
            </if>
            <if test="null != item.limitAttributeJson and '' != item.limitAttributeJson">and limit_attribute_json =
                #{item.limitAttributeJson}
            </if>
            <if test="null != item.proportionTotal and '' != item.proportionTotal">and proportion_total =
                #{item.proportionTotal}
            </if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from templet_title
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from templet_title
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from templet_title
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into templet_title
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.templetId">templet_id,</if>
            <if test="null != item.serialNumber">serial_number,</if>
            <if test="null != item.name">name,</if>
            <if test="null != item.type">type,</if>
            <if test="null != item.orderNumber">order_number,</if>
            <if test="null != item.globalOrder">global_order,</if>
            <if test="null != item.associateCondition">associate_condition,</if>
            <if test="null != item.jumpType">jump_type,</if>
            <if test="null != item.jumpUncondition">jump_uncondition,</if>
            <if test="null != item.canRequire">can_require,</if>
            <if test="null != item.arrangementMode">arrangement_mode,</if>
            <if test="null != item.width">width,</if>
            <if test="null != item.height">height,</if>
            <if test="null != item.defaultValue">default_value,</if>
            <if test="null != item.canDefault">can_default,</if>
            <if test="null != item.canBounds">can_bounds,</if>
            <if test="null != item.minBoundsValue">min_bounds_value,</if>
            <if test="null != item.maxBoundsValue">max_bounds_value,</if>
            <if test="null != item.validateType">validate_type,</if>
            <if test="null != item.chooseMin">choose_min,</if>
            <if test="null != item.chooseMax">choose_max,</if>
            <if test="null != item.rowCount">row_count,</if>
            <if test="null != item.columnCount">column_count,</if>
            <if test="null != item.verticalCount">vertical_count,</if>
            <if test="null != item.limitAttributeJson">limit_attribute_json,</if>
            <if test="null != item.proportionTotal">proportion_total,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.templetId">#{item.templetId},</if>
            <if test="null != item.serialNumber">#{item.serialNumber},</if>
            <if test="null != item.name">#{item.name},</if>
            <if test="null != item.type">#{item.type},</if>
            <if test="null != item.orderNumber">#{item.orderNumber},</if>
            <if test="null != item.globalOrder">#{item.globalOrder},</if>
            <if test="null != item.associateCondition">#{item.associateCondition},</if>
            <if test="null != item.jumpType">#{item.jumpType},</if>
            <if test="null != item.jumpUncondition">#{item.jumpUncondition},</if>
            <if test="null != item.canRequire">#{item.canRequire},</if>
            <if test="null != item.arrangementMode">#{item.arrangementMode},</if>
            <if test="null != item.width">#{item.width},</if>
            <if test="null != item.height">#{item.height},</if>
            <if test="null != item.defaultValue">#{item.defaultValue},</if>
            <if test="null != item.canDefault">#{item.canDefault},</if>
            <if test="null != item.canBounds">#{item.canBounds},</if>
            <if test="null != item.minBoundsValue">#{item.minBoundsValue},</if>
            <if test="null != item.maxBoundsValue">#{item.maxBoundsValue},</if>
            <if test="null != item.validateType">#{item.validateType},</if>
            <if test="null != item.chooseMin">#{item.chooseMin},</if>
            <if test="null != item.chooseMax">#{item.chooseMax},</if>
            <if test="null != item.rowCount">#{item.rowCount},</if>
            <if test="null != item.columnCount">#{item.columnCount},</if>
            <if test="null != item.verticalCount">#{item.verticalCount},</if>
            <if test="null != item.limitAttributeJson">#{item.limitAttributeJson},</if>
            <if test="null != item.proportionTotal">#{item.proportionTotal},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.templetId">templet_id = values(templet_id),</if>
            <if test="null != item.serialNumber">serial_number = values(serial_number),</if>
            <if test="null != item.name">name = values(name),</if>
            <if test="null != item.type">type = values(type),</if>
            <if test="null != item.orderNumber">order_number = values(order_number),</if>
            <if test="null != item.globalOrder">global_order = values(global_order),</if>
            <if test="null != item.associateCondition">associate_condition = values(associate_condition),</if>
            <if test="null != item.jumpType">jump_type = values(jump_type),</if>
            <if test="null != item.jumpUncondition">jump_uncondition = values(jump_uncondition),</if>
            <if test="null != item.canRequire">can_require = values(can_require),</if>
            <if test="null != item.arrangementMode">arrangement_mode = values(arrangement_mode),</if>
            <if test="null != item.width">width = values(width),</if>
            <if test="null != item.height">height = values(height),</if>
            <if test="null != item.defaultValue">default_value = values(default_value),</if>
            <if test="null != item.canDefault">can_default = values(can_default),</if>
            <if test="null != item.canBounds">can_bounds = values(can_bounds),</if>
            <if test="null != item.minBoundsValue">min_bounds_value = values(min_bounds_value),</if>
            <if test="null != item.maxBoundsValue">max_bounds_value = values(max_bounds_value),</if>
            <if test="null != item.validateType">validate_type = values(validate_type),</if>
            <if test="null != item.chooseMin">choose_min = values(choose_min),</if>
            <if test="null != item.chooseMax">choose_max = values(choose_max),</if>
            <if test="null != item.rowCount">row_count = values(row_count),</if>
            <if test="null != item.columnCount">column_count = values(column_count),</if>
            <if test="null != item.verticalCount">vertical_count = values(vertical_count),</if>
            <if test="null != item.limitAttributeJson">limit_attribute_json = values(limit_attribute_json),</if>
            <if test="null != item.proportionTotal">proportion_total = values(proportion_total),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update templet_title
        <set>
            <if test="null != item.templetId">templet_id = #{item.templetId},</if>
            <if test="null != item.serialNumber">serial_number = #{item.serialNumber},</if>
            <if test="null != item.name">name = #{item.name},</if>
            <if test="null != item.type">type = #{item.type},</if>
            <if test="null != item.orderNumber">order_number = #{item.orderNumber},</if>
            <if test="null != item.globalOrder">global_order = #{item.globalOrder},</if>
            <if test="null != item.associateCondition">associate_condition = #{item.associateCondition},</if>
            <if test="null != item.jumpType">jump_type = #{item.jumpType},</if>
            <if test="null != item.jumpUncondition">jump_uncondition = #{item.jumpUncondition},</if>
            <if test="null != item.canRequire">can_require = #{item.canRequire},</if>
            <if test="null != item.arrangementMode">arrangement_mode = #{item.arrangementMode},</if>
            <if test="null != item.width">width = #{item.width},</if>
            <if test="null != item.height">height = #{item.height},</if>
            <if test="null != item.defaultValue">default_value = #{item.defaultValue},</if>
            <if test="null != item.canDefault">can_default = #{item.canDefault},</if>
            <if test="null != item.canBounds">can_bounds = #{item.canBounds},</if>
            <if test="null != item.minBoundsValue">min_bounds_value = #{item.minBoundsValue},</if>
            <if test="null != item.maxBoundsValue">max_bounds_value = #{item.maxBoundsValue},</if>
            <if test="null != item.validateType">validate_type = #{item.validateType},</if>
            <if test="null != item.chooseMin">choose_min = #{item.chooseMin},</if>
            <if test="null != item.chooseMax">choose_max = #{item.chooseMax},</if>
            <if test="null != item.rowCount">row_count = #{item.rowCount},</if>
            <if test="null != item.columnCount">column_count = #{item.columnCount},</if>
            <if test="null != item.verticalCount">vertical_count = #{item.verticalCount},</if>
            <if test="null != item.limitAttributeJson">limit_attribute_json = #{item.limitAttributeJson},</if>
            <if test="null != item.proportionTotal">proportion_total = #{item.proportionTotal},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from templet_title
        <include refid="sql_where"/>
    </delete>
</mapper>
package com.cas.nc.questionnaire.dao.nosharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.OrderInfoPo;
import com.cas.nc.questionnaire.dao.query.OrderInfoQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderInfoDao extends BaseDao<OrderInfoPo, OrderInfoQuery> {
    int selectMaxVersion(@Param("item") OrderInfoQuery query);

    List<OrderInfoPo> selectListByPage(@Param("item") OrderInfoQuery query);

    int selectCount(@Param("item") OrderInfoQuery query);
}
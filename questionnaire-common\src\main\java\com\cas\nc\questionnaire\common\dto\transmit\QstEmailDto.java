package com.cas.nc.questionnaire.common.dto.transmit;

import com.cas.nc.questionnaire.common.vo.transmit.QstEmailVo;

public class QstEmailDto extends QstEmailVo {
    /**用户id*/
    private Long userId;

    /**来源类型，1：问卷设置，2：发送问卷*/
    private Integer sourceType;

    /**问卷名称*/
    private String quesName;

    /*每页行数*/
    private Integer pageSize;
    /*导向页*/
    private Integer page;

   public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public String getQuesName() {
        return quesName;
    }

    public void setQuesName(String quesName) {
        this.quesName = quesName;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }
}

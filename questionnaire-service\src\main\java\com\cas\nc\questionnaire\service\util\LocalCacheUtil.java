package com.cas.nc.questionnaire.service.util;

import com.cas.nc.questionnaire.common.dto.area.AreaProvinceRepDto;
import com.cas.nc.questionnaire.common.dto.school.SchoolProvinceRepDto;
import com.cas.nc.questionnaire.dao.po.BizConfigPo;
import com.cas.nc.questionnaire.dao.po.IpRpcConfigPo;
import com.cas.nc.questionnaire.dao.po.TaskConfigPo;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


public class LocalCacheUtil {


    public static Map<Integer, TaskConfigPo> TASK_CONFIG_CACHE = Maps.newHashMap();

    public static Map<String, String> IMAGE_CODE_MAP = Maps.newHashMap();

    public static List<AreaProvinceRepDto> PROVINCE_2_COUNTY_LIST = new ArrayList<>();

    public static Map<String, AreaProvinceRepDto> PROVINCE_2_CITY_MAP = new HashMap<>();

    public static List<AreaProvinceRepDto> PROVINCE_2_CITY_LIST = new ArrayList<>();

    public static List<SchoolProvinceRepDto> PROVINCE_SCHOOL_LIST = new ArrayList<>();

    public static List<IpRpcConfigPo> IP_RPC_CONFIG_LIST = new ArrayList<>();

    public static Map<Long, BizConfigPo> BIZ_CONFIG_MAP = new HashMap<>();

    public static Cache<String, String> IMAGE_CODE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(60, TimeUnit.SECONDS)
            .build();


}

package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.mylist.*;
import com.cas.nc.questionnaire.common.vo.mylist.*;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MyListConverter {
    MyListConverter INSTANCE = Mappers.getMapper(MyListConverter.class);

    MyListReqDto to(MyListReqVo vo);

    MyListRepVo to(MyListRepDto repDto);

    MyListRecycleBinListReqDto to(MyListRecycleBinListReqVo vo);

    List<MyListRecycleBinListRepVo> to(List<MyListRecycleBinListRepDto> repDtoList);

    MyListDeleteReqDto to(MyListDeleteReqVo vo);

    MyListPauseReqDto to(MyListPauseReqVo vo);

    MyListPublishReqDto to(MyListPublishReqVo vo);

    MyListRecoveryReqDto to(MyListRecoveryReqVo vo);

    MyListRecycleBinDeleteReqDto to(MyListRecycleBinDeleteReqVo vo);

    MyListCopyReqDto to(MyListCopyReqVo vo);

    MyListQuestionnaireRepDto to(QstQuestionnaireInfoPo bean);

    ExternalListReqDto to(ExternalListReqVo vo);

    CreateExternalQuestionnaireReqDto to(CreateExternalQuestionnaireReqVo vo);
}
package com.cas.nc.questionnaire.server.strategy.answeroptionstrategy;

import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateReqDto;
import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.constructBaseAnswerOption;

@Component("answerOptionOneDimensionalFillBlankStrategy")
public class AnswerOptionOneDimensionalFillBlankStrategy implements AnswerOptionStrategy {

    @Override
    public List<AnswerOptionPo> construct(AnswerCreateReqDto reqDto, String answerId, Map.Entry<String, Object> dataEntry, TitleTypeEnum titleTypeEnum) {
        List<AnswerOptionPo> resultList = new ArrayList<>();

        AnswerOptionPo option = constructBaseAnswerOption(reqDto, answerId, dataEntry.getKey(), titleTypeEnum);
        option.setWriteContent(dataEntry.getValue().toString());

        resultList.add(option);
        return resultList;
    }
}

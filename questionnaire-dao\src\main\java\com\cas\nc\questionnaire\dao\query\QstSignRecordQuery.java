package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.QstSignRecordPo;

import java.util.Date;
import java.util.List;

/**
 * 问卷签到记录查询条件
 */
public class QstSignRecordQuery extends QstSignRecordPo {
    
    /*开始索引*/
    private int startIndex;
    
    /*页面大小*/
    private Integer pageSize;
    
    /*签到开始时间*/
    private String beginSignTime;
    
    /*签到结束时间*/
    private String endSignTime;
    
    /*签到日期列表*/
    private List<Date> signDateList;
    
    /*openid列表*/
    private List<String> openidList;
    
    /*问卷ID列表*/
    private List<String> questionnaireIdList;

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getBeginSignTime() {
        return beginSignTime;
    }

    public void setBeginSignTime(String beginSignTime) {
        this.beginSignTime = beginSignTime;
    }

    public String getEndSignTime() {
        return endSignTime;
    }

    public void setEndSignTime(String endSignTime) {
        this.endSignTime = endSignTime;
    }

    public List<Date> getSignDateList() {
        return signDateList;
    }

    public void setSignDateList(List<Date> signDateList) {
        this.signDateList = signDateList;
    }

    public List<String> getOpenidList() {
        return openidList;
    }

    public void setOpenidList(List<String> openidList) {
        this.openidList = openidList;
    }

    public List<String> getQuestionnaireIdList() {
        return questionnaireIdList;
    }

    public void setQuestionnaireIdList(List<String> questionnaireIdList) {
        this.questionnaireIdList = questionnaireIdList;
    }
} 
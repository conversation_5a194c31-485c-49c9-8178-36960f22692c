package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.bo.ProvinceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.SourceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.TimeAnalysisBo;
import com.cas.nc.questionnaire.dao.po.QstBrowseRecordsPo;
import com.cas.nc.questionnaire.dao.query.QstBrowseRecordsQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstBrowseRecordsDao;
import com.cas.nc.questionnaire.service.QstBrowseRecordsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class QstBrowseRecordsServiceImpl implements QstBrowseRecordsService {

    @Resource
    private QstBrowseRecordsDao qstBrowseRecordsDao;

    @Override
    public void insert(QstBrowseRecordsPo po) {
        qstBrowseRecordsDao.insert(po);
    }

    @Override
    public List<SourceAnalysisBo> selectSourceStatistics(String questionnaireId) {
        QstBrowseRecordsQuery query = new QstBrowseRecordsQuery();
        query.setQuestionnaireId(questionnaireId);
//        filterCondition(query);

        return qstBrowseRecordsDao.selectSourceStatistics(query);
    }

    @Override
    public List<ProvinceAnalysisBo> selectProvinceStatistics(String questionnaireId) {
        QstBrowseRecordsQuery query = new QstBrowseRecordsQuery();
        query.setQuestionnaireId(questionnaireId);
//        filterCondition(query);

        return qstBrowseRecordsDao.selectProvinceStatistics(query);
    }

    @Override
    public List<TimeAnalysisBo> selectDayStatistics(String questionnaireId, String beginTime, String endTime) {
        QstBrowseRecordsQuery query = new QstBrowseRecordsQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setBeginCreateTime(beginTime);
        query.setEndCreateTime(endTime);

        filterCondition(query);

        return qstBrowseRecordsDao.selectDayStatistics(query);
    }

    @Override
    public List<TimeAnalysisBo> selectWeekDayStatistics(String questionnaireId, String beginTime, String endTime) {
        QstBrowseRecordsQuery query = new QstBrowseRecordsQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setBeginCreateTime(beginTime);
        query.setEndCreateTime(endTime);

        filterCondition(query);

        return qstBrowseRecordsDao.selectWeekDayStatistics(query);
    }

    @Override
    public List<TimeAnalysisBo> selectMonthStatistics(String questionnaireId, String beginTime, String endTime) {
        QstBrowseRecordsQuery query = new QstBrowseRecordsQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setBeginCreateTime(beginTime);
        query.setEndCreateTime(endTime);

        filterCondition(query);

        return qstBrowseRecordsDao.selectMonthStatistics(query);
    }

    private void filterCondition(QstBrowseRecordsQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

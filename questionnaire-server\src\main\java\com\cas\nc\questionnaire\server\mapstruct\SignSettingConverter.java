package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.sign.QstSignSettingDto;
import com.cas.nc.questionnaire.common.vo.sign.QstSignSettingVo;
import com.cas.nc.questionnaire.dao.po.QstSignSettingPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SignSettingConverter {
    
    SignSettingConverter INSTANCE = Mappers.getMapper(SignSettingConverter.class);
    
    QstSignSettingDto voToDto(QstSignSettingVo vo);
    
    QstSignSettingVo dtoToVo(QstSignSettingDto dto);
    
    QstSignSettingDto poToDto(QstSignSettingPo po);
    
    QstSignSettingPo dtoToPo(QstSignSettingDto dto);
} 
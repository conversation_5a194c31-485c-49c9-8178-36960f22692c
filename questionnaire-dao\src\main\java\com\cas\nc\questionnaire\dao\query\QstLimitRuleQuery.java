package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;

import java.util.List;

public class QstLimitRuleQuery extends QstLimitRulePo {
    private String tableColumns;

    /*跳转类型列表*/
    private List<Integer> returnTypeList;

    private String beginTimeStr;

    private String endTimeStr;

    private List<String> questionnaireIdList;

    public List<Integer> getReturnTypeList() {
        return returnTypeList;
    }

    public void setReturnTypeList(List<Integer> returnTypeList) {
        this.returnTypeList = returnTypeList;
    }

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public String getBeginTimeStr() {
        return beginTimeStr;
    }

    public void setBeginTimeStr(String beginTimeStr) {
        this.beginTimeStr = beginTimeStr;
    }

    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public List<String> getQuestionnaireIdList() {
        return questionnaireIdList;
    }

    public void setQuestionnaireIdList(List<String> questionnaireIdList) {
        this.questionnaireIdList = questionnaireIdList;
    }
}

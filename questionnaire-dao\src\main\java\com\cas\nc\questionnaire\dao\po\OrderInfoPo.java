package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class OrderInfoPo {
    /*自增id*/
    private Long id;

    /*用户id*/
    private Long userId;

    /*外部userid*/
    private String outUserId;

    /*订单号*/
    private String orderNo;

    /*商品id*/
    private Long productId;

    /*商品描述*/
    private String productDes;

    /*订单状态，1：初始化，2：支付成功，3：支付失败*/
    private Integer status;

    /*订单金额*/
    private Long amount;

    /*下单ip*/
    private String ip;

    /*支付时间*/
    private Date payTime;

    /*支付单号*/
    private String bankNo;

    /* 预支付交易会话标识*/
    private String prePayId;

    /*支付url*/
    private String payUrl;

    /*预支付时间*/
    private Date prePayTime;

    /*错误码*/
    private String errorCode;

    /*错误信息*/
    private String errorInfo;

    /*版本号*/
    private Integer version;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOutUserId() {
        return outUserId;
    }

    public void setOutUserId(String outUserId) {
        this.outUserId = outUserId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductDes() {
        return productDes;
    }

    public void setProductDes(String productDes) {
        this.productDes = productDes;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getPrePayId() {
        return prePayId;
    }

    public void setPrePayId(String prePayId) {
        this.prePayId = prePayId;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }

    public Date getPrePayTime() {
        return prePayTime;
    }

    public void setPrePayTime(Date prePayTime) {
        this.prePayTime = prePayTime;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorInfo() {
        return errorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        this.errorInfo = errorInfo;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
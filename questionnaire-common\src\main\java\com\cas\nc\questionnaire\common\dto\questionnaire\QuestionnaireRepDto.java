package com.cas.nc.questionnaire.common.dto.questionnaire;

import java.util.Date;

public class QuestionnaireRepDto {
    /*问卷id*/
    private String questionnaireId;
    /*标题*/
    private String title;
    /*状态*/
    private Integer status;
    /*答卷数量*/
    private Integer answerCount;
    /*问卷数量*/
    private String answerCountStr;
    /*创建人*/
    private String name;
    /*账号*/
    private String userName;
    /*时间*/
    private String time;
    /*开始时间*/
    private Date beginTime;
    /*结束时间*/
    private Date endTime;
    /*账号id*/
    private Long userId;
    /*外观*/
    private String appearance;
    /*问卷来源，1：问卷平台；2：继续教育平台*/
    private Integer channel;

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getAnswerCount() {
        return answerCount;
    }

    public void setAnswerCount(Integer answerCount) {
        this.answerCount = answerCount;
    }

    public String getAnswerCountStr() {
        return answerCountStr;
    }

    public void setAnswerCountStr(String answerCountStr) {
        this.answerCountStr = answerCountStr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getAppearance() {
        return appearance;
    }

    public void setAppearance(String appearance) {
        this.appearance = appearance;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }
}

package com.cas.nc.questionnaire.server.strategyfactory;

import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.common.utils.SingletonCacheUtil;
import com.cas.nc.questionnaire.common.utils.SpringBeanLoader;
import com.cas.nc.questionnaire.server.strategy.answeroptionstrategy.AnswerOptionStrategy;

import java.util.HashMap;
import java.util.Map;

import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.*;

public class AnswerOptionStrategyFactory {
    private static AnswerOptionStrategyFactory factory = new AnswerOptionStrategyFactory();

    private static Map<TitleTypeEnum, AnswerOptionStrategy> strategyMap = new HashMap<>();

    static {
        strategyMap.put(DANXUAN, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(DUOXUAN, getBean("answerOptionOneDimensionalDuoXuanStrategy"));
//        strategyMap.put(TUPIANDANXUAN, ); 暂且不支持
//        strategyMap.put(TUPIANDUOXUAN, ); 暂且不支持
        strategyMap.put(BIAOGEDANXUAN, getBean("answerOptionTwoDimensionalXuanZeStrategy"));
        strategyMap.put(BIAOGEDUOXUAN, getBean("answerOptionTwoDimensionalXuanZeStrategy"));
        strategyMap.put(XIALAXUANZE, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(BIAOGEXIALAXUANZE, getBean("answerOptionBiaoGeXiaLaXuanZeStrategy"));
        strategyMap.put(DANHANGTIANKONG, getBean("answerOptionOneDimensionalFillBlankStrategy"));
        strategyMap.put(DUOXIANGDANHANGTIANKONG, getBean("answerOptionDuoXiangDanHangTianKongStrategy"));
        strategyMap.put(BIAOGEWENBEN, getBean("answerOptionTwoDimensionalFillBlankStrategy"));
        strategyMap.put(BIAOGESHUZHI, getBean("answerOptionTwoDimensionalFillBlankStrategy"));
        strategyMap.put(LIANGBIAO, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(JUZHENLIANGBIAO, getBean("answerOptionTwoDimensionalXuanZeStrategy"));
        strategyMap.put(JUZHENHUADONGTIAO, getBean("answerOptionTwoDimensionalXuanZeStrategy"));
        strategyMap.put(PINGFENDANXUAN, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(PINGFENDUOXUAN, getBean("answerOptionOneDimensionalDuoXuanStrategy"));
        strategyMap.put(BIZHONG, getBean("answerOptionTwoDimensionalXuanZeStrategy"));
        strategyMap.put(HUADONGTIAO, getBean("answerOptionOneDimensionalDanXuanStrategy"));
//        strategyMap.put(MIAOSHUSHUOMING, ); 不是题目
//        strategyMap.put(FENYEFU, ); 不是题目
        strategyMap.put(XINGMING, getBean("answerOptionOneDimensionalFillBlankStrategy"));
        strategyMap.put(XINGBIE, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(SHOUJI, getBean("answerOptionOneDimensionalFillBlankStrategy"));
        strategyMap.put(YOUXIANG, getBean("answerOptionOneDimensionalFillBlankStrategy"));
        strategyMap.put(RIQISHIJIAN, getBean("answerOptionOneDimensionalFillBlankStrategy"));
        strategyMap.put(NIANLIANG, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(ZHIYE, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(GONGZUONIANXIN, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(JIAOYUCHENGDU, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(BIYEYUANXIAO, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(HUNYINZHUANGKUANG, getBean("answerOptionOneDimensionalDanXuanStrategy"));
        strategyMap.put(CHENGSHIDIZHI, getBean("answerOptionOneDimensionalFillBlankStrategy"));
        strategyMap.put(DILIWEIZHI, getBean("answerOptionOneDimensionalFillBlankStrategy"));
        strategyMap.put(TONGXUNDIZHI, getBean("answerOptionOneDimensionalFillBlankStrategy"));
        strategyMap.put(PAIXU, getBean("answerOptionPaiXuStrategy"));
        strategyMap.put(WENJIAN, getBean("answerOptionOneDimensionalFillBlankStrategy"));
    }


    private static AnswerOptionStrategy getBean(String id) {
        AnswerOptionStrategy strategy = (AnswerOptionStrategy) SingletonCacheUtil.getInstance().getValueByKey(id);

        if (strategy == null) {
            strategy = SpringBeanLoader.getSpringBean(id);

            if (strategy != null) {
                SingletonCacheUtil.getInstance().putValue(id, strategy);
            }
        }
        return strategy;
    }

    public static AnswerOptionStrategyFactory getInstance() {
        return factory;
    }

    public AnswerOptionStrategy getStrategy(TitleTypeEnum typeEnum) {
        return strategyMap.get(typeEnum);
    }
}

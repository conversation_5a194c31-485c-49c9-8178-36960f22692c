package com.cas.nc.questionnaire.common.vo.analysis;

import com.cas.nc.questionnaire.common.vo.base.BaseRequestVo;

public class TimeAnalysisReqVo extends BaseRequestVo {
    private Integer analysisType;
    private Integer timeType;
    private String beginTime;
    private String endTime;

    public Integer getAnalysisType() {
        return analysisType;
    }

    public void setAnalysisType(Integer analysisType) {
        this.analysisType = analysisType;
    }

    public Integer getTimeType() {
        return timeType;
    }

    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}

package com.cas.nc.questionnaire.common.vo.mylist;

import com.cas.nc.questionnaire.common.vo.base.BaseRequestVo;

import java.util.List;

public class MyListDeleteReqVo extends BaseRequestVo {
    /*问卷名称*/
    private String name;
    /*每页行数*/
    private Integer pageSize;
    /*导向页*/
    private Integer page;
    /*问卷集合*/
    private List<String> questionnaireIdList;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public List<String> getQuestionnaireIdList() {
        return questionnaireIdList;
    }

    public void setQuestionnaireIdList(List<String> questionnaireIdList) {
        this.questionnaireIdList = questionnaireIdList;
    }
}

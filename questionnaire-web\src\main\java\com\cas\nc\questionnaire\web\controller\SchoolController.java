package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.school.SchoolQueryProvinceRepDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.vo.school.SchoolQueryProvinceRepVo;
import com.cas.nc.questionnaire.server.SchoolServer;
import com.cas.nc.questionnaire.server.mapstruct.SchoolConverter;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/questionnaire/school")
public class SchoolController extends BaseController {

    @Resource
    private SchoolServer schoolServer;

    @RequestMapping("/queryprovince")
    public ApiReturnResult queryProvince() {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        SchoolQueryProvinceRepDto repDto = schoolServer.queryProvince();
        SchoolQueryProvinceRepVo repVo = SchoolConverter.INSTANCE.to(repDto);

        result.setData(repVo);
        return result;
    }
}

package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.QstFilterRulePo;

import java.util.List;

public class QstFilterRuleQuery extends QstFilterRulePo {
    private String tableColumns;

    /*id列表*/
    private List<Long> idList;

    private List<String> filterIdList;

    public List<String> getFilterIdList() {
        return filterIdList;
    }

    public void setFilterIdList(List<String> filterIdList) {
        this.filterIdList = filterIdList;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }
}

package com.cas.nc.questionnaire.dao.nosharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.TaskConfigPo;
import com.cas.nc.questionnaire.dao.query.TaskConfigQuery;

import java.util.List;

public interface TaskConfigDao extends BaseDao<TaskConfigPo, TaskConfigQuery> {
    /**
     * 查询任务配置
     *
     * @return
     */
    List<TaskConfigPo> selectTaskConfig();
}
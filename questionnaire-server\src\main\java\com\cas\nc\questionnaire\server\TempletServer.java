package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.templet.*;

import java.util.List;

public interface TempletServer {
    List<ListCategoryRepDto> listCategory();

    List<ListTempletRepDto> listTemplet(ListTempletReqDto reqDto);

    GetTempletRepDto getTemplet(GetTempletReqDto reqDto);

    ConvertTempletRepDto convertTemplet(ConvertTempletReqDto reqDto);

    void createTemplet(CreateTempletReqDto reqDto);
}

package com.cas.nc.questionnaire.common.enums;


public enum CodeEnum {
    //*/*/*/*/*/*公共类*/*/*/*/*/*/
    SUCCESS(1000000, "success"),
    UNKNOWN_RETURN_PAGE(1000001, "系统繁忙，请稍后重试！"),
    UNKNOWN_EXCEPTION(1000002, " unknown exception"),
    FAIL(1000003, "操作失败！"),

    //*/*/*/*/*/*校验参数类*/*/*/*/*/*/*/*/
    IS_NULL(1100000, " is null"),
    SIGN_EXCEPTION(1100001, "验签异常"),
    VALIDATE_PARAM_EXCEPTION(1100002, "校验参数异常"),
    USER_PWD_ERROR(1100003, "用户名或者密码错误"),
    IP_SEGMENT_ERROR(1100004, "ip段填写有误，必须有开始和结束"),
    ILLEGAL(1100005, " illegal"),
    EMAIL_NOT_NULL(1100006, "邮箱不能为空"),
    PWD_NOT_NULL(1100007, "密码不能为空"),
    USER_NAME_NOT_NULL(1100008, "用户名不能为空"),
    VALID_CODE_NOT_NULL(1100009, "验证码不能为空"),
    USER_TYPE_EXCEPTION(1100010, "用户类型异常"),
    SEND_EMAIL_FAIL(1100011, "发送邮件失败，请稍后重试！"),
    USER_NONEXISTENT(1100012, "用户不存在，请进行用户注册！"),
    VALID_DONE(1100013, "已经激活，请勿重复激活！"),
    EMAIL_WRONGFUL(1100014, "邮箱格式有误，请检查后重新输入！"),
    VALID_CODE_ERROR(1100015, "验证码错误，请重新输入！"),
    QST_NOT_BEGIN(1100016, "问卷还未开始，此问卷将在 %s 开始！"),
    QST_NOT_USEFUL_TIME_NOT_BEGIN(1100017, "问卷还未开始，此问卷有效期从 %s 到 %s！"),
    QST_END(1100018, "问卷已经结束，此问卷已在 %s 结束！"),
    QST_NOT_USEFUL_TIME_ENDED(1100019, "问卷已经结束，此问卷有效期从 %s 到 %s！"),
    ANSWER_COUNT_LIMIT(1100020, "答卷已经达到收集上限，感谢您的作答！"),
    HOURLY_FREQUENCY_OUT(1100021, "每小时只能作答%d次，"),
    DAILY_FREQUENCY_OUT(1100022, "每天只能作答%d次，"),
    WEEKLY_FREQUENCY_OUT(1100023, "每周只能作答%d次，"),
    MONTHLY_FREQUENCY_OUT(1100024, "每月只能作答%d次，"),
    ANNUALLY_FREQUENCY_OUT(1100025, "每年只能作答%d次，"),
    IP_NO_IN_SCOPE(1100026, "您的ip不在要求范围内！"),
    ANSWER_PWD_MUST(1100027, "请输入密码！"),
    ANSWER_PWD_ERROR(1100028, "密码错误，请核对密码！"),
    NO_ANSWER(1100029, "目前暂时还没有答卷，请您将答卷收集后再做分析！"),
    CROSS_ANALYSIS_VARIABLE_LIMIT(1100030, "变量参数最多支持两组！"),
    TITLE_REPEAT(1100031, "题目重复！"),
    NO_TIME_LIMIT_OUT(1100032, "只能作答%d次！"),
    BEGIN_END_TIME_COMPARE_ERROR(1100033, "开始时间不能大于结束时间！"),
    RETAIN_ANSWER_TITLE_LIMIT(1100034, "保留答案模式下，不能在中间插入或者删减题目，只能在最后面添加！"),
    RETAIN_ANSWER_OPTION_LIMIT(1100035, "保留答案模式下，不能在中间插入或者删减选项，只能在最后面添加！"),
    MUST_BE_GREATER_THAN_ZERO(1100036, " 必须大于0！"),
    TITLE_NAME_NOT_NULL(1100037, "题目名称不能为空！"),
    TITLE_NAME_OUT_LIMIT(1100038, "题目名称字数不能超过"),
    MIAO_SHU_OUT_LIMIT(1100039, "描述说明字数不能超过"),
    OLD_PWD_IS_NULL(1100040, "旧密码不能为空！"),
    OLD_PWD_IS_ERROR(1100041, "旧密码不正确！"),
    NEW_PWD_IS_NULL(1100042, "新密码不能为空！"),
    CONFIRM_NEW_PWD_IS_ERROR(1100043, "确认新密码不能为空！"),
    CONFIRM_NEW_PWD_NOT_MATCH(1100044, "新密码与确认新密码不匹配！"),
    OLD_PWD_NEW_PWD_IDENTICAL(1100045, "新密码与旧密码不能相同！"),
    BIZ_ID_NOT_EXIST(1100046, "业务id不存在！"),
    REQUEST_INVALID_RETRY(1100047, "请求失效，请重新请求！"),
    TEXT_NULL(1100048, "请输入文本信息！"),
    PWD_LONG_EXCEPTION(1100049, "密码长度"),
    DATE_FORMAT_EXCEPTION(1100050, "日期格式有误"),
    USER_PWD_RETRY_ERROR(1100051, "密码重试次数超限5次，请一个小时后重试！"),
    USER_EXISTENT(1100052, "用户已存在！"),
    QST_SHUOMING_LIMIT(1100053, "问卷说明字数不能超过"),
    VALID_CODE_OUT_TIME(1100054, "验证码失效，请重新刷新填写！"),
    REQUEST_INVALID(1100055, "请求失效"),
    ENCRYPT_INVALID(1100056, "加密字符串无效"),
    USERID_INVALID(1100057, "用户ID无效"),
    USER_ES_ERROR(1100058, "科技云登录失败:登录信息无效或者已过期"),
    USER_ES_NOT_ERROR(1100059, "科技云登录失败:未知错误"),
    PWF_LEGAL_ERROR(1100060, "密码至少8位，且必须包含数字、字母、下划线"),


    //*/*/*/*/*/*业务*/*/*/*/*/*/*/*/
    UPDATE_EXCEPTION(1200001, " 数据更新异常, 请稍后重试！"),
    DATA_EXCEPTION(1200002, "数据异常，请联系管理员！"),
    ROUTE_CONFIG_EXCEPTION(1200003, "无法获取路由配置信息"),
    DATA_EXIST(1200004, "数据已经存在，请勿重复操作！"),
    FILTER_CONDITION_ERROR(1200005, "过滤条件有误，请联系管理员处理！"),
    TITLE_TYPE_ERROR(1200006, "题目类型有误，请联系管理员处理！"),
    STATUS_EXCEPTION(1200007, "问卷状态异常，请联系管理员处理！"),
    STATUS_EXCEPTION1(1200036, "问卷已发布，请取消发布！"),
    EMAIL_VALID(1200008, "请前往注册邮箱进行验证！"),
    VALID_SUCCESS(1200009, "恭喜您，激活成功，请前往登录！"),
    QST_NOT_EXIST(1200010, "问卷不存在！"),
    QST_UPDATING(1200011, "管理员正在编辑问卷，请稍后重试！"),
    UN_SEND_QST(1200012, "请先发送问卷！"),
    EMAIL_ADDRESS_OUT(1200013, "没有可用的邮箱"),
    PLEASE_LOGIN(1200014, "请先登录或重新登录！"),
    AMOUNT_ERROR(1200015, "支付金额有误！"),
    ORDER_NOT_EXIST(1200016, "不存在该订单！"),
    TEMPLET_NOT_EXIST(1200017, "模板不存在！"),
    TITLE_TYPE_NOT_FILL_BLANK(1200018, "题目类型不是填空题！"),
    TITLE_NOT_EXIST(1200019, "问卷中不存在该题目！"),
    TITLE_TYPE_NOT_MATCH(1200020, "题目类型与入参不匹配！"),
    PWD_MODIFY_EXCEPTION(1200021, "密码修改失败，请重试！"),
    EMAIL_REGISTERED(1200022, "该邮箱已注册！"),
    USER_REGISTERED(1200023, "该用户名已注册！"),
    TIME_TYPE_NOT_EXIST(1200024, "时间类型不存在！"),
    SHARE_REQUEST_TYPE_NOT_EXIST(1200025, "分享请求类型不存在！"),
    SHARE_TYPE_NOT_EXIST(1200026, "分享类型不存在！"),
    RULE_TYPE_NOT_EXIST(1200027, "规则类型不存在！"),
    JUDGE_TYPE_ERROR(1200028, "判断类型错误！"),
    FILE_OUT_LIMIT(1200029, "文件过大，不能超过（M）"),
    FILE_TYPE_LIMIT(1200030, "文件类型只支持："),
    FILE_NOT_EXIST(1200031, "文件不存在！"),
    TEXT_CREATE_EXCEPTION(1200032, "请根据\"格式示例\"的说明核查文本信息！"),
    EMAIL_NOT_EXIST(1200033, "该邮箱未在问卷平台未注册，请重新输入或进行注册！"),
    RESET_PWD_URL_INVALIDATE(1200034, "链接已失效，请重新发送邮件！"),
    ONECE_URL_INVALIDATE(1200035, "链接只能被打开一次，请重新发送邮件！"),
    EMAIL_USERNAME_EXIST(120037, "账号或邮箱已存在"),

    //*/*/*/*/*/*rpc接口业务类*/*/*/*/*/*/*/*/
    WECHAT_API_RETURN_EXCEPTION(1301000, "微信统一下单接口返回非成功数据"),
    WECHAT_API_EXCEPTION(1301001, "微信统一下单接口异常"),

    //*/*/*/*/*/*任务类*/*/*/*/*/*/*/*/
    CREATE_TASK_FAIL(1400001, " 任务创建失败"),
    TASK_VALIDATE_IP_FAIL(1400002, " 任务校验ip失败"),

    ;

    private final Integer key;
    private final String value;


    CodeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }


}

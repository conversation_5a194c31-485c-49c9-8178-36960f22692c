package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.utils.Assert;


public enum FilterRuleSourceTypeEnum {
    QUALITY_FILTER(1, "质量控制-筛选"),
    QST_SET_RETURN(2, "问卷设置-跳转"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    FilterRuleSourceTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static boolean filterLegal(int key) {
        for (FilterRuleSourceTypeEnum bean : values()) {
            if (bean.equals(ELSE)) {
                continue;
            }
            if (bean.key.intValue() == key) {
                return true;
            }
        }
        return false;
    }

    public static boolean filterLegalException(int key) {
        Assert.isTrue(filterLegal(key), "FilterRuleSourceType", CodeEnum.ILLEGAL);
        return true;
    }


    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

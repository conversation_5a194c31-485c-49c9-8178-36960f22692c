package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.mylist.*;

import java.util.List;


public interface MyListServer {
    /**
     * 获取我的问卷列表
     *
     * @param reqDto
     * @return
     */
    MyListRepDto list(MyListReqDto reqDto);

    /**
     * 回收站列表
     *
     * @param reqDto
     * @return
     */
    List<MyListRecycleBinListRepDto> recycleBinList(MyListRecycleBinListReqDto reqDto);

    /**
     * 删除问卷
     *
     * @param reqDto
     * @return
     */
    void delete(MyListDeleteReqDto reqDto);

    /**
     * 回收站删除
     *
     * @param reqDto
     * @return
     */
    void recycleBinDelete(MyListRecycleBinDeleteReqDto reqDto);

    /**
     * 恢复删除
     *
     * @param reqDto
     * @return
     */
    void recovery(MyListRecoveryReqDto reqDto);

    /**
     * 暂停
     *
     * @param reqDto
     * @return
     */
    void pause(MyListPauseReqDto reqDto);

    /**
     * 复制
     *
     * @param reqDto
     */
    String copy(MyListCopyReqDto reqDto);

    /**
     * 发布
     *
     * @param reqDto
     */
    void publish(MyListPublishReqDto reqDto);

    List<ExternalListRepDto> externalList(ExternalListReqDto reqDto);
}

package com.cas.nc.questionnaire.common.vo.analysis;

import java.math.BigDecimal;
import java.util.List;


public class AnalysisReportRepVo {
    private List<AnalysisReportTitleRepVo> titleList;
    private Boolean independentShowFlag;
    // 打分类题目平均分之和
    private BigDecimal scoringTitleAverageSum;

    public List<AnalysisReportTitleRepVo> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<AnalysisReportTitleRepVo> titleList) {
        this.titleList = titleList;
    }

    public Boolean getIndependentShowFlag() {
        return independentShowFlag;
    }

    public void setIndependentShowFlag(Boolean independentShowFlag) {
        this.independentShowFlag = independentShowFlag;
    }
    
    public BigDecimal getScoringTitleAverageSum() {
        return scoringTitleAverageSum;
    }
    
    public void setScoringTitleAverageSum(BigDecimal scoringTitleAverageSum) {
        this.scoringTitleAverageSum = scoringTitleAverageSum;
    }
}

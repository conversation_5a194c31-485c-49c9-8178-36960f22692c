package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.file.FileRepDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.vo.file.FileBase64ReqVo;
import com.cas.nc.questionnaire.common.vo.file.FileRepVo;
import com.cas.nc.questionnaire.dao.po.FileRelationPo;
import com.cas.nc.questionnaire.server.FileServer;
import com.cas.nc.questionnaire.server.mapstruct.FileConverter;
import com.cas.nc.questionnaire.service.FileRelationService;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.FILE_NOT_EXIST;

@RestController
@RequestMapping("/questionnaire/file")
public class FileController extends BaseController {

    @Resource
    private FileServer fileServer;
    @Resource
    private FileRelationService fileRelationService;

    /**
     * 文件上传
     *
     * @param file
     * @return
     */
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public ApiReturnResult upload(@RequestParam("fileName") MultipartFile file) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Assert.notNull(file, "param");
        FileRepDto repDto = fileServer.upload(file);

        FileRepVo repVo = FileConverter.INSTANCE.to(repDto);
        result.setData(repVo);

        return result;
    }

    /**
     * 下载
     *
     * @param fileId
     * @param response
     */
    @RequestMapping("/download/{fileId}")
    public void downloadPicture(@PathVariable("fileId") String fileId, HttpServletResponse response) throws UnsupportedEncodingException {
        Assert.notNull(fileId, "fileId");
        FileRelationPo fileRelationPo = fileRelationService.selectOne(fileId);
        Assert.notNull(fileRelationPo, FILE_NOT_EXIST);

        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileRelationPo.getFileName(),"utf-8") + fileRelationPo.getFileSuffix());
        responseFile(response, fileRelationPo.getFileUrl());
    }

    /**
     * 文件显示
     *
     * @param fileId
     */
    @RequestMapping("/show/{fileId}")
    public void showPicture(@PathVariable("fileId") String fileId, HttpServletResponse response) {
        Assert.notNull(fileId, "fileId");
        FileRelationPo filePo = fileRelationService.selectOne(fileId);
        Assert.notNull(filePo, FILE_NOT_EXIST);

        responseFile(response, filePo.getFileUrl());
    }

    @RequestMapping(value = "/fileinfo", method = RequestMethod.POST)
    public ApiReturnResult doRegister(@RequestParam("fileId") String fileId) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Assert.notNull(fileId, "fileId");
        FileRelationPo filePo = fileRelationService.selectOne(fileId);
        Assert.notNull(filePo, FILE_NOT_EXIST);

        FileRepVo repVo = FileConverter.INSTANCE.to(filePo);
        result.setData(repVo);

        return result;
    }

    /**
     * 响应输出图片文件
     *
     * @param response
     * @param fileUrl
     */
    private void responseFile(HttpServletResponse response, String fileUrl) {
        HttpClient client = HttpClients.createDefault();
        HttpGet httpget = new HttpGet(fileUrl);
        HttpResponse httpResponse = null;

        try {
            httpResponse = client.execute(httpget);
        } catch (IOException e) {
            e.printStackTrace();
        }
        HttpEntity entity = httpResponse.getEntity();
        InputStream inputStream = null;
        try {
            inputStream = entity.getContent();
        } catch (IOException e) {
            logger.error("FileController.responseFile IOException1", e);
        }
        BufferedOutputStream outs = null;
        try {
            outs = new BufferedOutputStream(response.getOutputStream());
            int lenth;
            byte[] buffer = new byte[1024]; // 图片文件流缓存池
            while ((lenth = inputStream.read(buffer)) != -1) {
                outs.write(buffer, 0, lenth);
            }
            outs.flush();
        } catch (IOException e) {
            logger.error("FileController.responseFile IOException2", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outs != null) {
                    outs.close();
                }
            } catch (IOException e) {
                logger.error("FileController.responseFile IOException3", e);
            }
        }
    }

    /**
     * 将base64图片保存本地
     * @param reqVo
     * @return
     */
    @RequestMapping(value = "/uploadBase64", method = RequestMethod.POST)
    public ApiReturnResult saveBase64Img(@RequestBody FileBase64ReqVo reqVo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        FileRepDto fileRepDto = fileServer.uploadBase64(reqVo.getBase64Img());
        result.setData(fileRepDto.getFileName());
        return result;
    }

    /**
     * 文件上传
     *
     * @param file
     * @return
     */
    @RequestMapping(value = "/uploadFile", method = RequestMethod.POST)
    public ApiReturnResult upload(@RequestParam("fileName") MultipartFile file, @RequestParam("questionnaireId") String questionnaireId) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Assert.notNull(file, "param");
        FileRepDto repDto = fileServer.upload(file, questionnaireId);

        FileRepVo repVo = FileConverter.INSTANCE.to(repDto);
        result.setData(repVo);

        return result;
    }

}

package com.cas.nc.questionnaire.common.dto.setting;

import java.util.Date;

public class SettingSetQueryRepDto {
    /*问卷id*/
    private String questionnaireId;
    /*开始时间*/
    private Date beginTime;

    /*结束时间*/
    private Date endTime;

    /*答题密码*/
    private String pwd;

    /*无效答卷提示*/
    private String invalidAnswerHint;

    /*断点续答，2：不支持，1：支持*/
    private Integer breakPoint;

    /*问卷回收数量*/
    private Integer collectNumber;

    /*设备限时类型，1：不限时，2：每小时，3：每天，4：每月，5：每年*/
    private Integer deviceLimitType;

    /*设备限制次数*/
    private Integer deviceLimitFrequency;

    /*允许答题网段ip开始*/
    private String ipStart;

    /*允许答题网段ip结束*/
    private String ipEnd;

    /*ip限时类型，1：不限时，2：每小时，3：每天，4：每月，5：每年*/
    private Integer ipLimitType;

    /*ip限制次数*/
    private Integer ipLimitFrequency;

    /*跳转提示*/
    private String returnHint;

    /*跳转url*/
    private String returnUrl;

    /*答题结束提示*/
    private String endHint;

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getInvalidAnswerHint() {
        return invalidAnswerHint;
    }

    public void setInvalidAnswerHint(String invalidAnswerHint) {
        this.invalidAnswerHint = invalidAnswerHint;
    }

    public Integer getBreakPoint() {
        return breakPoint;
    }

    public void setBreakPoint(Integer breakPoint) {
        this.breakPoint = breakPoint;
    }

    public Integer getCollectNumber() {
        return collectNumber;
    }

    public void setCollectNumber(Integer collectNumber) {
        this.collectNumber = collectNumber;
    }

    public Integer getDeviceLimitType() {
        return deviceLimitType;
    }

    public void setDeviceLimitType(Integer deviceLimitType) {
        this.deviceLimitType = deviceLimitType;
    }

    public Integer getDeviceLimitFrequency() {
        return deviceLimitFrequency;
    }

    public void setDeviceLimitFrequency(Integer deviceLimitFrequency) {
        this.deviceLimitFrequency = deviceLimitFrequency;
    }

    public String getIpStart() {
        return ipStart;
    }

    public void setIpStart(String ipStart) {
        this.ipStart = ipStart;
    }

    public String getIpEnd() {
        return ipEnd;
    }

    public void setIpEnd(String ipEnd) {
        this.ipEnd = ipEnd;
    }

    public Integer getIpLimitType() {
        return ipLimitType;
    }

    public void setIpLimitType(Integer ipLimitType) {
        this.ipLimitType = ipLimitType;
    }

    public Integer getIpLimitFrequency() {
        return ipLimitFrequency;
    }

    public void setIpLimitFrequency(Integer ipLimitFrequency) {
        this.ipLimitFrequency = ipLimitFrequency;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public String getReturnHint() {
        return returnHint;
    }

    public void setReturnHint(String returnHint) {
        this.returnHint = returnHint;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getEndHint() {
        return endHint;
    }

    public void setEndHint(String endHint) {
        this.endHint = endHint;
    }
}

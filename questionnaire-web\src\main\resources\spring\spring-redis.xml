<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<bean class="redis.clients.jedis.JedisPool" id="jedisPool">
		<constructor-arg name="poolConfig" ref="jedisPoolConfig" />
		<constructor-arg name="host" value="${redis.hostName}" />
		<constructor-arg name="port" value="${redis.port}" />
		<constructor-arg name="timeout" value="2000"/>
		<constructor-arg name="password" value="${redis.password}"/>
	</bean>

	<bean class="redis.clients.jedis.JedisPoolConfig" id="jedisPoolConfig"/>
			
</beans>
package com.cas.nc.questionnaire.common.dto.analysis;

import com.cas.nc.questionnaire.common.dto.base.BaseRequestDto;

public class DownloadFillBlankReqDto extends BaseRequestDto {
    /*题目序号*/
    private Integer serialNumber;
    /*行坐标*/
    private Integer rowNumber;
    /*列坐标*/
    private Integer columnNumber;
    /*是否过滤null*/
    private Boolean filterNull;

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getRowNumber() {
        return rowNumber;
    }

    public void setRowNumber(Integer rowNumber) {
        this.rowNumber = rowNumber;
    }

    public Integer getColumnNumber() {
        return columnNumber;
    }

    public void setColumnNumber(Integer columnNumber) {
        this.columnNumber = columnNumber;
    }

    public Boolean getFilterNull() {
        return filterNull;
    }

    public void setFilterNull(Boolean filterNull) {
        this.filterNull = filterNull;
    }
}

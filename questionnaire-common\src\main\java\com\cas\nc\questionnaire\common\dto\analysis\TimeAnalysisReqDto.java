package com.cas.nc.questionnaire.common.dto.analysis;

import com.cas.nc.questionnaire.common.dto.base.BaseRequestDto;

public class TimeAnalysisReqDto extends BaseRequestDto {
    private Integer analysisType;
    private Integer timeType;
    private String beginTime;
    private String endTime;

    public Integer getAnalysisType() {
        return analysisType;
    }

    public void setAnalysisType(Integer analysisType) {
        this.analysisType = analysisType;
    }

    public Integer getTimeType() {
        return timeType;
    }

    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}

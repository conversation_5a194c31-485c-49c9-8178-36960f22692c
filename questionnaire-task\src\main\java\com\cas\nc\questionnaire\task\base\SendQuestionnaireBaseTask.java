package com.cas.nc.questionnaire.task.base;

import com.cas.nc.questionnaire.common.enums.YnEnum;
import com.cas.nc.questionnaire.common.enums.task.TaskStatusEnum;
import com.cas.nc.questionnaire.common.enums.task.TaskTypeEnum;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.dao.po.TaskConfigPo;
import com.cas.nc.questionnaire.dao.po.TaskPo;
import com.cas.nc.questionnaire.dao.query.TaskQuery;
import com.cas.nc.questionnaire.service.TaskConfigService;
import com.cas.nc.questionnaire.service.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.utils.Constants.FIVE;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Constants.ZERO;

public abstract class SendQuestionnaireBaseTask extends BaseTask<TaskPo> {
    private static Logger logger = LoggerFactory.getLogger(SendQuestionnaireBaseTask.class);
    @Resource
    protected TaskService taskService;
    @Resource
    protected TaskConfigService taskConfigService;

    /**
     * 任务类型
     *
     * @return
     */
    protected abstract TaskTypeEnum getTaskType();

    @Override
    protected List<TaskPo> getTasks() {
        TaskConfigPo configPo = queryTaskConfigPo();
        if (!YnEnum.isY(configPo.getYn())) {
            return new ArrayList<>();
        }

        TaskQuery query = new TaskQuery();
        query.setTaskStatus(TaskStatusEnum.INITIALIZATION.key());
        query.setFailMaxNum(configPo.getFailMaxNum());
        query.setTaskType(getTaskType().key());
        query.setBatchNum(configPo.getBatchNum());
        List<TaskPo> taskList = taskService.selectExecuteTaskList(query);

        int size = taskList == null ? ZERO : taskList.size();
        logger.info("SendQuestionnaireBaseTask.getTasks taskType[{}] configInfo[{}] query[{}] resultSize[{}]", getTaskType(), JSONUtil.toJSONString(configPo), JSONUtil.toJSONString(query), size);
        return taskList;
    }

    private TaskConfigPo queryTaskConfigPo() {
        Map<Integer, TaskConfigPo> configPoMap = taskConfigService.getAllTaskConfig();
        return configPoMap.get(getTaskType().key());
    }

    @Override
    protected void unlockTask() {
        List<TaskPo> list = getUnlockTask();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        TaskConfigPo configPo = taskConfigService.getAllTaskConfig().get(getTaskType().key());

        for (TaskPo bean : list) {
            TaskQuery query = new TaskQuery();
            query.setId(bean.getId());
            if (bean.getFailNum() == null || bean.getFailNum().intValue() == ZERO) {
                query.setFailNum(ONE);
            } else {
                query.setFailNum(bean.getFailNum() + ONE);
            }
            query.setRouteId(bean.getRouteId());

            if (query.getFailNum() >= configPo.getFailMaxNum().intValue()) {
                taskService.hangTask(query);
            } else {
                taskService.unlockTask(query);
            }
        }
    }

    @Override
    protected boolean beforeProcess(TaskPo task) {
        try {
            TaskQuery query = new TaskQuery();
            query.setRouteId(task.getRouteId());
            query.setId(task.getId());

            return taskService.lockTask(query) == ONE;
        } catch (Exception e) {
            logger.error("任务锁定异常 id[{}]", task.getId(), e);
        }
        return false;
    }

    @Override
    protected boolean afterProcess(TaskPo task) {
        try {
            TaskQuery query = new TaskQuery();
            query.setRouteId(task.getRouteId());
            query.setId(task.getId());

            return taskService.updateTaskSuccess(query) == ONE;
        } catch (Exception e) {
            logger.error("任务跟新完成异常 id[{}]", task.getId(), e);
        }
        return false;
    }

    private List<TaskPo> getUnlockTask() {
        TaskConfigPo configPo = queryTaskConfigPo();
        if (!YnEnum.isY(configPo.getYn())) {
            return new ArrayList<>();
        }

        TaskQuery query = new TaskQuery();
        query.setFailMaxNum(configPo.getFailMaxNum());
        query.setBatchNum(configPo.getBatchNum());
        query.setTaskStatus(TaskStatusEnum.LOCK.key());
        query.setTaskType(getTaskType().key());
        query.setUpdateTime(DateUtil.addMinutes(new Date(), -FIVE));

        List<TaskPo> taskList = taskService.selectLockTaskList(query);
        int size = taskList == null ? ZERO : taskList.size();
        logger.info("SendQuestionnaireBaseTask.getUnlockTask taskType[{}] configInfo[{}] query[{}] resultSize[{}]", getTaskType(), JSONUtil.toJSONString(configPo), JSONUtil.toJSONString(query), size);
        return taskList;
    }

}

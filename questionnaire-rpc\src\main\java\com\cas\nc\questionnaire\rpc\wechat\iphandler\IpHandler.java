package com.cas.nc.questionnaire.rpc.wechat.iphandler;

import com.cas.nc.questionnaire.rpc.wechat.entity.ipentity.IpAreaResult;

public abstract class IpHandler {
    protected IpHandler nextHandler;

    public void setNextHandler(IpHandler nextHandler) {
        this.nextHandler = nextHandler;
    }

    public IpHandler getNextHandler() {
        return nextHandler;
    }

    public IpAreaResult execute(String ip) {
        IpAreaResult areaResult = getIpArea(ip);
        if (areaResult != null) {
            return areaResult;
        }
        if (nextHandler != null) {
            return nextHandler.execute(ip);
        }
        return null;
    }

    abstract IpAreaResult getIpArea(String ip);
}

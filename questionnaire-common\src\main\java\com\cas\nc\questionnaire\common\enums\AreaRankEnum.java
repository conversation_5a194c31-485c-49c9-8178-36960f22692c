package com.cas.nc.questionnaire.common.enums;


public enum AreaRankEnum {
    ONE(1, "一级"),
    TWO(2, "二级"),
    THREE(3, "三级"),
    FOUR(4, "四级"),
    ELSE(99, "其他");

    private final Integer key;
    private final String value;


    AreaRankEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

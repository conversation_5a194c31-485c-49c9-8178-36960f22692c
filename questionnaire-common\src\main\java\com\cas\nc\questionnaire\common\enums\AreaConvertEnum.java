package com.cas.nc.questionnaire.common.enums;


public enum AreaConvertEnum {
    BEI_JING("北京", "北京市"),
    TIAN_JING("天津", "天津市"),
    CHONG_QING("重庆", "重庆市"),
    SHANG_HAI("上海", "上海市"),
    XI_ZANG("西藏", "西藏藏族自治区"),
    NING_XIA("宁夏", "宁夏回族自治区"),
    XIN_JIANG("新疆", "新疆维吾尔族自治区"),
    GUANG_XI("广西", "广西壮族自治区"),
    NEI_MENG_GU("内蒙古", "内蒙古自治区"),
    XIANG_GANG("香港", "香港特别行政区"),
    AO_MEN("澳门", "澳门特别行政区"),
    ;

    private final String key;
    private final String value;


    AreaConvertEnum(String key, String value) {
        this.value = value;
        this.key = key;
    }

    public String value() {
        return value;
    }

    public String key() {
        return key;
    }


    public static AreaConvertEnum toEnum(String provinceName) {
        for (AreaConvertEnum areaConvertEnum : values()) {
            if (areaConvertEnum.key.contains(provinceName) || provinceName.contains(areaConvertEnum.key)) {
                return areaConvertEnum;
            }
        }

        return null;
    }

}

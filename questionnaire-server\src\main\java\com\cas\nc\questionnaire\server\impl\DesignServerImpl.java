package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.design.DesignOptionReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.MyListCopyReqDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.query.QstOptionQuery;
import com.cas.nc.questionnaire.server.DesignServer;
import com.cas.nc.questionnaire.server.MyListServer;
import com.cas.nc.questionnaire.service.QstOptionService;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Constants.TWO;
import static com.cas.nc.questionnaire.common.utils.Constants.ZERO;


@Component("designServer")
public class DesignServerImpl implements DesignServer {

    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;

    @Override
    public void designOption(DesignOptionReqDto reqDto) {
        Assert.notNull(reqDto);
        Assert.notNull(reqDto.getType(), "type");

        int result = qstQuestionnaireInfoService.updateStatus2Pause(reqDto.getQuestionnaireId(), reqDto.getUserId());
        if (result == 0) {
            QstQuestionnaireInfoPo po = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId());
            Assert.isTrue(QuestionnaireStatusEnum.isPause(po.getStatus()), CodeEnum.UPDATE_EXCEPTION);
        }

    }

}

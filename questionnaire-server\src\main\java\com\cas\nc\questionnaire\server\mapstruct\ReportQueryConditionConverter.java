package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.questionnaire.*;
import com.cas.nc.questionnaire.common.vo.questionnaire.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ReportQueryConditionConverter {

    ReportQueryConditionConverter INSTANCE = Mappers.getMapper(ReportQueryConditionConverter.class);

    ReportQueryConditionSaveReqDto to(ReportQueryConditionSaveReqVo vo);

    ReportQueryConditionSaveReqDto.RegularQuestionItemDto to(ReportQueryConditionSaveReqVo.RegularQuestionItemVo vo);

    List<ReportQueryConditionSaveReqDto.RegularQuestionItemDto> toItemDtoList(List<ReportQueryConditionSaveReqVo.RegularQuestionItemVo> voList);

    ReportQueryConditionSaveRepVo to(ReportQueryConditionSaveRepDto dto);

    // 获取查询条件详情的转换方法
    ReportQueryConditionGetReqDto to(ReportQueryConditionGetReqVo vo);

    ReportQueryConditionGetRepVo to(ReportQueryConditionGetRepDto dto);

    ReportQueryConditionGetRepVo.RegularQuestionItemVo to(ReportQueryConditionGetRepDto.RegularQuestionItemDto dto);

    List<ReportQueryConditionGetRepVo.RegularQuestionItemVo> toGetItemVoList(List<ReportQueryConditionGetRepDto.RegularQuestionItemDto> dtoList);

    // 用户获取查询条件的转换方法
    ReportQueryConditionUserReqDto to(ReportQueryConditionUserReqVo vo);

    ReportQueryConditionUserRepVo to(ReportQueryConditionUserRepDto dto);

    ReportQueryConditionUserRepVo.RegularQuestionItemVo to(ReportQueryConditionUserRepDto.RegularQuestionItemDto dto);

    List<ReportQueryConditionUserRepVo.RegularQuestionItemVo> toUserItemVoList(List<ReportQueryConditionUserRepDto.RegularQuestionItemDto> dtoList);

    /**
     * VO转DTO - 查询答题列表请求
     */
    ReportQueryAnswerListReqDto to(ReportQueryAnswerListReqVo vo);

    /**
     * DTO转VO - 查询答题列表响应
     */
    ReportQueryAnswerListRepVo to(ReportQueryAnswerListRepDto dto);
}


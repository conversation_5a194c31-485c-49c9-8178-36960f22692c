package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;


public enum TimeTypeEnum {
    DAY(1, "天"),
    WEEK(2, "周"),
    MONTH(3, "月"),
    YEAR(4, "年"),
    ;
    private final Integer key;
    private final String value;

    TimeTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static TimeTypeEnum toEnum(int key) {
        for (TimeTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.TIME_TYPE_NOT_EXIST);
    }

    public static boolean isDay(int key) {
        return DAY.key.intValue() == key;
    }

    public static boolean isDay(TimeTypeEnum timeTypeEnum) {
        return DAY.equals(timeTypeEnum);
    }

    public static boolean isWeek(int key) {
        return WEEK.key.intValue() == key;
    }

    public static boolean isWeek(TimeTypeEnum timeTypeEnum) {
        return WEEK.equals(timeTypeEnum);
    }

    public static boolean isMonth(int key) {
        return MONTH.key.intValue() == key;
    }

    public static boolean isMonth(TimeTypeEnum timeTypeEnum) {
        return MONTH.equals(timeTypeEnum);
    }

    public static boolean isYear(int key) {
        return YEAR.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

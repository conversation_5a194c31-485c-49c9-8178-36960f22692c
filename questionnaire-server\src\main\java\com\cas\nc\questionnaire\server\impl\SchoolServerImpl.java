package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.school.SchoolProvinceRepDto;
import com.cas.nc.questionnaire.common.dto.school.SchoolQueryProvinceRepDto;
import com.cas.nc.questionnaire.common.dto.school.SchoolRepDto;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.dao.po.AreaPo;
import com.cas.nc.questionnaire.dao.po.SchoolPo;
import com.cas.nc.questionnaire.server.SchoolServer;
import com.cas.nc.questionnaire.service.AreaService;
import com.cas.nc.questionnaire.service.SchoolService;
import com.cas.nc.questionnaire.service.util.LocalCacheUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.cas.nc.questionnaire.common.utils.ColumnConstants.AREA_COLUMNS_ID;
import static com.cas.nc.questionnaire.common.utils.ColumnConstants.SCHOOL_COLUMN_1;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Constants.ZERO;

@Component
public class SchoolServerImpl implements SchoolServer {

    /*下次刷新的时间*/
    private static Date PROVINCE_NEXT_UPDATE_TIME = new Date();
    /*缓存刷新时间频率，单位：小时*/
    private static final int PROVINCE_UPDATE_FREQUENCY = 4;

    @Resource
    private AreaService areaService;
    @Resource
    private SchoolService schoolService;

    @Override
    public SchoolQueryProvinceRepDto queryProvince() {
        SchoolQueryProvinceRepDto repDto = new SchoolQueryProvinceRepDto();

        if (DateUtil.compareDate(PROVINCE_NEXT_UPDATE_TIME) == ONE || LocalCacheUtil.PROVINCE_SCHOOL_LIST.size() == ZERO) {
            PROVINCE_NEXT_UPDATE_TIME = DateUtil.add(new Date(), ZERO, PROVINCE_UPDATE_FREQUENCY, ZERO, ZERO);
            reloadProvince();
        }
        repDto.setProvinceList(LocalCacheUtil.PROVINCE_SCHOOL_LIST);
        return repDto;
    }

    private void reloadProvince() {
        List<SchoolProvinceRepDto> repDtoList = new ArrayList<>();
        List<AreaPo> provinceList = areaService.selectAllProvince(AREA_COLUMNS_ID);
        ListMultimap<Long, SchoolPo> schoolListMap = getSchoolListMap();

        for (AreaPo v : provinceList) {
            List<SchoolPo> schoolPoList = schoolListMap.get(v.getId());
            if (CollectionUtils.isEmpty(schoolPoList)) {
                continue;
            }
            SchoolProvinceRepDto repDto = new SchoolProvinceRepDto();
            List<SchoolRepDto> schoolRepDtoList = new ArrayList<>();
            schoolPoList.forEach(p -> {
                SchoolRepDto schoolRepDto = new SchoolRepDto();
                schoolRepDto.setId(p.getId());
                schoolRepDto.setName(p.getSchoolName());
                schoolRepDtoList.add(schoolRepDto);
            });

            repDto.setId(v.getId());
            repDto.setName(v.getName());
            repDto.setSchoolList(schoolRepDtoList);
            repDtoList.add(repDto);
        }
        LocalCacheUtil.PROVINCE_SCHOOL_LIST.clear();
        LocalCacheUtil.PROVINCE_SCHOOL_LIST.addAll(repDtoList);
    }

    private ListMultimap<Long, SchoolPo> getSchoolListMap() {
        List<SchoolPo> schoolPoList = schoolService.selectAllEffective(SCHOOL_COLUMN_1);
        ListMultimap<Long, SchoolPo> schoolListMap = ArrayListMultimap.create();
        schoolPoList.forEach(v -> schoolListMap.put(v.getProvinceId(), v));
        return schoolListMap;
    }
}

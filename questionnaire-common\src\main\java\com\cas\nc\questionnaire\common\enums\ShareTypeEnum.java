package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;

public enum ShareTypeEnum {
    NOT_OPEN(1, "不公开"),
    PART_OPEN(2, "部分公开"),
    OPEN(3, "公开"),
    <PERSON><PERSON><PERSON>(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    ShareTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static ShareTypeEnum toEnum(int key) {
        for (ShareTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.SHARE_TYPE_NOT_EXIST);
    }

    public static ShareTypeEnum toEnumNoException(int key) {
        for (ShareTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        return null;
    }

    public static boolean isNotOpen(int key) {
        return NOT_OPEN.key.intValue() == key;
    }

    public static boolean isPartOpen(int key) {
        return PART_OPEN.key.intValue() == key;
    }

    public static boolean isOpen(int key) {
        return OPEN.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

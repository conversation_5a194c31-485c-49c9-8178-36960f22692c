package com.cas.nc.questionnaire.common.enums;


public enum YnEnum {
    Y(1, "有效"),
    N(2, "无效"),
    ELSE(99, "其他");

    private final Integer key;
    private final String value;


    YnEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

    public static boolean isY(int key){
        return Y.key.intValue() == key;
    }

}

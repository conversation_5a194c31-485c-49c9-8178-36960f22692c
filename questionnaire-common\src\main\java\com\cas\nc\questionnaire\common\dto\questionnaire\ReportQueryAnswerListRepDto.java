package com.cas.nc.questionnaire.common.dto.questionnaire;

import java.util.Date;
import java.util.List;

/**
 * 测评报告查询答题列表响应DTO
 */
public class ReportQueryAnswerListRepDto {
    
    /**
     * 答题记录列表
     */
    private List<AnswerRecordDto> answerRecords;
    
    /**
     * 答题记录项
     */
    public static class AnswerRecordDto {
        /**
         * 答题ID
         */
        private String answerId;
        
        /**
         * 问卷名称
         */
        private String questionnaireName;
        
        /**
         * 提交时间
         */
        private Date submitTime;
        
        /**
         * 提交时间字符串
         */
        private String submitTimeStr;
        
        /**
         * 答题时长（单位：秒）
         */
        private Long duration;
        
        /**
         * 来源渠道
         */
        private String source;
        
        public String getAnswerId() {
            return answerId;
        }
        
        public void setAnswerId(String answerId) {
            this.answerId = answerId;
        }
        
        public String getQuestionnaireName() {
            return questionnaireName;
        }
        
        public void setQuestionnaireName(String questionnaireName) {
            this.questionnaireName = questionnaireName;
        }
        
        public Date getSubmitTime() {
            return submitTime;
        }
        
        public void setSubmitTime(Date submitTime) {
            this.submitTime = submitTime;
        }
        
        public String getSubmitTimeStr() {
            return submitTimeStr;
        }
        
        public void setSubmitTimeStr(String submitTimeStr) {
            this.submitTimeStr = submitTimeStr;
        }
        
        public Long getDuration() {
            return duration;
        }
        
        public void setDuration(Long duration) {
            this.duration = duration;
        }
        
        public String getSource() {
            return source;
        }
        
        public void setSource(String source) {
            this.source = source;
        }
    }
    
    public List<AnswerRecordDto> getAnswerRecords() {
        return answerRecords;
    }
    
    public void setAnswerRecords(List<AnswerRecordDto> answerRecords) {
        this.answerRecords = answerRecords;
    }
}
package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.QstEmailPo;

public class QstEmailQuery extends QstEmailPo {
    private String tableColumns;
    private Integer oldStatus;


    /*每页行数*/
    private Integer pageSize;

    /*开始索引*/
    private int startIndex;

    private String addresseesLike;

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getOldStatus() {
        return oldStatus;
    }

    public void setOldStatus(Integer oldStatus) {
        this.oldStatus = oldStatus;
    }

    public String getAddresseesLike() {
        return addresseesLike;
    }

    public void setAddresseesLike(String addresseesLike) {
        this.addresseesLike = addresseesLike;
    }
}

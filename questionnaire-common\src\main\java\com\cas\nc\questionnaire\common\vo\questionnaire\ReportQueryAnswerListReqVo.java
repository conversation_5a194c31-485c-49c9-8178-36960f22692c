package com.cas.nc.questionnaire.common.vo.questionnaire;

import java.util.List;

/**
 * 测评报告查询答题列表请求VO
 */
public class ReportQueryAnswerListReqVo {
    
    /**
     * 问卷ID
     */
    private String questionnaireId;
    
    /**
     * 查询条件列表
     */
    private List<QueryConditionVo> queryConditions;
    
    /**
     * 查询条件项
     */
    public static class QueryConditionVo {
        /**
         * 题目序号
         */
        private Integer titleSerialNumber;
        
        /**
         * 查询值
         */
        private String queryValue;
        
        public Integer getTitleSerialNumber() {
            return titleSerialNumber;
        }
        
        public void setTitleSerialNumber(Integer titleSerialNumber) {
            this.titleSerialNumber = titleSerialNumber;
        }
        
        public String getQueryValue() {
            return queryValue;
        }
        
        public void setQueryValue(String queryValue) {
            this.queryValue = queryValue;
        }
    }
    
    public String getQuestionnaireId() {
        return questionnaireId;
    }
    
    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }
    
    public List<QueryConditionVo> getQueryConditions() {
        return queryConditions;
    }
    
    public void setQueryConditions(List<QueryConditionVo> queryConditions) {
        this.queryConditions = queryConditions;
    }
}
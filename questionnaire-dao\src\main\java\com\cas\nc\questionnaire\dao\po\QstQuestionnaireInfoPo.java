package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class QstQuestionnaireInfoPo {
    /*自增id*/
    private Long id;

    /*问卷id*/
    private String questionnaireId;

    /*用户id*/
    private Long userId;

    /*第三方用户id*/
    private String outUserId;

    /*标题*/
    private String title;

    /*描述*/
    private String des;

    /*开始时间*/
    private Date beginTime;

    /*结束时间*/
    private Date endTime;

    /*状态，1：待发布，2：已发布*/
    private Integer status;

    /*控制状态，1：运行，2：暂停，3：标记删除*/
    private Integer controlStatus;

    /*业务id*/
    private Long bizId;

    /*业务单号*/
    private String bizNo;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    /*外观*/
    private String appearance;

    /*问卷来源，1：问卷平台；34：继续教育平台*/
    private Integer channel;

    /*答卷类型*/
    private Integer questionnaireType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOutUserId() {
        return outUserId;
    }

    public void setOutUserId(String outUserId) {
        this.outUserId = outUserId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getControlStatus() {
        return controlStatus;
    }

    public void setControlStatus(Integer controlStatus) {
        this.controlStatus = controlStatus;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getAppearance() {
        return appearance;
    }

    public void setAppearance(String appearance) {
        this.appearance = appearance;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getQuestionnaireType() {
        return questionnaireType;
    }

    public void setQuestionnaireType(Integer questionnaireType) {
        this.questionnaireType = questionnaireType;
    }
}

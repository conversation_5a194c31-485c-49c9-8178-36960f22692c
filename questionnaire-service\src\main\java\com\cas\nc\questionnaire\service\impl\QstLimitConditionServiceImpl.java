package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.po.QstLimitConditionPo;
import com.cas.nc.questionnaire.dao.query.QstLimitConditionQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstLimitConditionDao;
import com.cas.nc.questionnaire.service.QstLimitConditionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class QstLimitConditionServiceImpl implements QstLimitConditionService {
    private static Logger logger = LoggerFactory.getLogger(QstLimitConditionServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstLimitConditionDao qstLimitConditionDao;

    @Override
    public int insert(QstLimitConditionPo qstLimitConditionPo) {
        Assert.notNull(qstLimitConditionPo.getUserId(), "userId");
        return qstLimitConditionDao.insert(qstLimitConditionPo);
    }

    @Override
    public int delete(QstLimitConditionQuery query) {
        validateUserId(query);
        return qstLimitConditionDao.delete(query);
    }

    @Override
    public int delete(String questionnaireId, String limitConditionId, Long userId) {
        QstLimitConditionQuery query = new QstLimitConditionQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setLimitConditionId(limitConditionId);
        query.setUserId(userId);
        return delete(query);
    }

    @Override
    public List<QstLimitConditionPo> selectList(QstLimitConditionQuery query) {
        validateUserId(query);
        return qstLimitConditionDao.selectList(query);
    }

    @Override
    public List<QstLimitConditionPo> selectList(String questionnaireId) {
        QstLimitConditionQuery query = new QstLimitConditionQuery();
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);
        return selectList(query);
    }

    @Override
    public QstLimitConditionPo selectOne(QstLimitConditionQuery query) {
        validateUserId(query);
        return qstLimitConditionDao.selectOne(query);
    }

    @Override
    public QstLimitConditionPo selectOne(String limitConditionId, Long userId) {
        QstLimitConditionQuery query = new QstLimitConditionQuery();
        query.setLimitConditionId(limitConditionId);
        query.setUserId(userId);
        return selectOne(query);
    }

    @Override
    public int update(QstLimitConditionQuery query) {
        validateUserId(query);
        return qstLimitConditionDao.update(query);
    }

    private void validateUserId(QstLimitConditionQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }

    private void filterCondition(QstLimitConditionQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

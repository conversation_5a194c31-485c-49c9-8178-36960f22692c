package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstReportQueryConditionTitlePo;

import java.util.List;

/**
 * 测评报告查询条件题目Service接口
 */
public interface QstReportQueryConditionTitleService {
    
    /**
     * 插入记录
     * @param po 记录对象
     * @return 插入后的ID
     */
    Long insert(QstReportQueryConditionTitlePo po);
    
    /**
     * 批量插入记录
     * @param poList 记录列表
     * @return 影响行数
     */
    int insertBatch(List<QstReportQueryConditionTitlePo> poList);
    
    /**
     * 根据条件ID查询题目列表
     * @param conditionId 条件ID
     * @return 题目列表
     */
    List<QstReportQueryConditionTitlePo> selectByConditionId(Long conditionId);
    
    /**
     * 根据条件ID删除题目
     * @param conditionId 条件ID
     * @return 影响行数
     */
    int deleteByConditionId(Long conditionId);
} 
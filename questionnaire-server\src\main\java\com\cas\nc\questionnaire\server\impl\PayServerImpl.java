package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.mylist.MyListQuestionnaireRepDto;
import com.cas.nc.questionnaire.common.dto.pay.*;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.OrderStatusEnum;
import com.cas.nc.questionnaire.common.enums.TimeTypeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.*;
import com.cas.nc.questionnaire.dao.po.OrderInfoPo;
import com.cas.nc.questionnaire.dao.po.ProductAmountConfigPo;
import com.cas.nc.questionnaire.dao.query.OrderInfoQuery;
import com.cas.nc.questionnaire.rpc.wechat.WeChatRpc;
import com.cas.nc.questionnaire.rpc.wechat.entity.wechat.UnifiedOrderParam;
import com.cas.nc.questionnaire.rpc.wechat.entity.wechat.UnifiedOrderReturnResult;
import com.cas.nc.questionnaire.server.PayServer;
import com.cas.nc.questionnaire.server.mapstruct.PayConverter;
import com.cas.nc.questionnaire.service.OrderInfoService;
import com.cas.nc.questionnaire.service.ProductAmountConfigService;
import com.cas.nc.questionnaire.service.UserInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.AMOUNT_ERROR;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.DATA_EXCEPTION;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.ORDER_NOT_EXIST;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Constants.PRODUCT_DESC;
import static com.cas.nc.questionnaire.common.utils.MoneyUtils.changeF2YBig;
import static com.cas.nc.questionnaire.common.utils.SignGenerateUtil.getSign;


@Component
public class PayServerImpl implements PayServer {
    private static Logger logger = LoggerFactory.getLogger(PayServerImpl.class);

    @Resource
    private WeChatRpc weChatRpc;
    @Resource
    private OrderInfoService orderInfoService;
    @Resource
    private ProductAmountConfigService productAmountConfigService;
    @Resource
    private UserInfoService userInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void weChatNotify(String paramXml, Map<String, String> paramMap) {
        try {
            Map<String, String> parseMap;

            if (StringUtil.isNotBlank(paramXml)) {
                parseMap = weChatRpc.wechatNotify(paramXml);
            } else {
                parseMap = paramMap;
            }
            Assert.isTrue("SUCCESS".equals(parseMap.get("return_code")));
            String orderNo = parseMap.get("out_trade_no");
            OrderInfoPo orderInfoPo = orderInfoService.selectOne(orderNo);
            Assert.notNull(orderInfoPo, "orderInfo");

            OrderInfoQuery query = new OrderInfoQuery();
            query.setVersion(getMaxVersion(orderInfoPo) + 1);
            query.setOrderNo(orderNo);
            if ("SUCCESS".equals(parseMap.get("result_code"))) {
                long amount = Long.parseLong(parseMap.get("total_fee"));
                Assert.isTrue(orderInfoPo.getAmount().longValue() == amount, "订单金额不一致");
                query.setPayTime(DateUtil.parseDate(DateUtil.C_TIME_YYYYMMDDHHMMSS, parseMap.get("time_end")));
                query.setBankNo(parseMap.get("transaction_id"));
                query.setStatus(OrderStatusEnum.SUCCESS.key());
                query.setErrorCode(parseMap.get("result_code"));
            } else if ("FAIL".equals(parseMap.get("result_code"))) {
                query.setStatus(OrderStatusEnum.FAIL.key());
                query.setErrorCode(parseMap.get("err_code"));
                String errorInfo = parseMap.get("err_code_des");
                if (errorInfo.length() > 127) {
                    errorInfo = errorInfo.substring(0, 127);
                }
                query.setErrorInfo(errorInfo);
            }

            boolean isRepeat = updateOrderInfo(query);
            if (!isRepeat) {
                updateUserInfo(orderInfoPo.getProductId(), orderInfoPo.getUserId());
            }
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("PayServerImpl.weChatNotify Exception param[{}]", paramXml, e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
    }

    private void updateUserInfo(Long productId, Long userId) {
        ProductAmountConfigPo productAmountConfigPo = productAmountConfigService.selectOne(productId);
        userInfoService.addMembershipDuration(userId, productAmountConfigPo.getTimeCount(), TimeTypeEnum.toEnum(productAmountConfigPo.getTimeType()));
    }

    private Integer getMaxVersion(OrderInfoPo orderInfoPo) {
        OrderInfoQuery query = new OrderInfoQuery();
        query.setUserId(orderInfoPo.getUserId());
        query.setProductId(orderInfoPo.getProductId());
        query.setAmount(orderInfoPo.getAmount());

        return orderInfoService.selectMaxVersion(query);
    }

    @Override
    public List<ListProductRepDto> listProduct(Long userId) throws Exception {
        List<ListProductRepDto> resultList = new ArrayList<>();

        List<ProductAmountConfigPo> amountConfigPoList = productAmountConfigService.selectEffectiveList();
        List<ProductAmountConfigPo> productAmountConfigTopList = SafeUtil.of(amountConfigPoList).stream()
                .sorted(Comparator.comparing(ProductAmountConfigPo::getPriority))
                .limit(3)
                .collect(Collectors.toList());

        for (ProductAmountConfigPo bean : productAmountConfigTopList) {

            ListProductRepDto repDto = new ListProductRepDto();
            repDto.setProductId(bean.getId());
            repDto.setActivityCopywriting(bean.getActivityCopywriting());
            repDto.setPriority(bean.getPriority());
            repDto.setSalesPricing(changeF2YBig(bean.getSalesPricing()));
            repDto.setSellingPrice(changeF2YBig(bean.getSellingPrice()));
            repDto.setSign(getSign(repDto.getSellingPrice(), repDto.getProductId(), userId));

            resultList.add(repDto);
        }
        return resultList;
    }

    @Override
    public GetPayUrlRepDto getPayUrl(GetPayUrlReqDto reqDto) {
        validateProduct(reqDto);
        OrderInfoPo orderInfoPo = getOrder(reqDto);

        if (orderInfoPo != null) {
            if (orderInfoPo.getPayTime() != null) {
                if (orderInfoPo.getPayTime().getTime() < System.currentTimeMillis()) {
                    return constructPayUrl(orderInfoPo.getOrderNo(), orderInfoPo.getPrePayId());
                }
            }
            UnifiedOrderReturnResult prePayResult = weChatRpc.unifiedOrder(constructUnifiedOrderParam(reqDto, orderInfoPo.getOrderNo()));
            if (prePayResult.getOrderPaid() == null || !prePayResult.getOrderPaid()) {
                int updateResult = orderInfoService.updatePrePay(orderInfoPo.getOrderNo(), prePayResult.getCodeUrl(), prePayResult.getPrepayId());
                Assert.isTrue(updateResult == 1, DATA_EXCEPTION);

                return constructPayUrl(orderInfoPo.getOrderNo(), prePayResult.getCodeUrl());
            }
            weChatNotify(null, weChatRpc.orderQuery(orderInfoPo.getOrderNo()));
        }
        return getNewOrderPayUrl(reqDto);
    }

    @Override
    public GetPayStatusRepDto getPayStatus(GetPayStatusReqDto reqDto) {
        OrderInfoQuery query = new OrderInfoQuery();
        query.setOrderNo(reqDto.getOrderNo());
        query.setUserId(reqDto.getUserId());

        OrderInfoPo orderInfoPo = orderInfoService.selectOne(query);
        Assert.notNull(orderInfoPo, ORDER_NOT_EXIST);

        GetPayStatusRepDto result = new GetPayStatusRepDto();
        if (OrderStatusEnum.SUCCESS.key().intValue() == orderInfoPo.getStatus().intValue()) {
            result.setPayStatus(true);
        } else if (OrderStatusEnum.FAIL.key().intValue() == orderInfoPo.getStatus().intValue()) {
            result.setPayStatus(false);
        }

        return result;
    }

    @Override
    public ListOrderRepDto listOrder(ListOrderReqDto reqDto) {
        ListOrderRepDto result = new ListOrderRepDto();

        PaginateUtils<OrderRepTo> repDtoPaginate = new PaginateUtils<>(reqDto.getPage(), reqDto.getPageSize());
        OrderInfoQuery query = new OrderInfoQuery();
        query.setUserId(reqDto.getUserId());
        query.setStatus(OrderStatusEnum.SUCCESS.key());

        int count = orderInfoService.selectCount(query);
        repDtoPaginate.setRecordTotal(count);
        query.setPageSize(repDtoPaginate.getPageSize());
        query.setStartIndex(repDtoPaginate.getStartIndex());

        List<OrderInfoPo> orderInfoPoList = orderInfoService.selectListByPage(query);
        List<OrderRepTo> orderRepToList = PayConverter.INSTANCE.toListOrder(orderInfoPoList);

        result.setTotal(count);
        result.setOrderList(orderRepToList);
        return result;
    }

    private void validateProduct(GetPayUrlReqDto reqDto) {
        ProductAmountConfigPo productAmountConfigPo = productAmountConfigService.selectOne(reqDto.getProductId());
        Assert.notNull(productAmountConfigPo, "productAmountConfig");
        Assert.isTrue(MoneyUtils.changeY2FBig(reqDto.getSellingPrice()) == productAmountConfigPo.getSalesPricing(), AMOUNT_ERROR);
    }

    private void generateOrder(GetPayUrlReqDto reqDto, String orderNo, UnifiedOrderReturnResult prePayResult2) {
        OrderInfoPo po = new OrderInfoPo();
        po.setOrderNo(orderNo);
        po.setAmount(MoneyUtils.changeY2FBig(reqDto.getSellingPrice()));
        po.setProductId(reqDto.getProductId());
        po.setStatus(OrderStatusEnum.INITIALIZATION.key());
        po.setPrePayId(prePayResult2.getPrepayId());
        po.setPayUrl(prePayResult2.getCodeUrl());
        po.setPrePayTime(DateUtil.addMinutes(new Date(), 90));
        po.setVersion(1);
        po.setUserId(reqDto.getUserId());

        orderInfoService.insert(po);
    }

    private GetPayUrlRepDto constructPayUrl(String orderNo, String payUrl) {
        GetPayUrlRepDto result = new GetPayUrlRepDto();
        result.setOrderNo(orderNo);
        result.setPayUrl(payUrl);

        return result;
    }

    private OrderInfoPo getOrder(GetPayUrlReqDto reqDto) {
        OrderInfoQuery query = new OrderInfoQuery();
        query.setUserId(reqDto.getUserId());
        query.setProductId(reqDto.getProductId());
        query.setAmount(MoneyUtils.changeY2FBig(reqDto.getSellingPrice()));
        query.setStatus(OrderStatusEnum.INITIALIZATION.key());
        query.setVersion(1);

        return orderInfoService.selectOne(query);
    }

    private UnifiedOrderParam constructUnifiedOrderParam(GetPayUrlReqDto reqDto, String orderNo) {
        UnifiedOrderParam param = new UnifiedOrderParam();
        param.setBody(PRODUCT_DESC);
        param.setProductId(reqDto.getProductId().toString());
        param.setOutTradeNo(orderNo);
        param.setTotalFee(Math.toIntExact(MoneyUtils.changeY2FBig(reqDto.getSellingPrice())));

        return param;
    }

    private boolean updateOrderInfo(OrderInfoQuery query) {
        Boolean isRepeat = false;
        try {
            int result = orderInfoService.update(query);
            Assert.isTrue(result == ONE);
        } catch (ServerException e) {
            OrderInfoQuery orderInfoQuery = new OrderInfoQuery();
            orderInfoQuery.setOrderNo(query.getOrderNo());
            OrderInfoPo orderInfoPo = orderInfoService.selectOne(orderInfoQuery);
            Assert.isTrue(query.getStatus().intValue() == orderInfoPo.getStatus().intValue());
            isRepeat = true;
        } catch (Exception e) {
            logger.error("PayServerImpl.updateOrderInfo Exception param[{}]", JSONUtil.toJSONString2(query), e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
        return isRepeat;
    }

    public GetPayUrlRepDto getNewOrderPayUrl(GetPayUrlReqDto reqDto) {
        String orderNo = SequenceUtil.getInstance().generateOrderNo(reqDto.getUserId().toString());
        UnifiedOrderReturnResult prePayResult2 = weChatRpc.unifiedOrder(constructUnifiedOrderParam(reqDto, orderNo));

        generateOrder(reqDto, orderNo, prePayResult2);

        return constructPayUrl(orderNo, prePayResult2.getCodeUrl());
    }
}

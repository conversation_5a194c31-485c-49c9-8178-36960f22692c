package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.AreaPo;

import java.util.List;


public class AreaQuery extends AreaPo {
    private String tableColumns;

    private List<Long> idList;

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }
}

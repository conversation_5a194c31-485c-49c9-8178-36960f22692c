package com.cas.nc.questionnaire.dao.nosharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.QstSignSettingPo;
import org.apache.ibatis.annotations.Param;

public interface QstSignSettingDao extends BaseDao<QstSignSettingPo, QstSignSettingPo> {
    
    /**
     * 根据问卷ID查询签到设置
     * 
     * @param questionnaireId 问卷ID
     * @return 签到设置
     */
    QstSignSettingPo selectByQuestionnaireId(@Param("questionnaireId") String questionnaireId);
    
    /**
     * 根据问卷ID更新签到设置
     * 
     * @param signSetting 签到设置
     * @return 影响行数
     */
    int updateByQuestionnaireId(@Param("item") QstSignSettingPo signSetting);
} 
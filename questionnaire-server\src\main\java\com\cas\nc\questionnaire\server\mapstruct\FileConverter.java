package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.file.FileRepDto;
import com.cas.nc.questionnaire.common.utils.TypeConversionWorker;
import com.cas.nc.questionnaire.common.vo.file.FileRepVo;
import com.cas.nc.questionnaire.dao.po.FileRelationPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = TypeConversionWorker.class)
public interface FileConverter {
    FileConverter INSTANCE = Mappers.getMapper(FileConverter.class);

    FileRepVo to(FileRepDto repDto);

    FileRepVo to(FileRelationPo filePo);
}
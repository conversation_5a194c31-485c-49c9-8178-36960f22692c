package com.cas.nc.questionnaire.common.vo.transmit;

public class QstEmailVo {
    /**邮件id*/
    private String emailId;

    /**问卷id*/
    private String questionnaireId;

    /**邮件主题*/
    private String emailTitle;

    /**发件人*/
    private String sender;

    /**回复地址*/
    private String replyAddress;

    /**收件人，用分号分隔*/
    private String addressees;

    /**邮件正文*/
    private String emailContent;

    private String content;

    private String title;

    /**1、允许断点续答 2、只允许从邮件链接访问此问卷 3、允许多次填写*/
    private Integer settingType;

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public String getEmailTitle() {
        return emailTitle;
    }

    public void setEmailTitle(String emailTitle) {
        this.emailTitle = emailTitle;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReplyAddress() {
        return replyAddress;
    }

    public void setReplyAddress(String replyAddress) {
        this.replyAddress = replyAddress;
    }

    public String getAddressees() {
        return addressees;
    }

    public void setAddressees(String addressees) {
        this.addressees = addressees;
    }

    public String getEmailContent() {
        return emailContent;
    }

    public void setEmailContent(String emailContent) {
        this.emailContent = emailContent;
    }

    public Integer getSettingType() {
        return settingType;
    }

    public void setSettingType(Integer settingType) {
        this.settingType = settingType;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}

package com.cas.nc.questionnaire.common.dto.analysis;

import java.math.BigDecimal;
import java.util.List;


public class AnalysisReportRepDto {
    private List<AnalysisReportTitleRepDto> titleList;
    // 打分类题目平均分之和
    private BigDecimal scoringTitleAverageSum;

    public List<AnalysisReportTitleRepDto> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<AnalysisReportTitleRepDto> titleList) {
        this.titleList = titleList;
    }
    
    public BigDecimal getScoringTitleAverageSum() {
        return scoringTitleAverageSum;
    }
    
    public void setScoringTitleAverageSum(BigDecimal scoringTitleAverageSum) {
        this.scoringTitleAverageSum = scoringTitleAverageSum;
    }
}

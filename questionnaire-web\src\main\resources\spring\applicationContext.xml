<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task"
       default-autowire="byName"
       xsi:schemaLocation="http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		">

    <context:component-scan base-package="com.cas.nc.questionnaire"/>
    <context:property-placeholder location="classpath:config/*.properties" ignore-unresolvable="true"/>

    <context:annotation-config/>
    <aop:aspectj-autoproxy proxy-target-class="false"/>
    <task:annotation-driven executor="executorWithCallerRunsPolicy"/>
    <task:executor id="executorWithCallerRunsPolicy" keep-alive="10" pool-size="50-500" queue-capacity="10000"
                   rejection-policy="CALLER_RUNS"/>


    <import resource="classpath:spring/shardingContext.xml"/>
    <import resource="classpath:spring/spring-oauth.xml"/>
    <import resource="classpath:spring/spring-redis.xml"/>
    <import resource="classpath:spring/spring-task.xml"/>
    <import resource="classpath:spring/jms.xml"/>
</beans>
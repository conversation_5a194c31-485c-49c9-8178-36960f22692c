package com.cas.nc.questionnaire.server.strategy.answeroptionstrategy;

import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateReqDto;
import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_TITLE_SPLIT;
import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.constructBaseAnswerOption;

@Component("answerOptionBiaoGeXiaLaXuanZeStrategy")
public class AnswerOptionBiaoGeXiaLaXuanZeStrategy implements AnswerOptionStrategy {
    @Override
    public List<AnswerOptionPo> construct(AnswerCreateReqDto reqDto, String answerId, Map.Entry<String, Object> dataEntry, TitleTypeEnum titleTypeEnum) {
        List<AnswerOptionPo> resultList = new ArrayList<>();
        String k = dataEntry.getKey();
        Object v = dataEntry.getValue();
        String[] titles = k.split(ANSWER_TITLE_SPLIT);

        AnswerOptionPo biaoGeXiaLaAnswerOption = constructBaseAnswerOption(reqDto, answerId, titles[0], titleTypeEnum);
        biaoGeXiaLaAnswerOption.setRowNumber(Integer.valueOf(titles[1]) + 1);
        biaoGeXiaLaAnswerOption.setColumnNumber(Integer.valueOf(titles[2]) + 1);
        biaoGeXiaLaAnswerOption.setSerialNumber((Integer) v + 1);

        resultList.add(biaoGeXiaLaAnswerOption);
        return resultList;
    }
}

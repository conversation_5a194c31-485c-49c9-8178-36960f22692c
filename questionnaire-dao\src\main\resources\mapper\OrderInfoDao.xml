<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.OrderInfoDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.OrderInfoPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="out_user_id" jdbcType="VARCHAR" property="outUserId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_des" jdbcType="VARCHAR" property="productDes"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="amount" jdbcType="BIGINT" property="amount"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="bank_no" jdbcType="VARCHAR" property="bankNo"/>
        <result column="pre_pay_id" jdbcType="VARCHAR" property="prePayId"/>
        <result column="pay_url" jdbcType="VARCHAR" property="payUrl"/>
        <result column="pre_pay_time" jdbcType="TIMESTAMP" property="prePayTime"/>
        <result column="error_code" jdbcType="VARCHAR" property="errorCode"/>
        <result column="error_info" jdbcType="VARCHAR" property="errorInfo"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,user_id,out_user_id,order_no,product_id,product_des,status,
    amount,ip,pay_time,bank_no,pre_pay_id,pay_url,
    pre_pay_time,error_code,error_info,version,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.userId and '' != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.outUserId and '' != item.outUserId">and out_user_id = #{item.outUserId}</if>
            <if test="null != item.orderNo and '' != item.orderNo">and order_no = #{item.orderNo}</if>
            <if test="null != item.productId and '' != item.productId">and product_id = #{item.productId}</if>
            <if test="null != item.productDes and '' != item.productDes">and product_des = #{item.productDes}</if>
            <if test="null != item.status and '' != item.status">and status = #{item.status}</if>
            <if test="null != item.amount and '' != item.amount">and amount = #{item.amount}</if>
            <if test="null != item.ip and '' != item.ip">and ip = #{item.ip}</if>
            <if test="null != item.payTime and '' != item.payTime">and pay_time = #{item.payTime}</if>
            <if test="null != item.bankNo and '' != item.bankNo">and bank_no = #{item.bankNo}</if>
            <if test="null != item.prePayId and '' != item.prePayId">and pre_pay_id = #{item.prePayId}</if>
            <if test="null != item.payUrl and '' != item.payUrl">and pay_url = #{item.payUrl}</if>
            <if test="null != item.prePayTime and '' != item.prePayTime">and pre_pay_time = #{item.prePayTime}</if>
            <if test="null != item.errorCode and '' != item.errorCode">and error_code = #{item.errorCode}</if>
            <if test="null != item.errorInfo and '' != item.errorInfo">and error_info = #{item.errorInfo}</if>
            <if test="null != item.version and '' != item.version">and version = #{item.version}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from order_info
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from order_info
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from order_info
        <include refid="sql_where"/>
    </select>

    <sql id="sql_insert_columns">
        insert into order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.outUserId">out_user_id,</if>
            <if test="null != item.orderNo">order_no,</if>
            <if test="null != item.productId">product_id,</if>
            <if test="null != item.productDes">product_des,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.amount">amount,</if>
            <if test="null != item.ip">ip,</if>
            <if test="null != item.payTime">pay_time,</if>
            <if test="null != item.bankNo">bank_no,</if>
            <if test="null != item.prePayId">pre_pay_id,</if>
            <if test="null != item.payUrl">pay_url,</if>
            <if test="null != item.prePayTime">pre_pay_time,</if>
            <if test="null != item.errorCode">error_code,</if>
            <if test="null != item.errorInfo">error_info,</if>
            <if test="null != item.version">version,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.outUserId">#{item.outUserId},</if>
            <if test="null != item.orderNo">#{item.orderNo},</if>
            <if test="null != item.productId">#{item.productId},</if>
            <if test="null != item.productDes">#{item.productDes},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.amount">#{item.amount},</if>
            <if test="null != item.ip">#{item.ip},</if>
            <if test="null != item.payTime">#{item.payTime},</if>
            <if test="null != item.bankNo">#{item.bankNo},</if>
            <if test="null != item.prePayId">#{item.prePayId},</if>
            <if test="null != item.payUrl">#{item.payUrl},</if>
            <if test="null != item.prePayTime">#{item.prePayTime},</if>
            <if test="null != item.errorCode">#{item.errorCode},</if>
            <if test="null != item.errorInfo">#{item.errorInfo},</if>
            <if test="null != item.version">#{item.version},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.outUserId">out_user_id = values(out_user_id),</if>
            <if test="null != item.orderNo">order_no = values(order_no),</if>
            <if test="null != item.productId">product_id = values(product_id),</if>
            <if test="null != item.productDes">product_des = values(product_des),</if>
            <if test="null != item.status">status = values(status),</if>
            <if test="null != item.amount">amount = values(amount),</if>
            <if test="null != item.ip">ip = values(ip),</if>
            <if test="null != item.payTime">pay_time = values(pay_time),</if>
            <if test="null != item.bankNo">bank_no = values(bank_no),</if>
            <if test="null != item.prePayId">pre_pay_id = values(pre_pay_id),</if>
            <if test="null != item.payUrl">pay_url = values(pay_url),</if>
            <if test="null != item.prePayTime">pre_pay_time = values(pre_pay_time),</if>
            <if test="null != item.errorCode">error_code = values(error_code),</if>
            <if test="null != item.errorInfo">error_info = values(error_info),</if>
            <if test="null != item.version">version = values(version),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update order_info
        <set>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.outUserId">out_user_id = #{item.outUserId},</if>
            <if test="null != item.orderNo">order_no = #{item.orderNo},</if>
            <if test="null != item.productId">product_id = #{item.productId},</if>
            <if test="null != item.productDes">product_des = #{item.productDes},</if>
            <if test="null != item.status">status = #{item.status},</if>
            <if test="null != item.amount">amount = #{item.amount},</if>
            <if test="null != item.ip">ip = #{item.ip},</if>
            <if test="null != item.payTime">pay_time = #{item.payTime},</if>
            <if test="null != item.bankNo">bank_no = #{item.bankNo},</if>
            <if test="null != item.prePayId">pre_pay_id = #{item.prePayId},</if>
            <if test="null != item.payUrl">pay_url = #{item.payUrl},</if>
            <if test="null != item.prePayTime">pre_pay_time = #{item.prePayTime},</if>
            <if test="null != item.errorCode">error_code = #{item.errorCode},</if>
            <if test="null != item.errorInfo">error_info = #{item.errorInfo},</if>
            <if test="null != item.version">version = #{item.version},</if>
        </set>
        <where>
            <choose>
                <when test="null != item.orderNo">
                    and order_no = #{item.orderNo}
                </when>
                <when test="null != item.oldStatus">
                    and status = #{item.oldStatus}
                </when>
                <otherwise>
                    and id = #{item.id}
                </otherwise>
            </choose>
        </where>
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from order_info
        <include refid="sql_where"/>
    </delete>

    <select id="selectMaxVersion" resultType="java.lang.Integer">
        select max(version)
        from order_info
        where user_id = #{item.userId}
        and product_id = #{item.productId}
        and amount = #{item.amount}
    </select>

    <select id="selectListByPage" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from order_info
        <where>
            <if test="null != item.userId and '' != item.userId">
                and user_id = #{item.userId}
            </if>
            <if test="null != item.status and '' != item.status">
                and status = #{item.status}
            </if>
            <if test="null != item.statusList and '' != item.statusList">
                and status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
        order by pay_time desc
        <choose>
            <when test="null != item.startIndex and null != item.pageSize">
                limit #{item.startIndex}, #{item.pageSize}
            </when>
            <otherwise>
                limit 1
            </otherwise>
        </choose>
    </select>

    <select id="selectCount" resultType="int">
        select count(1)
        from order_info
        <where>
            <if test="null != item.userId and '' != item.userId">
                and user_id = #{item.userId}
            </if>
            <if test="null != item.status and '' != item.status">
                and status = #{item.status}
            </if>
            <if test="null != item.statusList and '' != item.statusList">
                and status in
                <foreach collection="item.statusList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
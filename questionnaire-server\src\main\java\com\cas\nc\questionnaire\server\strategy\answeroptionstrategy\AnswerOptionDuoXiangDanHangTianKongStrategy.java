package com.cas.nc.questionnaire.server.strategy.answeroptionstrategy;

import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateReqDto;
import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_TITLE_SPLIT;
import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.constructBaseAnswerOption;

@Component("answerOptionDuoXiangDanHangTianKongStrategy")
public class AnswerOptionDuoXiangDanHangTianKongStrategy implements AnswerOptionStrategy {
    @Override
    public List<AnswerOptionPo> construct(AnswerCreateReqDto reqDto, String answerId, Map.Entry<String, Object> dataEntry, TitleTypeEnum titleTypeEnum) {
        List<AnswerOptionPo> resultList = new ArrayList<>();
        String k = dataEntry.getKey();
        Object v = dataEntry.getValue();
        String[] titles = k.split(ANSWER_TITLE_SPLIT);

        AnswerOptionPo duoXiangDanHangTianKongPo = constructBaseAnswerOption(reqDto, answerId, titles[0], titleTypeEnum);
        duoXiangDanHangTianKongPo.setRowNumber(Integer.valueOf(titles[1]) + 1);
        duoXiangDanHangTianKongPo.setWriteContent(v.toString());
        resultList.add(duoXiangDanHangTianKongPo);

        return resultList;
    }
}

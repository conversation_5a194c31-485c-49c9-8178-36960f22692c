package com.cas.nc.questionnaire.common.dto.user;

import java.util.List;

public class ESLoginResult {
    private String access_token;//认证的标识信息可用其访问UMT中的其他资源
    private String refresh_token;//当access_token过期时可拿此token去重新换取access_token
    private String expires_in;//过期时间，示例返回值是3600，天知道是什么时间单位，没用到
    private String error;//错误标识
    private String error_description;//错误描述
    private String userInfo;//用户信息 string形式，json格式
    private UserInfo userInfoParse;//用户信息解析后结果

    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public String getRefresh_token() {
        return refresh_token;
    }

    public void setRefresh_token(String refresh_token) {
        this.refresh_token = refresh_token;
    }

    public String getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(String expires_in) {
        this.expires_in = expires_in;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getError_description() {
        return error_description;
    }

    public void setError_description(String error_description) {
        this.error_description = error_description;
    }

    public String getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(String userInfo) {
        this.userInfo = userInfo;
    }

    public UserInfo getUserInfoParse() {
        return userInfoParse;
    }

    public void setUserInfoParse(UserInfo userInfoParse) {
        this.userInfoParse = userInfoParse;
    }

    public static class UserInfo {
        private String umtId;//umtId：对应umt里面的id号
        private String truename;//truename：用户真实姓名
        private String type;//type：账户所属范围umt、coremail、uc
        private String securityEmail;//securityEmail：密保邮箱
        private String cstnetIdStatus;//cstnetIdStatus：主账户激活状态，即邮箱验证状态， 可选值：active-已激活，temp-未激活。应用可根据此状态判断是否允许该用户登录。
        private String cstnetId;//cstnetId：用户主邮箱
        /**
         * passwordType是登录时所用的密码类型，接入应用可根据密码类型判断是否允许该登录进入本应用，具体类型如下：
         * l password_core_mail：已开通中科院邮箱账号的中国科技网通行证账号
         * 2 password_umt：在中国科技网通行证平台上自助注册的用户，没有开通中科院邮箱账号。
         * 3 Password_third_party_xxx：中国科技网通行证平台已支持与中国科技网通行证绑定的第三方登录账号，xxx为对应的应用代码，目前支持的第三方登录账号有：
         *   3.1 geo（password_third_party_geo）：来自于第三方国家地球系统科学数据共享平台的账号；
         *   3.2 uaf（password_third_party_uaf）：来自于科技云认证联盟（UAF）的账号；
         *   3.3 cas_hq（password_cas_hq）：来自于第三方院机关统一认证平台的账号。
         */
        private String passwordType;//passwordType: 登录的密码类型
        private List<String> secondaryEmails;//secondaryEmails：辅助邮箱邮箱，暂不开放设置辅助邮箱的api

        public String getUmtId() {
            return umtId;
        }

        public void setUmtId(String umtId) {
            this.umtId = umtId;
        }

        public String getTruename() {
            return truename;
        }

        public void setTruename(String truename) {
            this.truename = truename;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getSecurityEmail() {
            return securityEmail;
        }

        public void setSecurityEmail(String securityEmail) {
            this.securityEmail = securityEmail;
        }

        public String getCstnetIdStatus() {
            return cstnetIdStatus;
        }

        public void setCstnetIdStatus(String cstnetIdStatus) {
            this.cstnetIdStatus = cstnetIdStatus;
        }

        public String getCstnetId() {
            return cstnetId;
        }

        public void setCstnetId(String cstnetId) {
            this.cstnetId = cstnetId;
        }

        public String getPasswordType() {
            return passwordType;
        }

        public void setPasswordType(String passwordType) {
            this.passwordType = passwordType;
        }

        public List<String> getSecondaryEmails() {
            return secondaryEmails;
        }

        public void setSecondaryEmails(List<String> secondaryEmails) {
            this.secondaryEmails = secondaryEmails;
        }
    }
}

package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.analysis.*;
import com.cas.nc.questionnaire.common.enums.*;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.PaginateUtils;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.common.utils.ThreadUtil;
import com.cas.nc.questionnaire.dao.bo.*;
import com.cas.nc.questionnaire.dao.po.*;
import com.cas.nc.questionnaire.dao.query.AnswerInfoQuery;
import com.cas.nc.questionnaire.dao.query.AnswerOptionQuery;
import com.cas.nc.questionnaire.server.AnalysisServer;
import com.cas.nc.questionnaire.server.AnswerServer;
import com.cas.nc.questionnaire.server.mapstruct.ReportConverter;
import com.cas.nc.questionnaire.service.*;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.AnalysisTypeEnum.isCompletionRate;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.NO_ANSWER;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.UNKNOWN_RETURN_PAGE;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.*;
import static com.cas.nc.questionnaire.common.enums.TimeTypeEnum.*;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.*;
import static com.cas.nc.questionnaire.common.utils.Constants.*;
import static com.cas.nc.questionnaire.common.utils.DateUtil.*;
import static com.cas.nc.questionnaire.common.utils.NumberUtil.*;

@Component
public class AnalysisServerImpl implements AnalysisServer {

    @Resource
    private QstTitleService qstTitleService;
    @Resource
    private QstOptionService qstOptionService;
    @Resource
    private AnswerOptionService answerOptionService;
    @Resource
    private AnswerInfoService answerInfoService;
    @Resource
    private AreaService areaService;
    @Resource
    private QstBrowseRecordsService qstBrowseRecordsService;
    @Resource
    private AnswerServer answerServer;

    @Override
    public AnalysisReportRepDto report(AnalysisReportReqDto reqDto) {
        AnalysisReportRepDto result = new AnalysisReportRepDto();

        List<AnalysisReportTitleRepDto> titleList = getAnalysisTitleList(reqDto);
        result.setTitleList(titleList);
        
        // 计算打分类题目平均分之和
        BigDecimal scoringTitleAverageSum = BigDecimal.ZERO;
        for (AnalysisReportTitleRepDto title : titleList) {
            Integer typeInt = title.getTypeInt();
            // 检查是否是1300系列题型（打分题类型）
            if (typeInt != null && typeInt >= 1300 && typeInt < 1400) {
                BigDecimal averageValue = title.getAverageValue();
                if (averageValue != null) {
                    scoringTitleAverageSum = scoringTitleAverageSum.add(averageValue);
                }
            }
        }
        result.setScoringTitleAverageSum(scoringTitleAverageSum);
        
        return result;
    }

    @Override
    public AnalysisReportCrossAnalysisRepDto crossAnalysis(AnalysisReportCrossAnalysisReqDto reqDto) {
        AnalysisReportCrossAnalysisRepDto result = new AnalysisReportCrossAnalysisRepDto();
        List<CrossAnalysisTitleRepDto> resultTitleList = new ArrayList<>();

        List<QstTitlePo> titlePoList = qstTitleService.selectList(reqDto.getQuestionnaireId());
        Map<Integer, QstTitlePo> titleMap = titlePoList.stream().collect(Collectors.toMap(QstTitlePo::getSerialNumber, v -> v, (k1, k2) -> k2));

        List<QstOptionPo> qstOptionPoList = qstOptionService.selectList(reqDto.getQuestionnaireId(), reqDto.getUserId());
        Map<Integer, List<QstOptionPo>> optionListMap = qstOptionPoList.stream().collect(Collectors.groupingBy(QstOptionPo::getTitleSerialNumber));

        if (reqDto.getIndependentVariableTitleList().size() == 1) {
            resultTitleList.addAll(getOneIndependentVariableResult(reqDto, titleMap, optionListMap));
        } else if (reqDto.getIndependentVariableTitleList().size() == 2) {
            resultTitleList.addAll(getTwoIndependentVariableResult(reqDto, titleMap, optionListMap));
        }

        result.setTitleList(resultTitleList);
        return result;
    }

    @Override
    public AnalysisListFillBlankRepDto listFillBlank(AnalysisListFillBlankReqDto reqDto) {
        AnalysisListFillBlankRepDto result = new AnalysisListFillBlankRepDto();
        List<FillBlankRepDto> fillBlankRepDtoList = new ArrayList<>();

        List<AnswerOptionPo> answerOptionPoList;
        List<AnswerInfoPo> answerInfoPoList;
        if (reqDto.getFilterNull()) {
            PaginateUtils<AnswerOptionPo> optionPoPaginate = listAnswerOptionPo(reqDto);
            if (optionPoPaginate.getRecordTotal() == 0) {
                return result;
            }

            answerOptionPoList = optionPoPaginate.getData();
            List<String> answerIdList = SafeUtil.of(answerOptionPoList).stream().map(AnswerOptionPo::getAnswerId).collect(Collectors.toList());
            answerInfoPoList = answerInfoService.selectList(reqDto.getQuestionnaireId(), answerIdList);

            result.setCount(optionPoPaginate.getRecordTotal());
        } else {
            PaginateUtils<AnswerInfoPo> answerInfoPoPaginate = listAnswerInfoPo(reqDto);
            if (answerInfoPoPaginate.getRecordTotal() == 0) {
                return result;
            }

            answerInfoPoList = answerInfoPoPaginate.getData();
            List<String> answerIdList = SafeUtil.of(answerInfoPoList).stream().map(AnswerInfoPo::getAnswerId).collect(Collectors.toList());
            answerOptionPoList = listAnswerOptionPo(reqDto, answerIdList);

            result.setCount(answerInfoPoPaginate.getRecordTotal());
        }

        Map<String, AnswerOptionPo> optionPoMap = SafeUtil.of(answerOptionPoList).stream().collect(Collectors.toMap(AnswerOptionPo::getAnswerId, v -> v));

        SafeUtil.of(answerInfoPoList).forEach(v -> {
            FillBlankRepDto repDto = new FillBlankRepDto();
            repDto.setAnswerId(v.getAnswerId());
            repDto.setAnswerTime(v.getCreateTime());
            repDto.setSource(SourceEnum.toEnum(v.getSource()).value());
            AnswerOptionPo answerOptionPo = optionPoMap.get(v.getAnswerId());
            if (answerOptionPo != null) {
                repDto.setContent(answerOptionPo.getWriteContent());
            }

            fillBlankRepDtoList.add(repDto);
        });

        result.setFillBlankRepDtoList(fillBlankRepDtoList);
        return result;
    }

    @Override
    public List<AnalysisReportTitleRepDto> getAnalysisTitleList(AnalysisReportReqDto reqDto) {
        List<AnalysisReportTitleRepDto> titleList = new ArrayList<>();

        List<QstTitlePo> qstTitlePoList = getQstTitlePosList(reqDto);
        qstTitlePoList = qstTitlePoList.stream().filter(v -> !NOT_SHOW_REPORT_LIST.contains(v.getType())).sorted(Comparator.comparing(QstTitlePo::getGlobalOrder)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(qstTitlePoList)) {
            return new ArrayList<>();
        }

        Map<Integer, List<QstOptionPo>> optionListMultiMap = getOptionPoListMultimap(reqDto);

        Map<Integer, AnswerAnalysisBo> analysisTitleMap = getAnalysisTitleMap(reqDto);

        List<Future<AnalysisReportTitleRepDto>> callableList = new ArrayList<>();
        qstTitlePoList.forEach(title -> {
            callableList.add(ThreadUtil.reportExecutor.submit((Callable) () -> getAnalysisReportTitleRepDto(reqDto, optionListMultiMap, analysisTitleMap, title)));
        });

        for (Future<AnalysisReportTitleRepDto> future : callableList) {
            try {
                titleList.add(future.get());
            } catch (Exception e) {
                throw new ServerException(UNKNOWN_RETURN_PAGE);
            }
        }

        return titleList;
    }

    public AnalysisReportTitleRepDto getAnalysisReportTitleRepDto(AnalysisReportReqDto reqDto, Map<Integer, List<QstOptionPo>> optionListMultiMap, Map<Integer, AnswerAnalysisBo> analysisTitleMap, QstTitlePo title) {
        AnalysisReportTitleRepDto titleRepDto = constructTitleRepDto(analysisTitleMap, title);
        List<QstOptionPo> optionPoList = optionListMultiMap.get(title.getSerialNumber());
        if (isHuaDongTiao(title.getType())) {
            constructHuaDongTiaoTitleRepDto(titleRepDto, reqDto);
        } else if (isOneDimensionalFillBlank(title.getType()) || isTwoDimensionalFillBlank(title.getType())) {
            titleRepDto.setOptionList(constructFillBlankOptionList(optionPoList));
        } else if (isAnalysisOneDimensional(title.getType())) {
            if (isPaiXu(title.getType())) {
                titleRepDto.setOptionList(constructPaiXuOptionList(reqDto, title, optionPoList, titleRepDto.getTotal()));
            } else {
                titleRepDto.setOptionList(constructOneDimensionalOptionList(reqDto, title, optionPoList, titleRepDto.getTotal()));
                if (reqDto.getAnswerIdList() != null && reqDto.getAnswerIdList().size() == 0) {
                    titleRepDto.setAverageValue(BigDecimal.ZERO);
                } else {
                    if (isPingFen(title.getType())) {
                        constructPingFen(titleRepDto, optionPoList);
                    } else if (isLiangBiao(title.getType())) {
                        constructLiangBiao(titleRepDto, optionPoList);
                    }
                }
            }
        } else if (isAnalysisTwoDimensional(title.getType())) {
            if (isJuZhenHuaDongTiao(title.getType())) {
                titleRepDto.setOptionList(constructJuZhenHuaDongTiaoOptionList(reqDto, title, optionPoList, titleRepDto));
            } else if (isBiZhong(title.getType())) {
                titleRepDto.setOptionList(constructBiZhongOptionList(reqDto, title, optionPoList, titleRepDto.getTotal()));
            } else {
                List<AnalysisReportOptionRepDto> tempList = constructTwoDimensionalOptionList(reqDto, title, optionPoList, titleRepDto.getTotal());
                if ((isBiaoGeDanXuan(title.getType()) || isBiaoGeDuoXuan(title.getType())) && !CollectionUtils.isEmpty(tempList)) {
                    tempList = tempList.stream().sorted(Comparator.comparing(AnalysisReportOptionRepDto::getRowNumber).thenComparing(AnalysisReportOptionRepDto::getColumnNumber)).collect(Collectors.toList());
                }
                titleRepDto.setOptionList(tempList);
            }
        } else if (isAnalysisThreeDimensional(title.getType())) {
            titleRepDto.setOptionList(constructThreeDimensionalOptionList(reqDto, title, optionPoList, titleRepDto.getTotal()));
        }
        return titleRepDto;
    }

    @Override
    public SourceAnalysisRepDto sourceAnalysis(SourceAnalysisReqDto reqDto) {
        SourceAnalysisRepDto result = new SourceAnalysisRepDto();
        List<SourceRepDto> sourceRepDtoList = new ArrayList<>();
        int totalCount = 0;
        Map<Integer, SourceAnalysisBo> browseRecordsMap = new HashMap<>();
        if (isCompletionRate(reqDto.getAnalysisType())) {
            List<SourceAnalysisBo> browseRecordsList = qstBrowseRecordsService.selectSourceStatistics(reqDto.getQuestionnaireId());
            browseRecordsMap = SafeUtil.of(browseRecordsList).stream().collect(Collectors.toMap(SourceAnalysisBo::getSource, v -> v));
        } else {
            totalCount = answerInfoService.selectCount(reqDto.getUserId(), reqDto.getQuestionnaireId());
        }
        List<SourceAnalysisBo> sourceAnalysisBoList = answerInfoService.selectSourceStatistics(reqDto.getQuestionnaireId(), reqDto.getUserId());

        Map<Integer, SourceAnalysisBo> finalBrowseRecordsMap = browseRecordsMap;
        int finalTotalCount = totalCount;
        sourceAnalysisBoList.forEach(v -> {
            SourceRepDto sourceRepDto = new SourceRepDto();
            sourceRepDto.setAnswerTotal(v.getTotal());
            sourceRepDto.setSourceName(SourceEnum.toEnum(v.getSource()).value());
            if (isCompletionRate(reqDto.getAnalysisType())) {
                SourceAnalysisBo sourceAnalysisBo = finalBrowseRecordsMap.get(v.getSource());
                if (sourceAnalysisBo == null) {
                    sourceRepDto.setPercentage(percentageCalculation(v.getTotal(), v.getTotal()));
                    sourceRepDto.setBrowseRecordsTotal(v.getTotal());
                } else {
                    sourceRepDto.setPercentage(percentageCalculation(v.getTotal(), sourceAnalysisBo.getTotal()));
                    sourceRepDto.setBrowseRecordsTotal(sourceAnalysisBo.getTotal());
                }
            } else {
                sourceRepDto.setPercentage(percentageCalculation(v.getTotal(), finalTotalCount));
            }

            sourceRepDtoList.add(sourceRepDto);
        });
        result.setSourceList(sourceRepDtoList);

        return result;
    }

    @Override
    public ProvinceAnalysisRepDto provinceAnalysis(ProvinceAnalysisReqDto reqDto) {
        ProvinceAnalysisRepDto result = new ProvinceAnalysisRepDto();
        List<ProvinceRepDto> provinceRepDtoList = new ArrayList<>();

        int totalCount = 0;
        Map<Long, ProvinceAnalysisBo> browseRecordsMap = new HashMap<>();
        if (isCompletionRate(reqDto.getAnalysisType())) {
            List<ProvinceAnalysisBo> browseRecordsList = qstBrowseRecordsService.selectProvinceStatistics(reqDto.getQuestionnaireId());
            browseRecordsMap = SafeUtil.of(browseRecordsList).stream().collect(Collectors.toMap(ProvinceAnalysisBo::getProvince, v -> v));
        } else {
            totalCount = answerInfoService.selectCount(reqDto.getUserId(), reqDto.getQuestionnaireId());
        }

        List<ProvinceAnalysisBo> provinceAnalysisBoList = answerInfoService.selectProvinceStatistics(reqDto.getQuestionnaireId(), reqDto.getUserId());
        int finalTotalCount = totalCount;
        Map<Long, ProvinceAnalysisBo> finalBrowseRecordsMap = browseRecordsMap;
        provinceAnalysisBoList.forEach(v -> {
            ProvinceRepDto provinceRepDto = new ProvinceRepDto();
            provinceRepDto.setAnswerTotal(v.getTotal());
            AreaPo areaPo = areaService.selectProvince(v.getProvince());
            if (areaPo == null) {
                provinceRepDto.setProvinceName("其他");
            } else {
                provinceRepDto.setProvinceName(areaPo.getName());
            }
            if (isCompletionRate(reqDto.getAnalysisType())) {
                ProvinceAnalysisBo provinceAnalysisBo = finalBrowseRecordsMap.get(v.getProvince());
                if (provinceAnalysisBo == null) {
                    provinceRepDto.setPercentage(percentageCalculation(v.getTotal(), v.getTotal()));
                    provinceRepDto.setBrowseRecordsTotal(v.getTotal());
                } else {
                    provinceRepDto.setPercentage(percentageCalculation(v.getTotal(), provinceAnalysisBo.getTotal()));
                    provinceRepDto.setBrowseRecordsTotal(provinceAnalysisBo.getTotal());
                }
                result.setAnswerTotal(result.getAnswerTotal() == null ? provinceRepDto.getAnswerTotal() : result.getAnswerTotal() + provinceRepDto.getAnswerTotal());
                result.setBrowseRecordsTotal(result.getBrowseRecordsTotal() == null ? provinceRepDto.getBrowseRecordsTotal() : result.getBrowseRecordsTotal() + provinceRepDto.getBrowseRecordsTotal());
            } else {
                provinceRepDto.setPercentage(percentageCalculation(v.getTotal(), finalTotalCount));
            }

            provinceRepDtoList.add(provinceRepDto);
        });
        result.setProvinceList(provinceRepDtoList);
        if (result.getAnswerTotal() != null && result.getBrowseRecordsTotal() != null) {
            result.setPercentage(percentageCalculation(result.getAnswerTotal(), result.getBrowseRecordsTotal()));
        }

        return result;
    }

    @Override
    public TimeAnalysisRepDto timeAnalysis(TimeAnalysisReqDto reqDto) {
        TimeAnalysisRepDto result = new TimeAnalysisRepDto();
        List<TimeRepDto> timeList = new ArrayList<>();
        int totalCount = 0;

        Map<String, TimeAnalysisBo> browseRecordsMap = new HashMap<>();
        if (isCompletionRate(reqDto.getAnalysisType())) {
            List<TimeAnalysisBo> browseRecordsList = listTimeAnalysis(reqDto, false);
            browseRecordsMap = SafeUtil.of(browseRecordsList).stream().collect(Collectors.toMap(TimeAnalysisBo::getTimeMark, v -> v));
        } else {
            totalCount = answerInfoService.selectCount(reqDto.getUserId(), reqDto.getQuestionnaireId());
        }

        List<TimeAnalysisBo> timeAnalysisBoList = listTimeAnalysis(reqDto, true);
        int finalTotalCount = totalCount;
        Map<String, TimeAnalysisBo> finalBrowseRecordsMap = browseRecordsMap;
        timeAnalysisBoList.forEach(v -> {
            TimeRepDto timeRepDto = new TimeRepDto();
            timeRepDto.setAnswerTotal(v.getTotal());
            timeRepDto.setTime(v.getTimeMark());
            if (isCompletionRate(reqDto.getAnalysisType())) {
                TimeAnalysisBo timeAnalysisBo = finalBrowseRecordsMap.get(v.getTimeMark());
                if (timeAnalysisBo == null) {
                    timeRepDto.setPercentage(percentageCalculation(v.getTotal(), v.getTotal()));
                    timeRepDto.setBrowseRecordsTotal(v.getTotal());
                } else {
                    timeRepDto.setPercentage(percentageCalculation(v.getTotal(), timeAnalysisBo.getTotal()));
                    timeRepDto.setBrowseRecordsTotal(timeAnalysisBo.getTotal());
                }
            } else {
                timeRepDto.setPercentage(percentageCalculation(v.getTotal(), finalTotalCount));
            }

            timeList.add(timeRepDto);
        });
        result.setTimeList(timeList);

        return result;
    }

    @Override
    public ListCheckedRepDto listChecked(ListCheckedReqDto reqDto) {
        ListCheckedRepDto result = new ListCheckedRepDto();
        List<CheckedRepDto> checkedList = new ArrayList<>();

        if (isProvince(reqDto.getRuleType())) {
            List<Long> provinceIdList = answerInfoService.selectProvinceGroupBy(reqDto.getQuestionnaireId(), reqDto.getUserId());
            List<AreaPo> provinceList = areaService.selectProvinceList(provinceIdList);
            SafeUtil.of(provinceList).forEach(v -> {
                CheckedRepDto checkedRepDto = new CheckedRepDto();
                checkedRepDto.setKey(v.getId().toString());
                checkedRepDto.setValue(v.getName());

                checkedList.add(checkedRepDto);
            });
        } else if (isCity(reqDto.getRuleType())) {
            List<Long> cityIdList = answerInfoService.selectCityGroupBy(reqDto.getQuestionnaireId(), reqDto.getUserId());
            List<AreaPo> cityList = areaService.selectCityList(cityIdList);
            SafeUtil.of(cityList).forEach(v -> {
                CheckedRepDto checkedRepDto = new CheckedRepDto();
                checkedRepDto.setKey(v.getId().toString());
                checkedRepDto.setValue(v.getName());

                checkedList.add(checkedRepDto);
            });
        } else if (isSourceChannel(reqDto.getRuleType())) {
            List<Long> sourceIdList = answerInfoService.selectSourceGroupBy(reqDto.getQuestionnaireId(), reqDto.getUserId());
            SafeUtil.of(sourceIdList).forEach(v -> {
                CheckedRepDto checkedRepDto = new CheckedRepDto();
                checkedRepDto.setKey(v.toString());
                checkedRepDto.setValue(SourceEnum.toEnum(Math.toIntExact(v)).value());

                checkedList.add(checkedRepDto);
            });
        }

        result.setCheckedList(checkedList);

        return result;
    }

    @Override
    public AnalysisReportRepDto customReport(CustomReportReqDto reqDto) {
        AnalysisReportRepDto result = new AnalysisReportRepDto();

        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setStatus(AnswerStatusEnum.FINISH.key());

        answerServer.parseRule(reqDto.getRuleType(), reqDto.getJudgeType(), reqDto.getContent(), query);

        List<String> answerIdList = answerOptionService.selectAnswerIdList(query);
        AnalysisReportReqDto reportReqDto = new AnalysisReportReqDto();
        reportReqDto.setUserId(reqDto.getUserId());
        reportReqDto.setQuestionnaireId(reqDto.getQuestionnaireId());
        reportReqDto.setAnswerIdList(answerIdList);

        List<AnalysisReportTitleRepDto> analysisTitleList = getAnalysisTitleList(reportReqDto);
        result.setTitleList(analysisTitleList);
        
        // 计算打分类题目平均分之和
        BigDecimal scoringTitleAverageSum = BigDecimal.ZERO;
        for (AnalysisReportTitleRepDto title : analysisTitleList) {
            Integer typeInt = title.getTypeInt();
            // 检查是否是1300系列题型（打分题类型）
            if (typeInt != null && typeInt >= 1300 && typeInt < 1400) {
                BigDecimal averageValue = title.getAverageValue();
                if (averageValue != null) {
                    scoringTitleAverageSum = scoringTitleAverageSum.add(averageValue);
                }
            }
        }
        result.setScoringTitleAverageSum(scoringTitleAverageSum);

        return result;
    }

    private List<TimeAnalysisBo> listTimeAnalysis(TimeAnalysisReqDto reqDto, boolean isAnswer) {
        if (isDay(reqDto.getTimeType())) {
            String endTime = format(addDays(parseDate(YYYYMMDD, reqDto.getEndTime()), 1), YYYYMMDD);
            if (isAnswer) {
                return answerInfoService.selectDayStatistics(reqDto.getQuestionnaireId(), reqDto.getBeginTime(), endTime);
            }
            return qstBrowseRecordsService.selectDayStatistics(reqDto.getQuestionnaireId(), reqDto.getBeginTime(), endTime);
        } else if (isWeek(reqDto.getTimeType())) {
            if (isAnswer) {
                return answerInfoService.selectWeekDayStatistics(reqDto.getQuestionnaireId(), reqDto.getBeginTime(), reqDto.getEndTime());
            }
            return qstBrowseRecordsService.selectWeekDayStatistics(reqDto.getQuestionnaireId(), reqDto.getBeginTime(), reqDto.getEndTime());
        } else if (isMonth(reqDto.getTimeType())) {
            String endTime = format(addMonth(parseDate(YYYYMM, reqDto.getEndTime()), 1), YYYYMM);
            if (isAnswer) {
                return answerInfoService.selectMonthStatistics(reqDto.getQuestionnaireId(), reqDto.getBeginTime(), endTime);
            }
            return qstBrowseRecordsService.selectMonthStatistics(reqDto.getQuestionnaireId(), reqDto.getBeginTime(), endTime);
        }

        return new ArrayList<>();
    }

    private void constructHuaDongTiaoTitleRepDto(AnalysisReportTitleRepDto titleRepDto, AnalysisReportReqDto reqDto) {
        if (reqDto.getAnswerIdList() != null && reqDto.getAnswerIdList().size() == 0) {
            titleRepDto.setTotalValue(BigDecimal.valueOf(0));
            titleRepDto.setAverageValue(BigDecimal.valueOf(0));
            return;
        }

        AnswerAnalysisBo answerAnalysisBo = answerOptionService.selectHuaDongTiaoAnalysis(reqDto.getQuestionnaireId(), titleRepDto.getHao(), reqDto.getUserId(), reqDto.getAnswerIdList());
        if (answerAnalysisBo == null) {
            titleRepDto.setTotalValue(BigDecimal.valueOf(0));
            titleRepDto.setAverageValue(BigDecimal.valueOf(0));
        } else {
            titleRepDto.setTotalValue(setScale(answerAnalysisBo.getTotalValue()));
            titleRepDto.setAverageValue(setScale(answerAnalysisBo.getAverageValue()));
        }
    }

    private void constructPingFen(AnalysisReportTitleRepDto titleRepDto, List<QstOptionPo> optionPoList) {
        Map<Integer, Integer> optionTotalMap = titleRepDto.getOptionList().stream().collect(Collectors.toMap(AnalysisReportOptionRepDto::getSerialNumber, AnalysisReportOptionRepDto::getTotal));
        AtomicReference<Long> totalScore = new AtomicReference<>(0L);
        optionPoList.forEach(v -> {
            if (YnEnum.Y.key().equals(v.getScoreType())) {
                totalScore.updateAndGet(v1 -> v1 + (v.getScore() * optionTotalMap.get(v.getSerialNumber())));
            }
        });

        titleRepDto.setAverageValue(avgCalculation(totalScore.get(), titleRepDto.getTotal()));
    }

    private void constructLiangBiao(AnalysisReportTitleRepDto titleRepDto, List<QstOptionPo> optionPoList) {
        AtomicReference<Long> totalScore = new AtomicReference<>(0L);
        Map<Integer, QstOptionPo> optionPoMap = optionPoList.stream().collect(Collectors.toMap(QstOptionPo::getSerialNumber, v -> v));
        SafeUtil.of(titleRepDto.getOptionList()).stream().filter(v -> v.getTotal() != null && v.getTotal() > 0).forEach(v -> {
            totalScore.updateAndGet(v1 -> v1 + (v.getTotal() * optionPoMap.get(v.getSerialNumber()).getScore()));
        });
        if (totalScore.get() == 0 || titleRepDto.getTotal() == 0) {
            titleRepDto.setAverageValue(BigDecimal.ZERO);
        } else {
            titleRepDto.setAverageValue(avgCalculation(totalScore.get(), titleRepDto.getTotal()));
        }
    }

    private List<AnalysisReportOptionRepDto> constructBiZhongOptionList(AnalysisReportReqDto reqDto, QstTitlePo title, List<QstOptionPo> optionPoList, Integer titleAnswerTotal) {
        List<AnalysisReportOptionRepDto> optionList = new ArrayList<>();

        if (reqDto.getAnswerIdList() != null && reqDto.getAnswerIdList().size() == 0) {
            optionPoList.forEach(option -> {
                AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
                optionRepDto.setTotal(0);
                optionRepDto.setPercentage(BigDecimal.valueOf(0));

                optionList.add(optionRepDto);
            });
            return optionList;
        }

        List<AnswerAnalysisBo> oneDimensionalAnalysisList = answerOptionService.selectBiZhongAnalysis(reqDto.getQuestionnaireId(), title.getSerialNumber(), reqDto.getUserId(), reqDto.getAnswerIdList());
        Map<Integer, AnswerAnalysisBo> oneDimensionalAnalysisMap = Maps.uniqueIndex(oneDimensionalAnalysisList, AnswerAnalysisBo::getRowNumber);
        optionPoList.forEach(option -> {
            AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
            AnswerAnalysisBo answerAnalysisBo = oneDimensionalAnalysisMap.get(option.getSerialNumber());

            if (answerAnalysisBo == null) {
                optionRepDto.setTotal(0);
                optionRepDto.setPercentage(BigDecimal.valueOf(0));
            } else {
                optionRepDto.setPercentage(percentageCalculation(answerAnalysisBo.getTotal(), titleAnswerTotal));
                optionRepDto.setTotal(answerAnalysisBo.getTotal());
                optionRepDto.setAverageValue(setScale(answerAnalysisBo.getAverageValue()));
            }

            optionList.add(optionRepDto);
        });
        return optionList;
    }


    private List<AnswerOptionPo> listAnswerOptionPo(AnalysisListFillBlankReqDto reqDto, List<String> answerIdList) {
        AnswerOptionQuery optionQuery = new AnswerOptionQuery();
        optionQuery.setQuestionnaireId(reqDto.getQuestionnaireId());
        optionQuery.setUserId(reqDto.getUserId());
        optionQuery.setTitleSerialNumber(reqDto.getSerialNumber());
        optionQuery.setRowNumber(reqDto.getRowNumber());
        optionQuery.setColumnNumber(reqDto.getColumnNumber());
        optionQuery.setAnswerIdList(answerIdList);
        return answerOptionService.selectList(optionQuery);
    }

    private PaginateUtils<AnswerInfoPo> listAnswerInfoPo(AnalysisListFillBlankReqDto reqDto) {
        PaginateUtils<AnswerInfoPo> repDtoPaginate = new PaginateUtils<>(reqDto.getPage(), reqDto.getPageSize());

        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());

        int count = answerInfoService.selectCount(query);
        repDtoPaginate.setRecordTotal(count);

        query.setPageSize(repDtoPaginate.getPageSize());
        query.setStartIndex(repDtoPaginate.getStartIndex());
        List<AnswerInfoPo> answerInfoPos = answerInfoService.selectListByPage(query);

        repDtoPaginate.setData(answerInfoPos);
        return repDtoPaginate;
    }

    private PaginateUtils<AnswerOptionPo> listAnswerOptionPo(AnalysisListFillBlankReqDto reqDto) {
        PaginateUtils<AnswerOptionPo> repDtoPaginate = new PaginateUtils<>(reqDto.getPage(), reqDto.getPageSize());

        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setTitleSerialNumber(reqDto.getSerialNumber());
        query.setRowNumber(reqDto.getRowNumber());
        query.setColumnNumber(reqDto.getColumnNumber());

        int count = answerOptionService.selectCount(query);
        repDtoPaginate.setRecordTotal(count);

        query.setPageSize(repDtoPaginate.getPageSize());
        query.setStartIndex(repDtoPaginate.getStartIndex());
        List<AnswerOptionPo> answerOptionPoList = answerOptionService.selectListByPage(query);

        repDtoPaginate.setData(answerOptionPoList);
        return repDtoPaginate;
    }

    private List<CrossAnalysisTitleRepDto> getTwoIndependentVariableResult(AnalysisReportCrossAnalysisReqDto reqDto, Map<Integer, QstTitlePo> titleMap, Map<Integer, List<QstOptionPo>> optionListMap) {
        List<CrossAnalysisTitleRepDto> resultTitleList = new ArrayList<>();
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setFirstTitleSerialNumber(reqDto.getIndependentVariableTitleList().get(0));
        query.setSecondTitleSerialNumber(reqDto.getIndependentVariableTitleList().get(1));

        reqDto.getCrossAnalysisDependentVariableList().forEach(v -> {
            query.setThirdTitleSerialNumber(v.getDependentVariableTitle());
            if (TitleDimensionalTypeEnum.isJuZhen(v.getDependentVariableType())) {
                query.setRowNumber(v.getDependentVariableOption() + 1);
            }

            List<AnswerAnalysisOptionBo> independentVariableList = answerOptionService.selectTwoDimensionalIndependentVariableCrossAnalysis(query);
            Map<String, AnswerAnalysisOptionBo> independentVariableListMap = independentVariableList.stream().collect(Collectors.toMap(this::getOptionMark, va -> va));

            List<AnswerAnalysisOptionBo> notNullCrossAnalysisList = answerOptionService.selectTwoDimensionalNotNullCrossAnalysis(query);
            Map<String, List<AnswerAnalysisOptionBo>> notNullCrossAnalysisListMap = notNullCrossAnalysisList.stream().collect(Collectors.groupingBy(this::getOptionMark));
            CrossAnalysisTitleRepDto repDto = constructCrossAnalysisTitleRepDto(titleMap, v, optionListMap);

            List<CrossAnalysisOptionRepDto> optionRepDtoList = new ArrayList<>();

            optionListMap.get(reqDto.getIndependentVariableTitleList().get(0)).forEach(olm -> {

                optionListMap.get(reqDto.getIndependentVariableTitleList().get(1)).forEach(olm2 -> {
                    CrossAnalysisOptionRepDto optionRepDto = new CrossAnalysisOptionRepDto();
                    optionRepDto.setSerialNumber(olm.getSerialNumber());
                    optionRepDto.setName(getCombinationContent(olm.getContent(), olm2.getContent()));
                    String optionMark = getOptionMark(olm, olm2);
                    AnswerAnalysisOptionBo independentVariableBo = independentVariableListMap.get(optionMark);
                    if (independentVariableBo != null) {
                        optionRepDto.setTotal(independentVariableBo.getTotal());
                        List<CrossAnalysisColumnOptionRepDto> columnOptionList = constructColumnOptionList(notNullCrossAnalysisListMap.get(optionMark), optionListMap.get(v.getDependentVariableTitle()), independentVariableBo.getTotal(), 2);
                        if (!reqDto.getHideNull()) {
                            CrossAnalysisColumnOptionRepDto nullColumn = getTwoDimensionalNullColumn(query, optionMark, independentVariableBo.getTotal());
                            columnOptionList.add(nullColumn);
                        }
                        optionRepDto.setColumnOptionList(columnOptionList);
                    } else {
                        optionRepDto.setTotal(0);
                        List<CrossAnalysisColumnOptionRepDto> columnOptionList = constructColumnOptionList(optionListMap.get(v.getDependentVariableTitle()));
                        if (!reqDto.getHideNull()) {
                            columnOptionList.add(getNullColumn());
                        }
                        optionRepDto.setColumnOptionList(columnOptionList);
                    }

                    optionRepDtoList.add(optionRepDto);
                });
                repDto.setOptionList(optionRepDtoList);
            });

            resultTitleList.add(repDto);
        });

        return resultTitleList;
    }

    private String getCombinationContent(String content, String content1) {
        return content + SLASH + content1;
    }

    private CrossAnalysisTitleRepDto constructCrossAnalysisTitleRepDto(Map<Integer, QstTitlePo> titleMap, CrossAnalysisDependentVariableDto v, Map<Integer, List<QstOptionPo>> optionListMap) {
        CrossAnalysisTitleRepDto repDto = new CrossAnalysisTitleRepDto();
        repDto.setSerialNumber(v.getDependentVariableTitle());
        repDto.setName(titleMap.get(v.getDependentVariableTitle()).getName());
        repDto.setType(v.getDependentVariableType());

        if (TitleDimensionalTypeEnum.isJuZhen(v.getDependentVariableType())) {
            List<QstOptionPo> titleOptionList = optionListMap.get(v.getDependentVariableTitle());
            QstOptionPo qstOptionPo = titleOptionList.stream().filter(p -> p.getRowNumber() == (v.getDependentVariableOption() + 1)).findAny().get();
            repDto.setRowTitleName(qstOptionPo.getRowTitle());
        }
        return repDto;
    }

    private List<CrossAnalysisTitleRepDto> getOneIndependentVariableResult(AnalysisReportCrossAnalysisReqDto reqDto, Map<Integer, QstTitlePo> titleMap, Map<Integer, List<QstOptionPo>> optionListMap) {
        List<CrossAnalysisTitleRepDto> resultTitleList = new ArrayList<>();
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setFirstTitleSerialNumber(reqDto.getIndependentVariableTitleList().get(0));

        reqDto.getCrossAnalysisDependentVariableList().forEach(v -> {
            query.setSecondTitleSerialNumber(v.getDependentVariableTitle());
            if (TitleDimensionalTypeEnum.isJuZhen(v.getDependentVariableType())) {
                query.setRowNumber(v.getDependentVariableOption() + 1);
            }
            List<AnswerAnalysisOptionBo> independentVariableList = answerOptionService.selectOneDimensionalIndependentVariableCrossAnalysis(query);
            Map<String, AnswerAnalysisOptionBo> independentVariableListMap = independentVariableList.stream().collect(Collectors.toMap(this::getFirstOptionMark, va -> va));
            List<AnswerAnalysisOptionBo> notNullCrossAnalysisList = answerOptionService.selectOneDimensionalNotNullCrossAnalysis(query);
            Map<String, List<AnswerAnalysisOptionBo>> notNullCrossAnalysisListMap = notNullCrossAnalysisList.stream().collect(Collectors.groupingBy(this::getFirstOptionMark));

            CrossAnalysisTitleRepDto repDto = constructCrossAnalysisTitleRepDto(titleMap, v, optionListMap);
            List<CrossAnalysisOptionRepDto> optionRepDtoList = new ArrayList<>();

            optionListMap.get(reqDto.getIndependentVariableTitleList().get(0)).forEach(olm -> {

                CrossAnalysisOptionRepDto optionRepDto = new CrossAnalysisOptionRepDto();
                optionRepDto.setSerialNumber(olm.getSerialNumber());
                optionRepDto.setName(olm.getContent());
                String optionMark = getOptionMark(olm);
                AnswerAnalysisOptionBo independentVariableBo = independentVariableListMap.get(optionMark);
                if (independentVariableBo != null) {
                    optionRepDto.setTotal(independentVariableBo.getTotal());
                    List<CrossAnalysisColumnOptionRepDto> columnOptionList = constructColumnOptionList(notNullCrossAnalysisListMap.get(optionMark), optionListMap.get(v.getDependentVariableTitle()), independentVariableBo.getTotal(), 1);
                    if (!reqDto.getHideNull()) {
                        CrossAnalysisColumnOptionRepDto nullColumn = getOneDimensionalNullColumn(query, optionMark, independentVariableBo.getTotal());
                        columnOptionList.add(nullColumn);
                    }
                    optionRepDto.setColumnOptionList(columnOptionList);
                } else {
                    optionRepDto.setTotal(0);
                    List<CrossAnalysisColumnOptionRepDto> columnOptionList = constructColumnOptionList(optionListMap.get(v.getDependentVariableTitle()));
                    if (!reqDto.getHideNull()) {
                        columnOptionList.add(getNullColumn());
                    }
                    optionRepDto.setColumnOptionList(columnOptionList);
                }

                optionRepDtoList.add(optionRepDto);
            });
            repDto.setOptionList(optionRepDtoList);
            resultTitleList.add(repDto);
        });

        return resultTitleList;
    }

    private CrossAnalysisColumnOptionRepDto getOneDimensionalNullColumn(AnswerOptionQuery query, String optionMark, Integer count) {

        List<AnswerAnalysisOptionBo> containNullList = answerOptionService.selectOneDimensionalContainNullCrossAnalysis(query);
        Map<String, AnswerAnalysisOptionBo> containNullMap = containNullList.stream().collect(Collectors.toMap(k -> getFirstOptionMark(k), value -> value));
        CrossAnalysisColumnOptionRepDto columnOptionRepDto = getCrossAnalysisColumnOptionRepDto(optionMark, count, containNullMap);
        return columnOptionRepDto;
    }

    private CrossAnalysisColumnOptionRepDto getTwoDimensionalNullColumn(AnswerOptionQuery query, String optionMark, Integer count) {

        List<AnswerAnalysisOptionBo> containNullList = answerOptionService.selectTwoDimensionalContainNullCrossAnalysis(query);
        Map<String, AnswerAnalysisOptionBo> containNullMap = containNullList.stream().collect(Collectors.toMap(k -> getOptionMark(k), value -> value));
        CrossAnalysisColumnOptionRepDto columnOptionRepDto = getCrossAnalysisColumnOptionRepDto(optionMark, count, containNullMap);
        return columnOptionRepDto;
    }

    private CrossAnalysisColumnOptionRepDto getCrossAnalysisColumnOptionRepDto(String optionMark, Integer count, Map<String, AnswerAnalysisOptionBo> containNullMap) {
        AnswerAnalysisOptionBo answerAnalysisOptionBo = containNullMap.get(optionMark);

        CrossAnalysisColumnOptionRepDto columnOptionRepDto = new CrossAnalysisColumnOptionRepDto();
        columnOptionRepDto.setName("空");
        if (answerAnalysisOptionBo != null) {
            columnOptionRepDto.setTotal(answerAnalysisOptionBo.getTotal());
            columnOptionRepDto.setPercentage(percentageCalculation(answerAnalysisOptionBo.getTotal(), count));
        } else {
            columnOptionRepDto.setTotal(0);
            columnOptionRepDto.setPercentage(BigDecimal.valueOf(0));
        }
        return columnOptionRepDto;
    }

    private CrossAnalysisColumnOptionRepDto getNullColumn() {

        CrossAnalysisColumnOptionRepDto columnOptionRepDto = new CrossAnalysisColumnOptionRepDto();
        columnOptionRepDto.setName("空");
        columnOptionRepDto.setTotal(0);
        columnOptionRepDto.setPercentage(BigDecimal.valueOf(0));
        return columnOptionRepDto;
    }

    private List<CrossAnalysisColumnOptionRepDto> constructColumnOptionList(List<QstOptionPo> qstOptionPos) {
        List<CrossAnalysisColumnOptionRepDto> result = new ArrayList<>();

        qstOptionPos.forEach(v -> {
            CrossAnalysisColumnOptionRepDto columnOptionRepDto = new CrossAnalysisColumnOptionRepDto();
            columnOptionRepDto.setSerialNumber(v.getSerialNumber());
            columnOptionRepDto.setName(v.getContent());
            columnOptionRepDto.setPercentage(BigDecimal.valueOf(0));
            columnOptionRepDto.setTotal(0);

            result.add(columnOptionRepDto);
        });
        return result;
    }

    private List<CrossAnalysisColumnOptionRepDto> constructColumnOptionList(List<AnswerAnalysisOptionBo> answerAnalysisOptionBos, List<QstOptionPo> qstOptionPos, Integer count, Integer independentCount) {
        List<CrossAnalysisColumnOptionRepDto> result = new ArrayList<>();
        Map<String, AnswerAnalysisOptionBo> optionBoMap = SafeUtil.of(answerAnalysisOptionBos).stream().collect(Collectors.toMap(p -> getThirdMark(p, independentCount), v -> v, (k1, k2) -> k2));

        qstOptionPos.forEach(v -> {
            CrossAnalysisColumnOptionRepDto columnOptionRepDto = new CrossAnalysisColumnOptionRepDto();
            columnOptionRepDto.setSerialNumber(v.getSerialNumber());
            columnOptionRepDto.setName(v.getContent());

            AnswerAnalysisOptionBo optionBo = optionBoMap.get(getThirdMark(v));
            if (optionBo != null) {
                columnOptionRepDto.setPercentage(percentageCalculation(optionBo.getTotal(), count));
                columnOptionRepDto.setTotal(optionBo.getTotal());
            } else {
                columnOptionRepDto.setPercentage(BigDecimal.valueOf(0));
                columnOptionRepDto.setTotal(0);
            }

            result.add(columnOptionRepDto);
        });
        return result;
    }

    private Map<String, Integer> getFirstTitleOptionMap(Map<String, List<AnswerAnalysisOptionBo>> notNullTitleListMap) {
        Map<String, Integer> result = new HashMap<>();

        notNullTitleListMap.forEach((k, v) -> {
            List<AnswerAnalysisOptionBo> optionBoList = v;
            AtomicReference<Integer> count = new AtomicReference<>(0);
            optionBoList.forEach(op -> count.set(count.get() + op.getTotal()));
            result.put(k, count.get());

        });
        return result;
    }

    private String getOptionMark(QstOptionPo olm) {
        StringBuilder builder = new StringBuilder();
        builder.append(olm.getTitleSerialNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(olm.getRowNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(olm.getColumnNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(olm.getSerialNumber());
        return builder.toString();
    }

    private String getOptionMark(AnswerAnalysisOptionBo obj) {
        StringBuilder builder = new StringBuilder();
        builder.append(obj.getFirstTitleSerialNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getFirstRowNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getFirstColumnNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getFirstSerialNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getSecondTitleSerialNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getSecondRowNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getSecondColumnNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getSecondSerialNumber());
        return builder.toString();
    }

    private String getOptionMark(QstOptionPo p1, QstOptionPo p2) {
        StringBuilder builder = new StringBuilder();
        builder.append(p1.getTitleSerialNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(p1.getRowNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(p1.getColumnNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(p1.getSerialNumber());

        builder.append(ANSWER_TITLE_SPLIT);

        builder.append(p2.getTitleSerialNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(p2.getRowNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(p2.getColumnNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(p2.getSerialNumber());
        return builder.toString();
    }

    private String getThirdMark(QstOptionPo v) {
        StringBuilder builder = new StringBuilder();
        builder.append(v.getTitleSerialNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(v.getRowNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(v.getColumnNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(v.getSerialNumber());
        return builder.toString();
    }

    private String getThirdMark(AnswerAnalysisOptionBo p, Integer independentCount) {
        StringBuilder builder = new StringBuilder();
        if (independentCount == 2) {
            builder.append(p.getThirdTitleSerialNumber());
            builder.append(ANSWER_TITLE_SPLIT);
            builder.append(p.getThirdRowNumber());
            builder.append(ANSWER_TITLE_SPLIT);
            builder.append(p.getThirdColumnNumber());
            builder.append(ANSWER_TITLE_SPLIT);
            builder.append(p.getThirdSerialNumber());
        } else {
            builder.append(p.getSecondTitleSerialNumber());
            builder.append(ANSWER_TITLE_SPLIT);
            builder.append(p.getSecondRowNumber());
            builder.append(ANSWER_TITLE_SPLIT);
            builder.append(p.getSecondColumnNumber());
            builder.append(ANSWER_TITLE_SPLIT);
            builder.append(p.getSecondSerialNumber());
        }
        return builder.toString();
    }

    private String getFirstOptionMark(AnswerAnalysisOptionBo obj) {
        StringBuilder builder = new StringBuilder();
        builder.append(obj.getFirstTitleSerialNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getFirstRowNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getFirstColumnNumber());
        builder.append(ANSWER_TITLE_SPLIT);
        builder.append(obj.getFirstSerialNumber());
        return builder.toString();
    }

    private List<AnalysisReportOptionRepDto> constructFillBlankOptionList(List<QstOptionPo> optionPoList) {
        return ReportConverter.INSTANCE.to(optionPoList);
    }

    private List<QstTitlePo> getQstTitlePosList(AnalysisReportReqDto reqDto) {
        List<QstTitlePo> qstTitlePoList = qstTitleService.selectList(reqDto.getQuestionnaireId(), reqDto.getUserId());
        return qstTitlePoList;
    }

    private Map<Integer, List<QstOptionPo>> getOptionPoListMultimap(AnalysisReportReqDto reqDto) {
        List<QstOptionPo> qstOptionPoList = qstOptionService.selectList(reqDto.getQuestionnaireId(), reqDto.getUserId());
        Assert.notNull(qstOptionPoList, "option");
        return qstOptionPoList.stream().collect(Collectors.groupingBy(QstOptionPo::getTitleSerialNumber));
    }

    private Map<Integer, AnswerAnalysisBo> getAnalysisTitleMap(AnalysisReportReqDto reqDto) {
        if (reqDto.getAnswerIdList() != null && reqDto.getAnswerIdList().size() == 0) {
            return new HashMap<>();
        }

        List<AnswerAnalysisBo> analysisTitleList = answerOptionService.selectTitleAnalysis(reqDto.getQuestionnaireId(), reqDto.getUserId(), reqDto.getAnswerIdList());
        Assert.notNull(analysisTitleList, NO_ANSWER);
        return Maps.uniqueIndex(analysisTitleList, AnswerAnalysisBo::getTitleSerialNumber);
    }

    private List<AnalysisReportOptionRepDto> constructThreeDimensionalOptionList(AnalysisReportReqDto reqDto, QstTitlePo title, List<QstOptionPo> optionPoList, Integer titleAnswerTotal) {
        List<AnalysisReportOptionRepDto> optionList = new ArrayList<>();

        if (reqDto.getAnswerIdList() != null && reqDto.getAnswerIdList().size() == 0) {
            optionPoList.forEach(option -> {
                AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
                optionRepDto.setTotal(0);
                optionRepDto.setPercentage(BigDecimal.valueOf(0));

                optionList.add(optionRepDto);
            });
            return optionList;
        }

        List<AnswerAnalysisBo> threeDimensionalAnalysisList = answerOptionService.selectThreeDimensionalAnalysis(reqDto.getQuestionnaireId(), title.getSerialNumber(), reqDto.getUserId(), reqDto.getAnswerIdList());
        Map<String, AnswerAnalysisBo> threeDimensionalAnalysisMap = Maps.uniqueIndex(threeDimensionalAnalysisList, input -> {
            StringBuilder buffer = constructThreeDimensionalKey(input.getRowNumber(), input.getColumnNumber(), input.getSerialNumber());
            return buffer.toString();
        });

        optionPoList.forEach(option -> {
            AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
            StringBuilder buffer = constructThreeDimensionalKey(option.getRowNumber(), option.getColumnNumber(), option.getSerialNumber());
            AnswerAnalysisBo answerAnalysisBo = threeDimensionalAnalysisMap.get(buffer.toString());
            if (answerAnalysisBo == null) {
                optionRepDto.setTotal(0);
                optionRepDto.setPercentage(BigDecimal.valueOf(0));
            } else {
                optionRepDto.setTotal(answerAnalysisBo.getTotal());
                optionRepDto.setPercentage(percentageCalculation(answerAnalysisBo.getTotal(), titleAnswerTotal));
            }
            optionList.add(optionRepDto);
        });
        return optionList;
    }

    private List<AnalysisReportOptionRepDto> constructJuZhenHuaDongTiaoOptionList(AnalysisReportReqDto reqDto, QstTitlePo title, List<QstOptionPo> optionPoList, AnalysisReportTitleRepDto titleRepDto) {
        List<AnalysisReportOptionRepDto> optionList = new ArrayList<>();

        if (reqDto.getAnswerIdList() != null && reqDto.getAnswerIdList().size() == 0) {
            for (QstOptionPo option : optionPoList) {
                AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
                optionRepDto.setAverageValue(BigDecimal.valueOf(0));
                optionRepDto.setTotal(0);

                optionList.add(optionRepDto);
            }

            titleRepDto.setTotalValue(BigDecimal.ZERO);
            titleRepDto.setAverageValue(BigDecimal.ZERO);

            return optionList;
        }

        List<AnswerAnalysisBo> twoDimensionalAnalysisList = answerOptionService.selectJuZhenHuaDongTiaoAnalysis(reqDto.getQuestionnaireId(), title.getSerialNumber(), reqDto.getUserId(), reqDto.getAnswerIdList());
        Map<Integer, AnswerAnalysisBo> twoDimensionalAnalysisMap = twoDimensionalAnalysisList.stream()
                .collect(Collectors.toMap(key -> key.getRowNumber(), v -> v, (key1, key2) -> key1));

        BigDecimal totalValue = new BigDecimal(0);
        BigDecimal totalAverageValue = new BigDecimal(0);
        for (QstOptionPo option : optionPoList) {
            AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
            AnswerAnalysisBo answerAnalysisBo = twoDimensionalAnalysisMap.get(option.getSerialNumber());
            if (answerAnalysisBo == null) {
                optionRepDto.setAverageValue(BigDecimal.valueOf(0));
                optionRepDto.setTotal(0);
            } else {
                optionRepDto.setTotal(answerAnalysisBo.getTotal());
                optionRepDto.setAverageValue(setScale(answerAnalysisBo.getAverageValue()));
                totalValue = totalValue.add(setScale(answerAnalysisBo.getTotalValue()));
                totalAverageValue = totalAverageValue.add(setScale(optionRepDto.getAverageValue()));
            }

            optionList.add(optionRepDto);
        }

        titleRepDto.setTotalValue(totalAverageValue);
        titleRepDto.setAverageValue(avgCalculation(totalAverageValue, optionList.size()));
        return optionList;
    }

    private List<AnalysisReportOptionRepDto> constructTwoDimensionalOptionList(AnalysisReportReqDto reqDto, QstTitlePo title, List<QstOptionPo> optionPoList, Integer titleAnswerTotal) {
        List<AnalysisReportOptionRepDto> optionList = new ArrayList<>();

        if (reqDto.getAnswerIdList() != null && reqDto.getAnswerIdList().size() == 0) {
            optionPoList.forEach(option -> {
                AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
                optionRepDto.setPercentage(BigDecimal.valueOf(0));
                optionRepDto.setTotal(0);

                optionList.add(optionRepDto);
            });
            return optionList;
        }

        List<AnswerAnalysisBo> twoDimensionalAnalysisList = answerOptionService.selectTwoDimensionalAnalysis(reqDto.getQuestionnaireId(), title.getSerialNumber(), reqDto.getUserId(), reqDto.getAnswerIdList());
        Map<String, AnswerAnalysisBo> twoDimensionalAnalysisMap = twoDimensionalAnalysisList.stream()
                .collect(Collectors.toMap(key -> constructTwoDimensionalKey(key.getRowNumber(), key.getSerialNumber()).toString(), v -> v, (key1, key2) -> key1));

        optionPoList.forEach(option -> {
            AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
            StringBuilder buffer = constructTwoDimensionalKey(option.getRowNumber(), option.getSerialNumber());
            AnswerAnalysisBo answerAnalysisBo = twoDimensionalAnalysisMap.get(buffer.toString());
            if (answerAnalysisBo == null) {
                optionRepDto.setPercentage(BigDecimal.valueOf(0));
                optionRepDto.setTotal(0);
            } else {
                optionRepDto.setTotal(answerAnalysisBo.getTotal());
                optionRepDto.setPercentage(percentageCalculation(answerAnalysisBo.getTotal(), titleAnswerTotal));
            }

            optionList.add(optionRepDto);
        });
        return optionList;
    }

    private List<AnalysisReportOptionRepDto> constructOneDimensionalOptionList(AnalysisReportReqDto reqDto, QstTitlePo title, List<QstOptionPo> optionPoList, Integer titleAnswerTotal) {
        List<AnalysisReportOptionRepDto> optionList = new ArrayList<>();

        if (reqDto.getAnswerIdList() != null && reqDto.getAnswerIdList().size() == 0) {
            optionPoList.forEach(option -> {
                AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
                optionRepDto.setTotal(0);
                optionRepDto.setPercentage(BigDecimal.valueOf(0));
                optionRepDto.setAverageValue(BigDecimal.valueOf(0));
                optionList.add(optionRepDto);
            });
            return optionList.stream().sorted(Comparator.comparing(AnalysisReportOptionRepDto::getOrderNumber)).collect(Collectors.toList());
        }

        List<AnswerAnalysisBo> oneDimensionalAnalysisList = answerOptionService.selectOneDimensionalAnalysis(reqDto.getQuestionnaireId(), title.getSerialNumber(), reqDto.getUserId(), reqDto.getAnswerIdList());
        Map<Integer, AnswerAnalysisBo> oneDimensionalAnalysisMap = Maps.uniqueIndex(oneDimensionalAnalysisList, AnswerAnalysisBo::getSerialNumber);

        optionPoList.forEach(option -> {
            AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
            AnswerAnalysisBo answerAnalysisBo = oneDimensionalAnalysisMap.get(option.getSerialNumber());
            if (answerAnalysisBo == null) {
                optionRepDto.setTotal(0);
                optionRepDto.setPercentage(BigDecimal.valueOf(0));
                optionRepDto.setAverageValue(BigDecimal.valueOf(0));
            } else {
                optionRepDto.setPercentage(percentageCalculation(answerAnalysisBo.getTotal(), titleAnswerTotal));
                optionRepDto.setTotal(answerAnalysisBo.getTotal());
            }

            optionList.add(optionRepDto);
        });
        return optionList.stream().sorted(Comparator.comparing(AnalysisReportOptionRepDto::getOrderNumber)).collect(Collectors.toList());
    }

    private List<AnalysisReportOptionRepDto> constructPaiXuOptionList(AnalysisReportReqDto reqDto, QstTitlePo title, List<QstOptionPo> optionPoList, Integer titleAnswerTotal) {
        List<AnalysisReportOptionRepDto> optionList = new ArrayList<>();

        if (reqDto.getAnswerIdList() != null && reqDto.getAnswerIdList().size() == 0) {
            optionPoList.forEach(option -> {
                AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
                optionRepDto.setPercentage(BigDecimal.valueOf(0));
                optionRepDto.setTotal(0);
                optionRepDto.setAverageValue(BigDecimal.valueOf(0));

                optionList.add(optionRepDto);
            });

            return optionList;
        }

        List<AnswerAnalysisBo> oneDimensionalAnalysisList = answerOptionService.selectPaiXuAnalysis(reqDto.getQuestionnaireId(), title.getSerialNumber(), reqDto.getUserId(), reqDto.getAnswerIdList());
        Map<Integer, AnswerAnalysisBo> oneDimensionalAnalysisMap = Maps.uniqueIndex(oneDimensionalAnalysisList, AnswerAnalysisBo::getSerialNumber);
        Integer totalScore = oneDimensionalAnalysisList.stream().map(AnswerAnalysisBo::getTotal).reduce(0, (a, b) -> a + b);

        optionPoList.forEach(option -> {
            AnswerAnalysisBo answerAnalysisBo = oneDimensionalAnalysisMap.get(option.getSerialNumber());
            AnalysisReportOptionRepDto optionRepDto = ReportConverter.INSTANCE.to(option);
            if (answerAnalysisBo == null) {
                optionRepDto.setPercentage(BigDecimal.valueOf(0));
                optionRepDto.setTotal(0);
                optionRepDto.setAverageValue(BigDecimal.valueOf(0));
            } else {
                optionRepDto.setAverageValue(avgCalculation(answerAnalysisBo.getTotal(), titleAnswerTotal));
                optionRepDto.setTotal(answerAnalysisBo.getTotal());
                if (totalScore == 0) {
                    optionRepDto.setPercentage(BigDecimal.valueOf(0));
                } else {
                    optionRepDto.setPercentage(percentageCalculation(answerAnalysisBo.getTotal(), totalScore));
                }
            }

            optionList.add(optionRepDto);
        });

        return optionList;
    }

    private StringBuilder constructTwoDimensionalKey(int rowNumber, int serialNumber) {
        StringBuilder buffer = new StringBuilder();
        buffer.append(rowNumber);
        buffer.append(SPLIT_FLAG);
        buffer.append(serialNumber);
        return buffer;
    }

    private StringBuilder constructThreeDimensionalKey(int rowNumber, int columnNumber, int serialNumber) {
        StringBuilder buffer = constructTwoDimensionalKey(rowNumber, columnNumber);
        buffer.append(SPLIT_FLAG);
        buffer.append(serialNumber);
        return buffer;
    }

    private AnalysisReportTitleRepDto constructTitleRepDto(Map<Integer, AnswerAnalysisBo> analysisTitleMap, QstTitlePo title) {
        AnalysisReportTitleRepDto titleRepDto = new AnalysisReportTitleRepDto();
        titleRepDto.setHao(title.getSerialNumber());
        titleRepDto.setOrder(title.getOrderNumber());
        titleRepDto.setName(title.getName());
        titleRepDto.setType(TitleTypeEnum.toEnum(title.getType()).name().toLowerCase());
        titleRepDto.setTypeInt(title.getType());
        if (analysisTitleMap.get(title.getSerialNumber()) == null) {
            titleRepDto.setTotal(0);
        } else {
            titleRepDto.setTotal(analysisTitleMap.get(title.getSerialNumber()).getTotal());
        }
        return titleRepDto;
    }

}

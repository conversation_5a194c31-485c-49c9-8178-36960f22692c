package com.cas.nc.questionnaire.common.utils;

import org.apache.commons.lang3.time.DateUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

import static com.cas.nc.questionnaire.common.utils.Constants.ONE;

public class DateUtil extends DateUtils {

    public static final String C_DATE_DIVISION = "-";
    public static final String C_TIME_PATTON_DEFAULT = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYYMMDDHH = "yyyy-MM-dd HH";
    public static final String YYYYMM = "yyyy-MM";
    public static final String YYYY = "yyyy";
    public static final String YYYYMMDD = "yyyy-MM-dd";
    public static final String C_DATE_PATTON_DEFAULT = "yyyy-MM-dd";
    public static final String C_DATA_PATTON_YYYYMMDD = "yyyyMMdd";
    public static final String C_TIME_PATTON_HHMMSS = "HH:mm:ss";
    public static final String C_TIME_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static final String C_TIME_TZ = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    public static final int C_ONE_SECOND = 1000;
    public static final int C_ONE_MINUTE = 60 * C_ONE_SECOND;
    public static final int C_ONE_HOUR = 60;
    public static final long C_ONE_DAY = 24 * C_ONE_HOUR;
    public static final String DIFF_TYPE_DAY = "D";
    public static final String DIFF_TYPE_HOUR = "H";
    public static final String DIFF_TYPE_MINUTE = "M";
    public static final String DIFF_TYPE_SECOND = "S";
    public static final String ZERO_TIME = "0000-00-00 00:00:00";

    /**
     * 日期格式化为"yyyy-MM-dd'T'HH:mm:ss'Z'"
     *
     * @param date
     * @return
     */
    public static String formatDateTimeTZ(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(C_TIME_TZ);
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        return sdf.format(new Date());
    }

    /**
     * 日期格式化函数"yyyy-MM-dd HH:mm:ss"
     *
     * @param dateTimeStr
     * @return Date
     */
    public static Date formatDateTime(String dateTimeStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date formatDateTime = null;
        try {
            formatDateTime = sdf.parse(dateTimeStr);
        } catch (ParseException e) {
            return null;
        }
        return formatDateTime;
    }

    /**
     * 从date类型格式化为字符串类型的指定格式"yyyyMMddHHmmss"
     *
     * @param tempDateTime
     * @return String
     */
    public static String getTimeStrFromDate(Date tempDateTime) {
        String moedifiedTime = null;
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(
                    "yyyyMMddHHmmss");
            moedifiedTime = new String(dateFormat.format(tempDateTime));
        } catch (Exception e) {
            return "";
        }
        return moedifiedTime;
    }

    /**
     * Return the current date
     *
     * @return － DATE<br>
     */
    public static Date getCurrentDate() {
        Calendar cal = Calendar.getInstance();
        Date currDate = cal.getTime();

        return currDate;
    }

    /**
     * Return the current date string
     *
     * @return － 产生的日期字符串<br>
     */
    public static String getCurrentDateStr() {
        Calendar cal = Calendar.getInstance();
        Date currDate = cal.getTime();

        return format(currDate);
    }

    /**
     * Return the current date in the specified format
     *
     * @param strFormat
     * @return
     */
    public static String getCurrentDateStr(String strFormat) {
        Calendar cal = Calendar.getInstance();
        Date currDate = cal.getTime();

        return format(currDate, strFormat);
    }

    /**
     * Parse a string and return a date value
     *
     * @param dateValue
     * @return
     * @throws Exception
     */
    public static Date parseDate(String dateValue) {
        return parseDate(C_DATE_PATTON_DEFAULT, dateValue);
    }

    /**
     * Parse a strign and return a datetime value
     *
     * @param dateValue
     * @return
     */
    public static Date parseDateTime(String dateValue) {
        return parseDate(C_TIME_PATTON_DEFAULT, dateValue);
    }

    /**
     * Parse a string and return the date value in the specified format
     *
     * @param strFormat
     * @param dateValue
     * @return
     * @throws ParseException
     * @throws Exception
     */
    public static Date parseDate(String strFormat, String dateValue) {
        if (dateValue == null)
            return null;

        if (strFormat == null)
            strFormat = C_TIME_PATTON_DEFAULT;

        SimpleDateFormat dateFormat = new SimpleDateFormat(strFormat);
        Date newDate = null;

        try {
            newDate = dateFormat.parse(dateValue);
        } catch (ParseException pe) {
            newDate = null;
        }
        return newDate;
    }

    /**
     * 将Timestamp类型的日期转换为系统参数定义的格式的字符串。
     *
     * @param aTs_Datetime 需要转换的日期。
     * @return 转换后符合给定格式的日期字符串
     */
    public static String format(Date aTs_Datetime) {
        return format(aTs_Datetime, C_DATE_PATTON_DEFAULT);
    }

    /**
     * 将Timestamp类型的日期转换为系统参数定义的格式的字符串。
     *
     * @param aTs_Datetime 需要转换的日期。
     * @return 转换后符合给定格式的日期字符串
     */
    public static String formatTime(Date aTs_Datetime) {
        return format(aTs_Datetime, C_TIME_PATTON_DEFAULT);
    }

    /**
     * 将Date类型的日期转换为系统参数定义的格式的字符串。
     *
     * @param aTs_Datetime
     * @param as_Pattern
     * @return
     */
    public static String format(Date aTs_Datetime, String as_Pattern) {
        if (aTs_Datetime == null || as_Pattern == null)
            return null;
        SimpleDateFormat dateFromat = new SimpleDateFormat();
        dateFromat.applyPattern(as_Pattern);

        return dateFromat.format(aTs_Datetime);
    }

    /**
     * @param aTs_Datetime
     * @param as_Format
     * @return
     */
    public static String formatTime(Date aTs_Datetime, String as_Format) {
        if (aTs_Datetime == null || as_Format == null)
            return null;
        SimpleDateFormat dateFromat = new SimpleDateFormat();
        dateFromat.applyPattern(as_Format);

        return dateFromat.format(aTs_Datetime);
    }

    /**
     * 将字符串按照  as_Format 的格式转化成日期
     *
     * @param s_date
     * @param as_Format
     * @return
     */
    public static Date formatToDate(String s_date, String as_Format) {
        SimpleDateFormat sdf = new SimpleDateFormat(as_Format);
        try {
            return sdf.parse(s_date);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }


    public static String getFormatTime(Date dateTime) {
        return formatTime(dateTime, C_TIME_PATTON_HHMMSS);
    }

    /**
     * @param aTs_Datetime
     * @param as_Pattern
     * @return
     */
    public static String format(Timestamp aTs_Datetime, String as_Pattern) {
        if (aTs_Datetime == null || as_Pattern == null)
            return null;
        SimpleDateFormat dateFromat = new SimpleDateFormat();
        dateFromat.applyPattern(as_Pattern);

        return dateFromat.format(aTs_Datetime);
    }

    /**
     * 取得指定日期N天后的日期
     *
     * @param date
     * @param days
     * @return
     */
    public static Date addDays(Date date, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        cal.add(Calendar.DAY_OF_MONTH, days);

        return cal.getTime();
    }

    /**
     * 取得指定日期N月后的日期
     *
     * @param date
     * @param months
     * @return
     */
    public static Date addMonth(Date date, int months) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        cal.add(Calendar.MONTH, months);

        return cal.getTime();
    }

    /**
     * 取得N分钟后的时间
     *
     * @param date
     * @param minute
     * @return
     */
    public static Date addMinutes(Date date, int minute) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        cal.add(Calendar.MINUTE, minute);

        return cal.getTime();
    }

    /**
     * 获取N个小时候后的时间
     *
     * @param date
     * @param hour
     * @return
     */
    public static Date addHours(Date date, int hour) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        cal.add(Calendar.HOUR, hour);

        return cal.getTime();
    }

    public static Date addWeeks(Date date, int week) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        cal.add(Calendar.WEEK_OF_MONTH, week);

        return cal.getTime();
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int daysBetween(Date date1, Date date2) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date1);
        long time1 = cal.getTimeInMillis();
        cal.setTime(date2);
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);

        return Integer.parseInt(String.valueOf(between_days));
    }

    /**
     * 计算当前日期相对于"1977-12-01"的天数
     *
     * @param date
     * @return
     */
    public static long getRelativeDays(Date date) {
        Date relativeDate = DateUtil.parseDate("yyyy-MM-dd", "1977-12-01");

        return DateUtil.daysBetween(relativeDate, date);
    }

    public static Date getDateBeforTwelveMonth() {
        String date = "";
        Calendar cla = Calendar.getInstance();
        cla.setTime(getCurrentDate());
        int year = cla.get(Calendar.YEAR) - 1;
        int month = cla.get(Calendar.MONTH) + 1;
        if (month > 9) {
            date = String.valueOf(year) + C_DATE_DIVISION
                    + String.valueOf(month) + C_DATE_DIVISION + "01";
        } else {
            date = String.valueOf(year) + C_DATE_DIVISION + "0"
                    + String.valueOf(month) + C_DATE_DIVISION + "01";
        }

        Date dateBefore = parseDate(date);
        return dateBefore;
    }

    /**
     * 传入时间字符串,加一天后返回Date
     *
     * @param date 时间 格式 YYYY-MM-DD
     * @return
     */
    public static Date addDate(String date) {
        if (date == null) {
            return null;
        }
        Date tempDate = parseDate(C_DATE_PATTON_DEFAULT, date);
        String year = format(tempDate, "yyyy");
        String month = format(tempDate, "MM");
        String day = format(tempDate, "dd");

        GregorianCalendar calendar = new GregorianCalendar(
                Integer.parseInt(year), Integer.parseInt(month) - 1,
                Integer.parseInt(day));

        calendar.add(GregorianCalendar.DATE, 1);
        return calendar.getTime();
    }

    /**
     * 前推天数
     */
    public static Date parseDay(int day) {
        int INTERVAL_DAY = -day;
        Calendar calender = Calendar.getInstance();
        calender.setTime(new Date());
        calender.add(Calendar.DATE, INTERVAL_DAY);
        return calender.getTime();
    }

    /**
     * 获取指定时间与当前时间的分钟时间差
     *
     * @param dateStr 格式：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static long getMinute(String dateStr) {
        long datalong = DateUtil.parseDate(C_TIME_PATTON_DEFAULT, dateStr).getTime();
        return (datalong - System.currentTimeMillis()) / 1000 / 60;
    }

    public static long getMinute(Date dateStr) {
        long datalong = dateStr.getTime();
        return (datalong - System.currentTimeMillis()) / 1000 / 60;
    }

    /**
     * 计算时间间隔(天，小时，分钟，秒)
     *
     * @param startTime
     * @param endTime
     * @param str
     * @return
     */
    public static long dateDiff(Date startTime, Date endTime, String str) {
        if (startTime == null || endTime == null || StringUtil.isBlank(str)) {
            return 0;
        }
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
        long nh = 1000 * 60 * 60;// 一小时的毫秒数
        long nm = 1000 * 60;// 一分钟的毫秒数
        long ns = 1000;// 一秒钟的毫秒数
        long diff;
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        // 获得两个时间的毫秒时间差异
        diff = endTime.getTime() - startTime.getTime();
        day = diff / nd;// 计算差多少天
        hour = diff % nd / nh + day * 24;// 计算差多少小时
        min = diff % nd % nh / nm + hour * 60;// 计算差多少分钟
        sec = diff / ns;// 计算差多少秒
        if ("d".equalsIgnoreCase(str)) {
            return day;
        } else if ("h".equalsIgnoreCase(str)) {
            return hour;
        } else if ("m".equalsIgnoreCase(str)) {
            return min;
        } else {
            return sec;
        }
    }


    /**
     * 返回时间相差描述
     *
     * @param endDate   大时间戳
     * @param startDate 小时间戳
     * @return
     */
    public static String getDatePoor(Date endDate, Date startDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        String desc = (day > 0L ? day + "天" : "" )+ (hour > 0L ? hour + "小时" : "" )+ (min > 0L ? min + "分钟" : "");
        return desc;
    }


    /**
     * 当前日期与指定日期比较
     *
     * @param date
     * @return
     */
    public static int compareDate(Date date) {
        try {
            Date new_date = new Date();
            if (new_date.getTime() > date.getTime()) {
                return 1;
            } else if (new_date.getTime() < date.getTime()) {
                return -1;
            } else {
                return 0;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return 0;
    }

    /**
     * 当前日期与指定日期比较是否在两者之间
     *
     * @param beginDateStr
     * @param endDateStr
     * @return
     */
    public static boolean compareDateBetween(String beginDateStr, String endDateStr) {
        try {
            Date currentDate = new Date();
            Date beginDate = parseDate(C_TIME_PATTON_DEFAULT, beginDateStr);
            Date endDate = parseDate(C_TIME_PATTON_DEFAULT, endDateStr);
            return beginDate.getTime() <= currentDate.getTime() && currentDate.getTime() <= endDate.getTime();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return false;
    }

    /**
     * 给date添加(days,hours,minutes , seconds)时间偏移
     *
     * @param date
     * @param days
     * @param hours
     * @param minutes
     * @param seconds
     * @return Date
     */
    public static synchronized Date add(Date date, int days, int hours, int minutes, int seconds) {
        Date dt = date;
        if (dt != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(dt);
            if (days != 0)
                calendar.add(Calendar.DATE, days);
            if (hours != 0)
                calendar.add(Calendar.HOUR, hours);
            if (minutes != 0)
                calendar.add(Calendar.MINUTE, minutes);
            if (seconds != 0)
                calendar.add(Calendar.SECOND, seconds);
            dt = calendar.getTime();
        }
        return dt;
    }

    /**
     * 获取00:00:00的时间
     *
     * @param date
     * @return
     */
    public static Date truncateFirst(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    /**
     * 获取23:59:59的时间
     *
     * @param date
     * @return
     */
    public static Date truncateLast(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    /**
     * 判断给定的时间 参数1是否小于参数2
     * true 表示参数1<参数2
     *
     * @param time1
     * @param time2
     * @return
     */
    public static boolean before(Date time1, Date time2) {
        if (time1 == null || time2 == null) {
            throw new IllegalArgumentException("time1 | time2 is null");
        }

        return time1.getTime() < time2.getTime();
    }

    public static boolean isLastTowDay(String time) {
        Date nowDate = new Date();
        Date beforeDate = addDays(nowDate, -1);
        Date date = parseDate(C_TIME_PATTON_DEFAULT, time);
        if (before(nowDate, date)) {
            return false;
        }
        if (daysBetween(date, nowDate) == 0 || daysBetween(beforeDate, date) == 0) {
            return true;
        }
        return false;
    }

    public static String getFirstOfWeek(String dataStr) throws ParseException {
        Calendar cal = getCalendar(dataStr);
        // 所在周开始日期
        String data1 = new SimpleDateFormat(YYYYMMDD).format(cal.getTime());
        return data1;
    }

    public static String getLastOfWeek(String dataStr) throws ParseException {
        Calendar cal = getCalendar(dataStr);
        cal.add(Calendar.DAY_OF_WEEK, 6);
        // 所在周结束日期
        String data2 = new SimpleDateFormat(YYYYMMDD).format(cal.getTime());
        return data2;
    }

    private static Calendar getCalendar(String dataStr) throws ParseException {
        Calendar cal = Calendar.getInstance();

        cal.setTime(new SimpleDateFormat(YYYYMMDD).parse(dataStr));

        int d = 0;
        if (cal.get(Calendar.DAY_OF_WEEK) == ONE) {
            d = -6;
        } else {
            d = 2 - cal.get(Calendar.DAY_OF_WEEK);
        }
        cal.add(Calendar.DAY_OF_WEEK, d);
        return cal;
    }

    public static String constructTimeDefalut(Date date) {
        if (date == null) {
            return ZERO_TIME;
        }
        return format(date, C_TIME_PATTON_DEFAULT);
    }


}
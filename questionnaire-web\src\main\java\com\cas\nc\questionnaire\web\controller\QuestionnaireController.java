package com.cas.nc.questionnaire.web.controller;

import com.alibaba.fastjson.JSON;
import com.cas.nc.questionnaire.common.dto.analysis.DownloadWordReqDto;
import com.cas.nc.questionnaire.common.dto.mylist.CreateExternalQuestionnaireRepDto;
import com.cas.nc.questionnaire.common.dto.mylist.CreateExternalQuestionnaireReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.*;
import com.cas.nc.questionnaire.common.dto.setting.SettingSetQueryRepDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.to.AnswerShareTo;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.SignGenerateUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.common.utils.UserAgentUtil;
import com.cas.nc.questionnaire.common.utils.ValidateUtils;
import com.cas.nc.questionnaire.common.vo.analysis.DownloadWordReqVo;
import com.cas.nc.questionnaire.common.vo.mylist.CreateExternalQuestionnaireRepVo;
import com.cas.nc.questionnaire.common.vo.mylist.CreateExternalQuestionnaireReqVo;
import com.cas.nc.questionnaire.common.vo.questionnaire.*;
import com.cas.nc.questionnaire.dao.po.BizConfigPo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.server.*;
import com.cas.nc.questionnaire.server.mapstruct.DownloadWordConverter;
import com.cas.nc.questionnaire.server.mapstruct.ExternalListConverter;
import com.cas.nc.questionnaire.server.mapstruct.MyListConverter;
import com.cas.nc.questionnaire.server.mapstruct.QuestionnaireCreateConverter;
import com.cas.nc.questionnaire.server.mapstruct.QuestionnaireEditConverter;
import com.cas.nc.questionnaire.server.mapstruct.QuestionnaireQueryConverter;
import com.cas.nc.questionnaire.server.mapstruct.RegularQuestionConverter;
import com.cas.nc.questionnaire.service.BizConfigService;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import com.cas.nc.questionnaire.service.ShareSettingService;
import com.cas.nc.questionnaire.service.UserInfoService;
import com.lowagie.text.Document;
import com.lowagie.text.PageSize;
import com.lowagie.text.rtf.RtfWriter2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.ANSWER_PWD_ERROR;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.BIZ_ID_NOT_EXIST;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.REQUEST_INVALID_RETRY;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.SIGN_EXCEPTION;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.TEXT_CREATE_EXCEPTION;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.USER_NONEXISTENT;
import static com.cas.nc.questionnaire.common.utils.Constants.REQUEST_TIME_VALID_TIME;
import static com.cas.nc.questionnaire.common.utils.DateUtil.C_TIME_PATTON_DEFAULT;

@RestController
@RequestMapping("/questionnaire/multiple")
public class QuestionnaireController extends BaseController {

    @Resource
    private QuestionnaireServer questionnaireServer;
    @Resource
    private SettingServer settingServer;
    @Resource
    private BizConfigService bizConfigService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private ShareSettingService shareSettingService;
    @Resource
    private DownloadWordServer downloadWordServer;
    @Resource
    private UserServer userServer;
    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;
    @Resource
    private FileServer fileServer;

    @Value("${text.demo}")
    private String textDemo;
    @Value("${text.explain}")
    private String textExplain;

    /**
     * 查询问卷信息，例如请求问卷，预览等
     *
     * @param vo
     * @return
     */
    @RequestMapping(value = "/query", method = {RequestMethod.POST})
    public ApiReturnResult query(@RequestBody QuestionnaireQueryReqVo vo) {
        logger.info("QuestionnaireController.query param[{}]", JSON.toJSON(vo));

        ApiReturnResult result;
        try {
            ValidateUtils.validateNotNullContain(vo, "questionnaireId");

            QuestionnaireQueryReqDto reqDto = QuestionnaireQueryConverter.INSTANCE.to(vo);
            reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));

            QuestionnaireQueryRepDto repDto;
            if (vo.getPreview() != null && vo.getPreview()) {
                Long loginUserId = getUserIdNoException();

                AnswerShareTo shareTo = shareSettingService.getAnswerShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
//                reqDto.setUserId(shareTo.getUserId());
                reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));
                repDto = questionnaireServer.preview(reqDto);
            } else {
                result = validatePwd(vo, reqDto);
                if (result != null) {
                    return result;
                }
                reqDto.setDeviceCode(vo.getDeviceCode());
                reqDto.setPwd(vo.getPwd());
                reqDto.setIp(getIp());
                reqDto.setSource(UserAgentUtil.parseSource(getHttpServletRequest()).key());
                repDto = questionnaireServer.query(reqDto);
            }

            QuestionnaireQueryRepVo repVo = QuestionnaireQueryConverter.INSTANCE.to(repDto);
            repVo.setBeginTime(new Date());
            result = new ApiReturnResult(CodeEnum.SUCCESS);
            result.setData(repVo);
        } catch (Error error) {
            logger.error("AnswerController.query error", error);
            return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
        }

        logger.info("QuestionnaireController.query result[{}]", JSON.toJSON(result));
        return result;
    }

    @RequestMapping(value = "/edit" , method = {RequestMethod.POST})
    public ApiReturnResult edit(@RequestBody QuestionnaireEditReqVo vo) {
        logger.info("QuestionnaireController.edit param[{}]", JSON.toJSON(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        QuestionnaireEditReqDto reqDto = QuestionnaireEditConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        QuestionnaireEditRepDto repDto = questionnaireServer.edit(reqDto);
        QuestionnaireEditRepVo repVo = QuestionnaireEditConverter.INSTANCE.to(repDto);

        result.setData(repVo);
        logger.info("QuestionnaireController.edit result[{}]", JSON.toJSON(result));
        return result;
    }

    @RequestMapping(value = "/gettitle", method = {RequestMethod.POST})
    public ApiReturnResult getTitle(@RequestBody GetTitleReqVo vo) {
        logger.info("QuestionnaireController.getTitle param[{}]", JSON.toJSON(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        GetTitleReqDto reqDto = QuestionnaireEditConverter.INSTANCE.to(vo);
//        reqDto.setUserId(getUserId());
        reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));

        String title = questionnaireServer.getTitle(reqDto);

        result.setData(title);
        logger.info("QuestionnaireController.getTitle result[{}]", JSON.toJSON(result));
        return result;
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public ApiReturnResult create(@RequestBody QuestionnaireCreateReqVo vo) {
        logger.info("QuestionnaireController.create param[{}]", JSON.toJSON(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        if(StringUtils.isNotBlank(vo.getEncrypt())) {
            vo.setChannel(2);
        }
        QuestionnaireCreateReqDto reqDto = QuestionnaireCreateConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());
        reqDto.setChannel(vo.getChannel());

        QuestionnaireCreateRepDto repDto = questionnaireServer.create(reqDto);
        result.setData(repDto);
        logger.info("QuestionnaireController.create result[{}]", JSON.toJSON(result));
        return result;
    }

    @RequestMapping(value = "/createdetail" , method = {RequestMethod.POST})
    public ApiReturnResult createDetail(@RequestBody String questionnaireJson) {
        logger.info("QuestionnaireController.createDetail param[{}]", questionnaireJson);
        Assert.notNull(questionnaireJson, CodeEnum.VALIDATE_PARAM_EXCEPTION);
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        QuestionnaireCreateDetailReqDto reqDto = new QuestionnaireCreateDetailReqDto();
        reqDto.setUserId(getUserId());
        reqDto.setRequestJson(questionnaireJson);

        questionnaireServer.createDetail(reqDto);
        logger.info("QuestionnaireController.createDetail result[{}]", JSON.toJSON(result));
        return result;
    }

    @RequestMapping(value = "/downloadword", method = RequestMethod.POST)
    public ApiReturnResult downloadWord(DownloadWordReqVo vo) throws IOException {
        logger.info("QuestionnaireController.downloadWord param[{}]", JSONUtil.toJSONString(vo));
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getQuestionnaireId(), "questionnaireId");
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        DownloadWordReqDto reqDto = DownloadWordConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        QstQuestionnaireInfoPo questionnaireInfoPo = downloadWordServer.getQuestionnaire(reqDto.getQuestionnaireId(), reqDto.getUserId());
        reqDto.setTitle(questionnaireInfoPo.getTitle());

        String fileName = URLEncoder.encode(questionnaireInfoPo.getTitle(), "UTF-8");
        fileName = fileName.replaceAll("\\+", "%20");

        OutputStream outputStream = getHttpServletResponse().getOutputStream();
        getHttpServletResponse().setContentType("application/vnd.ms-word");
        getHttpServletResponse().setHeader("content-disposition", "attachment;filename=" + fileName + ".doc");

        Document doc = new Document(PageSize.A4);
        try {
            // 创建word文档,并设置纸张的大小
            //建立一个书写器与document对象关联，通过书写器可以将文档写入到输出流中
            RtfWriter2.getInstance(doc, outputStream);
            doc.open();
            downloadWordServer.qstWriteWordDoc(reqDto, doc);
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("QuestionnaireController.downloadWord error[{}]", e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        } finally {
            doc.close();
            if (outputStream != null) {
                outputStream.close();
            }
        }
        logger.info("QuestionnaireController.downloadWord result[{}]", result);

        return result;
    }

    @RequestMapping(value = "/textexplaindemo", method = {RequestMethod.POST})
    public ApiReturnResult textExplainDemo() {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        TextExplainDemoRepVo repVo = new TextExplainDemoRepVo();
        repVo.setText(textExplain + textDemo);

        result.setData(repVo);
        logger.info("QuestionnaireController.textExplainDemo result[{}]", JSON.toJSON(result));
        return result;
    }

    @RequestMapping(value = "/textdemo", method = {RequestMethod.POST})
    public ApiReturnResult textDemo() {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        TextDemoRepVo repVo = new TextDemoRepVo();
        repVo.setText(textDemo);

        result.setData(repVo);
        logger.info("QuestionnaireController.textDemo result[{}]", JSON.toJSON(result));
        return result;
    }

    @RequestMapping(value = "/textcreate", method = {RequestMethod.POST})
    public ApiReturnResult textCreate(@RequestBody TextCreateReqVo reqVo) {
        logger.info("QuestionnaireController.textCreate param[{}]", reqVo);
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullExclude(reqVo);
        try {
            TextCreateRepDto repDto = questionnaireServer.textCreate(getUserId(), reqVo.getText());
            result.setData(repDto);
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.info("QuestionnaireController.textCreate exception", e);
            throw new ServerException(TEXT_CREATE_EXCEPTION);
        }

        logger.info("QuestionnaireController.textCreate result[{}]", JSON.toJSON(result));
        return result;
    }

    @RequestMapping(value = "/previewtext", method = {RequestMethod.POST})
    public ApiReturnResult previewText(@RequestBody PreviewTextReqVo reqVo) {
        logger.info("QuestionnaireController.previewText param[{}]", reqVo);
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullExclude(reqVo);

        QuestionnaireQueryRepDto repDto = questionnaireServer.previewText(23L, reqVo.getText());

        QuestionnaireQueryRepVo repVo = QuestionnaireQueryConverter.INSTANCE.to(repDto);
        repVo.setBeginTime(new Date());
        result.setData(repVo);
        logger.info("QuestionnaireController.previewText result[{}]", JSON.toJSON(result));

        return result;
    }

    @RequestMapping(value = "/createexternalquestionnaire", method = {RequestMethod.POST})
    public ApiReturnResult createExternalQuestionnaire(@RequestBody CreateExternalQuestionnaireReqVo vo) {
        logger.info("MyListController.createExternalQuestionnaire param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullExclude(vo, "questionnaireId", "userId", "email");
        validateRequestTime(vo.getRequestTime());
        String md5Key = validateSign(vo, vo.getBizId(), vo.getSign());

        if (vo.getBizId() == 2) {
            Assert.notBlank(vo.getEmail(), "email");
            vo.setUserId(Long.valueOf(userServer.getUserInfo(vo.getEmail()).getUserId()));
        } else {
            Assert.notNull(vo.getUserId(), "userId");
        }
        validateUser(vo.getUserId());
        CreateExternalQuestionnaireReqDto reqDto = MyListConverter.INSTANCE.to(vo);

        CreateExternalQuestionnaireRepDto repDto = questionnaireServer.createExternalQuestionnaire(reqDto);
        CreateExternalQuestionnaireRepVo repVo = ExternalListConverter.INSTANCE.to(repDto);

        repVo.setSign(SignGenerateUtil.generateSignByObject(repVo, md5Key));
        result.setData(repVo);

        logger.info("MyListController.createExternalQuestionnaire result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    /**
     * 第三方应用查询问券
     *
     * @param vo
     * @return
     */
    @RequestMapping(value = "/externalquery", method = {RequestMethod.POST})
    public ApiReturnResult externalQuery(@RequestBody QuestionnaireExternalQueryReqVo vo) {
        logger.info("QuestionnaireController.externalQuery param[{}]", JSON.toJSON(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        try {
            ValidateUtils.validateNotNullExclude(vo, "deviceCode", "userId");
            String key = validateSign(vo, vo.getBizId(), vo.getSign());

            QuestionnaireQueryReqDto reqDto = QuestionnaireQueryConverter.INSTANCE.to(vo);
            reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));

            reqDto.setDeviceCode(vo.getDeviceCode());
            reqDto.setIp(getIp());
            reqDto.setSource(UserAgentUtil.parseSource(getHttpServletRequest()).key());
            QuestionnaireQueryRepDto repDto = questionnaireServer.query(reqDto);

            QuestionnaireQueryRepVo repVo = QuestionnaireQueryConverter.INSTANCE.to(repDto);
            repVo.setBeginTime(new Date());
            result.setData(JSONUtil.toJSONString(repVo));
            result.setSign(SignGenerateUtil.generateSignByObject(result, key));
        } catch (Error error) {
            logger.error("AnswerController.externalQuery error", error);
            return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
        }

        logger.info("QuestionnaireController.externalQuery result[{}]", JSON.toJSON(result));
        return result;
    }

    private void validateUser(Long userId) {
        UserInfoPo userInfoPo = userInfoService.selectOne(userId);
        Assert.notNull(userInfoPo, USER_NONEXISTENT);
    }

    public void validateRequestTime(String requestTime) {
        Date validDate = DateUtil.addSeconds(DateUtil.parseDate(C_TIME_PATTON_DEFAULT, requestTime), REQUEST_TIME_VALID_TIME);
        int i = DateUtil.compareDate(validDate);
        Assert.isTrue(i == -1, REQUEST_INVALID_RETRY);
    }

    public String validateSign(Object param, Long bizId, String paramSign) {
        BizConfigPo bizConfigPo = bizConfigService.selectOne(bizId);
        Assert.notNull(bizConfigPo, BIZ_ID_NOT_EXIST);
        String sign = SignGenerateUtil.generateSignByObject(param, bizConfigPo.getMd5Key());
        Assert.isTrue(paramSign.equalsIgnoreCase(sign), SIGN_EXCEPTION);

        return bizConfigPo.getMd5Key();
    }

    public ApiReturnResult validatePwd(QuestionnaireQueryReqVo vo, QuestionnaireQueryReqDto reqDto) {
        ApiReturnResult result;
        SettingSetQueryRepDto settingConfig = settingServer.query(QuestionnaireQueryConverter.INSTANCE.to(reqDto));
        if (settingConfig != null && StringUtil.isNotBlank(settingConfig.getPwd())) {
            if (StringUtil.isBlank(vo.getPwd())) {
                result = new ApiReturnResult(CodeEnum.SUCCESS);
                QuestionnaireQueryRepVo repVo = new QuestionnaireQueryRepVo();
                repVo.setNeedPwd(true);
                result.setData(repVo);
                return result;
            }
            Assert.isTrue(settingConfig.getPwd().equals(vo.getPwd()), ANSWER_PWD_ERROR);
        }
        return null;
    }

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public ApiReturnResult list(@RequestBody QuestionnaireListReqVo vo) {
        logger.info("QustionnaireController.list param[{}]", JSONUtil.toJSONString(vo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullContain(vo, "pageSize", "page");

        QuestionnaireListReqDto reqDto = QuestionnaireQueryConverter.INSTANCE.to(vo);
        QuestionnaireListRepDto repDto = questionnaireServer.list(reqDto);
        QuestionnaireListRepVo repVo = QuestionnaireQueryConverter.INSTANCE.to(repDto);

        repVo.setPage(vo.getPage());
        repVo.setPageSize(vo.getPageSize());
        result.setData(repVo);

        logger.info("QustionnaireController.list result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    /**
     * 查询问卷信息，例如请求问卷，预览等
     *
     * @param vo
     * @return
     */
    @RequestMapping(value = "/commonquery", method = {RequestMethod.POST})
    public ApiReturnResult commonQuery(@RequestBody QuestionnaireQueryReqVo vo) {
        logger.info("QuestionnaireController.commonQuery param[{}]", JSON.toJSON(vo));

        ApiReturnResult result;
        try {
            ValidateUtils.validateNotNullContain(vo, "questionnaireId");

            QuestionnaireQueryReqDto reqDto = QuestionnaireQueryConverter.INSTANCE.to(vo);
            reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));

            QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
            Long loginUserId = qstQuestionnaireInfoPo.getUserId();

            AnswerShareTo shareTo = shareSettingService.getAnswerShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
            reqDto.setUserId(shareTo.getUserId());
            QuestionnaireQueryRepDto repDto = questionnaireServer.preview(reqDto);

            QuestionnaireQueryRepVo repVo = QuestionnaireQueryConverter.INSTANCE.to(repDto);
            repVo.setBeginTime(new Date());
            result = new ApiReturnResult(CodeEnum.SUCCESS);
            result.setData(repVo);
        } catch (Error error) {
            logger.error("AnswerController.commonQuery error", error);
            return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
        }

        logger.info("QuestionnaireController.commonQuery result[{}]", JSON.toJSON(result));
        return result;
    }

    @RequestMapping(value = "/commongettitle", method = {RequestMethod.POST})
    public ApiReturnResult commonGetTitle(@RequestBody GetTitleReqVo vo) {
        logger.info("QuestionnaireController.commonGetTitle param[{}]", JSON.toJSON(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        GetTitleReqDto reqDto = QuestionnaireEditConverter.INSTANCE.to(vo);
        QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
        reqDto.setUserId(qstQuestionnaireInfoPo.getUserId());

        String title = questionnaireServer.getTitle(reqDto);

        result.setData(title);
        logger.info("QuestionnaireController.commonGetTitle result[{}]", JSON.toJSON(result));
        return result;
    }

    /**
     * 获取问卷外观
     * @param reqVo
     * @return
     */
    @RequestMapping(value =  "/getAppearance" , method = {RequestMethod.POST})
    public ApiReturnResult getAppearance(@RequestBody QuestionnaireAppearanceReqVo reqVo) throws IOException {
        logger.info("QuestionnaireController.getAppearance param[{}]", JSON.toJSON(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        String appearance = questionnaireServer.getQuestionnaireAppearance(reqVo.getQuestionnaireId());
        QuestionnaireAppearanceRepVo repVo = new QuestionnaireAppearanceRepVo();
        repVo.setAppearance(appearance);
        repVo.setQuestionnaireId(reqVo.getQuestionnaireId());

        //获取背景图
        repVo.setBgImgs(fileServer.findBackgroundImgList());

        result.setData(repVo);
        logger.info("QuestionnaireController.getAppearance result[{}]", JSON.toJSON(result));
        return result;

    }

    /**
     * 设置问卷外观
     * @param reqVo
     * @return
     */
    @RequestMapping(value = "/setAppearance", method = {RequestMethod.POST})
    public ApiReturnResult setAppearance(@RequestBody QuestionnaireAppearanceReqVo reqVo) {
        logger.info("QuestionnaireController.setAppearance param[{}]", JSON.toJSON(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        questionnaireServer.setQuestionnaireAppearance(reqVo.getQuestionnaireId(), reqVo.getAppearance());

        return result;
    }

    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    public ApiReturnResult deleteQuestionnaire(@RequestBody QuestionnaireDeleteReqVo reqVo) {
        logger.info("QuestionnaireController.setAppearance param[{}]", JSON.toJSON(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        questionnaireServer.deleteQuestionnaire(reqVo.getQuestionnaireIdList());
        return result;
    }

    @RequestMapping(value = "/updateStatus", method = {RequestMethod.POST})
    public ApiReturnResult updateQuestionnaireStatus(@RequestBody QuestionnairePublishReqVo reqVo) {
        logger.info("QuestionnaireController.updateQuestionnaireStatus param[{}]", JSON.toJSON(reqVo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        if(Objects.equals(reqVo.getStatus(), QuestionnaireStatusEnum.INIT.key())) {
            reqVo.setStatus(QuestionnaireStatusEnum.PAUSE.key());
        }
        questionnaireServer.publishQuestionnaire(reqVo.getQuestionnaireId(), reqVo.getStatus());
        return result;
    }

    @RequestMapping(value = "/regular-questions", method = {RequestMethod.POST})
    public ApiReturnResult getRegularQuestions(@RequestBody RegularQuestionReqVo vo) {
        logger.info("QuestionnaireController.getRegularQuestions param[{}]", JSON.toJSON(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getQuestionnaireId(), "questionnaireId");
        
        RegularQuestionReqDto reqDto = RegularQuestionConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());
        
        RegularQuestionRepDto repDto = questionnaireServer.getRegularQuestions(reqDto);
        RegularQuestionRepVo repVo = RegularQuestionConverter.INSTANCE.to(repDto);
        
        result.setData(repVo);
        logger.info("QuestionnaireController.getRegularQuestions result[{}]", JSON.toJSON(result));
        return result;
    }
}

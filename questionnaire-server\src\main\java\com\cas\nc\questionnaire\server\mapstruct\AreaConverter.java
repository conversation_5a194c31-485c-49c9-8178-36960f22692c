package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.area.AreaQueryProvince2CountyRepDto;
import com.cas.nc.questionnaire.common.vo.area.AreaQueryProvince2CountyRepVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AreaConverter {
    AreaConverter INSTANCE = Mappers.getMapper(AreaConverter.class);

    AreaQueryProvince2CountyRepVo to(AreaQueryProvince2CountyRepDto repDto);
}
package com.cas.nc.questionnaire.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil extends StringUtils {

    /**
     * 数组中是否包含target
     *
     * @param array
     * @param target
     * @return 包含返回true，不包含返回false
     */
    public static boolean contains(String[] array, String target) {
        boolean contains = false;
        for (String one : array) {
            if (one.equals(target)) {
                contains = true;
                break;
            }
        }
        return contains;
    }

    /**
     * 数组中是否有某个元素包含 target
     *
     * @param array
     * @param target
     * @return 数组中某元素包含target则返回true，否则返回false
     */
    public static boolean containsItemContains(String[] array, String target) {
        boolean contains = false;
        for (String one : array) {
            if (target.indexOf(one) != -1) {
                contains = true;
            }
        }
        return contains;
    }

    public static boolean isDate(String value, String format) {

        SimpleDateFormat sdf = null;
        ParsePosition pos = new ParsePosition(0);//指定从所传字符串的首位开始解析  

        if (value == null || isEmpty(format)) {
            return false;
        }
        try {
            sdf = new SimpleDateFormat(format);
            sdf.setLenient(false);
            Date date = sdf.parse(value, pos);
            if (date == null) {
                return false;
            } else {
                return pos.getIndex() <= format.trim().length();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将指定位置的字符进行替换
     *
     * @param source 原字符串
     * @param start  开始位置	 最小为1
     * @param end    结束位置         最大为字符串长度
     * @param regix  替换后的字符串
     *               例：将  123456789 转换成 123***789  则 start=4,end=6
     * @return
     */
    public static String replaceChar(String source, int start, int end, char regix) {
        StringBuffer str = new StringBuffer(source);
        for (int i = start - 1; i < end; i++) {
            str.setCharAt(i, regix);
        }
        return str.toString();
    }

    /**
     * 是否JSON格式
     *
     * @param json
     * @return
     */
    public static boolean isJsonObject(String json) {
        if (StringUtils.isBlank(json)) {
            return false;
        }
        try {
            //JSONObject.parse(json);
            JSONObject.parseObject(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 是否JSONArray格式
     *
     * @param json
     * @return
     */
    public static boolean isJsonArray(String json) {
        if (StringUtils.isBlank(json)) {
            return false;
        }
        try {
            JSONArray.parseArray(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 基本功能：判断是否包含以"<"开头以">"结尾的标签
     * <p>
     *
     * @param str
     * @return String
     */
    public static boolean containsHtml(String str) {
        String regxpForHtml = "<([^>]*)>";
        Pattern pattern = Pattern.compile(regxpForHtml);
        Matcher matcher = pattern.matcher(str);
        boolean result1 = matcher.find();
        return result1;
    }

    /**
     * 按照特定需求截取字符串
     *
     * @param str
     * @param beginIndex
     * @param endIndex
     * @return
     */
    public static String subStr(String str, int beginIndex, int endIndex) {
        if (StringUtils.isBlank(str)) {
            return "";
        } else if (str.length() > endIndex - beginIndex) {
            return str.substring(beginIndex, endIndex);
        } else {
            return str.substring(beginIndex, str.length());
        }
    }


    /**
     * 过滤标签字符串，返回纯文本
     *
     * @param inputString
     * @return
     */
    public static String html2Text(String inputString) {
        String htmlStr = inputString; // 含html标签的字符串
        String textStr = "";
        Pattern p_script;
        Matcher m_script;
        Pattern p_style;
        Matcher m_style;
        Pattern p_html;
        Matcher m_html;

        try {
            String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>"; // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script>
            // }
            String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>"; // 定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style>
            // }
            String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式

            p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
            m_script = p_script.matcher(htmlStr);
            htmlStr = m_script.replaceAll(""); // 过滤script标签

            p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
            m_style = p_style.matcher(htmlStr);
            htmlStr = m_style.replaceAll(""); // 过滤style标签

            p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
            m_html = p_html.matcher(htmlStr);
            htmlStr = m_html.replaceAll(""); // 过滤html标签

            textStr = htmlStr;

        } catch (Exception e) {
            System.err.println("Html2Text: " + e.getMessage());
        }

        return textStr;// 返回文本字符串
    }

    public static boolean containsScript(String str) {
        String regxpForScript = ">|<|,|\\[|\\]|\\{|\\}|\\+|\\||\\'|\\\\|\\\"|:|;|\\~|\\!|\\@|\\#|\\*|\\$|\\^|\\(|\\)|`";
        Pattern pattern = Pattern.compile(regxpForScript);
        Matcher matcher = pattern.matcher(str);
        return matcher.find();
    }

    /**
     * 判断是否是手机号
     *
     * @param str
     * @return
     */
    public static boolean isPhoneNum(String str) {
        if (StringUtil.isBlank(str) || str.length() != 11) {
            return false;
        }
        String regxpForScript = "/^(13[0-9]|15[0-9]|17[0-9]|18[0-9]|14[0-9])[0-9]{8}$/";
        Pattern pattern = Pattern.compile(regxpForScript);
        Matcher matcher = pattern.matcher(str);
        return matcher.find();
    }

    public static String null2default(String param) {
        return StringUtil.isBlank(param) ? "" : param;
    }

    public static String default2Null(String param) {
        return StringUtil.isBlank(param) ? null : param;
    }

}

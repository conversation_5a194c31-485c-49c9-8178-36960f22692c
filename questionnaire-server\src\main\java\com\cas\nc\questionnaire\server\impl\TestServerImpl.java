package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.dao.po.TestPo;
import com.cas.nc.questionnaire.dao.query.TestQuery;
import com.cas.nc.questionnaire.server.TestServer;
import com.cas.nc.questionnaire.service.TestService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class TestServerImpl implements TestServer {

    @Resource
    private TestService testService;

    @Override
    public int createTest(String name) {
        TestPo testPo = new TestPo();
        testPo.setName(name);
        return testService.insert(testPo);
    }

    @Override
    public TestPo queryTest(String name) {
        TestQuery query = new TestQuery();
        query.setName(name);
        return testService.selectOne(query);
    }
}

package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.analysis.DownloadWordReqDto;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;

import java.io.IOException;

public interface DownloadWordServer {

    QstQuestionnaireInfoPo getQuestionnaire(String questionnaireId, Long userId);

    void writeWordDoc(DownloadWordReqDto reqDto, Document doc) throws DocumentException, IOException;

    void qstWriteWordDoc(DownloadWordReqDto reqDto, Document doc) throws DocumentException;
}

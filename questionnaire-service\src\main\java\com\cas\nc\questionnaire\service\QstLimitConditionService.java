package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstLimitConditionPo;
import com.cas.nc.questionnaire.dao.query.QstLimitConditionQuery;

import java.util.List;


public interface QstLimitConditionService {
    /**
     * 数据插入
     *
     * @param qstLimitConditionPo
     * @return
     */
    int insert(QstLimitConditionPo qstLimitConditionPo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstLimitConditionQuery query);

    /**
     * 删除
     *
     * @param questionnaireId
     * @param limitConditionId
     * @return
     */
    int delete(String questionnaireId, String limitConditionId, Long userId);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstLimitConditionPo> selectList(QstLimitConditionQuery query);

    /**
     * 查询list
     *
     * @param questionnaireId
     * @return
     */
    List<QstLimitConditionPo> selectList(String questionnaireId);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstLimitConditionPo selectOne(QstLimitConditionQuery query);

    /**
     * 查询单条记录
     *
     * @param limitConditionId
     * @param userId
     * @return
     */
    QstLimitConditionPo selectOne(String limitConditionId, Long userId);

    /**
     * 更新
     *
     * @param query
     * @return
     */
    int update(QstLimitConditionQuery query);
}

package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.YnEnum;
import com.cas.nc.questionnaire.dao.nosharddao.ProductAmountConfigDao;
import com.cas.nc.questionnaire.dao.po.ProductAmountConfigPo;
import com.cas.nc.questionnaire.dao.query.ProductAmountConfigQuery;
import com.cas.nc.questionnaire.service.ProductAmountConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ProductAmountConfigServiceImpl implements ProductAmountConfigService {

    @Resource
    private ProductAmountConfigDao productAmountConfigDao;

    @Override
    public List<ProductAmountConfigPo> selectEffectiveList() {
        ProductAmountConfigQuery query = new ProductAmountConfigQuery();
        query.setYn(YnEnum.Y.key());

        return productAmountConfigDao.selectList(query);
    }

    @Override
    public ProductAmountConfigPo selectOne(Long id) {
        ProductAmountConfigQuery query = new ProductAmountConfigQuery();
        query.setId(id);

        return productAmountConfigDao.selectOne(query);
    }
}

package com.cas.nc.questionnaire.common.dto.sign;

import java.util.Date;

/**
 * 问卷签到记录DTO
 */
public class QstSignRecordDto {
    /*主键ID*/
    private Long id;
    
    /*签到ID*/
    private String signId;
    
    /*问卷ID*/
    private String questionnaireId;
    
    /*问卷创建者用户ID*/
    private Long userId;
    
    /*微信用户唯一标识*/
    private String openid;
    
    /*微信用户昵称*/
    private String nickname;
    
    /*头像URL*/
    private String headimgurl;
    
    /*签到时间*/
    private Date signTime;
    
    /*签到位置纬度*/
    private Double latitude;
    
    /*签到位置经度*/
    private Double longitude;
    
    /*签到日期*/
    private Date signDate;
    
    /*状态 1-有效 0-无效*/
    private Integer status;
    
    /*更新时间*/
    private Date updateTime;
    
    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSignId() {
        return signId;
    }

    public void setSignId(String signId) {
        this.signId = signId;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadimgurl() {
        return headimgurl;
    }

    public void setHeadimgurl(String headimgurl) {
        this.headimgurl = headimgurl;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
} 
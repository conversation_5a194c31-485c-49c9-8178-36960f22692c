package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.AnalysisReportCrossAnalysisRepDto;
import com.cas.nc.questionnaire.common.dto.analysis.AnalysisReportCrossAnalysisReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.AnalysisReportCrossAnalysisRepVo;
import com.cas.nc.questionnaire.common.vo.analysis.AnalysisReportCrossAnalysisReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CrossAnalysisConverter {
    CrossAnalysisConverter INSTANCE = Mappers.getMapper(CrossAnalysisConverter.class);

    AnalysisReportCrossAnalysisReqDto to(AnalysisReportCrossAnalysisReqVo vo);

    AnalysisReportCrossAnalysisRepVo to(AnalysisReportCrossAnalysisRepDto repDto);
}
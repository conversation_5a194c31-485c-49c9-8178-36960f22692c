package com.cas.nc.questionnaire.common.enums.task;


public enum TaskStatusEnum {
    INITIALIZATION(1, "初始化"),
    LOCK(2, "锁定"),
    SUCCESS(3, "成功"),
    FAIL(4, "失败"),
    HANG(5, "挂起");

    private final Integer key;
    private final String value;

    TaskStatusEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

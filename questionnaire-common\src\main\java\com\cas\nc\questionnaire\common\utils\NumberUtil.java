package com.cas.nc.questionnaire.common.utils;

import java.math.BigDecimal;
import java.util.Objects;

import static com.cas.nc.questionnaire.common.utils.Constants.ZERO;


public class NumberUtil {
    public static Integer default2Null(Integer param) {
        return param != null && param == ZERO ? null : param;
    }

    public static Integer null2Default(Integer param) {
        return param == null ? ZERO : param;
    }

    public static BigDecimal percentageCalculation(Integer molecule, Integer denominator) {
        return BigDecimal.valueOf(molecule).divide(BigDecimal.valueOf(denominator), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal avgCalculation(Long molecule, Integer denominator) {
        if(Objects.equals(denominator, 0)) return BigDecimal.ZERO;
        return BigDecimal.valueOf(molecule).divide(BigDecimal.valueOf(denominator), 2, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal avgCalculation(Integer molecule, Integer denominator) {
        return BigDecimal.valueOf(molecule).divide(BigDecimal.valueOf(denominator), 2, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal setScale(BigDecimal target) {
        if(target == null) return BigDecimal.ZERO;
        return target.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal avgCalculation(BigDecimal source, Integer avgTarget) {
        return source.divide(BigDecimal.valueOf(avgTarget), 2, BigDecimal.ROUND_HALF_UP);
    }
}

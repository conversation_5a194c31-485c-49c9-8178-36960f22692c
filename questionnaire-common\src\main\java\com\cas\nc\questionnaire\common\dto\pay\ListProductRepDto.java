package com.cas.nc.questionnaire.common.dto.pay;

import java.math.BigDecimal;

public class ListProductRepDto {
    /*产品id*/
    private Long productId;
    /*销售价格，单位元*/
    private BigDecimal sellingPrice;
    /*销售定价，单位元*/
    private BigDecimal salesPricing;
    /*活动文案*/
    private String activityCopywriting;
    /*优先级*/
    private Integer priority;
    /*签名*/
    private String sign;

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public BigDecimal getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(BigDecimal sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public BigDecimal getSalesPricing() {
        return salesPricing;
    }

    public void setSalesPricing(BigDecimal salesPricing) {
        this.salesPricing = salesPricing;
    }

    public String getActivityCopywriting() {
        return activityCopywriting;
    }

    public void setActivityCopywriting(String activityCopywriting) {
        this.activityCopywriting = activityCopywriting;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}

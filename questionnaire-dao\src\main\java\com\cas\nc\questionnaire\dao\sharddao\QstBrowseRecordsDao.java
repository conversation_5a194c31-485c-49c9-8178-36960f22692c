package com.cas.nc.questionnaire.dao.sharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.bo.ProvinceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.SourceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.TimeAnalysisBo;
import com.cas.nc.questionnaire.dao.po.QstBrowseRecordsPo;
import com.cas.nc.questionnaire.dao.query.QstBrowseRecordsQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QstBrowseRecordsDao extends BaseDao<QstBrowseRecordsPo, QstBrowseRecordsQuery> {
    List<SourceAnalysisBo> selectSourceStatistics(@Param("item") QstBrowseRecordsQuery query);

    List<ProvinceAnalysisBo> selectProvinceStatistics(@Param("item") QstBrowseRecordsQuery query);

    List<TimeAnalysisBo> selectDayStatistics(@Param("item") QstBrowseRecordsQuery query);

    List<TimeAnalysisBo> selectWeekDayStatistics(@Param("item") QstBrowseRecordsQuery query);

    List<TimeAnalysisBo> selectMonthStatistics(@Param("item") QstBrowseRecordsQuery query);
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstEmailDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstEmailPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="email_id" jdbcType="VARCHAR" property="emailId"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="foreign_id" jdbcType="VARCHAR" property="foreignId"/>
        <result column="email_title" jdbcType="VARCHAR" property="emailTitle"/>
        <result column="sender" jdbcType="VARCHAR" property="sender"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="source_type" jdbcType="INTEGER" property="sourceType"/>
        <result column="reply_address" jdbcType="VARCHAR" property="replyAddress"/>
        <result column="addressee_type" jdbcType="INTEGER" property="addresseeType"/>
        <result column="serial_number" jdbcType="INTEGER" property="serialNumber"/>
        <result column="send_time" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="email_content" jdbcType="LONGVARCHAR" property="emailContent"/>
        <result column="addressees" jdbcType="LONGVARCHAR" property="addressees"/>
    </resultMap>
    <sql id="sql_columns">
    id,email_id,questionnaire_id,user_id,foreign_id,email_title,sender,
    status,source_type,reply_address,addressee_type,serial_number,send_time,
    update_time,create_time,email_content,addressees
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.emailId">and email_id = #{item.emailId}</if>
            <if test="null != item.questionnaireId">and questionnaire_id = #{item.questionnaireId}</if>
            <if test="null != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.foreignId">and foreign_id = #{item.foreignId}</if>
            <if test="null != item.emailTitle">and email_title = #{item.emailTitle}</if>
            <if test="null != item.sender">and sender = #{item.sender}</if>
            <if test="null != item.status">and status = #{item.status}</if>
            <if test="null != item.sourceType">and source_type = #{item.sourceType}</if>
            <if test="null != item.replyAddress">and reply_address = #{item.replyAddress}</if>
            <if test="null != item.addresseeType">and addressee_type = #{item.addresseeType}</if>
            <if test="null != item.serialNumber">and serial_number = #{item.serialNumber}</if>
            <if test="null != item.sendTime">and send_time = #{item.sendTime}</if>
            <if test="null != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime">and create_time = #{item.createTime}</if>
            <if test="null != item.emailContent">and email_content = #{item.emailContent}</if>
            <if test="null != item.addressees">and addressees = #{item.addressees}</if>

            <if test="null != item.oldStatus">and status = #{item.oldStatus}</if>
            <if test="null != item.addresseesLike">and addressees like concat('%', #{item.addresseesLike} ,'%')</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_email
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_email
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_email
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into qst_email
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.emailId">email_id,</if>
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.foreignId">foreign_id,</if>
            <if test="null != item.emailTitle">email_title,</if>
            <if test="null != item.sender">sender,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.sourceType">source_type,</if>
            <if test="null != item.replyAddress">reply_address,</if>
            <if test="null != item.addresseeType">addressee_type,</if>
            <if test="null != item.serialNumber">serial_number,</if>
            <if test="null != item.sendTime">send_time,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
            <if test="null != item.emailContent">email_content,</if>
            <if test="null != item.addressees">addressees,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.emailId">#{item.emailId},</if>
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.foreignId">#{item.foreignId},</if>
            <if test="null != item.emailTitle">#{item.emailTitle},</if>
            <if test="null != item.sender">#{item.sender},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.sourceType">#{item.sourceType},</if>
            <if test="null != item.replyAddress">#{item.replyAddress},</if>
            <if test="null != item.addresseeType">#{item.addresseeType},</if>
            <if test="null != item.serialNumber">#{item.serialNumber},</if>
            <if test="null != item.sendTime">#{item.sendTime},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
            <if test="null != item.emailContent">#{item.emailContent},</if>
            <if test="null != item.addressees">#{item.addressees},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.emailId">email_id = values(email_id),</if>
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.foreignId">foreign_id = values(foreign_id),</if>
            <if test="null != item.emailTitle">email_title = values(email_title),</if>
            <if test="null != item.sender">sender = values(sender),</if>
            <if test="null != item.status">status = values(status),</if>
            <if test="null != item.sourceType">source_type = values(source_type),</if>
            <if test="null != item.replyAddress">reply_address = values(reply_address),</if>
            <if test="null != item.addresseeType">addressee_type = values(addressee_type),</if>
            <if test="null != item.serialNumber">serial_number = values(serial_number),</if>
            <if test="null != item.sendTime">send_time = values(send_time),</if>
            <if test="null != item.emailContent">email_content = values(email_content),</if>
            <if test="null != item.addressees">addressees = values(addressees),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update qst_email
        <set>
            <if test="null != item.foreignId">foreign_id = #{item.foreignId},</if>
            <if test="null != item.emailTitle">email_title = #{item.emailTitle},</if>
            <if test="null != item.sender">sender = #{item.sender},</if>
            <if test="null != item.status">status = #{item.status},</if>
            <if test="null != item.sourceType">source_type = #{item.sourceType},</if>
            <if test="null != item.replyAddress">reply_address = #{item.replyAddress},</if>
            <if test="null != item.addresseeType">addressee_type = #{item.addresseeType},</if>
            <if test="null != item.serialNumber">serial_number = #{item.serialNumber},</if>
            <if test="null != item.sendTime">send_time = #{item.sendTime},</if>
            <if test="null != item.emailContent">email_content = #{item.emailContent},</if>
            <if test="null != item.addressees">addressees = #{item.addressees},</if>
        </set>
        where email_id = #{item.emailId}
        and user_id = #{item.userId}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_email
        <include refid="sql_where"/>
    </delete>


    <select id="selectCount" resultType="int">
        select count(1)
        from qst_email
        <include refid="sql_where"/>
    </select>


    <select id="selectListPage" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_email
        <include refid="sql_where"/>
        order by update_time desc
        <choose>
            <when test="null != item.startIndex and null != item.pageSize">
                limit #{item.startIndex}, #{item.pageSize}
            </when>
            <otherwise>
                limit 1
            </otherwise>
        </choose>
    </select>
</mapper>
package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.DATA_EXCEPTION;


public enum CanMutexEnum {
    CAN(1, "互斥"),
    CANNOT(2, "不互斥"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    CanMutexEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static CanMutexEnum toEnum(int key) {
        for (CanMutexEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        if (key == 0) {
            return CANNOT;
        }
        throw new ServerException(DATA_EXCEPTION);
    }

    public static Boolean convert2Boolean(int key) {
        if (toEnum(key).equals(CAN)) {
            return true;
        }
        return false;
    }

    public static CanMutexEnum convert2Enum(Boolean flag) {
        if (flag != null && flag) {
            return CAN;
        }
        return CANNOT;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

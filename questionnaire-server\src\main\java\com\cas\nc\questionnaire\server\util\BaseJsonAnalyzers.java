package com.cas.nc.questionnaire.server.util;

import okhttp3.Response;

/**
 * 默认json解析器
 *
 * @param <T> 解析泛型
 */
public interface BaseJsonAnalyzers<T> {
    /**
     * 成功时解析回文
     *
     * @param result 返回的原始body数据
     * @return 解析后对象
     */
    T successAnalysis(String result);

    /**
     * 默认失败返回对象
     *
     * @param response 接口response
     * @return 默认失败返回对象，默认是null
     */
    T defaultFail(Response response);
}

package com.cas.nc.questionnaire.dao.sharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.QstSignRecordPo;
import com.cas.nc.questionnaire.dao.query.QstSignRecordQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 问卷签到记录DAO
 */
public interface QstSignRecordDao extends BaseDao<QstSignRecordPo, QstSignRecordQuery> {
    
    /**
     * 根据问卷ID和openid查询某一天是否已签到
     *
     * @param questionnaireId 问卷ID
     * @param openid 微信openid
     * @param signDate 签到日期
     * @return 签到记录
     */
    QstSignRecordPo selectByQuestionnaireIdAndOpenidAndDate(@Param("questionnaireId") String questionnaireId, 
                                                            @Param("openid") String openid,
                                                            @Param("signDate") Date signDate);
    
    /**
     * 查询分页列表
     *
     * @param query 查询条件
     * @return 签到记录列表
     */
    List<QstSignRecordPo> selectListByPage(@Param("item") QstSignRecordQuery query);
    
    /**
     * 查询记录数
     *
     * @param query 查询条件
     * @return 记录数
     */
    int selectCount(@Param("item") QstSignRecordQuery query);
} 
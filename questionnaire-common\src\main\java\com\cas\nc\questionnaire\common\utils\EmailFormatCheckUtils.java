package com.cas.nc.questionnaire.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class EmailFormatCheckUtils {
    public static boolean isEmailLegal(String userNameGeneral) {
        if (StringUtil.isBlank(userNameGeneral)) {
            return false;
        }
        String regEx1 = "^([a-zA-Z0-9]*[-_]?[a-zA-Z0-9]+)*@([a-zA-Z0-9]*[-_]?[a-zA-Z0-9]+)+[\\\\.][A-Za-z]{2,3}([\\\\.][A-Za-z]{2})?$";
        Pattern p;
        Matcher m;
        p = Pattern.compile(regEx1);
        m = p.matcher(userNameGeneral);
        return m.matches();
    }
}

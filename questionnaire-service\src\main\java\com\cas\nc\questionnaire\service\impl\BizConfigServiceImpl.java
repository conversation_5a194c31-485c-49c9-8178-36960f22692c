package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.dao.nosharddao.BizConfigDao;
import com.cas.nc.questionnaire.dao.po.BizConfigPo;
import com.cas.nc.questionnaire.dao.query.BizConfigQuery;
import com.cas.nc.questionnaire.service.BizConfigService;
import com.cas.nc.questionnaire.service.util.LocalCacheUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.VALIDATE_PARAM_EXCEPTION;

@Service
public class BizConfigServiceImpl implements BizConfigService {

    /*下次刷新的时间*/
    private static Date NEXT_UPDATE_TIME = new Date();
    /*缓存刷新时间频率，单位：分钟*/
    private static final int UPDATE_FREQUENCY = 30;

    @Resource
    private BizConfigDao bizConfigDao;

    @Override
    public BizConfigPo selectOne(Long id) {
        Assert.notNull(id, VALIDATE_PARAM_EXCEPTION);

        if (DateUtil.compareDate(NEXT_UPDATE_TIME) == 1 || LocalCacheUtil.IP_RPC_CONFIG_LIST.size() == 0) {
            loadLocalCache();
            NEXT_UPDATE_TIME = DateUtil.add(new Date(), 0, 0, UPDATE_FREQUENCY, 0);
        }

        return LocalCacheUtil.BIZ_CONFIG_MAP.get(id);
    }

    private void loadLocalCache() {
        BizConfigQuery query = new BizConfigQuery();

        List<BizConfigPo> list = bizConfigDao.selectList(query);
        Map<Long, BizConfigPo> map = SafeUtil.of(list).stream().collect(Collectors.toMap(BizConfigPo::getId, v -> v));

        LocalCacheUtil.BIZ_CONFIG_MAP.clear();
        LocalCacheUtil.BIZ_CONFIG_MAP.putAll(map);
    }

}

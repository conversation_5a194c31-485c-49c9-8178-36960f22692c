package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstSignSettingPo;

/**
 * 签到设置服务接口
 */
public interface QstSignSettingService {

    /**
     * 创建签到设置
     *
     * @param signSetting 签到设置
     * @return 影响行数
     */
    int insert(QstSignSettingPo signSetting);

    /**
     * 更新签到设置
     *
     * @param signSetting 签到设置
     * @return 影响行数
     */
    int update(QstSignSettingPo signSetting);

    /**
     * 根据问卷ID查询签到设置
     *
     * @param questionnaireId 问卷ID
     * @return 签到设置
     */
    QstSignSettingPo selectByQuestionnaireId(String questionnaireId);

    /**
     * 根据ID查询签到设置
     *
     * @param id 签到设置ID
     * @return 签到设置
     */
    QstSignSettingPo selectOne(Long id);
} 
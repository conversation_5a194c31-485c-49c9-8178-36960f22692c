package com.cas.nc.questionnaire.common.utils;

import com.google.common.base.Splitter;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.beans.factory.xml.ResourceEntityResolver;
import org.springframework.beans.factory.xml.XmlBeanDefinitionReader;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;

import java.io.IOException;
import java.util.Map;
import java.util.Map.Entry;

public class SpringBeanLocator implements ApplicationContextAware {

    private final static SpringBeanLocator INSTANCE = new SpringBeanLocator();

    private ConfigurableApplicationContext context;

    public static SpringBeanLocator getInstance() {
        return INSTANCE;
    }

    /*
     * 注入ApplicationContext
     */
    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        //在加载Spring时自动获得context
        this.context = (ConfigurableApplicationContext) context;
    }

    public <T> T getBean(Class<T> requiredType) {
        return context.getBean(requiredType);
    }

    @SuppressWarnings("unchecked")
    public <T> T getBean(final String beanName) {
        return (T) context.getBean(beanName);
    }

    public <T> T getBean(String name, Class<T> requiredType) {
        return context.getBean(name, requiredType);
    }

    public boolean containsBean(String name) {
        return context.containsBean(name);
    }

    /** 
     * 向spring的beanFactory动态地装载bean 
     * @param configLocationString 要装载的bean所在的xml配置文件位置。 
    spring配置中的contextConfigLocation，同样支持诸如"/WEB-INF/ApplicationContext-*.xml"的写法。 
    @see http://elicer.iteye.com/blog/438026
    @see http://zhyi-12.iteye.com/blog/953295
          加载push的配置文件
       SpringBeanLocator.getInstance().loadConfig("/WEB-INF/spring/push-context.xml");
     */
    public void loadConfig(String configLocationString) {
        XmlBeanDefinitionReader beanDefinitionReader = new XmlBeanDefinitionReader(
                (BeanDefinitionRegistry) context.getBeanFactory());
        beanDefinitionReader.setResourceLoader(context);
        beanDefinitionReader.setEntityResolver(new ResourceEntityResolver(context));
        try {
            for (String configLocation : Splitter.on(",").split(configLocationString))
                beanDefinitionReader.loadBeanDefinitions(context.getResources(configLocation));

        } catch (BeansException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void loadBean(Class<?> clazz) {
        BeanDefinitionBuilder userBeanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(clazz);
        // 增加对象
        //  userBeanDefinitionBuilder.addPropertyReference(name, beanName)
        DefaultListableBeanFactory acf = (DefaultListableBeanFactory) context.getAutowireCapableBeanFactory();
        acf.registerBeanDefinition(clazz.getName(), userBeanDefinitionBuilder.getRawBeanDefinition());
    }

    public void loadBean(Class<?> clazz, Map<String, Object> props) {
        BeanDefinitionBuilder userBeanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(clazz);
        //  增加属性
        for (Entry<String, Object> entry : props.entrySet()) {
            userBeanDefinitionBuilder.addPropertyValue(entry.getKey(), entry.getValue());
        }
        // 增加对象
        //  userBeanDefinitionBuilder.addPropertyReference(name, beanName)
        DefaultListableBeanFactory acf = (DefaultListableBeanFactory) context.getAutowireCapableBeanFactory();
        acf.registerBeanDefinition(clazz.getName(), userBeanDefinitionBuilder.getRawBeanDefinition());

    }

    public void removeBean(String name) {
        DefaultListableBeanFactory acf = (DefaultListableBeanFactory) context.getAutowireCapableBeanFactory();
        if (acf.containsBean(name)) {
            acf.removeBeanDefinition(name);
        }
    }

    /**
     * 发送一个事件
     * @see  详解Spring事件驱动模型 http://jinnianshilongnian.iteye.com/blog/1902886
     * @param event
     */
    public void publishEvent(ApplicationEvent event) {
        context.publishEvent(event);
    }

    /**
     * 添加一个Listener
     * @param listener
     */
    public void addApplicationListener(ApplicationListener<?> listener) {
        context.addApplicationListener(listener);
    }

}

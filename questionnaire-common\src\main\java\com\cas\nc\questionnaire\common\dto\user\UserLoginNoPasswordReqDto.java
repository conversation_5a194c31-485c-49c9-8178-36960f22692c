package com.cas.nc.questionnaire.common.dto.user;

public class UserLoginNoPasswordReqDto {

    private String email;

    private Integer id;

    private String encrypt;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getEncrypt() {
        return encrypt;
    }

    public void setEncrypt(String encrypt) {
        this.encrypt = encrypt;
    }
}

package com.cas.nc.questionnaire.common.vo.setting;

import com.cas.nc.questionnaire.common.to.QuotaOptionTo;

import java.util.Date;
import java.util.List;


public class SettingQuotaRuleListRepVo {
    /*配额id*/
    private String quotaRuleId;

    /*名称*/
    private String name;

    /*条件类型，1：省，2：市，3：题目*/
    private Integer conditionType;

    /*配额*/
    private Integer quota;

    /*配额类型，1：显式，2：隐式*/
    private Integer quotaType;

    /*题目序号*/
    private Integer titleSerialNumber;

    /*选项及配额*/
    private List<QuotaOptionTo> optionInfo;

    /*创建时间*/
    private Date createTime;

    public String getQuotaRuleId() {
        return quotaRuleId;
    }

    public void setQuotaRuleId(String quotaRuleId) {
        this.quotaRuleId = quotaRuleId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getConditionType() {
        return conditionType;
    }

    public void setConditionType(Integer conditionType) {
        this.conditionType = conditionType;
    }

    public Integer getQuota() {
        return quota;
    }

    public void setQuota(Integer quota) {
        this.quota = quota;
    }

    public Integer getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(Integer quotaType) {
        this.quotaType = quotaType;
    }

    public Integer getTitleSerialNumber() {
        return titleSerialNumber;
    }

    public void setTitleSerialNumber(Integer titleSerialNumber) {
        this.titleSerialNumber = titleSerialNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public List<QuotaOptionTo> getOptionInfo() {
        return optionInfo;
    }

    public void setOptionInfo(List<QuotaOptionTo> optionInfo) {
        this.optionInfo = optionInfo;
    }
}

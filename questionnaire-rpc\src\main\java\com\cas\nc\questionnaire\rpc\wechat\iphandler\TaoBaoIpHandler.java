package com.cas.nc.questionnaire.rpc.wechat.iphandler;

import com.alibaba.fastjson.JSON;
import com.cas.nc.questionnaire.common.utils.HttpClientUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.rpc.wechat.entity.ipentity.IpAreaResult;
import com.cas.nc.questionnaire.rpc.wechat.entity.ipentity.IpResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TaoBaoIpHandler extends IpHandler {
    private static Logger logger = LoggerFactory.getLogger(TaoBaoIpHandler.class);

    @Override
    protected IpAreaResult getIpArea(String ip) {
        try {
            String paramUrl = "http://ip.taobao.com/service/getIpInfo.php?ip=" + ip;
            String resultStr = HttpClientUtil.doGet(paramUrl, "UTF-8");
            logger.info("TaoBaoIpHandler.getIpArea paramUrl[{}] result[{}]", paramUrl, resultStr);
            if (StringUtil.isNotBlank(resultStr)) {
                if (resultStr.contains("html")) {
                    return null;
                }
                IpResult ipResult = JSON.parseObject(resultStr, IpResult.class);
                IpAreaResult result = new IpAreaResult();
                result.setProvince(ipResult.getData().getRegion());
                result.setCity(ipResult.getData().getCity());

                return result;
            }
        } catch (Exception e) {
            logger.error("TaoBaoIpHandler.getIpArea param[{}] exception", ip, e);
        }

        return null;
    }
}

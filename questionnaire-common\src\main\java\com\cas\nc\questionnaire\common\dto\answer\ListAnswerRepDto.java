package com.cas.nc.questionnaire.common.dto.answer;

import java.util.Date;

public class ListAnswerRepDto {
    /*序号*/
    private String answerId;

    /*答卷提交时间*/
    private Date commitTime;

    /*答题时长*/
    private Long duration;

    /*来源*/
    private String source;

    /*ip*/
    private String ip;

    /*ip归属地*/
    private String ipArea;

    public String getAnswerId() {
        return answerId;
    }

    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }

    public Date getCommitTime() {
        return commitTime;
    }

    public void setCommitTime(Date commitTime) {
        this.commitTime = commitTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpArea() {
        return ipArea;
    }

    public void setIpArea(String ipArea) {
        this.ipArea = ipArea;
    }
}

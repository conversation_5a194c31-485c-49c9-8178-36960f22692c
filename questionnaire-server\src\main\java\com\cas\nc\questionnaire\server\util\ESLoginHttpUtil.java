package com.cas.nc.questionnaire.server.util;

import com.alibaba.fastjson.JSON;

import com.cas.nc.questionnaire.common.dto.user.ESLoginResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import okhttp3.Response;
import java.util.HashMap;

/**
 * 科技云第三方登录sdk请求工具
 */
@Component
public class ESLoginHttpUtil extends BaseOkHttpUtil {


    public ESLoginHttpUtil() {
        super();
    }

    /**
     * 默认请求参数对象构建，方便后期增加统一参数
     *
     * @return 默认请求参数对象
     */
    @Override
    protected HashMap<String, Object> defaultRequestParams() {
        HashMap<String, Object> result = new HashMap<>();
        result.put("sec", "true");
        return result;
    }

    /**
     * 获取中国科技云token等信息
     *
     * @param code 科技云登录code
     * @return 请求结果
     */
    public ESLoginResult requestESToken(String code, String redirectUri) {
        HashMap<String, Object> params = defaultRequestParams();
        if(redirectUri == null || "".equals(redirectUri)){
            redirectUri = "https://survey.casmooc.cn";
        }
        //区分协议使用不同的科技云登录key
        params.put("client_id", 25942);
        params.put("client_secret", "ENOULZyqbAuxsQnevMkErnq7DgHPi6Ir");
        params.put("grant_type", "authorization_code");
        params.put("redirect_uri", redirectUri);
        params.put("code", code);
        return analysis(postParamsInFormBody("https://passport.escience.cn/oauth2/token", defaultHeaderParams(), params),
            new BaseJsonAnalyzers<ESLoginResult>() {
                @Override
                public ESLoginResult successAnalysis(String json) {
                    ESLoginResult result = JSON.parseObject(json, ESLoginResult.class);
                    result.setUserInfoParse(JSON.parseObject(result.getUserInfo(), ESLoginResult.UserInfo.class));
                    return result;
                }
                @Override
                public ESLoginResult defaultFail(Response response) {
                    try {
                        String resultJson = response.body().string();
                        if (StringUtils.isBlank(resultJson)){
                            throw new NullPointerException("返回空");
                        } else {
                            return JSON.parseObject(resultJson, ESLoginResult.class);
                        }
                    } catch (Throwable e) {
                        logger.error("科技云登录发生错误:code = " + response.code(), e);
                        return new ESLoginResult();
                    }
                }
            });
    }

    public static void main(String[] args) {
        ESLoginHttpUtil util = new ESLoginHttpUtil();
        ESLoginResult a = util.requestESToken("aaa", "");
        System.out.println(JSON.toJSONString(a));
    }
}

package com.cas.nc.questionnaire.common.vo.analysis;

import java.util.List;

public class CrossAnalysisOptionRepVo {
    /*选项序号*/
    private Integer serialNumber;
    /*选项名称*/
    private String name;
    /*有效答题数量*/
    private Integer total;
    /*列信息*/
    private List<CrossAnalysisColumnOptionRepVo> columnOptionList;

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<CrossAnalysisColumnOptionRepVo> getColumnOptionList() {
        return columnOptionList;
    }

    public void setColumnOptionList(List<CrossAnalysisColumnOptionRepVo> columnOptionList) {
        this.columnOptionList = columnOptionList;
    }
}

package com.cas.nc.questionnaire.common.utils;

import com.cas.nc.questionnaire.common.enums.SourceEnum;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


public class UserAgentUtil {

    private final static List<String> MOBILE_FLAG_LIST = new ArrayList<String>(Arrays.asList("android", "iPhone", "windows phone", "Symbian", "WindowsPhone", "iPod", "BlackBerry", "Windows CE", "ipad", "UCWEB", "Mobile"));
    private final static List<String> PC_FLAG_LIST = new ArrayList<>(Arrays.asList("Windows 98", "Windows ME", "Windows 2000", "Windows XP", "Windows NT", "Win64", "Linux", "Ubuntu", "MAC", "Macintosh"));
    private final static List<String> WECHAT_FLAG_LIST = new ArrayList<>(Arrays.asList("MicroMessenger"));

    public static boolean isWeChat(HttpServletRequest request) {
        return judgeType(request, WECHAT_FLAG_LIST);
    }

    public static boolean isMobile(HttpServletRequest request) {
        return judgeType(request, MOBILE_FLAG_LIST);
    }

    public static boolean isPc(HttpServletRequest request) {
        return judgeType(request, PC_FLAG_LIST);
    }

    public static SourceEnum parseSource(HttpServletRequest request) {
        if (isWeChat(request)) {
            return SourceEnum.WECHAT;
        }
        if (isMobile(request)) {
            return SourceEnum.MOBILE;
        }
        if (isPc(request)) {
            return SourceEnum.PC;
        }
        return SourceEnum.ELSE;
    }

    private static boolean judgeType(HttpServletRequest request, List<String> pcFlagList) {
        String userAgent = request.getHeader("user-agent").toLowerCase();
        for (String flag : pcFlagList) {
            if (userAgent.contains(flag.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
}

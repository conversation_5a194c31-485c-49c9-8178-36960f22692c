/**
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 * <p/>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p/>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.cas.nc.questionnaire.common.shard;


import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;



import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;


public class SequenceUtil {

    private final static String MARK = "1";

    private static final long WORKER_ID = new Random().nextInt(31);
    private static final long WORKER_ID_BITS = 5L;
    private static final long MAX_WORKER_ID = -1L ^ (-1L << WORKER_ID_BITS);
    private static final long TWEPOCH = 1450340682953L;

    private static final long SEQUENCE_BITS = 12L;
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    private static final long SEQUENCE_MASK = -1L ^ (-1L << SEQUENCE_BITS);

    private long lastTimestamp = -1L;
    private long sequence;

    private static class SequenceUtilHandler {
        private static final SequenceUtil INSTANCE = new SequenceUtil();
    }

    public static SequenceUtil getInstance() {
        return SequenceUtilHandler.INSTANCE;
    }

    private SequenceUtil() {
        this(0);
    }

    public SequenceUtil(long sequence) {
        this.sequence = sequence;
        if (WORKER_ID < 0 || WORKER_ID > MAX_WORKER_ID) {
            throw new IllegalArgumentException("WORKER_ID is illegal: " + WORKER_ID);
        }
    }

    public static void main(String[] args) {
        String id = SequenceUtil.getInstance().generateTemplateId("23");
        System.out.println(id);
        System.out.println(SequenceUtil.getInstance().parse2UserId("2003200323111017603639136366592001"));
    }

    /**
     * 获取流水号
     *
     * @return
     */
    public long getGenerateId(String routeField) {
        return nextId();
    }

    private synchronized long nextId() {
        Date date = timeGen();
        long timestamp = date.getTime();
        if (timestamp < lastTimestamp) {
            throw new IllegalStateException("Clock moved backwards.");
        }
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & SEQUENCE_MASK;
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0;
        }
        lastTimestamp = timestamp;
        return (((timestamp - TWEPOCH) << TIMESTAMP_LEFT_SHIFT)//
                | (WORKER_ID << WORKER_ID_SHIFT)//
                | sequence);
    }

    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen().getTime();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen().getTime();
        }
        return timestamp;
    }

    private Date timeGen() {
        return new Date();
    }

    /**
     * 序列号类型
     */
    private enum NoType {
        QUESTIONNAIRE_ID("001"),
        FILTER_RULE_ID("002"),
        EMAIL_ID("003"),
        SMS_ID("004"),
        IMAGE_ID("005"),
        ANSWER_ID("006"),
        LIMIT_CONDITION_ID("007"),
        QUOTA_RULE_ID("008"),
        TEMPLATE_ID("009"),
        FILE_ID("010"),
        ;

        private String type;

        private NoType(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }

    public String generateImageId() {
        StringBuffer sb = initId();
        sb.append(NoType.IMAGE_ID.type);
        return sb.toString();
    }

    public String generateQuestionnaireId(String userId) {
        StringBuffer sb = initId(userId);
        sb.append(NoType.QUESTIONNAIRE_ID.type);
        return sb.toString();
    }

    public String generateTemplateId(String userId) {
        StringBuffer sb = initId(userId);
        sb.append(NoType.TEMPLATE_ID.type);
        return sb.toString();
    }

    public String generateAnswerId(String userId) {
        StringBuffer sb = initId(userId);
        sb.append(NoType.ANSWER_ID.type);
        return sb.toString();
    }

    public String generateLimitConditonId(String userId) {
        StringBuffer sb = initId(userId);
        sb.append(NoType.LIMIT_CONDITION_ID.type);
        return sb.toString();
    }

    private StringBuffer initId(String userId) {
        StringBuffer sb = new StringBuffer();
        sb.append(DateUtil.getCurrentDateStr(DateUtil.C_TIME_YYYYMMDDHHMMSS).substring(2, DateUtil.C_TIME_YYYYMMDDHHMMSS.length()));
        sb.append(userId);
        sb.append(Long.valueOf(nextId()).toString());
        return sb;
    }

    private StringBuffer initId() {
        StringBuffer sb = new StringBuffer();
        sb.append(DateUtil.getCurrentDateStr(DateUtil.C_TIME_YYYYMMDDHHMMSS).substring(2, DateUtil.C_TIME_YYYYMMDDHHMMSS.length()));
        sb.append(Long.valueOf(nextId()).toString());
        return sb;
    }

    public String generateFilterRuleId(String userId) {
        StringBuffer sb = initId(userId);
        sb.append(NoType.FILTER_RULE_ID.type);
        return sb.toString();
    }

    public String generateQuotaRuleId(String userId) {
        StringBuffer sb = initId(userId);
        sb.append(NoType.QUOTA_RULE_ID.type);
        return sb.toString();
    }

    public String generateEmailId(String userId) {
        StringBuffer sb = initId(userId);
        sb.append(NoType.EMAIL_ID.type);
        return sb.toString();
    }

    public String generateSmsId(String userId) {
        StringBuffer sb = initId(userId);
        sb.append(NoType.SMS_ID.type);
        return sb.toString();
    }

    public String generateOrderNo(String userId) {
        StringBuffer sb = initId(userId);
        return sb.toString();
    }

    public String generateFileId() {
        StringBuffer sb = initId();
        sb.append(NoType.FILE_ID.type);
        return sb.toString();
    }

    public String parse2UserId(String questionnaireId) {
        int length = questionnaireId.length();
        return StringUtil.subStr(questionnaireId, 12, length - 20);
    }

    public Long parse2UserId4Long(String questionnaireId) {
        int length = questionnaireId.length();
        return Long.valueOf(StringUtil.subStr(questionnaireId, 12, length - 20));
    }

    /**
     * 生成签到ID
     *
     * @param questionnaireId 问卷ID
     * @return 签到ID
     */
    public String generateSignId(String questionnaireId) {
        String id = "";
        String prefix = "SN";
        try {
            Date now = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmss");
            id = prefix + sdf.format(now) + questionnaireId.substring(0, Math.min(questionnaireId.length(), 6)) + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 10);
        } catch (Exception e) {
            e.printStackTrace();
            id = prefix + UUID.randomUUID().toString().replaceAll("-", "");
        }
        return id;
    }

}

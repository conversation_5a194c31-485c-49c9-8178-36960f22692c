package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class SchoolPo {
    /*主键id*/
    private Long id;

    /*学校编码(对外关联的唯一码,有校区之别)*/
    private String schoolNo;

    /*学校名称*/
    private String schoolName;

    /*新学校名称*/
    private String newSchoolName;

    /*学校简码(没有校区之分)*/
    private String schoolCode;

    /*校区*/
    private String schoolZone;

    /*校区code*/
    private String schoolZoneCode;

    /*学校层级*/
    private String schoolLevel;

    /*省份id*/
    private Long provinceId;

    /*省份名称*/
    private String provinceName;

    /*城市id*/
    private Long cityId;

    /*城市名称*/
    private String cityName;

    /*区域id*/
    private Long areaId;

    /*区域名称*/
    private String areaName;

    /*街道id*/
    private Long streetId;

    /*街道名称*/
    private String streetName;

    /*地理位置*/
    private String geographical;

    /*经度*/
    private String longitude;

    /*纬度*/
    private String latitude;

    /*轮转经度*/
    private String rotaryLongitude;

    /*轮转纬度*/
    private String rotaryLatitude;

    /*状态，1：有效，2：无效*/
    private Integer status;

    /*创建时间*/
    private Date createTime;

    /*修改时间*/
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSchoolNo() {
        return schoolNo;
    }

    public void setSchoolNo(String schoolNo) {
        this.schoolNo = schoolNo;
    }

    public String getSchoolName() {
        return schoolName;
    }

    public void setSchoolName(String schoolName) {
        this.schoolName = schoolName;
    }

    public String getNewSchoolName() {
        return newSchoolName;
    }

    public void setNewSchoolName(String newSchoolName) {
        this.newSchoolName = newSchoolName;
    }

    public String getSchoolCode() {
        return schoolCode;
    }

    public void setSchoolCode(String schoolCode) {
        this.schoolCode = schoolCode;
    }

    public String getSchoolZone() {
        return schoolZone;
    }

    public void setSchoolZone(String schoolZone) {
        this.schoolZone = schoolZone;
    }

    public String getSchoolZoneCode() {
        return schoolZoneCode;
    }

    public void setSchoolZoneCode(String schoolZoneCode) {
        this.schoolZoneCode = schoolZoneCode;
    }

    public String getSchoolLevel() {
        return schoolLevel;
    }

    public void setSchoolLevel(String schoolLevel) {
        this.schoolLevel = schoolLevel;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Long getStreetId() {
        return streetId;
    }

    public void setStreetId(Long streetId) {
        this.streetId = streetId;
    }

    public String getStreetName() {
        return streetName;
    }

    public void setStreetName(String streetName) {
        this.streetName = streetName;
    }

    public String getGeographical() {
        return geographical;
    }

    public void setGeographical(String geographical) {
        this.geographical = geographical;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getRotaryLongitude() {
        return rotaryLongitude;
    }

    public void setRotaryLongitude(String rotaryLongitude) {
        this.rotaryLongitude = rotaryLongitude;
    }

    public String getRotaryLatitude() {
        return rotaryLatitude;
    }

    public void setRotaryLatitude(String rotaryLatitude) {
        this.rotaryLatitude = rotaryLatitude;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.nosharddao.TempletQuestionnaireInfoDao;
import com.cas.nc.questionnaire.dao.po.TempletQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.query.TempletQuestionnaireInfoQuery;
import com.cas.nc.questionnaire.service.TempletQuestionnaireInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class TempletQuestionnaireInfoServiceImpl implements TempletQuestionnaireInfoService {
    private static Logger logger = LoggerFactory.getLogger(TempletQuestionnaireInfoServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private TempletQuestionnaireInfoDao templetQuestionnaireInfoDao;

    @Override
    public int insert(TempletQuestionnaireInfoPo templetQuestionnaireInfoPo) {
        return templetQuestionnaireInfoDao.insert(templetQuestionnaireInfoPo);
    }

    @Override
    public int delete(TempletQuestionnaireInfoQuery query) {
        filterCondition(query);
        return templetQuestionnaireInfoDao.delete(query);
    }

    @Override
    public List<TempletQuestionnaireInfoPo> selectList(TempletQuestionnaireInfoQuery query) {
        return templetQuestionnaireInfoDao.selectList(query);
    }

    @Override
    public TempletQuestionnaireInfoPo selectOne(TempletQuestionnaireInfoQuery query) {
        return templetQuestionnaireInfoDao.selectOne(query);
    }

    @Override
    public List<TempletQuestionnaireInfoPo> selectList(String templetCategoryId) {
        TempletQuestionnaireInfoQuery query = new TempletQuestionnaireInfoQuery();
        query.setTempletCategoryId(templetCategoryId);

        return selectList(query);
    }

    @Override
    public TempletQuestionnaireInfoPo selectOne(String templetId) {
        TempletQuestionnaireInfoQuery query = new TempletQuestionnaireInfoQuery();
        query.setTempletId(templetId);

        return selectOne(query);
    }

    private void filterCondition(TempletQuestionnaireInfoQuery query) {
        Assert.notNull(query.getId(), "id");
    }
}

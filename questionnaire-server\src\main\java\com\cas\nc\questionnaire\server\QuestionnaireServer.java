package com.cas.nc.questionnaire.server;

import com.cas.nc.questionnaire.common.dto.mylist.CreateExternalQuestionnaireRepDto;
import com.cas.nc.questionnaire.common.dto.mylist.CreateExternalQuestionnaireReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.*;
import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.google.common.collect.ListMultimap;

import java.util.List;

public interface QuestionnaireServer {
    /**
     * 查询问卷信息，例如请求问卷，预览等
     *
     * @param reqDto
     */
    QuestionnaireQueryRepDto query(QuestionnaireQueryReqDto reqDto);

    /**
     * 创建问卷信息
     *
     * @param reqDto
     * @return
     */
    QuestionnaireCreateRepDto create(QuestionnaireCreateReqDto reqDto);

    /**
     * 创建问卷明细，生成题目和选项信息
     *
     * @param reqDto
     */
    void createDetail(QuestionnaireCreateDetailReqDto reqDto);

    /**
     * 问卷编辑
     *
     * @param reqDto
     * @return
     */
    QuestionnaireEditRepDto edit(QuestionnaireEditReqDto reqDto);

    List<Object> constructTitles(List<QstTitlePo> titlePoList, ListMultimap<Integer, QstOptionPo> optionListMultiMap);

    QuestionnaireQueryRepDto preview(QuestionnaireQueryReqDto reqDto);

    TextCreateRepDto textCreate(Long userId, String text);

    CreateExternalQuestionnaireRepDto createExternalQuestionnaire(CreateExternalQuestionnaireReqDto reqDto);

    QuestionnaireQueryRepDto previewText(Long userId, String text);

    String getTitle(GetTitleReqDto reqDto);

    QuestionnaireListRepDto list(QuestionnaireListReqDto reqDto);

    /**
     * 获取问卷外观
     * @param questionnaireId
     * @return
     */
    String getQuestionnaireAppearance(String questionnaireId);

    /**
     * 设置问卷外观
     * @param questionnaireId
     * @param appearance
     */
    void setQuestionnaireAppearance(String questionnaireId, String appearance);

    /**
     * 删除问卷
     * @param questionnaireIdList
     */
    void deleteQuestionnaire(List<String> questionnaireIdList);

    /**
     * 发布/取消发布问卷
     * @param questionnaireId
     * @param status
     */
    void publishQuestionnaire(String questionnaireId, Integer status);
    
    /**
     * 获取问卷中的常规性问题
     * @param reqDto
     * @return
     */
    RegularQuestionRepDto getRegularQuestions(RegularQuestionReqDto reqDto);
}

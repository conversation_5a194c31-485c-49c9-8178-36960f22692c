package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.SourceAnalysisRepDto;
import com.cas.nc.questionnaire.common.dto.analysis.SourceAnalysisReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.SourceAnalysisReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SourceAnalysisConverter {
    SourceAnalysisConverter INSTANCE = Mappers.getMapper(SourceAnalysisConverter.class);

    SourceAnalysisReqDto to(SourceAnalysisReqVo vo);

    SourceAnalysisRepDto to(SourceAnalysisRepDto repDto);
}
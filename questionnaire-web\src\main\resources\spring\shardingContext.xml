<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:rdb="http://www.dangdang.com/schema/ddframe/rdb"
       xmlns:tx="http://www.springframework.org/schema/tx"
       default-autowire="byName"
       xsi:schemaLocation="http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd http://www.dangdang.com/schema/ddframe/rdb http://www.dangdang.com/schema/ddframe/rdb/rdb.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <bean id="dataSource_config" class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <!-- 数据库基本信息配置 -->
        <property name="driverClassName" value="${config.db.driverName}"/>
        <property name="url" value="${config.db.url}"/>
        <property name="username" value="${config.db.username}"/>
        <property name="password" value="${config.db.password}"/>
        <!-- 初始化连接数量 -->
        <property name="initialSize" value="${initialSize}"/>
        <!-- 最大并发连接数 -->
        <property name="maxActive" value="${maxActive}"/>
        <!-- 最小空闲连接数 -->
        <property name="minIdle" value="${minIdle}"/>
        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="${maxWait}"/>
        <!-- 超过时间限制是否回收 -->
        <property name="removeAbandoned" value="${removeAbandoned}"/>
        <!-- 超过时间限制多长； -->
        <property name="removeAbandonedTimeout" value="${removeAbandonedTimeout}"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="${timeBetweenEvictionRunsMillis}"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="${minEvictableIdleTimeMillis}"/>
        <!-- 用来检测连接是否有效的sql，要求是一个查询语句-->
        <property name="validationQuery" value="${validationQuery}"/>
        <!-- 申请连接的时候检测 -->
        <property name="testWhileIdle" value="${testWhileIdle}"/>
        <!-- 申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能 -->
        <property name="testOnBorrow" value="${testOnBorrow}"/>
        <!-- 归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能  -->
        <property name="testOnReturn" value="${testOnReturn}"/>
        <!--属性类型是字符串，通过别名的方式配置扩展插件，常用的插件有：
                监控统计用的filter:stat
                日志用的filter:log4j
               防御SQL注入的filter:wall -->
        <property name="filters" value="stat"/>
        <!-- 并在filters属性中配置了log4j -->
        <property name="proxyFilters">
            <list>
                <ref bean="stat-filter"/>
            </list>
        </property>
    </bean>

    <bean id="dataSource_1" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <!-- 数据库基本信息配置 -->
        <property name="driverClassName" value="${datasource1.db.driverName}"/>
        <property name="url" value="${datasource1.db.url}"/>
        <property name="username" value="${datasource1.db.username}"/>
        <property name="password" value="${datasource1.db.password}"/>
        <!-- 初始化连接数量 -->
        <property name="initialSize" value="${initialSize}"/>
        <!-- 最大并发连接数 -->
        <property name="maxActive" value="${maxActive}"/>
        <!-- 最小空闲连接数 -->
        <property name="minIdle" value="${minIdle}"/>
        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="${maxWait}"/>
        <!-- 超过时间限制是否回收 -->
        <property name="removeAbandoned" value="${removeAbandoned}"/>
        <!-- 超过时间限制多长； -->
        <property name="removeAbandonedTimeout" value="${removeAbandonedTimeout}"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="${timeBetweenEvictionRunsMillis}"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="${minEvictableIdleTimeMillis}"/>
        <!-- 用来检测连接是否有效的sql，要求是一个查询语句-->
        <property name="validationQuery" value="${validationQuery}"/>
        <!-- 申请连接的时候检测 -->
        <property name="testWhileIdle" value="${testWhileIdle}"/>
        <!-- 申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能 -->
        <property name="testOnBorrow" value="${testOnBorrow}"/>
        <!-- 归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能  -->
        <property name="testOnReturn" value="${testOnReturn}"/>
        <!--属性类型是字符串，通过别名的方式配置扩展插件，常用的插件有：
                监控统计用的filter:stat
                日志用的filter:log4j
               防御SQL注入的filter:wall -->
        <property name="filters" value="stat"/>
        <property name="proxyFilters">
            <list>
                <ref bean="stat-filter"/>
            </list>
        </property>
    </bean>

    <rdb:strategy id="tableShardingStrategy" sharding-columns="user_id"
                  algorithm-class="com.cas.nc.questionnaire.common.shard.algorithm.SingleKeyModuloTableShardingAlgorithm"/>
    <rdb:strategy id="answerTableShardingStrategy" sharding-columns="questionnaire_id"
                  algorithm-class="com.cas.nc.questionnaire.common.shard.algorithm.AnswerSingleKeyModuloTableShardingAlgorithm"/>

    <rdb:data-source id="shardingDataSource">
        <rdb:sharding-rule data-sources="dataSource_1">
            <rdb:table-rules>
                <rdb:table-rule logic-table="qst_email" actual-tables="qst_email_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_filter_rule" actual-tables="qst_filter_rule_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_limit_condition" actual-tables="qst_limit_condition_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_limit_rule" actual-tables="qst_limit_rule_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_option" actual-tables="qst_option_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_questionnaire_info" actual-tables="qst_questionnaire_info_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_quota_option_rule" actual-tables="qst_quota_option_rule_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_quota_rule" actual-tables="qst_quota_rule_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_sms" actual-tables="qst_sms_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_title" actual-tables="qst_title_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>

                <rdb:table-rule logic-table="answer_info" actual-tables="answer_info_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="answer_option" actual-tables="answer_option_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>
                <rdb:table-rule logic-table="qst_browse_records" actual-tables="qst_browse_records_${0..127}"
                                table-strategy="tableShardingStrategy">
                </rdb:table-rule>

            </rdb:table-rules>
        </rdb:sharding-rule>
    </rdb:data-source>

    <!-- 慢SQL记录-->
    <bean id="stat-filter" class="com.alibaba.druid.filter.stat.StatFilter">
        <property name="mergeSql" value="true"/>
        <property name="slowSqlMillis" value="10000"/>
        <property name="logSlowSql" value="true"/>
    </bean>

    <bean id="druid-stat-interceptor" class="com.alibaba.druid.support.spring.stat.DruidStatInterceptor">
    </bean>
    <bean id="druid-stat-pointcut" class="org.springframework.aop.support.JdkRegexpMethodPointcut" scope="prototype">
        <property name="patterns">
            <list>

                <value>com.cas.nc.questionnaire.service.*</value>
                <value>com.cas.nc.questionnaire.dao.*</value>
            </list>
        </property>
    </bean>
    <aop:config proxy-target-class="true">
        <aop:advisor advice-ref="druid-stat-interceptor" pointcut-ref="druid-stat-pointcut"/>
    </aop:config>

    <!--第一个 start-->
    <bean id="sqlSessionFactory1" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="shardingDataSource"/>
        <property name="configLocation" value="classpath:shard-mybatis.xml"/>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.cas.nc.questionnaire.dao.sharddao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory1"/>
    </bean>

    <bean id="transactionManager1" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="shardingDataSource"/>
    </bean>
    <tx:annotation-driven transaction-manager="transactionManager1"/>
    <!--第一个 end-->

    <!--第二个 start-->
    <bean id="sqlSessionFactory2" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource_config"/>
        <property name="configLocation" value="classpath:noshard-mybatis.xml"/>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.cas.nc.questionnaire.dao.nosharddao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory2"/>
    </bean>

    <bean id="transactionManager2" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource_config"/>
    </bean>
    <tx:annotation-driven transaction-manager="transactionManager2"/>
    <!--第二个 end-->
</beans>

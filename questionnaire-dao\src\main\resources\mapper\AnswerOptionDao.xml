<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.AnswerOptionDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.AnswerOptionPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="answer_id" jdbcType="VARCHAR" property="answerId"/>
        <result column="option_id" jdbcType="VARCHAR" property="optionId"/>
        <result column="serial_number" jdbcType="INTEGER" property="serialNumber"/>
        <result column="answer_user_id" jdbcType="VARCHAR" property="answerUserId"/>
        <result column="title_serial_number" jdbcType="INTEGER" property="titleSerialNumber"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="column_title" jdbcType="VARCHAR" property="columnTitle"/>
        <result column="option_content" jdbcType="VARCHAR" property="optionContent"/>
        <result column="write_content" jdbcType="VARCHAR" property="writeContent"/>
        <result column="score" jdbcType="BIGINT" property="score"/>
        <result column="row_number" jdbcType="INTEGER" property="rowNumber"/>
        <result column="column_number" jdbcType="INTEGER" property="columnNumber"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,questionnaire_id,user_id,answer_id,option_id,serial_number,answer_user_id,
    title_serial_number,title,type,status,column_title,option_content,
    write_content,score,`row_number`,column_number,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.questionnaireId">and questionnaire_id = #{item.questionnaireId}</if>
            <if test="null != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.answerId">and answer_id = #{item.answerId}</if>
            <if test="null != item.optionId">and option_id = #{item.optionId}</if>
            <if test="null != item.serialNumber">and serial_number = #{item.serialNumber}</if>
            <if test="null != item.answerUserId">and answer_user_id = #{item.answerUserId}</if>
            <if test="null != item.titleSerialNumber">and title_serial_number = #{item.titleSerialNumber}</if>
            <if test="null != item.title">and title = #{item.title}</if>
            <if test="null != item.type">and type = #{item.type}</if>
            <if test="null != item.status">and status = #{item.status}</if>
            <if test="null != item.columnTitle">and column_title = #{item.columnTitle}</if>
            <if test="null != item.optionContent">and option_content = #{item.optionContent}</if>
            <if test="null != item.writeContent">and write_content = #{item.writeContent}</if>
            <if test="null != item.score">and score = #{item.score}</if>
            <if test="null != item.rowNumber">and `row_number` = #{item.rowNumber}</if>
            <if test="null != item.columnNumber">and column_number = #{item.columnNumber}</if>
            <if test="null != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime">and create_time = #{item.createTime}</if>

            <if test="null != item.titleSerialNumberList and item.titleSerialNumberList.size() != 0">
                and title_serial_number in
                <foreach collection="item.titleSerialNumberList" index="index" item="tag" open="(" separator=","
                         close=")">
                    #{tag}
                </foreach>
            </if>
            <if test="null != item.answerIdList and item.answerIdList.size() > 0">
                and answer_id in
                <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_option
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_option
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_option
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into answer_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.answerId">answer_id,</if>
            <if test="null != item.optionId">option_id,</if>
            <if test="null != item.serialNumber">serial_number,</if>
            <if test="null != item.answerUserId">answer_user_id,</if>
            <if test="null != item.titleSerialNumber">title_serial_number,</if>
            <if test="null != item.title">title,</if>
            <if test="null != item.type">type,</if>
            <if test="null != item.status">status,</if>
            <if test="null != item.columnTitle">column_title,</if>
            <if test="null != item.optionContent">option_content,</if>
            <if test="null != item.writeContent">write_content,</if>
            <if test="null != item.score">score,</if>
            <if test="null != item.rowNumber">`row_number`,</if>
            <if test="null != item.columnNumber">column_number,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.answerId">#{item.answerId},</if>
            <if test="null != item.optionId">#{item.optionId},</if>
            <if test="null != item.serialNumber">#{item.serialNumber},</if>
            <if test="null != item.answerUserId">#{item.answerUserId},</if>
            <if test="null != item.titleSerialNumber">#{item.titleSerialNumber},</if>
            <if test="null != item.title">#{item.title},</if>
            <if test="null != item.type">#{item.type},</if>
            <if test="null != item.status">#{item.status},</if>
            <if test="null != item.columnTitle">#{item.columnTitle},</if>
            <if test="null != item.optionContent">#{item.optionContent},</if>
            <if test="null != item.writeContent">#{item.writeContent},</if>
            <if test="null != item.score">#{item.score},</if>
            <if test="null != item.rowNumber">#{item.rowNumber},</if>
            <if test="null != item.columnNumber">#{item.columnNumber},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.answerId">answer_id = values(answer_id),</if>
            <if test="null != item.optionId">option_id = values(option_id),</if>
            <if test="null != item.serialNumber">serial_number = values(serial_number),</if>
            <if test="null != item.answerUserId">answer_user_id = values(answer_user_id),</if>
            <if test="null != item.titleSerialNumber">title_serial_number = values(title_serial_number),</if>
            <if test="null != item.title">title = values(title),</if>
            <if test="null != item.type">type = values(type),</if>
            <if test="null != item.status">status = values(status),</if>
            <if test="null != item.columnTitle">column_title = values(column_title),</if>
            <if test="null != item.optionContent">option_content = values(option_content),</if>
            <if test="null != item.writeContent">write_content = values(write_content),</if>
            <if test="null != item.score">score = values(score),</if>
            <if test="null != item.rowNumber">`row_number` = values(`row_number`),</if>
            <if test="null != item.columnNumber">column_number = values(column_number),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update answer_option
        <set>
            <if test="null != item.questionnaireId">questionnaire_id = #{item.questionnaireId},</if>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.answerId">answer_id = #{item.answerId},</if>
            <if test="null != item.optionId">option_id = #{item.optionId},</if>
            <if test="null != item.serialNumber">serial_number = #{item.serialNumber},</if>
            <if test="null != item.answerUserId">answer_user_id = #{item.answerUserId},</if>
            <if test="null != item.titleSerialNumber">title_serial_number = #{item.titleSerialNumber},</if>
            <if test="null != item.title">title = #{item.title},</if>
            <if test="null != item.type">type = #{item.type},</if>
            <if test="null != item.status">status = #{item.status},</if>
            <if test="null != item.columnTitle">column_title = #{item.columnTitle},</if>
            <if test="null != item.optionContent">option_content = #{item.optionContent},</if>
            <if test="null != item.writeContent">write_content = #{item.writeContent},</if>
            <if test="null != item.score">score = #{item.score},</if>
            <if test="null != item.rowNumber">`row_number` = #{item.rowNumber},</if>
            <if test="null != item.columnNumber">column_number = #{item.columnNumber},</if>
        </set>
        where user_id = #{item.userId}
        <choose>
            <when test="item.answerId != null">
                and answer_id = #{item.answerId}
            </when>
            <otherwise>
                and id = #{item.id}
            </otherwise>
        </choose>
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from answer_option
        <include refid="sql_where"/>
    </delete>

    <select id="selectFilterList" resultMap="BaseResultMap">
        select answer_id, count(id) a
        from answer_option
        where questionnaire_id = #{item.questionnaireId}
        and serial_number = #{item.serialNumber}
        and option_number
        <if test="item.intFlag">
            in
        </if>
        <if test="!item.intFlag">
            not in
        </if>
        <foreach collection="item.optionNumberList" index="index" item="tag" open="(" separator="," close=")">
            #{tag}
        </foreach>
        group by answer_id having a = #{item.optionNumberList.size()}
    </select>

    <select id="selectTitleAnalysis" resultType="AnswerAnalysisBo">
        select t.title_serial_number, count(t.title_serial_number) total
        from (
        select title_serial_number, answer_id
        from answer_option
        where questionnaire_id = #{item.questionnaireId}
        and user_id = #{item.userId}
        and status = #{item.status}
        <if test="item.answerIdList!=null and item.answerIdList.size() > 0">
            and answer_id in
            <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        group by answer_id, title_serial_number
        ) t
        group by t.title_serial_number
        order by t.title_serial_number
    </select>

    <select id="selectOneDimensionalAnalysis" resultType="AnswerAnalysisBo">
        select serial_number, count(serial_number) total
        from answer_option
        where questionnaire_id = #{item.questionnaireId}
        and title_serial_number = #{item.titleSerialNumber}
        and user_id = #{item.userId}
        and status = #{item.status}
        <if test="item.answerIdList!=null and item.answerIdList.size() > 0">
            and answer_id in
            <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        group by serial_number
        order by serial_number
    </select>

    <select id="selectTwoDimensionalAnalysis" resultType="AnswerAnalysisBo">
        select `row_number`, serial_number, count(serial_number) total
        from answer_option
        where questionnaire_id = #{item.questionnaireId}
        and title_serial_number = #{item.titleSerialNumber}
        and user_id = #{item.userId}
        and status = #{item.status}
        <if test="item.answerIdList!=null and item.answerIdList.size() > 0">
            and answer_id in
            <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        group by `row_number`, serial_number
        order by `row_number`, serial_number
    </select>

    <select id="selectBiZhongAnalysis" resultType="AnswerAnalysisBo">
        select `row_number`, count(`row_number`) total, avg(serial_number) averageValue
        from answer_option
        where questionnaire_id = #{item.questionnaireId}
        and title_serial_number = #{item.titleSerialNumber}
        and user_id = #{item.userId}
        and status = #{item.status}
        <if test="item.answerIdList!=null and item.answerIdList.size() > 0">
            and answer_id in
            <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        group by `row_number`
        order by `row_number`
    </select>

    <select id="selectPaiXuAnalysis" resultType="AnswerAnalysisBo">
        select serial_number, sum(score) total
        from answer_option
        where questionnaire_id = #{item.questionnaireId}
        and title_serial_number = #{item.titleSerialNumber}
        and user_id = #{item.userId}
        and status = #{item.status}
        <if test="item.answerIdList!=null and item.answerIdList.size() > 0">
            and answer_id in
            <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        group by serial_number
    </select>

    <select id="selectHuaDongTiaoAnalysis" resultType="AnswerAnalysisBo">
        select count(serial_number) total, sum(serial_number) totalValue, avg(serial_number) averageValue
        from answer_option
        where questionnaire_id = #{item.questionnaireId}
        and title_serial_number = #{item.titleSerialNumber}
        and user_id = #{item.userId}
        and status = #{item.status}
        <if test="item.answerIdList!=null and item.answerIdList.size() > 0">
            and answer_id in
            <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
    </select>

    <select id="selectJuZhenHuaDongTiaoAnalysis" resultType="AnswerAnalysisBo">
        select `row_number`, count(`row_number`) total, sum(serial_number) totalValue, avg(serial_number) averageValue
        from answer_option
        where questionnaire_id = #{item.questionnaireId}
        and title_serial_number = #{item.titleSerialNumber}
        and user_id = #{item.userId}
        and status = #{item.status}
        <if test="item.answerIdList!=null and item.answerIdList.size() > 0">
            and answer_id in
            <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        group by `row_number`
        order by `row_number`
    </select>

    <select id="selectThreeDimensionalAnalysis" resultType="AnswerAnalysisBo">
        select `row_number`, column_number, serial_number, count(serial_number) total
        from answer_option
        where questionnaire_id = #{item.questionnaireId}
        and title_serial_number = #{item.titleSerialNumber}
        and user_id = #{item.userId}
        and status = #{item.status}
        <if test="item.answerIdList!=null and item.answerIdList.size() > 0">
            and answer_id in
            <foreach collection="item.answerIdList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        group by `row_number`, column_number, serial_number
        order by `row_number`, column_number, serial_number
    </select>

    <select id="selectCount" resultType="int">
        select count(*)
        from answer_option
        <include refid="sql_where"/>
    </select>

    <select id="selectOneDimensionalIndependentVariableCrossAnalysis" resultType="AnswerAnalysisOptionBo">
        select a.title_serial_number firstTitleSerialNumber, a.`row_number` firstRowNumber,
        a.column_number firstcolumnNumber, a.serial_number firstSerialNumber,
        count(1) total

        from answer_option a

        where a.title_serial_number = #{item.firstTitleSerialNumber}
        and a.questionnaire_id = #{item.questionnaireId}
        and a.user_id = #{item.userId}

        group by a.title_serial_number, a.`row_number`,
        a.column_number, a.serial_number
    </select>

    <select id="selectOneDimensionalContainNullCrossAnalysis" resultType="AnswerAnalysisOptionBo">
        select a.title_serial_number firstTitleSerialNumber, a.`row_number` firstRowNumber,
        a.column_number firstcolumnNumber, a.serial_number firstSerialNumber,
        b.title_serial_number secondTitleSerialNumber, b.`row_number` secondRowNumber,
        b.column_number secondColumnNumber, b.serial_number secondSerialNumber,
        count(1) total

        from answer_option a
        left join answer_option b
        on a.answer_id = b.answer_id
        and b.questionnaire_id = #{item.questionnaireId}
        and b.title_serial_number = #{item.secondTitleSerialNumber}
        <if test="item.rowNumber != null">
            and b.`row_number` = #{item.rowNumber}
        </if>
        and b.user_id = #{item.userId}

        where a.title_serial_number = #{item.firstTitleSerialNumber}
        and a.questionnaire_id = #{item.questionnaireId}
        and a.user_id = #{item.userId}
        and b.title_serial_number is null

        group by a.title_serial_number, a.`row_number`,
        a.column_number, a.serial_number,
        b.title_serial_number, b.`row_number`,
        b.column_number, b.serial_number
    </select>

    <select id="selectOneDimensionalNotNullCrossAnalysis" resultType="AnswerAnalysisOptionBo">
        select a.title_serial_number firstTitleSerialNumber, a.`row_number` firstRowNumber,
        a.column_number firstcolumnNumber, a.serial_number firstSerialNumber,
        b.title_serial_number secondTitleSerialNumber, b.`row_number` secondRowNumber,
        b.column_number secondColumnNumber, b.serial_number secondSerialNumber,
        count(1) total

        from answer_option a
        left join answer_option b
        on a.answer_id = b.answer_id
        and b.questionnaire_id = #{item.questionnaireId}
        and b.title_serial_number = #{item.secondTitleSerialNumber}
        <if test="item.rowNumber != null">
            and b.`row_number` = #{item.rowNumber}
        </if>
        and b.user_id = #{item.userId}

        where a.title_serial_number = #{item.firstTitleSerialNumber}
        and a.questionnaire_id = #{item.questionnaireId}
        and a.user_id = #{item.userId}
        and b.title_serial_number is not null

        group by a.title_serial_number, a.`row_number`,
        a.column_number, a.serial_number,
        b.title_serial_number, b.`row_number`,
        b.column_number, b.serial_number
    </select>

    <select id="selectTwoDimensionalIndependentVariableCrossAnalysis" resultType="AnswerAnalysisOptionBo">
        select a.title_serial_number firstTitleSerialNumber, a.`row_number` firstRowNumber,
        a.column_number firstcolumnNumber, a.serial_number firstSerialNumber,
        b.title_serial_number secondTitleSerialNumber, b.`row_number` secondRowNumber,
        b.column_number secondColumnNumber, b.serial_number secondSerialNumber,
        count(1) total

        from answer_option a
        left join answer_option b
        on a.answer_id = b.answer_id
        and b.questionnaire_id = #{item.questionnaireId}
        and b.user_id = #{item.userId}

        where a.title_serial_number = #{item.firstTitleSerialNumber}
        and b.title_serial_number = #{item.secondTitleSerialNumber}
        and a.questionnaire_id = #{item.questionnaireId}
        and a.user_id = #{item.userId}

        group by a.title_serial_number, a.`row_number`,
        a.column_number, a.serial_number,
        b.title_serial_number, b.`row_number`,
        b.column_number, b.serial_number
    </select>

    <select id="selectTwoDimensionalContainNullCrossAnalysis" resultType="AnswerAnalysisOptionBo">
        select a.title_serial_number firstTitleSerialNumber, a.`row_number` firstRowNumber,
        a.column_number firstcolumnNumber, a.serial_number firstSerialNumber,
        b.title_serial_number secondTitleSerialNumber, b.`row_number` secondRowNumber,
        b.column_number secondColumnNumber, b.serial_number secondSerialNumber,
        c.title_serial_number thirdTitleSerialNumber, c.`row_number` thirdRowNumber,
        c.column_number thirdColumnNumber, c.serial_number thirdSerialNumber,
        count(1) total

        from answer_option a
        left join answer_option b
        on a.answer_id = b.answer_id
        left join answer_option c
        on b.answer_id = c.answer_id
        and c.title_serial_number = #{item.thirdTitleSerialNumber}
        <if test="item.rowNumber != null">
            and c.`row_number` = #{item.rowNumber}
        </if>
        and b.questionnaire_id = #{item.questionnaireId}
        and c.questionnaire_id = #{item.questionnaireId}
        and b.user_id = #{item.userId}
        and c.user_id = #{item.userId}

        where a.title_serial_number = #{item.firstTitleSerialNumber}
        and b.title_serial_number = #{item.secondTitleSerialNumber}
        and a.questionnaire_id = #{item.questionnaireId}
        and a.user_id = #{item.userId}
        and c.title_serial_number is null

        group by a.title_serial_number, a.`row_number`,
        a.column_number, a.serial_number,
        b.title_serial_number, b.`row_number`,
        b.column_number, b.serial_number,
        c.title_serial_number, c.`row_number`,
        c.column_number, c.serial_number
    </select>

    <select id="selectTwoDimensionalNotNullCrossAnalysis" resultType="AnswerAnalysisOptionBo">
        select a.title_serial_number firstTitleSerialNumber, a.`row_number` firstRowNumber,
        a.column_number firstcolumnNumber, a.serial_number firstSerialNumber,
        b.title_serial_number secondTitleSerialNumber, b.`row_number` secondRowNumber,
        b.column_number secondColumnNumber, b.serial_number secondSerialNumber,
        c.title_serial_number thirdTitleSerialNumber, c.`row_number` thirdRowNumber,
        c.column_number thirdColumnNumber, c.serial_number thirdSerialNumber,
        count(1) total

        from answer_option a
        left join answer_option b
        on a.answer_id = b.answer_id
        left join answer_option c
        on b.answer_id = c.answer_id
        and c.title_serial_number = #{item.thirdTitleSerialNumber}
        <if test="item.rowNumber != null">
            and c.`row_number` = #{item.rowNumber}
        </if>
        and b.questionnaire_id = #{item.questionnaireId}
        and c.questionnaire_id = #{item.questionnaireId}
        and b.user_id = #{item.userId}
        and c.user_id = #{item.userId}

        where a.title_serial_number = #{item.firstTitleSerialNumber}
        and b.title_serial_number = #{item.secondTitleSerialNumber}
        and a.questionnaire_id = #{item.questionnaireId}
        and a.user_id = #{item.userId}
        and c.title_serial_number is not null

        group by a.title_serial_number, a.`row_number`,
        a.column_number, a.serial_number,
        b.title_serial_number, b.`row_number`,
        b.column_number, b.serial_number,
        c.title_serial_number, c.`row_number`,
        c.column_number, c.serial_number
    </select>

    <select id="selectListByPage" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from answer_option
        <include refid="sql_where"/>

        order by create_time desc, id desc
        limit #{item.startIndex}, #{item.pageSize}
    </select>

    <select id="selectAnswerIdList" resultType="String">
        SELECT ao.answer_id
        FROM answer_info_24 ai
        LEFT JOIN answer_option_24 ao
        ON ai.answer_id = ao.answer_id
        WHERE ai.user_id = #{item.userId}
        AND ao.user_id = #{item.userId}
        AND ai.questionnaire_id = #{item.questionnaireId}
        AND ao.questionnaire_id = #{item.questionnaireId}
        AND ai.status = #{item.status}
        <if test="item.titleSerialNumber!=null">
            AND ao.title_serial_number = #{item.titleSerialNumber}
        </if>
        <if test="item.rowNumber!=null">
            AND ao.`row_number` = #{item.rowNumber}
        </if>
        <if test="item.columnNumber!=null">
            AND ao.column_number = #{item.columnNumber}
        </if>
        <if test="item.serialNumber!=null">
            AND ao.serial_number = #{item.serialNumber}
        </if>
        <if test="item.neqSerialNumber!=null">
            AND ao.serial_number != #{item.neqSerialNumber}
        </if>
        <if test="item.lessThanSerialNumber!=null">
            <![CDATA[ AND ao.serial_number < #{item.lessThanSerialNumber} ]]>
        </if>
        <if test="item.greaterThanSerialNumber!=null">
            AND ao.serial_number > #{item.greaterThanSerialNumber}
        </if>
        <if test="item.beginSerialNumber!=null">
            AND ao.serial_number >= #{item.beginSerialNumber}
        </if>
        <if test="item.endSerialNumber!=null">
            <![CDATA[ AND ao.serial_number <= #{item.endSerialNumber}]]>
        </if>
        <if test="item.serialNumberList!=null and item.serialNumberList.size() > 0">
            AND ao.serial_number in
            <foreach collection="item.serialNumberList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>

        <if test="item.provinceList!=null and item.provinceList.size() > 0">
            AND ai.province in
            <foreach collection="item.provinceList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        <if test="item.cityList!=null and item.cityList.size() > 0">
            AND ai.city in
            <foreach collection="item.cityList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        <if test="item.sourceList!=null and item.sourceList.size() > 0">
            AND ai.source in
            <foreach collection="item.sourceList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        <if test="item.ip!=null">
            AND ai.ip = #{item.ip}
        </if>
        <if test="item.createTime!=null">
            AND ai.create_time = #{item.createTime}
        </if>
        <if test="item.neqCreateTime!=null">
            AND ai.create_time = #{item.neqCreateTime}
        </if>
        <if test="item.greaterThanCreateTime!=null">
            AND ai.create_time > #{item.greaterThanCreateTime}
        </if>
        <if test="item.lessThanCreateTime!=null">
            <![CDATA[ AND ai.create_time < #{item.lessThanCreateTime}]]>
        </if>
        <if test="item.beginCreateTime!=null">
            AND ai.create_time >= #{item.beginCreateTime}
        </if>
        <if test="item.endCreateTime!=null">
            <![CDATA[ AND ai.create_time <= #{item.endCreateTime}]]>
        </if>
        <if test="item.duration!=null">
            AND ai.duration = #{item.duration}
        </if>
        <if test="item.neqDuration!=null">
            AND ai.duration != #{item.neqDuration}
        </if>
        <if test="item.greaterThanDuration!=null">
            AND ai.duration > #{item.greaterThanDuration}
        </if>
        <if test="item.lessThanDuration!=null">
            <![CDATA[ AND ai.duration < #{item.lessThanDuration}]]>
        </if>
        <if test="item.beginDuration!=null">
            AND ai.duration >= #{item.beginDuration}
        </if>
        <if test="item.endDuration!=null">
            <![CDATA[ AND ai.duration <= #{item.endDuration}]]>
        </if>

        <if test="item.optionList!=null and item.optionList.size() > 0">
            AND ao.serial_number in
            <foreach collection="item.optionList" index="index" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>

        <if test="null != item.writeContent">and write_content = #{item.writeContent}</if>
        <if test="item.neqWriteContent!=null">
            and ao.write_content != #{item.neqWriteContent}
        </if>
        <if test="item.containWriteContent!=null">
            and ao.write_content like concat('%', #{item.containWriteContent} ,'%')
        </if>
        <if test="item.notContainWriteContent!=null">
            and ao.write_content not like concat('%', #{item.notContainWriteContent} ,'%')
        </if>
        <if test="item.lessThanWriteContent!=null">
            <![CDATA[ and ao.write_content < #{item.lessThanWriteContent} ]]>
        </if>
        <if test="item.greaterThanWriteContent!=null">
            and ao.write_content > #{item.greaterThanWriteContent}
        </if>
        <if test="item.beginWriteContent!=null">
            AND ai.duration >= #{item.beginWriteContent}
        </if>
        <if test="item.endWriteContent!=null">
            <![CDATA[ AND ai.duration <= #{item.endWriteContent}]]>
        </if>

    </select>

</mapper>

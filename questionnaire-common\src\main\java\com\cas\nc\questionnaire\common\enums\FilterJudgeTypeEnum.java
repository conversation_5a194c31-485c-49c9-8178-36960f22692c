package com.cas.nc.questionnaire.common.enums;


public enum FilterJudgeTypeEnum {
    Y(1, "是"),
    N(2, "非"),
    LESS_THAN(3, "小于"),
    EQUAL(4, "等于"),
    MORE_THAN(5, "大于"),
    BETWEEN(6, "之间"),
    CONTAIN(7, "包含"),
    NOT_CONTAIN(8, "不包含"),
    NOT_EQUAL(9, "不等于"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    FilterJudgeTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static boolean isY(int key) {
        return Y.key.intValue() == key;
    }

    public static boolean isN(int key) {
        return N.key.intValue() == key;
    }

    public static boolean isLessThan(int key) {
        return LESS_THAN.key.intValue() == key;
    }

    public static boolean isEqual(int key) {
        return EQUAL.key.intValue() == key;
    }

    public static boolean isMoreThan(int key) {
        return MORE_THAN.key.intValue() == key;
    }

    public static boolean isContain(int key) {
        return CONTAIN.key.intValue() == key;
    }

    public static boolean isNotContain(int key) {
        return NOT_CONTAIN.key.intValue() == key;
    }

    public static boolean isBetween(int key) {
        return BETWEEN.key.intValue() == key;
    }

    public static boolean isNotEqual(int key) {
        return NOT_EQUAL.key.intValue() == key;
    }

    public static boolean isBetLessMore(int key) {
        return BETWEEN.key.intValue() == key
                || LESS_THAN.key.intValue() == key
                || MORE_THAN.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

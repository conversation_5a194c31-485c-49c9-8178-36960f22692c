package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.dao.nosharddao.TaskConfigDao;
import com.cas.nc.questionnaire.dao.po.TaskConfigPo;
import com.cas.nc.questionnaire.service.TaskConfigService;
import com.cas.nc.questionnaire.service.util.LocalCacheUtil;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Constants.ZERO;

@Service
public class TaskConfigServiceImpl implements TaskConfigService {
    /*下次刷新的时间*/
    private static Date NEXT_UPDATE_TIME = new Date();
    /*缓存刷新时间频率，单位：小时*/
    private static final int UPDATE_FREQUENCY = 1;
    private static Logger logger = LoggerFactory.getLogger(TaskConfigServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private TaskConfigDao taskConfigDao;

    @Override
    public List<TaskConfigPo> selectTaskConfig() {
        return taskConfigDao.selectTaskConfig();
    }

    /**
     * 加载任务配置信息
     */
    @PostConstruct
    @Override
    public void reloadTaskConfigInfo() {
        List<TaskConfigPo> configPoList = taskConfigDao.selectTaskConfig();
        if (configPoList != null && configPoList.size() > ZERO){
            logger.info("TaskConfigServiceImpl.reloadTaskConfigInfo info[{}]", JSONUtil.toJSONString(configPoList));
            Map<Integer, TaskConfigPo> map = Maps.uniqueIndex(configPoList, TaskConfigPo::getTaskType);
            LocalCacheUtil.TASK_CONFIG_CACHE.clear();
            LocalCacheUtil.TASK_CONFIG_CACHE.putAll(map);
            logger.info("TaskConfigServiceImpl.reloadTaskConfigInfo success");
        }
    }

    @Override
    public Map<Integer, TaskConfigPo> getAllTaskConfig() {
        if (DateUtil.compareDate(NEXT_UPDATE_TIME) == ONE || LocalCacheUtil.TASK_CONFIG_CACHE.size() == ZERO) {
            reloadTaskConfigInfo();
            NEXT_UPDATE_TIME = DateUtil.add(new Date(), ZERO, UPDATE_FREQUENCY, ZERO, ZERO);
        }
        return LocalCacheUtil.TASK_CONFIG_CACHE;
    }
}

package com.cas.nc.questionnaire.server.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cas.nc.questionnaire.common.constants.Constants;
import com.cas.nc.questionnaire.common.dto.setting.*;
import com.cas.nc.questionnaire.common.enums.*;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.to.FilterTitleJsonInfoTo;
import com.cas.nc.questionnaire.common.to.QuotaOptionTo;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.common.utils.ValidateUtils;
import com.cas.nc.questionnaire.dao.po.AnswerInfoPo;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import com.cas.nc.questionnaire.dao.po.QstEmailPo;
import com.cas.nc.questionnaire.dao.po.QstFilterRulePo;
import com.cas.nc.questionnaire.dao.po.QstLimitConditionPo;
import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;
import com.cas.nc.questionnaire.dao.po.QstQuotaRulePo;
import com.cas.nc.questionnaire.dao.po.QstSmsPo;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.AnswerInfoQuery;
import com.cas.nc.questionnaire.dao.query.AnswerOptionQuery;
import com.cas.nc.questionnaire.dao.query.QstEmailQuery;
import com.cas.nc.questionnaire.dao.query.QstFilterRuleQuery;
import com.cas.nc.questionnaire.dao.query.QstLimitConditionQuery;
import com.cas.nc.questionnaire.dao.query.QstLimitRuleQuery;
import com.cas.nc.questionnaire.dao.query.QstQuotaRuleQuery;
import com.cas.nc.questionnaire.dao.query.QstSmsQuery;
import com.cas.nc.questionnaire.server.SettingServer;
import com.cas.nc.questionnaire.server.mapstruct.ConditionReturnListQueryConverter;
import com.cas.nc.questionnaire.server.mapstruct.QueryFilterRuleListConverter;
import com.cas.nc.questionnaire.server.mapstruct.SettingConverter;
import com.cas.nc.questionnaire.server.util.ConvertBeanUtil;
import com.cas.nc.questionnaire.service.AnswerInfoService;
import com.cas.nc.questionnaire.service.AnswerOptionService;
import com.cas.nc.questionnaire.service.GlobalConfigService;
import com.cas.nc.questionnaire.service.QstEmailService;
import com.cas.nc.questionnaire.service.QstFilterRuleService;
import com.cas.nc.questionnaire.service.QstLimitConditionService;
import com.cas.nc.questionnaire.service.QstLimitRuleService;
import com.cas.nc.questionnaire.service.QstQuotaRuleService;
import com.cas.nc.questionnaire.service.QstSmsService;
import com.cas.nc.questionnaire.service.UserInfoService;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.cas.nc.questionnaire.common.enums.FilterJudgeTypeEnum.isBetLessMore;
import static com.cas.nc.questionnaire.common.enums.FilterJudgeTypeEnum.isBetween;
import static com.cas.nc.questionnaire.common.enums.FilterJudgeTypeEnum.isLessThan;
import static com.cas.nc.questionnaire.common.enums.FilterJudgeTypeEnum.isN;
import static com.cas.nc.questionnaire.common.enums.FilterJudgeTypeEnum.isY;
import static com.cas.nc.questionnaire.common.enums.ShareRequestTypeEnum.isShareAnswer;
import static com.cas.nc.questionnaire.common.enums.ShareRequestTypeEnum.isShareReport;
import static com.cas.nc.questionnaire.common.enums.ShareTypeEnum.isPartOpen;
import static com.cas.nc.questionnaire.common.enums.UserTypeEnum.isVip;
import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_LIMIT;
import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_LIMIT_TIP;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.JSONUtil.toJSONString;


@Component
public class SettingServerImpl implements SettingServer {

    @Resource
    private QstFilterRuleService qstFilterRuleService;
    @Resource
    private QstQuotaRuleService qstQuotaRuleService;
    @Resource
    private QstLimitRuleService qstLimitRuleService;
    @Resource
    private QstLimitConditionService qstLimitConditionService;
    @Resource
    private AnswerOptionService answerOptionService;
    @Resource
    private AnswerInfoService answerInfoService;
    @Resource
    private QstEmailService qstEmailService;
    @Resource
    private QstSmsService qstSmsService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private GlobalConfigService globalConfigService;

    @Override
    public List<SettingFilterRuleListRepDto> queryFilterRuleList(SettingFilterRuleListReqDto reqDto) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setUserId(reqDto.getUserId());
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setDataSource(FilterDataSourceEnum.QUALITY_CONTROL_FILTER.key());

        List<QstFilterRulePo> poList = qstFilterRuleService.selectList(query);
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>();
        }

        List<SettingFilterRuleListRepDto> repDtoList = QueryFilterRuleListConverter.INSTANCE.to(poList);
        return repDtoList;
    }

    @Override
    public void addFilterRule(SettingFilterRuleAddReqDto reqDto) {
        QstFilterRulePo po = SettingConverter.INSTANCE.to(reqDto);
        try {
            qstFilterRuleService.insert(po);
        } catch (DuplicateKeyException e) {
            throw new ServerException(CodeEnum.DATA_EXIST);
        }
    }

    @Override
    public void updateFilterRule(SettingFilterRuleUpdateReqDto reqDto) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setId(reqDto.getId());
        query.setName(reqDto.getName());
        query.setFilterType(reqDto.getFilterType());

        Assert.isTrue(qstFilterRuleService.update(query) == ONE, CodeEnum.UPDATE_EXCEPTION);
    }

    @Override
    public void deleteFilterRule(SettingFilterRuleDeleteReqDto reqDto) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setFilterRuleId(reqDto.getFilterRuleId());
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());

        int result = qstFilterRuleService.delete(query);
        Assert.isTrue(result == ONE, CodeEnum.UPDATE_EXCEPTION);
    }

    @Override
    public List<SettingQuotaRuleListRepDto> queryQuotaList(SettingQuotaRuleListReqDto reqDto) {
        List<SettingQuotaRuleListRepDto> repDtoList = new ArrayList<>();

        QstQuotaRuleQuery query = new QstQuotaRuleQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        List<QstQuotaRulePo> poList = qstQuotaRuleService.selectList(query);
        if (CollectionUtils.isEmpty(poList)) {
            return repDtoList;
        }

        for (QstQuotaRulePo bean : poList) {
            SettingQuotaRuleListRepDto repDto = SettingConverter.INSTANCE.to(bean);
            if (!"".equals(bean.getOptionJsonInfo())) {
                List<QuotaOptionTo> optionToList = JSON.parseArray(bean.getOptionJsonInfo(), QuotaOptionTo.class);
                repDto.setOptionInfo(optionToList);
            }
            repDtoList.add(repDto);
        }
        return repDtoList;
    }

    @Override
    public void addQuotaRule(SettingQuotaRuleAddReqDto reqDto) {
        QstQuotaRulePo po = parseObject(toJSONString(reqDto), QstQuotaRulePo.class);
        if (!CollectionUtils.isEmpty(reqDto.getOptionInfo())) {
            po.setOptionJsonInfo(JSONUtil.toJSONString2(reqDto.getOptionInfo()));
        }
        try {
            qstQuotaRuleService.insert(po);
        } catch (DuplicateKeyException e) {
            throw new ServerException(CodeEnum.DATA_EXIST);
        }
    }

    @Override
    public void deleteQuotaRule(SettingQuotaRuleDeleteReqDto reqDto) {
        QstQuotaRuleQuery query = new QstQuotaRuleQuery();
        query.setQuotaRuleId(reqDto.getQuotaRuleId());
        query.setUserId(reqDto.getUserId());
        query.setQuestionnaireId(reqDto.getQuestionnaireId());

        int result = qstQuotaRuleService.delete(query);
        Assert.isTrue(result == ONE, CodeEnum.UPDATE_EXCEPTION);
    }

    @Override
    public void updateQuotaRule(SettingQuotaRuleUpdateReqDto reqDto) {
        QstQuotaRuleQuery query = SettingConverter.INSTANCE.to(reqDto);
        if (!CollectionUtils.isEmpty(reqDto.getOptionList())) {
            query.setOptionJsonInfo(JSONUtil.toJSONString2(reqDto.getOptionList()));
        }

        Assert.isTrue(qstQuotaRuleService.update(query) == ONE, CodeEnum.UPDATE_EXCEPTION);
    }

    @Override
    public void set(SettingSetReqDto reqDto) {
        boolean startNotBlankAndEndBlank = StringUtil.isNotBlank(reqDto.getIpStart()) && StringUtil.isBlank(reqDto.getIpEnd());
        boolean startBlankAndEndNotBlank = StringUtil.isBlank(reqDto.getIpStart()) && StringUtil.isNotBlank(reqDto.getIpEnd());
        if (startNotBlankAndEndBlank || startBlankAndEndNotBlank) {
            throw new ServerException(CodeEnum.IP_SEGMENT_ERROR);
        }
        QstLimitRuleQuery ruleQuery = ConvertBeanUtil.convert2QstLimitRulePo(reqDto);
        QstLimitRulePo qstLimitRulePo = qstLimitRuleService.selectOne(reqDto.getQuestionnaireId(), reqDto.getUserId());

        if (qstLimitRulePo == null) {
            QstLimitRulePo paramPo = ruleQuery;
            qstLimitRuleService.insert(paramPo);
        } else {
            qstLimitRuleService.updateDealTime(ruleQuery);
        }
    }

    @Override
    public void filterUse(SettingFilterRuleUseReqDto reqDto) {
        ValidateUtils.validateNotNullExclude(reqDto, "userId");
        Assert.isTrue(FilterTypeEnum.filterLegal(reqDto.getFilterType()), "filterType", CodeEnum.ILLEGAL);

        QstFilterRuleQuery ruleQuery = new QstFilterRuleQuery();
        ruleQuery.setId(reqDto.getId());
        QstFilterRulePo rulePo = qstFilterRuleService.selectOne(ruleQuery);
        if (rulePo == null) {
            return;
        }

        List<String> targetList = new ArrayList<>();
        switch (FilterRuleTypeEnum.toEnum(rulePo.getRuleType())) {
            case TITILE:
                targetList = title(rulePo);
                break;
            case PROVINCE:
                targetList = province(rulePo);
                break;
            case CITY:
                targetList = city(rulePo);
                break;
            case TIME_SEGMENT:
                targetList = timeSegment(rulePo);
                break;
            case SOURCE_CHANNEL:
                targetList = source(rulePo);
                break;
            case IP:
                targetList = ip(rulePo);
                break;
            default:
                break;
        }

        updateAnswerInfo(rulePo, reqDto.getFilterType(), targetList);
    }

    @Override
    public List<SettingConditionReturnListRepDto> conditionReturnList(SettingConditionReturnListReqDto reqDto) {
        List<SettingConditionReturnListRepDto> resultList = new ArrayList<>();
        List<QstLimitConditionPo> limitConditionPoList = queryQstLimitCondition(reqDto);

        for (QstLimitConditionPo bean : limitConditionPoList) {
            SettingConditionReturnListRepDto repDto = new SettingConditionReturnListRepDto();
            repDto.setLimitConditionId(bean.getLimitConditionId());
            repDto.setType(bean.getType());
            resultList.add(repDto);
        }
        return resultList;
    }

    @Override
    public void conditionReturnListAdd(SettingConditionReturnAddReqDto reqDto) {
        String emailId = "";
        String smsId = "";
        String limitConditionId = SequenceUtil.getInstance().generateLimitConditonId(reqDto.getUserId().toString());
        if (ConditionTypeEnum.filterLegal(reqDto.getConditionType()) && ConditionTypeEnum.isConditional(reqDto.getConditionType())) {
            insertFilterRuleByCondition(reqDto, limitConditionId);
        }
        if (reqDto.getEmailInfo() != null) {
            emailId = insertEmailByCondition(reqDto);
        }
        if (reqDto.getSmsInfo() != null) {
            smsId = insertSmsByCondition(reqDto);
        }
        insertLimitRuleCondition(reqDto, limitConditionId, emailId, smsId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void conditionReturnListUpdate(SettingConditionReturnUpdateReqDto reqDto) {
        QstLimitConditionPo qstLimitConditionPo = qstLimitConditionService.selectOne(reqDto.getLimitConditionId(), reqDto.getUserId());
        QstLimitConditionQuery limitConditionQuery = new QstLimitConditionQuery();
        if (qstLimitConditionPo == null) {
            return;
        }
        deleteFilterRule4Update(reqDto, qstLimitConditionPo);

        if (ConditionTypeEnum.isConditional(reqDto.getConditionType())) {
            Assert.notNull(reqDto.getFilterRuleInfoList(), "filterRule");
            insertFilterRuleByCondition(reqDto, qstLimitConditionPo.getLimitConditionId());
        }

        operateEmail(reqDto, qstLimitConditionPo, limitConditionQuery);

        operateSms(reqDto, qstLimitConditionPo, limitConditionQuery);

        updateLimitRuleCondition(reqDto, limitConditionQuery);
    }

    @Override
    public SettingConditionReturnQueryRepDto conditionReturnListQuery(SettingConditionReturnQueryReqDto reqDto) {
        QstLimitConditionPo qstLimitConditionPo = qstLimitConditionService.selectOne(reqDto.getLimitConditionId(), reqDto.getUserId());
        SettingConditionReturnQueryRepDto repDto = SettingConverter.INSTANCE.to(qstLimitConditionPo);
        repDto.setConditionType(qstLimitConditionPo.getType());

        List<QstFilterRulePo> filterRulePoList = qstFilterRuleService.selectList(qstLimitConditionPo.getQuestionnaireId(), qstLimitConditionPo.getLimitConditionId(), FilterDataSourceEnum.SET_RETURN.key());
        if (!CollectionUtils.isEmpty(filterRulePoList)) {
            List<SettingConditionReturnFilterRuleDto> ruleDtoList = ConditionReturnListQueryConverter.INSTANCE.to(filterRulePoList);
            repDto.setFilterRuleInfoList(ruleDtoList);
        }

        if (StringUtil.isNotBlank(qstLimitConditionPo.getEmailId())) {
            QstEmailPo qstEmailPo = qstEmailService.selectOne(qstLimitConditionPo.getEmailId(), reqDto.getUserId());
            if (qstEmailPo != null) {
                SettingConditionReturnEmailDto emailDto = SettingConverter.INSTANCE.to(qstEmailPo);
                repDto.setEmailInfo(emailDto);
            }
        }

        if (StringUtil.isNotBlank(qstLimitConditionPo.getSmsId())) {
            QstSmsPo qstSmsPo = qstSmsService.selectOne(qstLimitConditionPo.getSmsId(), reqDto.getUserId());
            if (qstSmsPo != null) {
                SettingConditionReturnSmsDto smsDto = SettingConverter.INSTANCE.to(qstSmsPo);
                repDto.setSmsInfo(smsDto);
            }
        }
        return repDto;
    }

    @Override
    public SettingSetQueryRepDto query(SettingSetQueryReqDto reqDto) {
        SettingSetQueryRepDto repDto;

        QstLimitRulePo limitRulePo = qstLimitRuleService.selectOne(reqDto.getQuestionnaireId(), reqDto.getUserId());
        if (limitRulePo != null) {
            repDto = ConvertBeanUtil.convert2SettingSetQueryRepDto(limitRulePo);
        } else {
            repDto = new SettingSetQueryRepDto();
            repDto.setQuestionnaireId(reqDto.getQuestionnaireId());
        }

        return repDto;
    }

    @Override
    public void deleteConditionReturn(SettingDeleteConditionReturnReqDto reqDto) {
        int delete = qstLimitConditionService.delete(reqDto.getQuestionnaireId(), reqDto.getLimitConditionId(), reqDto.getUserId());
        Assert.isTrue(delete == ONE, CodeEnum.UNKNOWN_RETURN_PAGE);
    }

    @Override
    public GetReportShareRepDto getReportShare(GetReportShareReqDto reqDto) {
        GetReportShareRepDto result = new GetReportShareRepDto();

        QstLimitRulePo qstLimitRulePo = qstLimitRuleService.selectOne(reqDto.getQuestionnaireId());
        if (qstLimitRulePo == null) {
            result.setShareType(ShareTypeEnum.NOT_OPEN.key());
        } else {
            if (isShareReport(reqDto.getShareRequestType())) {
                if (qstLimitRulePo.getReportShareType() == null) {
                    result.setShareType(ShareTypeEnum.NOT_OPEN.key());
                } else {
                    ShareTypeEnum shareTypeEnum = ShareTypeEnum.toEnumNoException(qstLimitRulePo.getReportShareType());
                    if (shareTypeEnum == null) {
                        result.setShareType(ShareTypeEnum.NOT_OPEN.key());
                    } else {
                        if (isPartOpen(shareTypeEnum.key())) {
                            result.setPwd(qstLimitRulePo.getReportPwd());
                        }
                        result.setShareType(shareTypeEnum.key());
                    }
                }
            } else if (isShareAnswer(reqDto.getShareRequestType())) {
                if (qstLimitRulePo.getAnswerShareType() == null) {
                    result.setShareType(ShareTypeEnum.NOT_OPEN.key());
                } else {
                    ShareTypeEnum shareTypeEnum = ShareTypeEnum.toEnumNoException(qstLimitRulePo.getAnswerShareType());
                    if (shareTypeEnum == null) {
                        result.setShareType(ShareTypeEnum.NOT_OPEN.key());
                    } else {
                        if (isPartOpen(shareTypeEnum.key())) {
                            result.setPwd(qstLimitRulePo.getAnswerPwd());
                        }
                        result.setShareType(shareTypeEnum.key());
                    }
                }
            } else {
                result.setShareType(ShareTypeEnum.NOT_OPEN.key());
            }
        }
        return result;
    }

    @Override
    public void reportShareSet(ReportShareSetReqDto reqDto) {
        QstLimitRulePo qstLimitRulePo = qstLimitRuleService.selectOne(reqDto.getQuestionnaireId());
        if (qstLimitRulePo == null) {
            QstLimitRulePo po = new QstLimitRulePo();
            po.setQuestionnaireId(reqDto.getQuestionnaireId());
            po.setUserId(reqDto.getUserId());
            if (isShareReport(reqDto.getShareRequestType())) {
                po.setReportShareType(reqDto.getShareType());
                po.setReportPwd(reqDto.getPwd());
            } else if (isShareAnswer(reqDto.getShareRequestType())) {
                po.setAnswerShareType(reqDto.getShareType());
                po.setAnswerPwd(reqDto.getPwd());
            }
            qstLimitRuleService.insertUpdate(po);
        } else {
            QstLimitRuleQuery query = new QstLimitRuleQuery();
            query.setUserId(reqDto.getUserId());
            query.setId(qstLimitRulePo.getId());
            if (isShareReport(reqDto.getShareRequestType())) {
                query.setReportShareType(reqDto.getShareType());
                query.setReportPwd(reqDto.getPwd());
            } else if (isShareAnswer(reqDto.getShareRequestType())) {
                query.setAnswerShareType(reqDto.getShareType());
                query.setAnswerPwd(reqDto.getPwd());
            }
            int update = qstLimitRuleService.update(query);
            Assert.isTrue(update == 1, CodeEnum.UPDATE_EXCEPTION);
        }
    }

    @Override
    public int getTip(Long userId) {
        UserInfoPo userInfoPo = userInfoService.selectOne(userId);
//        if (userInfoPo != null) {
//            if (isVip(userInfoPo.getType())) {
//                return "";
//            }
//        }

        Integer limit = globalConfigService.selectAnswerLimit();
        if (limit == null) {
            limit = ANSWER_LIMIT;
        }
//        String tip = String.format(ANSWER_LIMIT_TIP, limit);

        return limit;
    }

    @Override
    public void setTip(Integer answerLimit) {
        globalConfigService.setAnswerLimit(answerLimit);
    }

    /**
     * 生成发短信数据
     *
     * @param reqDto
     */
    private String insertSmsByCondition(SettingConditionReturnUpdateReqDto reqDto) {
        QstSmsPo po = SettingConverter.INSTANCE.to(reqDto.getSmsInfo());
        po.setUserId(reqDto.getUserId());
        po.setQuestionnaireId(reqDto.getQuestionnaireId());
        po.setSmsId(SequenceUtil.getInstance().generateSmsId(po.getUserId().toString()));
        po.setSourceType(EmailSmsSourceTypeEnum.QST_SET.key());

        qstSmsService.insert(po);
        return po.getSmsId();
    }

    private String insertEmailByCondition(SettingConditionReturnUpdateReqDto reqDto) {
        QstEmailPo po = SettingConverter.INSTANCE.to(reqDto.getEmailInfo());
        po.setQuestionnaireId(reqDto.getQuestionnaireId());
        po.setEmailId(SequenceUtil.getInstance().generateEmailId(reqDto.getUserId().toString()));
        po.setUserId(reqDto.getUserId());
        po.setSourceType(EmailSmsSourceTypeEnum.QST_SET.key());

        qstEmailService.insert(po);
        return po.getEmailId();
    }

    private void updateSms(SettingConditionReturnUpdateReqDto reqDto, String smsId) {
        QstSmsQuery query = new QstSmsQuery();
        query.setSmsId(smsId);
        query.setUserId(reqDto.getUserId());
        query.setContent(StringUtil.null2default(reqDto.getSmsInfo().getContent()));
        query.setPhoneType(reqDto.getSmsInfo().getPhoneType());
        query.setSerialNumber(reqDto.getSmsInfo().getSerialNumber() == null ? 0 : reqDto.getSmsInfo().getSerialNumber());
        query.setPhones(reqDto.getSmsInfo().getPhones());

        int update = qstSmsService.update(query);
        Assert.isTrue(update == ONE, CodeEnum.UNKNOWN_RETURN_PAGE);
    }

    private void updateEmail(SettingConditionReturnUpdateReqDto reqDto, String emailId) {
        QstEmailQuery query = new QstEmailQuery();
        query.setEmailId(emailId);
        query.setUserId(reqDto.getUserId());
        query.setEmailTitle(reqDto.getEmailInfo().getEmailTitle());
        query.setEmailContent(reqDto.getEmailInfo().getEmailContent());
        query.setSender(reqDto.getEmailInfo().getSender());
        query.setReplyAddress(StringUtil.null2default(reqDto.getEmailInfo().getReplyAddress()));
        query.setAddresseeType(reqDto.getEmailInfo().getAddresseeType());
        query.setSerialNumber(reqDto.getEmailInfo().getSerialNumber() == null ? 0 : reqDto.getEmailInfo().getSerialNumber());
        query.setEmailContent(StringUtil.null2default(reqDto.getEmailInfo().getEmailContent()));
        query.setAddressees(StringUtil.null2default(reqDto.getEmailInfo().getAddressees()));

        int update = qstEmailService.update(query);
        Assert.isTrue(update == ONE, CodeEnum.UNKNOWN_RETURN_PAGE);
    }

    private void updateLimitRuleCondition(SettingConditionReturnUpdateReqDto reqDto, QstLimitConditionQuery query) {
        query.setLimitConditionId(reqDto.getLimitConditionId());
        query.setUserId(reqDto.getUserId());
        query.setType(reqDto.getConditionType());
        query.setReturnUrl(StringUtil.null2default(reqDto.getReturnUrl()));
        query.setReturnHint(StringUtil.null2default(reqDto.getReturnHint()));
        query.setEndHint(StringUtil.null2default(reqDto.getEndHint()));

        int update = qstLimitConditionService.update(query);
        Assert.isTrue(update == ONE, CodeEnum.UNKNOWN_RETURN_PAGE);
    }

    /**
     * 生成发短信数据
     *
     * @param reqDto
     */
    private String insertSmsByCondition(SettingConditionReturnAddReqDto reqDto) {
        QstSmsPo po = SettingConverter.INSTANCE.to(reqDto.getSmsInfo());
        po.setUserId(reqDto.getUserId());
        po.setSmsId(SequenceUtil.getInstance().generateSmsId(po.getUserId().toString()));
        po.setQuestionnaireId(reqDto.getQuestionnaireId());
        po.setSourceType(EmailSmsSourceTypeEnum.QST_SET.key());

        qstSmsService.insert(po);
        return po.getSmsId();
    }

    /**
     * 生成发邮件数据
     *
     * @param reqDto
     */
    private String insertEmailByCondition(SettingConditionReturnAddReqDto reqDto) {
        QstEmailPo po = SettingConverter.INSTANCE.to(reqDto.getEmailInfo());
        po.setUserId(reqDto.getUserId());
        po.setEmailId(SequenceUtil.getInstance().generateEmailId(reqDto.getUserId().toString()));
        po.setQuestionnaireId(reqDto.getQuestionnaireId());
        po.setSourceType(EmailSmsSourceTypeEnum.QST_SET.key());

        qstEmailService.insert(po);
        return po.getEmailId();
    }


    /**
     * 生成过滤数据
     *
     * @param reqDto
     * @param limitConditionId
     */
    private void insertFilterRuleByCondition(SettingConditionReturnUpdateReqDto reqDto, String limitConditionId) {
        List<SettingConditionReturnFilterRuleDto> filterRuleInfoList = reqDto.getFilterRuleInfoList();
        filterRuleInfoList.forEach(filterRuleDto -> {
            Assert.notNull(filterRuleDto, CodeEnum.DATA_EXCEPTION);
            QstFilterRulePo po = new QstFilterRulePo();
            po.setForeignId(limitConditionId);
            po.setUserId(reqDto.getUserId());
            po.setQuestionnaireId(reqDto.getQuestionnaireId());
            po.setFilterRuleId(SequenceUtil.getInstance().generateFilterRuleId(po.getUserId().toString()));
            po.setRuleType(filterRuleDto.getRuleType());
            po.setJudgeType(filterRuleDto.getJudgeType());
            po.setDataSource(FilterRuleSourceTypeEnum.QST_SET_RETURN.key());
            po.setContent(filterRuleDto.getContent());

            qstFilterRuleService.insert(po);
        });
    }

    /**
     * 生成过滤数据
     *
     * @param reqDto
     * @param limitConditionId
     */
    private void insertFilterRuleByCondition(SettingConditionReturnAddReqDto reqDto, String limitConditionId) {
        List<SettingConditionReturnFilterRuleDto> filterRuleInfoList = reqDto.getFilterRuleInfoList();
        filterRuleInfoList.forEach(filterRuleInfo -> {
            Assert.notNull(filterRuleInfo, CodeEnum.DATA_EXCEPTION);
            QstFilterRulePo po = new QstFilterRulePo();
            po.setForeignId(limitConditionId);
            po.setQuestionnaireId(reqDto.getQuestionnaireId());
            po.setUserId(reqDto.getUserId());
            po.setFilterRuleId(SequenceUtil.getInstance().generateFilterRuleId(po.getUserId().toString()));
            po.setRuleType(filterRuleInfo.getRuleType());
            po.setJudgeType(filterRuleInfo.getJudgeType());
            String content = StringEscapeUtils.unescapeJava(filterRuleInfo.getContent());
            if (StringUtil.isNotBlank(content) && content.startsWith("\"")) {
                content = content.substring(1, content.length() - 1);
            }
            if (FilterRuleTypeEnum.isTitle(filterRuleInfo.getRuleType())) {
                JSONObject jsonObject = JSONUtil.parseObject(content);
                String titleType = jsonObject.getString("titleType");
                TitleTypeEnum titleTypeEnum = TitleTypeEnum.toEnum(titleType);
                jsonObject.put("titleType", titleTypeEnum.key());
                po.setContent(jsonObject.toJSONString());
            } else {
                po.setContent(content);
            }
            po.setDataSource(FilterRuleSourceTypeEnum.QST_SET_RETURN.key());
            qstFilterRuleService.insert(po);
        });
    }

    /**
     * 生成或者更新条件设置
     *
     * @param reqDto
     * @param emailId
     * @param smsId
     */
    private void insertLimitRuleCondition(SettingConditionReturnAddReqDto reqDto, String limitConditionId, String emailId, String smsId) {
        QstLimitConditionPo po = new QstLimitConditionPo();
        po.setUserId(reqDto.getUserId());
        po.setQuestionnaireId(reqDto.getQuestionnaireId());
        po.setLimitConditionId(limitConditionId);
        po.setType(ConditionTypeEnum.toEnum(reqDto.getConditionType()).key());

        po.setReturnUrl(reqDto.getReturnUrl());
        if (reqDto.getReturnUrl() == null) {
            po.setReturnUrl(Constants.STRING_DEFAULT);
        }
        po.setReturnHint(reqDto.getReturnHint());
        if (reqDto.getReturnHint() == null) {
            po.setReturnHint(Constants.STRING_DEFAULT);
        }
        po.setEndHint(reqDto.getEndHint());
        if (reqDto.getEndHint() == null) {
            po.setEndHint(Constants.END_HINT_DEFAULT);
        }
        po.setEmailId(emailId);
        po.setSmsId(smsId);
        qstLimitConditionService.insert(po);
    }

    /**
     * 查询过滤条件
     *
     * @param filterIdList
     * @param questionnaireId
     * @return
     */
    private List<QstFilterRulePo> queryFilterRuleListByIdList(List<String> filterIdList, String questionnaireId) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setFilterIdList(filterIdList);
        return qstFilterRuleService.selectList(query);
    }

    /**
     * 查询设置
     *
     * @param reqDto
     * @return
     */
    private List<QstLimitConditionPo> queryQstLimitCondition(SettingConditionReturnListReqDto reqDto) {
        QstLimitConditionQuery query = new QstLimitConditionQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        return qstLimitConditionService.selectList(query);
    }

    /**
     * ip过滤
     *
     * @param rulePo
     * @return
     */
    private List<String> ip(QstFilterRulePo rulePo) {
        List<String> resultList = new ArrayList<>();
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(rulePo.getQuestionnaireId());
        query.setIp(rulePo.getContent());
        query.setUserId(rulePo.getUserId());
        constructResultList(resultList, query);
        return resultList;
    }

    private void constructResultList(List<String> resultList, AnswerInfoQuery query) {
        List<AnswerInfoPo> list = answerInfoService.selectListByFilterCondition(query);
        if (!CollectionUtils.isEmpty(list)) {
            for (AnswerInfoPo bean : list) {
                resultList.add(bean.getAnswerId());
            }
        }
    }

    /**
     * 渠道来源过滤
     *
     * @param rulePo
     * @return
     */
    private List<String> source(QstFilterRulePo rulePo) {
        List<String> resultList = new ArrayList<>();
        List<Integer> sourceList = JSON.parseArray(rulePo.getContent(), Integer.class);
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setUserId(rulePo.getUserId());
        query.setQuestionnaireId(rulePo.getQuestionnaireId());
        query.setSourceList(sourceList);
        constructResultList(resultList, query);
        return resultList;
    }

    /**
     * 时间段过滤
     *
     * @param rulePo
     * @return
     */
    private List<String> timeSegment(QstFilterRulePo rulePo) {
        Assert.isTrue(isBetLessMore(rulePo.getJudgeType()), CodeEnum.DATA_EXCEPTION);
        List<String> resultList = new ArrayList<>();
        List<Long> timeSegmentList = JSON.parseArray(rulePo.getContent(), Long.class);
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setUserId(rulePo.getUserId());
        query.setQuestionnaireId(rulePo.getQuestionnaireId());
        if (isBetween(rulePo.getJudgeType())) {
            query.setDurationStart(timeSegmentList.get(0));
            query.setDurationEnd(timeSegmentList.get(1));
        } else if (isLessThan(rulePo.getJudgeType())) {
            query.setDurationEnd(timeSegmentList.get(0));
        } else {
            query.setDurationStart(timeSegmentList.get(0));
        }
        constructResultList(resultList, query);
        return resultList;
    }

    /**
     * 城市过滤
     *
     * @param rulePo
     * @return
     */
    private List<String> city(QstFilterRulePo rulePo) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        List<String> resultList = new ArrayList<>();
        List<Long> cityList = JSON.parseArray(rulePo.getContent(), Long.class);
        query.setUserId(rulePo.getUserId());
        query.setCityList(cityList);
        query.setQuestionnaireId(rulePo.getQuestionnaireId());
        constructResultList(resultList, query);
        return resultList;
    }

    /**
     * 省过滤
     *
     * @param rulePo
     * @return
     */
    private List<String> province(QstFilterRulePo rulePo) {
        List<String> resultList = new ArrayList<>();
        List<Long> provinceList = JSON.parseArray(rulePo.getContent(), Long.class);
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(rulePo.getQuestionnaireId());
        query.setUserId(rulePo.getUserId());
        query.setProvinceList(provinceList);
        constructResultList(resultList, query);
        return resultList;
    }

    /**
     * 过滤类型为题目时处理逻辑
     *
     * @param rulePo
     */
    private List<String> title(QstFilterRulePo rulePo) {
        List<FilterTitleJsonInfoTo> jsonInfoToList = JSON.parseArray(rulePo.getContent(), FilterTitleJsonInfoTo.class);
        if (CollectionUtils.isEmpty(jsonInfoToList)) {
            return new ArrayList<>();
        }

        List<List<String>> resultList = new ArrayList<>();
        List<String> targetList = new ArrayList<>();

        queryAnswerOption(rulePo, jsonInfoToList, resultList);

        if (jsonInfoToList.size() != resultList.size()) {
            return new ArrayList<>();
        }
        for (String answerId : resultList.get(0)) {
            int countFlag = 0;
            for (int i = 1; i < resultList.size(); i++) {
                if (resultList.get(i).contains(answerId)) {
                    countFlag++;
                }
            }
            if (countFlag == (resultList.size() - 1)) {
                targetList.add(answerId);
            }
        }

        return targetList;
    }

    /**
     * 更新答卷信息
     *
     * @param rulePo
     * @param filterType
     * @param targetList
     */
    private void updateAnswerInfo(QstFilterRulePo rulePo, Integer filterType, List<String> targetList) {
        if (CollectionUtils.isEmpty(targetList)) {
            return;
        }
        AnswerInfoQuery answerInfoQuery = new AnswerInfoQuery();
        answerInfoQuery.setAnswerIdList(targetList);
        answerInfoQuery.setStatus(AnswerStatusEnum.filterTypeConvert(filterType).key());
        answerInfoQuery.setFilterRuleId(rulePo.getId());
        answerInfoService.updateStatusList(answerInfoQuery);
    }

    /**
     * 查询含有过滤选项的答卷
     *
     * @param rulePo
     * @param jsonInfoToList
     * @param resultList
     */
    private void queryAnswerOption(QstFilterRulePo rulePo, List<FilterTitleJsonInfoTo> jsonInfoToList, List<List<String>> resultList) {
        for (FilterTitleJsonInfoTo bean : jsonInfoToList) {
            AnswerOptionQuery query = new AnswerOptionQuery();
            query.setQuestionnaireId(rulePo.getQuestionnaireId());
            query.setOptionNumberList(bean.getOptionList());
            query.setSerialNumber(bean.getSerialNumber());
            if (isY(rulePo.getJudgeType())) {
                query.setInFlag(true);
            } else if (isN(rulePo.getJudgeType())) {
                query.setInFlag(false);
            } else {
                throw new ServerException(CodeEnum.FILTER_CONDITION_ERROR);
            }
            List<AnswerOptionPo> answerOptionList = answerOptionService.selectFilterList(query);
            if (CollectionUtils.isEmpty(answerOptionList)) {
                continue;
            }
            List<String> tempList = new ArrayList<>();
            for (AnswerOptionPo optionPo : answerOptionList) {
                tempList.add(optionPo.getAnswerId());
            }
            resultList.add(tempList);
        }
    }

    private void deleteFilterRule4Update(SettingConditionReturnUpdateReqDto reqDto, QstLimitConditionPo qstLimitConditionPo) {
        List<QstFilterRulePo> filterRulePoList = queryFilterRule4Update(qstLimitConditionPo, reqDto.getUserId());
        if (!CollectionUtils.isEmpty(filterRulePoList)) {
            filterRulePoList.forEach(v -> {
                int delete = qstFilterRuleService.delete(v.getFilterRuleId(), reqDto.getUserId());
                Assert.isTrue(delete == ONE, CodeEnum.UNKNOWN_RETURN_PAGE);
            });
        }
    }

    private List<QstFilterRulePo> queryFilterRule4Update(QstLimitConditionPo qstLimitConditionPo, Long userId) {
        QstFilterRuleQuery query = new QstFilterRuleQuery();
        query.setQuestionnaireId(qstLimitConditionPo.getQuestionnaireId());
        query.setForeignId(qstLimitConditionPo.getLimitConditionId());
        query.setTableColumns("filter_rule_id");
        query.setUserId(userId);
        return qstFilterRuleService.selectList(query);
    }

    private void operateSms(SettingConditionReturnUpdateReqDto reqDto, QstLimitConditionPo qstLimitConditionPo, QstLimitConditionQuery limitConditionQuery) {
        if (reqDto.getSmsInfo() == null && StringUtil.isNotBlank(qstLimitConditionPo.getSmsId())) {
            limitConditionQuery.setSmsId("");
            int delete = qstSmsService.delete(qstLimitConditionPo.getSmsId(), reqDto.getUserId());
            Assert.isTrue(delete == ONE, CodeEnum.UNKNOWN_RETURN_PAGE);
        } else if (reqDto.getSmsInfo() != null && StringUtil.isNotBlank(qstLimitConditionPo.getSmsId())) {
            updateSms(reqDto, qstLimitConditionPo.getSmsId());
        } else if (reqDto.getSmsInfo() != null && StringUtil.isBlank(qstLimitConditionPo.getSmsId())) {
            String smsId = insertSmsByCondition(reqDto);
            limitConditionQuery.setSmsId(smsId);
        }
    }

    private void operateEmail(SettingConditionReturnUpdateReqDto reqDto, QstLimitConditionPo qstLimitConditionPo, QstLimitConditionQuery limitConditionQuery) {
        if (reqDto.getEmailInfo() == null && StringUtil.isNotBlank(qstLimitConditionPo.getEmailId())) {
            limitConditionQuery.setEmailId("");
            int delete = qstEmailService.delete(qstLimitConditionPo.getEmailId(), reqDto.getUserId());
            Assert.isTrue(delete == ONE, CodeEnum.UNKNOWN_RETURN_PAGE);
        } else if (reqDto.getEmailInfo() != null && StringUtil.isNotBlank(qstLimitConditionPo.getEmailId())) {
            updateEmail(reqDto, qstLimitConditionPo.getEmailId());
        } else if (reqDto.getEmailInfo() != null && StringUtil.isBlank(qstLimitConditionPo.getEmailId())) {
            String emailId = insertEmailByCondition(reqDto);
            limitConditionQuery.setEmailId(emailId);
        }
    }
}

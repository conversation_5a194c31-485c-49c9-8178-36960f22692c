package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import com.cas.nc.questionnaire.dao.po.TempletOptionPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface GetTempletOptionConverter {
    GetTempletOptionConverter INSTANCE = Mappers.getMapper(GetTempletOptionConverter.class);

    List<QstOptionPo> to(List<TempletOptionPo> templetOptionPoList);
}

package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstFilterRulePo;
import com.cas.nc.questionnaire.dao.query.QstFilterRuleQuery;

import java.util.List;

public interface QstFilterRuleService {
    /**
     * 数据插入
     *
     * @param qstFilterRulePo
     * @return
     */
    int insert(QstFilterRulePo qstFilterRulePo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstFilterRuleQuery query);

    /**
     * 删除
     *
     * @param filterRuleId
     * @param userId
     * @return
     */
    int delete(String filterRuleId, Long userId);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstFilterRulePo> selectList(QstFilterRuleQuery query);

    /**
     * 查询list
     *
     * @param dataSource
     * @return
     */
    List<QstFilterRulePo> selectList(String questionnaireId, int dataSource);

    /**
     * 查询list
     *
     * @param foreignId
     * @param dataSource
     * @return
     */
    List<QstFilterRulePo> selectList(String questionnaireId, String foreignId, int dataSource);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstFilterRulePo selectOne(QstFilterRuleQuery query);

    /**
     * 查询单条记录
     *
     * @param filterRuleId
     * @return
     */
    QstFilterRulePo selectOne(String filterRuleId);

    /**
     * 查询单条记录
     *
     * @param filterRuleId
     * @return
     */
    QstFilterRulePo selectOne(String filterRuleId, Long userId);

    /**
     * 更新数据
     *
     * @param query
     * @return
     */
    int update(QstFilterRuleQuery query);
}

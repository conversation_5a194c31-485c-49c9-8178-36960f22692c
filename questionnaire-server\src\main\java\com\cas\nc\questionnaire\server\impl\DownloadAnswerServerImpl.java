package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.analysis.DownloadAnswerReqDto;
import com.cas.nc.questionnaire.common.dto.analysis.DownloadFillBlankReqDto;
import com.cas.nc.questionnaire.common.enums.AnswerStatusEnum;
import com.cas.nc.questionnaire.common.enums.HeaderCellTitleEnum;
import com.cas.nc.questionnaire.common.enums.ListFillBlankHeaderCellTitleEnum;
import com.cas.nc.questionnaire.common.enums.SourceEnum;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.*;
import com.cas.nc.questionnaire.dao.query.AnswerInfoQuery;
import com.cas.nc.questionnaire.dao.query.AnswerOptionQuery;
import com.cas.nc.questionnaire.dao.query.UserInfoQuery;
import com.cas.nc.questionnaire.server.DownloadAnswerServer;
import com.cas.nc.questionnaire.service.*;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.DATA_EXCEPTION;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.NO_ANSWER;
import static com.cas.nc.questionnaire.common.enums.DownloadExcelTypeEnum.DOWNLOAD_TEXT;
import static com.cas.nc.questionnaire.common.enums.HeaderCellTitleEnum.getHeaderCellTitleList;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.*;
import static com.cas.nc.questionnaire.common.utils.Constants.*;
import static com.cas.nc.questionnaire.common.utils.DateUtil.C_TIME_PATTON_DEFAULT;
import static com.cas.nc.questionnaire.common.utils.DateUtil.format;

@Component("downloadAnswerServer")
public class DownloadAnswerServerImpl implements DownloadAnswerServer {

    @Resource
    private QstTitleService qstTitleService;
    @Resource
    private QstOptionService qstOptionService;
    @Resource
    private AnswerInfoService answerInfoService;
    @Resource
    private AnswerOptionService answerOptionService;
    @Resource
    private AreaService areaService;
    @Resource
    private UserInfoService userInfoService;

    @Override
    public Workbook downloadAnswer(DownloadAnswerReqDto reqDto) {

        List<AnswerInfoPo> answerInfoPoList = answerInfoService.selectList(constructAnswerInfoQuery(reqDto));
        Assert.notNull(answerInfoPoList, NO_ANSWER);
        boolean isKx = reqDto.getBizId() == 1 && StringUtil.isNotBlank(reqDto.getBizNo());
        Map<String, String> outUserIdMap = null;

        if (isKx) {
            outUserIdMap = getUserMap(answerInfoPoList);
        }

        List<AnswerOptionPo> answerOptionPoList = answerOptionService.selectList(constructAnswerOptionQuery(reqDto));
        Assert.notNull(answerOptionPoList, NO_ANSWER);
        Map<String, List<AnswerOptionPo>> answerOptionListMap = answerOptionPoList.stream().collect(Collectors.groupingBy(AnswerOptionPo::getAnswerId));

        List<QstTitlePo> titlePoList = qstTitleService.selectList(reqDto.getQuestionnaireId(), reqDto.getUserId());
        Assert.notNull(titlePoList, DATA_EXCEPTION);
        titlePoList = titlePoList.stream().sorted(Comparator.comparing(QstTitlePo::getGlobalOrder)).collect(Collectors.toList());

        List<QstOptionPo> optionPoList = qstOptionService.selectList(reqDto.getQuestionnaireId(), reqDto.getUserId());
        Assert.notNull(optionPoList, DATA_EXCEPTION);
        Map<Integer, List<QstOptionPo>> optionListMap = optionPoList.stream().collect(Collectors.groupingBy(QstOptionPo::getTitleSerialNumber));

        Workbook workbook = new HSSFWorkbook();
        HSSFSheet hssfSheet = (HSSFSheet) workbook.createSheet("sheet1");
        HSSFRow row = hssfSheet.createRow(0);
        HSSFCellStyle hssfCellStyle = (HSSFCellStyle) workbook.createCellStyle();
        //居中样式
        hssfCellStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);

        if (reqDto.getType() != null && reqDto.getType().intValue() == DOWNLOAD_TEXT.key()) {
            constructCellText(titlePoList, optionListMap, row, hssfCellStyle);
            constructAnswerText(answerInfoPoList, answerOptionListMap, titlePoList, optionListMap, hssfSheet, hssfCellStyle);
        } else {
            constructCell(titlePoList, optionListMap, row, hssfCellStyle, isKx);
            constructAnswer(answerInfoPoList, answerOptionListMap, titlePoList, optionListMap, hssfSheet, hssfCellStyle, isKx, outUserIdMap);
        }

        return workbook;
    }

    public Map<String, String> getUserMap(List<AnswerInfoPo> answerInfoPoList) {
        List<String> outUserIdList = answerInfoPoList.stream().filter(v -> StringUtil.isNotBlank(v.getAnswerUserId())).map(AnswerInfoPo::getAnswerUserId).collect(Collectors.toList());
        UserInfoQuery query = new UserInfoQuery();
        query.setOutUserIdList(outUserIdList);
        query.setTableColumns("out_user_id, name");

        List<UserInfoPo> userInfoPoList = userInfoService.selectList(query);
        return userInfoPoList.stream().collect(Collectors.toMap(UserInfoPo::getOutUserId, UserInfoPo::getName, (v1, v2) -> v1));
    }

    @Override
    public Workbook downloadFillBlank(DownloadFillBlankReqDto reqDto) {
        Workbook workbook = new HSSFWorkbook();
        HSSFSheet hssfSheet = (HSSFSheet) workbook.createSheet("sheet1");
        HSSFRow row = hssfSheet.createRow(0);
        HSSFCellStyle hssfCellStyle = (HSSFCellStyle) workbook.createCellStyle();
        //居中样式
        hssfCellStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);

        constructCell(row, hssfCellStyle);

        List<AnswerOptionPo> answerOptionPoList;
        List<AnswerInfoPo> answerInfoPoList;
        if (reqDto.getFilterNull() == null || reqDto.getFilterNull()) {
            answerOptionPoList = listAnswerOption(reqDto);
            if (CollectionUtils.isEmpty(answerOptionPoList)) {
                return workbook;
            }
            List<String> answerIdList = SafeUtil.of(answerOptionPoList).stream().map(AnswerOptionPo::getAnswerId).collect(Collectors.toList());
            answerInfoPoList = answerInfoService.selectList(reqDto.getQuestionnaireId(), answerIdList);

            if (CollectionUtils.isEmpty(answerInfoPoList)) {
                return workbook;
            }
        } else {
            answerInfoPoList = listAnswerInfo(reqDto);
            if (CollectionUtils.isEmpty(answerInfoPoList)) {
                return workbook;
            }
            List<String> answerIdList = SafeUtil.of(answerInfoPoList).stream().map(AnswerInfoPo::getAnswerId).collect(Collectors.toList());
            answerOptionPoList = listAnswerOption(reqDto, answerIdList);

            if (CollectionUtils.isEmpty(answerOptionPoList)) {
                return workbook;
            }
        }

        constructRow(hssfSheet, hssfCellStyle, answerOptionPoList, answerInfoPoList);
        return workbook;
    }

    public void constructRow(HSSFSheet hssfSheet, HSSFCellStyle hssfCellStyle, List<AnswerOptionPo> answerOptionPoList, List<AnswerInfoPo> answerInfoPoList) {
        Map<String, AnswerOptionPo> optionPoMap = SafeUtil.of(answerOptionPoList).stream().collect(Collectors.toMap(AnswerOptionPo::getAnswerId, v -> v));
        AtomicInteger rowNum = new AtomicInteger(1);

        for (AnswerInfoPo answerInfoPo : answerInfoPoList) {
            HSSFCell rowCell;
            AtomicInteger cellNum = new AtomicInteger(0);
            HSSFRow row = hssfSheet.createRow(rowNum.getAndIncrement());
            constructWriteContentRow(hssfCellStyle, row, cellNum, answerInfoPo.getAnswerId());

            rowCell = row.createCell(cellNum.getAndIncrement());
            rowCell.setCellValue(DateUtil.format(answerInfoPo.getCreateTime(), C_TIME_PATTON_DEFAULT));
            rowCell.setCellStyle(hssfCellStyle);

            rowCell = row.createCell(cellNum.getAndIncrement());
            rowCell.setCellValue(SourceEnum.toEnum(answerInfoPo.getSource()).value());
            rowCell.setCellStyle(hssfCellStyle);

            AnswerOptionPo answerOptionPo = optionPoMap.get(answerInfoPo.getAnswerId());

            if (answerInfoPo != null) {
                constructWriteContentRow(hssfCellStyle, row, cellNum, answerOptionPo.getWriteContent());
            } else {
                constructWriteContentRow(hssfCellStyle, row, cellNum, "");
            }

        }
    }

    public void constructWriteContentRow(HSSFCellStyle hssfCellStyle, HSSFRow row, AtomicInteger cellNum, String writeContent) {
        HSSFCell rowCell;
        rowCell = row.createCell(cellNum.getAndIncrement());
        rowCell.setCellValue(writeContent);
        rowCell.setCellStyle(hssfCellStyle);
    }

    private void constructCell(HSSFRow row, HSSFCellStyle hssfCellStyle) {
        HSSFCell cell = null;
        AtomicInteger columnNum = new AtomicInteger(0);
        for (ListFillBlankHeaderCellTitleEnum titleEnum : ListFillBlankHeaderCellTitleEnum.getHeaderCellTitleList()) {
            constructWriteContentRow(hssfCellStyle, row, columnNum, titleEnum.value());
        }
    }

    private List<AnswerOptionPo> listAnswerOption(DownloadFillBlankReqDto reqDto, List<String> answerIdList) {
        AnswerOptionQuery optionQuery = new AnswerOptionQuery();
        optionQuery.setQuestionnaireId(reqDto.getQuestionnaireId());
        optionQuery.setTitleSerialNumber(reqDto.getSerialNumber());
        optionQuery.setUserId(reqDto.getUserId());
        optionQuery.setRowNumber(reqDto.getRowNumber());
        optionQuery.setColumnNumber(reqDto.getColumnNumber());
        optionQuery.setAnswerIdList(answerIdList);

        return answerOptionService.selectList(optionQuery);
    }

    private List<AnswerInfoPo> listAnswerInfo(DownloadFillBlankReqDto reqDto) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());

        return answerInfoService.selectList(query);
    }

    private List<AnswerOptionPo> listAnswerOption(DownloadFillBlankReqDto reqDto) {
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setTitleSerialNumber(reqDto.getSerialNumber());
        query.setRowNumber(reqDto.getRowNumber());
        query.setColumnNumber(reqDto.getColumnNumber());

        return answerOptionService.selectList(query);
    }

    private void setRowInfo(HSSFRow row, HSSFCellStyle hssfCellStyle, AtomicInteger cellNum, String optionKey, List<String> answerList) {
        HSSFCell rowCell;
        if (answerList.contains(optionKey)) {
            rowCell = row.createCell(cellNum.getAndIncrement());
            rowCell.setCellValue(1);
            rowCell.setCellStyle(hssfCellStyle);
        } else {
            rowCell = row.createCell(cellNum.getAndIncrement());
            rowCell.setCellValue(0);
            rowCell.setCellStyle(hssfCellStyle);
        }
    }

    private void setRowInfo(HSSFRow row, HSSFCellStyle hssfCellStyle, AtomicInteger cellNum, String optionKey, Map<String, AnswerOptionPo> answerMap) {
        HSSFCell rowCell;
        AnswerOptionPo answerOptionPo = SafeUtil.of(answerMap).get(optionKey);
        rowCell = row.createCell(cellNum.getAndIncrement());
        if (answerOptionPo != null) {
            if (StringUtil.isNotBlank(answerOptionPo.getWriteContent())) {
                rowCell.setCellValue(1 + " 【" + answerOptionPo.getWriteContent() + "】");
            } else {
                rowCell.setCellValue(1);
            }
        } else {
            rowCell.setCellValue(0);
        }
        rowCell.setCellStyle(hssfCellStyle);
    }

    private void setRowInfoText(HSSFRow row, HSSFCellStyle hssfCellStyle, AtomicInteger cellNum, String optionKey, Map<String, AnswerOptionPo> answerMap) {
        HSSFCell rowCell;
        AnswerOptionPo answerOptionPo = SafeUtil.of(answerMap).get(optionKey);
        rowCell = row.createCell(cellNum.getAndIncrement());
        if (answerOptionPo != null) {
            if (StringUtil.isNotBlank(answerOptionPo.getWriteContent())) {
                rowCell.setCellValue(1 + " 【" + answerOptionPo.getWriteContent() + "】");
            } else {
                rowCell.setCellValue(1);
            }
        } else {
            rowCell.setCellValue("");
        }
        rowCell.setCellStyle(hssfCellStyle);
    }

    private void setHeaderCellContent(HSSFRow row, AnswerInfoPo answerInfoPo, AtomicInteger cellNum, HSSFCellStyle hssfCellStyle, boolean isKx, Map<String, String> outUserIdMap) {
        HSSFCell cell;
        for (HeaderCellTitleEnum titleEnum : getHeaderCellTitleList()) {
            if (titleEnum.equals(HeaderCellTitleEnum.NUM)) {

                cell = row.createCell(cellNum.getAndIncrement());
                cell.setCellValue(answerInfoPo.getAnswerId());
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                cell.setCellStyle(hssfCellStyle);
            } else if (titleEnum.equals(HeaderCellTitleEnum.SUBMIT_TIME)) {

                cell = row.createCell(cellNum.getAndIncrement());
                cell.setCellValue(DateUtil.format(answerInfoPo.getCreateTime(), C_TIME_PATTON_DEFAULT));
                cell.setCellStyle(hssfCellStyle);
            } else if (titleEnum.equals(HeaderCellTitleEnum.DURATION)) {

                cell = row.createCell(cellNum.getAndIncrement());
                cell.setCellValue(answerInfoPo.getDuration());
                cell.setCellStyle(hssfCellStyle);
            } else if (titleEnum.equals(HeaderCellTitleEnum.SOURCE)) {

                cell = row.createCell(cellNum.getAndIncrement());
                cell.setCellValue(SourceEnum.toEnum(answerInfoPo.getSource()).value());
                cell.setCellStyle(hssfCellStyle);
            } else if (titleEnum.equals(HeaderCellTitleEnum.IP)) {

                constructWriteContentRow(hssfCellStyle, row, cellNum, answerInfoPo.getIp());
            } else if (titleEnum.equals(HeaderCellTitleEnum.IP_ADDRESS)) {

                cell = row.createCell(cellNum.getAndIncrement());
                cell.setCellValue(areaService.getAddress(answerInfoPo.getProvince(), answerInfoPo.getCity()));
                cell.setCellStyle(hssfCellStyle);
            } else {

                constructWriteContentRow(hssfCellStyle, row, cellNum, "");
            }
        }
        if (isKx) {
            String answerUserId = answerInfoPo.getAnswerUserId();
            String name = outUserIdMap.get(answerUserId);

            cell = row.createCell(cellNum.getAndIncrement());
            cell.setCellValue(StringUtil.isBlank(name) ? "" : name);
            cell.setCellStyle(hssfCellStyle);
        }
    }

    private String getOptionKey(Integer rowNumber, Integer columnNumber) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(rowNumber);
        buffer.append(SPLIT_FLAG);
        buffer.append(columnNumber);

        return buffer.toString();
    }

    private String getOptionKey(Integer serialNumber, Integer rowNumber, Integer columnNumber) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(serialNumber);
        buffer.append(SPLIT_FLAG);
        buffer.append(rowNumber);
        buffer.append(SPLIT_FLAG);
        buffer.append(columnNumber);

        return buffer.toString();
    }

    private String getCellTitle(Integer serialNumber, String rowTitle, String columnTitle) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(serialNumber);
        buffer.append(CAESURA_SIGN);
        buffer.append(rowTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(columnTitle);

        return buffer.toString();
    }

    private String getCellTitle(String initCellTitle, String rowTitle, String columnTitle) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(initCellTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(rowTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(columnTitle);

        return buffer.toString();
    }

    private String getCellTitle(String initCellTitle, String rowTitle, String columnTitle, String verticalTitle) {
        StringBuffer buffer = getInitCellTitle(initCellTitle);
        buffer.append(rowTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(columnTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(verticalTitle);

        return buffer.toString();
    }

    private StringBuffer getInitCellTitle(String initCellTitle) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(initCellTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        return buffer;
    }

    private String getCellTitle(Integer serialNumber, String rowTitle, String columnTitle, String verticalTitle) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(serialNumber);
        buffer.append(CAESURA_SIGN);
        buffer.append(rowTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(columnTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(verticalTitle);

        return buffer.toString();
    }

    private String getCellTitle(String initCellTitle, String optionTitle) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(initCellTitle);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(optionTitle);

        return buffer.toString();
    }

    private String getCellTitle(Integer serialNumber, String title) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(serialNumber);
        buffer.append(CAESURA_SIGN);
        buffer.append(title);

        return buffer.toString();
    }

    private void constructAnswer(List<AnswerInfoPo> answerInfoPoList, Map<String, List<AnswerOptionPo>> answerOptionListMap, List<QstTitlePo> titlePoList, Map<Integer, List<QstOptionPo>> optionListMap, HSSFSheet hssfSheet, HSSFCellStyle hssfCellStyle, boolean isKx, Map<String, String> outUserIdMap) {
        HSSFRow row;
        AtomicInteger rowNum = new AtomicInteger(1);
        for (AnswerInfoPo answerInfoPo : answerInfoPoList) {
            row = hssfSheet.createRow(rowNum.getAndIncrement());
            AtomicInteger cellNum = new AtomicInteger(0);

            setHeaderCellContent(row, answerInfoPo, cellNum, hssfCellStyle, isKx, outUserIdMap);

            List<AnswerOptionPo> answerOptionCurrentList = answerOptionListMap.get(answerInfoPo.getAnswerId());
            Map<Integer, List<AnswerOptionPo>> answerOptionTempListMap = answerOptionCurrentList.stream().collect(Collectors.groupingBy(AnswerOptionPo::getTitleSerialNumber));
            HSSFCell rowCell;
            for (QstTitlePo titlePo : titlePoList) {

                List<AnswerOptionPo> answerOptionTempList = answerOptionTempListMap.get(titlePo.getSerialNumber());
                List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());

                if (isDuoXiangDanHangTianKong(titlePo.getType())) {
                    for (QstOptionPo qstOptionPo : tempOptionList) {
                        if (CollectionUtils.isEmpty(answerOptionTempList)) {

                            rowCell = row.createCell(cellNum.getAndIncrement());
                            rowCell.setCellValue("");
                            rowCell.setCellStyle(hssfCellStyle);
                        } else {

                            Map<Integer, AnswerOptionPo> answerMap = answerOptionTempList.stream().collect(Collectors.toMap(k -> k.getRowNumber(), v -> v));
                            AnswerOptionPo answerOptionPo = answerMap.get(qstOptionPo.getRowNumber());

                            if (answerOptionPo != null) {

                                constructWriteContentRow(hssfCellStyle, row, cellNum, answerOptionPo.getWriteContent());
                            } else {

                                constructWriteContentRow(hssfCellStyle, row, cellNum, "");
                            }
                        }
                    }
                } else if (isOneDimensionalFillBlank(titlePo.getType())) {
                    if (CollectionUtils.isEmpty(answerOptionTempList)) {

                        constructWriteContentRow(hssfCellStyle, row, cellNum, "");
                    } else {

                        AnswerOptionPo answerOptionPo = answerOptionTempList.get(0);
                        if (isHuaDongTiao(titlePo.getType())) {
                            constructWriteContentRow(hssfCellStyle, row, cellNum, answerOptionPo.getSerialNumber().toString());
                        } else {
                            constructWriteContentRow(hssfCellStyle, row, cellNum, answerOptionPo.getWriteContent());
                        }
                    }

                } else if (isAnalysisOneDimensional(titlePo.getType())) {
                    tempOptionList = tempOptionList.stream().sorted(Comparator.comparing(QstOptionPo::getOrderNumber)).collect(Collectors.toList());

                    for (QstOptionPo qstOptionPo : tempOptionList) {

                        if (CollectionUtils.isEmpty(answerOptionTempList)) {
                            rowCell = row.createCell(cellNum.getAndIncrement());
                            rowCell.setCellValue(0);
                            rowCell.setCellStyle(hssfCellStyle);
                        } else {
                            Map<Integer, AnswerOptionPo> optionPoMap = answerOptionTempList.stream().collect(Collectors.toMap(k -> k.getSerialNumber(), v -> v));
                            AnswerOptionPo answerOptionPo = optionPoMap.get(qstOptionPo.getSerialNumber());
                            if (answerOptionPo != null) {
                                rowCell = row.createCell(cellNum.getAndIncrement());
                                if (isPaiXu(titlePo.getType())) {
                                    rowCell.setCellValue(answerOptionPo.getScore());
                                } else {
                                    if (StringUtil.isNotBlank(answerOptionPo.getWriteContent())) {
                                        rowCell.setCellValue(1 + "【" + answerOptionPo.getWriteContent() + "】");
                                    } else {
                                        rowCell.setCellValue(1);
                                    }
                                }
                                rowCell.setCellStyle(hssfCellStyle);
                            } else {
                                rowCell = row.createCell(cellNum.getAndIncrement());
                                rowCell.setCellValue(0);
                                rowCell.setCellStyle(hssfCellStyle);
                            }
                        }
                    }

                } else if (isTwoDimensionalFillBlank(titlePo.getType())) {

                    for (QstOptionPo qstOptionPo : tempOptionList) {
                        if (CollectionUtils.isEmpty(answerOptionTempList)) {

                            rowCell = row.createCell(cellNum.getAndIncrement());
                            rowCell.setCellValue(0);
                            rowCell.setCellStyle(hssfCellStyle);
                        } else {

                            String optionKey = getOptionKey(qstOptionPo.getRowNumber(), qstOptionPo.getColumnNumber());
                            Map<String, AnswerOptionPo> answerMap = answerOptionTempList.stream().collect(Collectors.toMap(k -> getOptionKey(k.getRowNumber(), k.getColumnNumber()), v -> v));
                            AnswerOptionPo answerOptionPo = answerMap.get(optionKey);

                            if (answerOptionPo != null) {

                                constructWriteContentRow(hssfCellStyle, row, cellNum, answerOptionPo.getWriteContent());
                            } else {

                                constructWriteContentRow(hssfCellStyle, row, cellNum, "");
                            }
                        }
                    }

                } else if (isAnalysisTwoDimensional(titlePo.getType())) {
                    if (isBiaoGeDuoXuan(titlePo.getType())) {
                        tempOptionList = tempOptionList.stream().sorted(Comparator.comparing(QstOptionPo::getRowNumber).thenComparing(QstOptionPo::getOrderNumber)).collect(Collectors.toList());
                    }
                    for (QstOptionPo qstOptionPo : tempOptionList) {

                        if (CollectionUtils.isEmpty(answerOptionTempList)) {
                            rowCell = row.createCell(cellNum.getAndIncrement());
                            rowCell.setCellValue(0);
                            rowCell.setCellStyle(hssfCellStyle);
                        } else {
                            if (isBiZhong(titlePo.getType()) || isJuZhenHuaDongTiao(titlePo.getType())) {
                                Map<Integer, AnswerOptionPo> optionPoMap = answerOptionTempList.stream().collect(Collectors.toMap(k -> k.getRowNumber(), v -> v));

                                AnswerOptionPo answerOptionPo = optionPoMap.get(qstOptionPo.getSerialNumber());
                                if (answerOptionPo != null) {

                                    rowCell = row.createCell(cellNum.getAndIncrement());
                                    rowCell.setCellValue(answerOptionPo.getSerialNumber());
                                    rowCell.setCellStyle(hssfCellStyle);
                                } else {
                                    rowCell = row.createCell(cellNum.getAndIncrement());
                                    rowCell.setCellValue(0);
                                    rowCell.setCellStyle(hssfCellStyle);
                                }
                            } else {
                                String optionKey;
                                if (isBiaoGeDuoXuan(titlePo.getType())) {
                                    optionKey = getOptionKey(qstOptionPo.getRowNumber(), qstOptionPo.getSerialNumber());
                                } else {
                                    optionKey = getOptionKey(qstOptionPo.getRowNumber(), qstOptionPo.getColumnNumber());
                                }
                                Map<String, AnswerOptionPo> answerMap = answerOptionTempList.stream().collect(Collectors.toMap(a -> getOptionKey(a.getRowNumber(), a.getSerialNumber()), v -> v, (v1, v2) -> v1));

                                setRowInfo(row, hssfCellStyle, cellNum, optionKey, answerMap);
                            }
                        }
                    }

                } else if (isAnalysisThreeDimensional(titlePo.getType())) {
                    for (QstOptionPo qstOptionPo : tempOptionList) {

                        if (CollectionUtils.isEmpty(answerOptionTempList)) {

                            rowCell = row.createCell(cellNum.getAndIncrement());
                            rowCell.setCellValue(0);
                            rowCell.setCellStyle(hssfCellStyle);
                        } else {
                            String optionKey = getOptionKey(qstOptionPo.getRowNumber(), qstOptionPo.getColumnNumber(), qstOptionPo.getVerticalNumber());
                            List<String> answerList = answerOptionTempList.stream().map(v -> getOptionKey(v.getRowNumber(), v.getColumnNumber(), v.getSerialNumber())).collect(Collectors.toList());

                            setRowInfo(row, hssfCellStyle, cellNum, optionKey, answerList);
                        }
                    }
                } else {
                    continue;
                }
            }
        }
    }

    private void constructAnswerText(List<AnswerInfoPo> answerInfoPoList, Map<String, List<AnswerOptionPo>> answerOptionListMap, List<QstTitlePo> titlePoList, Map<Integer, List<QstOptionPo>> optionListMap, HSSFSheet hssfSheet, HSSFCellStyle hssfCellStyle) {
        HSSFRow row;
        AtomicInteger rowNum = new AtomicInteger(1);
        for (AnswerInfoPo answerInfoPo : answerInfoPoList) {
            row = hssfSheet.createRow(rowNum.getAndIncrement());
            AtomicInteger cellNum = new AtomicInteger(0);

            setHeaderCellContent(row, answerInfoPo, cellNum, hssfCellStyle, false, null);

            List<AnswerOptionPo> answerOptionCurrentList = answerOptionListMap.get(answerInfoPo.getAnswerId());
            Map<Integer, List<AnswerOptionPo>> answerOptionTempListMap = answerOptionCurrentList.stream().collect(Collectors.groupingBy(AnswerOptionPo::getTitleSerialNumber));
            HSSFCell rowCell;
            for (QstTitlePo titlePo : titlePoList) {

                List<AnswerOptionPo> answerOptionTempList = answerOptionTempListMap.get(titlePo.getSerialNumber());
                List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());

                if (isDuoXiangDanHangTianKong(titlePo.getType())) {
                    for (QstOptionPo qstOptionPo : tempOptionList) {
                        if (CollectionUtils.isEmpty(answerOptionTempList)) {

                            rowCell = row.createCell(cellNum.getAndIncrement());
                            rowCell.setCellValue("");
                            rowCell.setCellStyle(hssfCellStyle);
                        } else {

                            Map<Integer, AnswerOptionPo> answerMap = answerOptionTempList.stream().collect(Collectors.toMap(k -> k.getRowNumber(), v -> v));
                            AnswerOptionPo answerOptionPo = answerMap.get(qstOptionPo.getRowNumber());

                            if (answerOptionPo != null) {

                                constructWriteContentRow(hssfCellStyle, row, cellNum, answerOptionPo.getWriteContent());
                            } else {

                                constructWriteContentRow(hssfCellStyle, row, cellNum, "");
                            }
                        }
                    }
                } else if (isOneDimensionalFillBlank(titlePo.getType())) {
                    if (CollectionUtils.isEmpty(answerOptionTempList)) {

                        constructWriteContentRow(hssfCellStyle, row, cellNum, "");
                    } else {

                        AnswerOptionPo answerOptionPo = answerOptionTempList.get(0);
                        if (isHuaDongTiao(titlePo.getType())) {
                            constructWriteContentRow(hssfCellStyle, row, cellNum, answerOptionPo.getSerialNumber().toString());
                        } else {
                            constructWriteContentRow(hssfCellStyle, row, cellNum, answerOptionPo.getWriteContent());
                        }
                    }

                } else if (isAnalysisOneDimensional(titlePo.getType())) {
                    if (CollectionUtils.isEmpty(answerOptionTempList)) {
                        rowCell = row.createCell(cellNum.getAndIncrement());
                        rowCell.setCellValue("");
                        rowCell.setCellStyle(hssfCellStyle);
                    } else {
                        Map<Integer, QstOptionPo> optionMap = tempOptionList.stream().collect(Collectors.toMap(QstOptionPo::getSerialNumber, v -> v));

                        rowCell = row.createCell(cellNum.getAndIncrement());
                        if (isOneDimensionalDanXuanList(titlePo.getType())) {
                            AnswerOptionPo answerOptionPo = answerOptionTempList.get(0);
                            QstOptionPo qstOptionPo = optionMap.get(answerOptionPo.getSerialNumber());
                            if (qstOptionPo != null) {
                                String content = qstOptionPo.getContent();
                                if (StringUtil.isNotBlank(answerOptionPo.getWriteContent())) {
                                    rowCell.setCellValue(content + "『" + answerOptionPo.getWriteContent() + "』");
                                } else {
                                    rowCell.setCellValue(content);
                                }
                            } else {
                                rowCell.setCellValue("");
                            }
                        } else if (isOneDimensionalDuoXuanList(titlePo.getType())) {
                            List<AnswerOptionPo> answerOptionPoList = answerOptionTempList.stream().sorted(Comparator.comparing(AnswerOptionPo::getSerialNumber)).collect(Collectors.toList());
                            StringBuffer buffer = new StringBuffer();
                            for (int i = 0, size = answerOptionPoList.size(); i < size; i++) {
                                AnswerOptionPo answerOptionPo = answerOptionPoList.get(i);
                                buffer.append(optionMap.get(answerOptionPo.getSerialNumber()).getContent());

                                if (StringUtil.isNotBlank(answerOptionPo.getWriteContent())) {
                                    buffer.append("『" + answerOptionPo.getWriteContent() + "』");
                                }

                                if (i != size - 1) {
                                    buffer.append(SEPARATOR);
                                }
                            }
                            rowCell.setCellValue(buffer.toString());

                        } else if (isPaiXu(titlePo.getType())) {
                            StringBuffer buffer = new StringBuffer();
                            List<AnswerOptionPo> answerOptionPoList = answerOptionTempList.stream().sorted(Comparator.comparing(AnswerOptionPo::getScore).reversed()).collect(Collectors.toList());
                            for (int i = 0, size = answerOptionPoList.size(); i < size; i++) {
                                AnswerOptionPo answerOptionPo = answerOptionPoList.get(i);
                                buffer.append(optionMap.get(answerOptionPo.getSerialNumber()).getContent());
                                if (i != size - 1) {
                                    buffer.append(RIGHT_ARROW);
                                }
                            }
                            rowCell.setCellValue(buffer.toString());
                        }
                    }

                } else if (isTwoDimensionalFillBlank(titlePo.getType())) {

                    for (QstOptionPo qstOptionPo : tempOptionList) {
                        if (CollectionUtils.isEmpty(answerOptionTempList)) {
                            rowCell = row.createCell(cellNum.getAndIncrement());
                            rowCell.setCellValue(0);
                            rowCell.setCellStyle(hssfCellStyle);
                        } else {
                            String optionKey = getOptionKey(qstOptionPo.getRowNumber(), qstOptionPo.getColumnNumber());
                            Map<String, AnswerOptionPo> answerMap = answerOptionTempList.stream().collect(Collectors.toMap(k -> getOptionKey(k.getRowNumber(), k.getColumnNumber()), v -> v));
                            AnswerOptionPo answerOptionPo = answerMap.get(optionKey);

                            if (answerOptionPo != null) {
                                constructWriteContentRow(hssfCellStyle, row, cellNum, answerOptionPo.getWriteContent());
                            } else {
                                constructWriteContentRow(hssfCellStyle, row, cellNum, "");
                            }
                        }
                    }

                } else if (isAnalysisTwoDimensional(titlePo.getType())) {
                    if (isBiaoGeDuoXuan(titlePo.getType())) {
                        tempOptionList = tempOptionList.stream().sorted(Comparator.comparing(QstOptionPo::getRowNumber).thenComparing(QstOptionPo::getOrderNumber)).collect(Collectors.toList());
                    }

                    Map<Integer, List<QstOptionPo>> rowTitleMap;
                    if (isBiaoGeDanXuan(titlePo.getType()) || isBiaoGeDuoXuan(titlePo.getType())) {
                        rowTitleMap = tempOptionList.stream().collect(Collectors.groupingBy(QstOptionPo::getRowNumber));
                    } else {
                        rowTitleMap = tempOptionList.stream().collect(Collectors.groupingBy(QstOptionPo::getSerialNumber));
                    }

                    for (Map.Entry<Integer, List<QstOptionPo>> integerListEntry : rowTitleMap.entrySet()) {
                        QstOptionPo qstOptionPo1 = integerListEntry.getValue().get(0);
                        List<QstOptionPo> optionPoList = integerListEntry.getValue();
                        if (CollectionUtils.isEmpty(answerOptionTempList)) {
                            rowCell = row.createCell(cellNum.getAndIncrement());
                            rowCell.setCellValue("");
                            rowCell.setCellStyle(hssfCellStyle);
                            continue;
                        }

                        if (isBiaoGeDanXuan(titlePo.getType()) || isBiaoGeDuoXuan(titlePo.getType())) {
                            Map<Integer, List<AnswerOptionPo>> answerMap = answerOptionTempList.stream().collect(Collectors.groupingBy(AnswerOptionPo::getRowNumber));
                            List<AnswerOptionPo> answerOptionPoList = answerMap.get(qstOptionPo1.getRowNumber());
                            StringBuffer buffer = new StringBuffer();
                            Map<Integer, QstOptionPo> optionPoMap = optionPoList.stream().collect(Collectors.toMap(QstOptionPo::getSerialNumber, v -> v, (key1, key2) -> key1));

                            rowCell = row.createCell(cellNum.getAndIncrement());
                            if (CollectionUtils.isEmpty(answerOptionPoList)) {
                                rowCell.setCellValue("");
                            } else {
                                for (int i = 0, size = answerOptionPoList.size(); i < size; i++) {
                                    AnswerOptionPo answerOptionPo = answerOptionPoList.get(i);
                                    QstOptionPo qstOptionPo = optionPoMap.get(answerOptionPo.getSerialNumber());
                                    if (qstOptionPo == null) {
                                        continue;
                                    }
                                    buffer.append(qstOptionPo.getContent());

                                    if (StringUtil.isNotBlank(answerOptionPo.getWriteContent())) {
                                        buffer.append("『" + answerOptionPo.getWriteContent() + "』");
                                    }
                                    if (i != size - 1) {
                                        buffer.append(SEPARATOR);
                                    }
                                }
                            }
                            rowCell.setCellValue(buffer.toString());
                            rowCell.setCellStyle(hssfCellStyle);

                        }  else {
                            Map<Integer, AnswerOptionPo> optionPoMap = answerOptionTempList.stream().collect(Collectors.toMap(k -> k.getRowNumber(), v -> v));

                            AnswerOptionPo answerOptionPo = optionPoMap.get(qstOptionPo1.getSerialNumber());
                            rowCell = row.createCell(cellNum.getAndIncrement());
                            if (answerOptionPo != null) {
                                rowCell.setCellValue(answerOptionPo.getSerialNumber());
                            } else {
                                rowCell.setCellValue("");
                            }
                            rowCell.setCellStyle(hssfCellStyle);
                        }
                    }

                } else if (isAnalysisThreeDimensional(titlePo.getType())) {
                    List<QstOptionPo> tempOptionList1 = optionListMap.get(titlePo.getSerialNumber());
                    Map<String, List<QstOptionPo>> optionMap = new TreeMap<>(tempOptionList1.stream().collect(Collectors.groupingBy(v -> v.getRowNumber() + SPLIT_FLAG + v.getColumnNumber())));

                    for (Map.Entry<String, List<QstOptionPo>> entry : optionMap.entrySet()) {
                        if (CollectionUtils.isEmpty(answerOptionTempList)) {
                            rowCell = row.createCell(cellNum.getAndIncrement());
                            rowCell.setCellValue("");
                            rowCell.setCellStyle(hssfCellStyle);
                        } else {
                            Map<Integer, QstOptionPo> optionPoMap = tempOptionList1.stream().collect(Collectors.toMap(QstOptionPo::getSerialNumber, v -> v, (key1, key2) -> key1));
                            Map<String, AnswerOptionPo> answerMap = answerOptionTempList.stream().collect(Collectors.toMap(v -> v.getRowNumber() + SPLIT_FLAG + v.getColumnNumber(), val -> val, (key1, key2) -> key1));
                            AnswerOptionPo answerOptionPo = answerMap.get(entry.getKey());
                            if (answerOptionPo == null) {
                                rowCell = row.createCell(cellNum.getAndIncrement());
                                rowCell.setCellValue("");
                                rowCell.setCellStyle(hssfCellStyle);
                            } else {
                                QstOptionPo qstOptionPo = optionPoMap.get(answerOptionPo.getSerialNumber());
                                rowCell = row.createCell(cellNum.getAndIncrement());
                                rowCell.setCellValue(qstOptionPo.getContent());
                                rowCell.setCellStyle(hssfCellStyle);
                            }
                        }
                    }

                } else {
                    continue;
                }
            }
        }
    }

    private void constructCell(List<QstTitlePo> titlePoList, Map<Integer, List<QstOptionPo>> optionListMap, HSSFRow row, HSSFCellStyle hssfCellStyle, boolean isKx) {
        HSSFCell cell;
        AtomicInteger columnNum = new AtomicInteger(0);

        for (HeaderCellTitleEnum titleEnum : getHeaderCellTitleList()) {
            constructWriteContentRow(hssfCellStyle, row, columnNum, titleEnum.value());
        }
        if (isKx) {
            constructWriteContentRow(hssfCellStyle, row, columnNum, HeaderCellTitleEnum.NAME.value());
        }

        for (QstTitlePo titlePo : titlePoList) {
            String initCellTitle = getCellTitle(titlePo.getSerialNumber(), titlePo.getName());

            if (isOneDimensionalFillBlank(titlePo.getType())) {
                if (isDuoXiangDanHangTianKong(titlePo.getType())) {
                    List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());
                    boolean firstOptionTitle = true;

                    for (QstOptionPo qstOptionPo : tempOptionList) {
                        cell = row.createCell(columnNum.getAndIncrement());
                        if (firstOptionTitle) {
                            cell.setCellValue(getCellTitle(initCellTitle, qstOptionPo.getRowTitle()));
                            firstOptionTitle = false;
                        } else {
                            cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), qstOptionPo.getRowTitle()));
                        }
                        cell.setCellStyle(hssfCellStyle);//列居中显示
                    }
                } else {
                    constructWriteContentRow(hssfCellStyle, row, columnNum, initCellTitle);
                }

            } else if (isAnalysisOneDimensional(titlePo.getType())) {
                List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());
                tempOptionList = tempOptionList.stream().sorted(Comparator.comparing(QstOptionPo::getOrderNumber)).collect(Collectors.toList());
                boolean firstOptionTitle = true;

                for (QstOptionPo qstOptionPo : tempOptionList) {
                    cell = row.createCell(columnNum.getAndIncrement());
                    if (firstOptionTitle) {
                        cell.setCellValue(getCellTitle(initCellTitle, qstOptionPo.getContent()));
                        firstOptionTitle = false;
                    } else {
                        cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), qstOptionPo.getContent()));
                    }
                    cell.setCellStyle(hssfCellStyle);//列居中显示
                }

            } else if (isAnalysisTwoDimensional(titlePo.getType())) {
                List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());
                boolean firstOptionTitle = true;
                if (isBiaoGeDuoXuan(titlePo.getType())) {
                    tempOptionList = tempOptionList.stream().sorted(Comparator.comparing(QstOptionPo::getRowNumber).thenComparing(QstOptionPo::getOrderNumber)).collect(Collectors.toList());
                }

                for (QstOptionPo qstOptionPo : tempOptionList) {
                    String columnTitle = qstOptionPo.getContent();
                    if (isTwoDimensionalFillBlank(titlePo.getType())) {
                        columnTitle = qstOptionPo.getColumnTitle();
                    }

                    cell = row.createCell(columnNum.getAndIncrement());
                    if (firstOptionTitle) {
                        if (isBiZhong(titlePo.getType()) || isJuZhenHuaDongTiao(titlePo.getType())) {
                            cell.setCellValue(getCellTitle(initCellTitle, qstOptionPo.getRowTitle()));
                        } else {
                            cell.setCellValue(getCellTitle(initCellTitle, qstOptionPo.getRowTitle(), columnTitle));
                        }
                        firstOptionTitle = false;
                    } else {
                        if (isBiZhong(titlePo.getType()) || isJuZhenHuaDongTiao(titlePo.getType())) {
                            cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), qstOptionPo.getRowTitle()));
                        } else {
                            cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), qstOptionPo.getRowTitle(), columnTitle));
                        }
                    }
                    cell.setCellStyle(hssfCellStyle);//列居中显示
                }

            } else if (isTwoDimensionalFillBlank(titlePo.getType())) {
                List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());
                boolean firstOptionTitle = true;

                for (QstOptionPo qstOptionPo : tempOptionList) {

                    cell = row.createCell(columnNum.getAndIncrement());
                    if (firstOptionTitle) {
                        cell.setCellValue(getCellTitle(initCellTitle, qstOptionPo.getRowTitle(), qstOptionPo.getColumnTitle()));
                        firstOptionTitle = false;
                    } else {
                        cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), qstOptionPo.getRowTitle(), qstOptionPo.getColumnTitle()));
                    }
                    cell.setCellStyle(hssfCellStyle);//列居中显示
                }
            } else if (isAnalysisThreeDimensional(titlePo.getType())) {
                List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());
                boolean firstOptionTitle = true;

                for (QstOptionPo qstOptionPo : tempOptionList) {
                    cell = row.createCell(columnNum.getAndIncrement());
                    if (firstOptionTitle) {
                        cell.setCellValue(getCellTitle(initCellTitle, qstOptionPo.getRowTitle(), qstOptionPo.getColumnTitle(), qstOptionPo.getContent()));
                        firstOptionTitle = false;
                    } else {
                        cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), qstOptionPo.getRowTitle(), qstOptionPo.getColumnTitle(), qstOptionPo.getContent()));
                    }
                    cell.setCellStyle(hssfCellStyle);//列居中显示
                }

            } else {
                continue;
            }
        }
    }

    private void constructCellText(List<QstTitlePo> titlePoList, Map<Integer, List<QstOptionPo>> optionListMap, HSSFRow row, HSSFCellStyle hssfCellStyle) {
        HSSFCell cell;
        AtomicInteger columnNum = new AtomicInteger(0);

        for (HeaderCellTitleEnum titleEnum : getHeaderCellTitleList()) {
            constructWriteContentRow(hssfCellStyle, row, columnNum, titleEnum.value());
        }

        for (QstTitlePo titlePo : titlePoList) {
            String initCellTitle = getCellTitle(titlePo.getSerialNumber(), titlePo.getName());

            if (isOneDimensionalFillBlank(titlePo.getType())) {
                if (isDuoXiangDanHangTianKong(titlePo.getType())) {
                    List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());
                    boolean firstOptionTitle = true;

                    for (QstOptionPo qstOptionPo : tempOptionList) {
                        cell = row.createCell(columnNum.getAndIncrement());
                        if (firstOptionTitle) {
                            cell.setCellValue(getCellTitle(initCellTitle, qstOptionPo.getRowTitle()));
                            firstOptionTitle = false;
                        } else {
                            cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), qstOptionPo.getRowTitle()));
                        }
                        cell.setCellStyle(hssfCellStyle);//列居中显示
                    }
                } else {
                    constructWriteContentRow(hssfCellStyle, row, columnNum, initCellTitle);
                }

            } else if (isAnalysisOneDimensional(titlePo.getType())) {
                cell = row.createCell(columnNum.getAndIncrement());
                cell.setCellValue(initCellTitle);
                cell.setCellStyle(hssfCellStyle);//列居中显示
            } else if (isAnalysisTwoDimensional(titlePo.getType())) {
                List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());
                boolean firstOptionTitle = true;
                if (isBiaoGeDuoXuan(titlePo.getType())) {
                    tempOptionList = tempOptionList.stream().sorted(Comparator.comparing(QstOptionPo::getRowNumber).thenComparing(QstOptionPo::getOrderNumber)).collect(Collectors.toList());
                }
                Map<Integer, List<QstOptionPo>> rowTitleMap;
                if (isBiaoGeDanXuan(titlePo.getType()) || isBiaoGeDuoXuan(titlePo.getType())) {
                    rowTitleMap = tempOptionList.stream().collect(Collectors.groupingBy(QstOptionPo::getRowNumber));
                } else {
                    rowTitleMap = tempOptionList.stream().collect(Collectors.groupingBy(QstOptionPo::getSerialNumber));
                }

                for (Map.Entry<Integer, List<QstOptionPo>> integerListEntry : rowTitleMap.entrySet()) {
                    QstOptionPo optionPo = integerListEntry.getValue().get(0);
                    cell = row.createCell(columnNum.getAndIncrement());
                    if (firstOptionTitle) {
                        cell.setCellValue(getCellTitle(initCellTitle, optionPo.getRowTitle()));
                        firstOptionTitle = false;
                    } else {
                        cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), optionPo.getRowTitle()));
                    }
                    cell.setCellStyle(hssfCellStyle);//列居中显示
                }

            } else if (isTwoDimensionalFillBlank(titlePo.getType())) {
                List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());
                boolean firstOptionTitle = true;

                for (QstOptionPo qstOptionPo : tempOptionList) {

                    cell = row.createCell(columnNum.getAndIncrement());
                    if (firstOptionTitle) {
                        cell.setCellValue(getCellTitle(initCellTitle, qstOptionPo.getRowTitle(), qstOptionPo.getColumnTitle()));
                        firstOptionTitle = false;
                    } else {
                        cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), qstOptionPo.getRowTitle(), qstOptionPo.getColumnTitle()));
                    }
                    cell.setCellStyle(hssfCellStyle);//列居中显示
                }
            } else if (isAnalysisThreeDimensional(titlePo.getType())) {
                List<QstOptionPo> tempOptionList = optionListMap.get(titlePo.getSerialNumber());
                boolean firstOptionTitle = true;
                Map<String, List<QstOptionPo>> optionMap = new TreeMap<>(tempOptionList.stream().collect(Collectors.groupingBy(v -> v.getRowNumber() + SPLIT_FLAG + v.getColumnNumber())));
                for (Map.Entry<String, List<QstOptionPo>> entry : optionMap.entrySet()) {
                    QstOptionPo qstOptionPo = entry.getValue().get(0);
                    cell = row.createCell(columnNum.getAndIncrement());
                    if (firstOptionTitle) {
                        cell.setCellValue(getCellTitle(initCellTitle, qstOptionPo.getRowTitle(), qstOptionPo.getColumnTitle()));
                        firstOptionTitle = false;
                    } else {
                        cell.setCellValue(getCellTitle(titlePo.getSerialNumber(), qstOptionPo.getRowTitle(), qstOptionPo.getColumnTitle()));
                    }
                    cell.setCellStyle(hssfCellStyle);//列居中显示
                }
            } else {
                continue;
            }
        }
    }

    public AnswerOptionQuery constructAnswerOptionQuery(DownloadAnswerReqDto reqDto) {
        AnswerOptionQuery optionQuery = new AnswerOptionQuery();
        optionQuery.setQuestionnaireId(reqDto.getQuestionnaireId());
        optionQuery.setUserId(reqDto.getUserId());
        optionQuery.setStatus(AnswerStatusEnum.FINISH.key());
        return optionQuery;
    }

    public AnswerInfoQuery constructAnswerInfoQuery(DownloadAnswerReqDto reqDto) {
        AnswerInfoQuery answerInfoQuery = new AnswerInfoQuery();
        answerInfoQuery.setQuestionnaireId(reqDto.getQuestionnaireId());
        answerInfoQuery.setUserId(reqDto.getUserId());
        answerInfoQuery.setStatus(AnswerStatusEnum.FINISH.key());
        return answerInfoQuery;
    }
}

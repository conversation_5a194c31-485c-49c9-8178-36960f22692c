package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.AnswerStatusEnum;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.bo.AnswerAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.AnswerAnalysisOptionBo;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import com.cas.nc.questionnaire.dao.query.AnswerOptionQuery;
import com.cas.nc.questionnaire.dao.sharddao.AnswerOptionDao;
import com.cas.nc.questionnaire.service.AnswerOptionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.ArrayList;

@Service
public class AnswerOptionServiceImpl implements AnswerOptionService {

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private AnswerOptionDao answerOptionDao;

    @Override
    public int insert(AnswerOptionPo answerOptionPo) {
        return answerOptionDao.insert(answerOptionPo);
    }

    @Override
    public int delete(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.delete(query);
    }

    @Override
    public List<AnswerOptionPo> selectList(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.selectList(query);
    }

    @Override
    public AnswerOptionPo selectOne(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.selectOne(query);
    }

    @Override
    public List<AnswerOptionPo> selectFilterList(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.selectFilterList(query);
    }

    @Override
    public List<AnswerAnalysisBo> selectTitleAnalysis(String questionnaireId, Long userId, List<String> answerIdList) {
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setUserId(userId);
        query.setStatus(AnswerStatusEnum.FINISH.key());
        query.setAnswerIdList(answerIdList);

        validateUserId(query);
        return answerOptionDao.selectTitleAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisBo> selectOneDimensionalAnalysis(String questionnaireId, Integer titleSerialNumber, Long userId, List<String> answerIdList) {
        AnswerOptionQuery query = getCommonDimensionalQuery(questionnaireId, titleSerialNumber, userId);
        query.setAnswerIdList(answerIdList);

        validateUserId(query);
        return answerOptionDao.selectOneDimensionalAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisBo> selectTwoDimensionalAnalysis(String questionnaireId, Integer titleSerialNumber, Long userId, List<String> answerIdList) {
        AnswerOptionQuery query = getCommonDimensionalQuery(questionnaireId, titleSerialNumber, userId);
        query.setAnswerIdList(answerIdList);

        validateUserId(query);
        return answerOptionDao.selectTwoDimensionalAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisBo> selectThreeDimensionalAnalysis(String questionnaireId, Integer titleSerialNumber, Long userId, List<String> answerIdList) {
        AnswerOptionQuery query = getCommonDimensionalQuery(questionnaireId, titleSerialNumber, userId);
        query.setAnswerIdList(answerIdList);

        validateUserId(query);
        return answerOptionDao.selectThreeDimensionalAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisBo> selectBiZhongAnalysis(String questionnaireId, Integer titleSerialNumber, Long userId, List<String> answerIdList) {
        AnswerOptionQuery query = getCommonDimensionalQuery(questionnaireId, titleSerialNumber, userId);
        query.setAnswerIdList(answerIdList);

        validateUserId(query);
        return answerOptionDao.selectBiZhongAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisBo> selectPaiXuAnalysis(String questionnaireId, Integer titleSerialNumber, Long userId, List<String> answerIdList) {
        AnswerOptionQuery query = getCommonDimensionalQuery(questionnaireId, titleSerialNumber, userId);
        query.setAnswerIdList(answerIdList);

        validateUserId(query);
        return answerOptionDao.selectPaiXuAnalysis(query);
    }

    @Override
    public AnswerAnalysisBo selectHuaDongTiaoAnalysis(String questionnaireId, Integer titleSerialNumber, Long userId, List<String> answerIdList) {
        AnswerOptionQuery query = getCommonDimensionalQuery(questionnaireId, titleSerialNumber, userId);
        query.setAnswerIdList(answerIdList);

        validateUserId(query);

        return answerOptionDao.selectHuaDongTiaoAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisBo> selectJuZhenHuaDongTiaoAnalysis(String questionnaireId, Integer titleSerialNumber, Long userId, List<String> answerIdList) {
        AnswerOptionQuery query = getCommonDimensionalQuery(questionnaireId, titleSerialNumber, userId);
        query.setAnswerIdList(answerIdList);

        validateUserId(query);

        return answerOptionDao.selectJuZhenHuaDongTiaoAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisOptionBo> selectOneDimensionalContainNullCrossAnalysis(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.selectOneDimensionalContainNullCrossAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisOptionBo> selectOneDimensionalNotNullCrossAnalysis(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.selectOneDimensionalNotNullCrossAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisOptionBo> selectTwoDimensionalContainNullCrossAnalysis(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.selectTwoDimensionalContainNullCrossAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisOptionBo> selectTwoDimensionalNotNullCrossAnalysis(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.selectTwoDimensionalNotNullCrossAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisOptionBo> selectOneDimensionalIndependentVariableCrossAnalysis(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.selectOneDimensionalIndependentVariableCrossAnalysis(query);
    }

    @Override
    public List<AnswerAnalysisOptionBo> selectTwoDimensionalIndependentVariableCrossAnalysis(AnswerOptionQuery query) {
        validateUserId(query);
        return answerOptionDao.selectTwoDimensionalIndependentVariableCrossAnalysis(query);
    }

    @Override
    public int selectCount(AnswerOptionQuery query) {
        filterCondition(query);
        return answerOptionDao.selectCount(query);
    }

    @Override
    public List<AnswerOptionPo> selectList(String questionnaireId, Long userId, List<Integer> titleSerialNumber) {
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setUserId(userId);
        query.setQuestionnaireId(questionnaireId);
        query.setTitleSerialNumberList(titleSerialNumber);

        validateUserId(query);
        return answerOptionDao.selectList(query);
    }

    @Override
    public List<AnswerOptionPo> selectList(String questionnaireId, Long userId) {
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setUserId(userId);
        query.setQuestionnaireId(questionnaireId);

        return selectList(query);
    }

    @Override
    public List<AnswerOptionPo> selectList(Long userId, String answerId) {
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setAnswerId(answerId);
        query.setUserId(userId);

        return answerOptionDao.selectList(query);
    }

    @Override
    public List<AnswerOptionPo> selectListByPage(AnswerOptionQuery query) {
        return answerOptionDao.selectListByPage(query);
    }

    @Override
    public int update(AnswerOptionQuery query) {
        return answerOptionDao.update(query);
    }

    @Override
    public List<String> selectAnswerIdList(AnswerOptionQuery query) {
        return answerOptionDao.selectAnswerIdList(query);
    }

    @Override
    public List<String> selectAnswerIdsByCondition(String questionnaireId, Integer titleSerialNumber, String queryValue) {
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setTitleSerialNumber(titleSerialNumber);
        query.setWriteContent(queryValue);
        
        List<AnswerOptionPo> answerOptionList = selectList(query);
        List<String> answerIdList = new ArrayList<>();
        for (AnswerOptionPo answerOption : answerOptionList) {
            if (!answerIdList.contains(answerOption.getAnswerId())) {
                answerIdList.add(answerOption.getAnswerId());
            }
        }
        return answerIdList;
    }

    private void validateUserId(AnswerOptionQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }

    private void filterCondition(AnswerOptionQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }

    private AnswerOptionQuery getCommonDimensionalQuery(String questionnaireId, Integer titleSerialNumber, Long userId) {
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setQuestionnaireId(questionnaireId);
        query.setTitleSerialNumber(titleSerialNumber);
        query.setUserId(userId);
        query.setStatus(AnswerStatusEnum.FINISH.key());
        return query;
    }
}

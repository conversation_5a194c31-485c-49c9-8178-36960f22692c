package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.AnalysisListFillBlankRepDto;
import com.cas.nc.questionnaire.common.dto.analysis.AnalysisListFillBlankReqDto;
import com.cas.nc.questionnaire.common.dto.analysis.DownloadFillBlankReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.AnalysisListFillBlankRepVo;
import com.cas.nc.questionnaire.common.vo.analysis.AnalysisListFillBlankReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ListFillBlankConverter {
    ListFillBlankConverter INSTANCE = Mappers.getMapper(ListFillBlankConverter.class);

    AnalysisListFillBlankReqDto to(AnalysisListFillBlankReqVo vo);

    @Mappings({
            @Mapping(source = "fillBlankRepDtoList", target = "fillBlankRepVoList"),
    })
    AnalysisListFillBlankRepVo to(AnalysisListFillBlankRepDto repDto);

    DownloadFillBlankReqDto to(AnalysisListFillBlankReqDto reqDto);
}
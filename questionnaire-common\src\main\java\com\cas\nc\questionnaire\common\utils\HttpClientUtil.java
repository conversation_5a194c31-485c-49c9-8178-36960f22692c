
package com.cas.nc.questionnaire.common.utils;

import org.apache.commons.httpclient.ConnectTimeoutException;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.Header;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.Cookie;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;


public class HttpClientUtil {

    private static Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    private static final int CONNECT_TIMEOUT = 5000;
    private static final int READ_TIMEOUT = 5000;

    public static String doPost(String url, Map<String, ?> params) {

        String result = null;
        HttpClient httpClient = new HttpClient();
        PostMethod postMethod = new PostMethod(url);
        httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(CONNECT_TIMEOUT);
        httpClient.getParams().setContentCharset("utf-8");
        Set<?> keySet = params.entrySet();
        for (Iterator<Entry<String, ?>> iterator = (Iterator<Entry<String, ?>>) keySet.iterator(); iterator.hasNext(); ) {
            Entry<String, ?> entry = iterator.next();
            if (null == entry.getValue()) {
                continue;
            }
            postMethod.addParameter(entry.getKey(), entry.getValue().toString());
        }
        BufferedReader br = null;
        InputStream is = null;
        try {
            int status = httpClient.executeMethod(postMethod);
            if (200 != status) {
                logger.error(MessageFormat.format("URL:{0} 访问出错，http状态码：{1}", url, status));
            }
            is = postMethod.getResponseBodyAsStream();
//            br = new BufferedReader(new InputStreamReader(is));
//            StringBuilder sb = new StringBuilder();
//            String line = null;
//            while ((line = br.readLine()) != null) {
//                sb.append(line);
//            }
//            result = sb.toString();
            result = inputStreamTOString(is, "UTF-8");
        } catch (ConnectTimeoutException e) {
            logger.error(MessageFormat.format("URL:{0} 连接超时，超时时间：{1} ms", url, CONNECT_TIMEOUT), e.fillInStackTrace());
        } catch (SocketTimeoutException e) {
            logger.error(MessageFormat.format("URL:{0} 长时间没有返回数据，读取超时，超时时间：{1} ms", url, READ_TIMEOUT), e.fillInStackTrace());
        } catch (HttpException e) {
            logger.error("", e.fillInStackTrace());
        } catch (IOException e) {
            logger.error("", e.fillInStackTrace());
        } finally {
            if (null != postMethod) {
                postMethod.releaseConnection();
            }
            try {
                if (null != br) {
                    br.close();
                }
                if (null != is) {
                    is.close();
                }
            } catch (IOException e) {
                logger.error("", e.fillInStackTrace());
            }
        }
        return result;
    }

    /**
     * httpClient的get请求方式2
     *
     * @return
     * @throws Exception
     */
    public static String doGet(String url, String charset)
            throws Exception {
    /*
     * 使用 GetMethod 来访问一个 URL 对应的网页,实现步骤: 1:生成一个 HttpClinet 对象并设置相应的参数。
     * 2:生成一个 GetMethod 对象并设置响应的参数。 3:用 HttpClinet 生成的对象来执行 GetMethod 生成的Get
     * 方法。 4:处理响应状态码。 5:若响应正常，处理 HTTP 响应内容。 6:释放连接。
     */
    /* 1 生成 HttpClinet 对象并设置参数 */
        HttpClient httpClient = new HttpClient();
        // 设置 Http 连接超时为5秒
        httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(500000);
    /* 2 生成 GetMethod 对象并设置参数 */
        GetMethod getMethod = new GetMethod(url);
        // 设置 get 请求超时为 5 秒
        getMethod.getParams().setParameter(HttpMethodParams.SO_TIMEOUT, 500000);
        // 设置请求重试处理，用的是默认的重试处理：请求三次
        getMethod.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler());
        String response = "";
    /* 3 执行 HTTP GET 请求 */
        try {
            int statusCode = httpClient.executeMethod(getMethod);
      /* 4 判断访问的状态码 */
            if (statusCode != HttpStatus.SC_OK) {
                logger.error("请求出错：{}", getMethod.getStatusLine());
            }
      /* 5 处理 HTTP 响应内容 */
            // HTTP响应头部信息，这里简单打印
            Header[] headers = getMethod.getResponseHeaders();
//            for (Header h : headers)
//                System.out.println(h.getName() + "------------ " + h.getValue());
            // 读取 HTTP 响应内容，这里简单打印网页内容
            byte[] responseBody = getMethod.getResponseBody();// 读取为字节数组
            response = new String(responseBody, charset);
            // 读取为 InputStream，在网页内容数据量大时候推荐使用
            // InputStream response = getMethod.getResponseBodyAsStream();
        } catch (HttpException e) {
            // 发生致命的异常，可能是协议不对或者返回的内容有问题
            logger.error("HttpClientUtil.doGet HttpException", e);
        } catch (IOException e) {
            // 发生网络异常
            logger.error("HttpClientUtil.doGet IOException", e);
        } finally {
      /* 6 .释放连接 */
            getMethod.releaseConnection();
        }
        return response;
    }

    public static byte[] inputStreamTOByte(InputStream in) throws IOException {
        int BUFFER_SIZE = 4096;
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] data = new byte[BUFFER_SIZE];
        int count = -1;
        while ((count = in.read(data, 0, BUFFER_SIZE)) != -1)
            outStream.write(data, 0, count);
        data = null;
        byte[] outByte = outStream.toByteArray();
        outStream.close();
        return outByte;
    }

    /**
     * InputStream转换成String 注意:流关闭需要自行处理
     *
     * @param in
     * @param
     * @return String
     * @throws Exception
     */
    public static String inputStreamTOString(InputStream in, String encoding) throws IOException {
        return new String(inputStreamTOByte(in), encoding);
    }

    /**
     * post请求
     *
     * @param url
     * @param json
     * @return
     */

    public static String doPost(String url, String json) {
        DefaultHttpClient client = new DefaultHttpClient();
        HttpPost post = new HttpPost(url);
        String result = "";
        try {
            StringEntity s = new StringEntity(json);
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");//发送json数据需要设置contentType
            post.setHeader("Content-Type", "application/json;charset=UTF-8");
            post.setEntity(s);
            HttpResponse res = client.execute(post);
            if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                HttpEntity entity = res.getEntity();
                result = EntityUtils.toString(res.getEntity(), "UTF-8");// 返回json格式：
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    public static String post(String url, String json) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost post = getHttpPost(url, 100000);

        CloseableHttpResponse response = null;
        try {
            post.setHeader("Content-Type", "application/json;charset=UTF-8");
            post.setEntity(new StringEntity(json, "UTF-8"));
            response = httpClient.execute(post);
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                logger.info("http status code is not 200, http status code={}, url={}, json={}", response.getStatusLine().getStatusCode(), url, json);
                return "";
            }
            HttpEntity entity = response.getEntity();
            String result = EntityUtils.toString(entity);
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            logger.error("post is error {}", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                logger.error("post is error {}", e);
            }
        }
        return "";
    }

    public static String post1(String url, String json, String token) {
        CookieStore cookieStore = new BasicCookieStore();
        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCookieStore(cookieStore)
                .build();
        HttpPost post = getHttpPost(url, 10000);

        CloseableHttpResponse response = null;
        try {
            BasicClientCookie cookie = new BasicClientCookie("ssoinfo", token);
            cookie.setDomain("140.143.65.12");
            cookie.setPath("/");
            cookieStore.addCookie(cookie);

            post.setHeader("Content-Type", "application/json;charset=UTF-8");
            post.setEntity(new StringEntity(json, "UTF-8"));
            response = httpClient.execute(post);
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                logger.info("http status code is not 200, http status code={}, url={}, json={}", response.getStatusLine().getStatusCode(), url, json);
                return "";
            }
            HttpEntity entity = response.getEntity();
            String result = EntityUtils.toString(entity);
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            logger.error("post is error {}", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                logger.error("post is error {}", e);
            }
        }
        return "";
    }

    public static void upload(String url, String localFile, Map<String, String> param) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            httpClient = HttpClients.createDefault();

            // 把一个普通参数和文件上传给下面这个地址 是一个servlet
            HttpPost httpPost = new HttpPost(url);

            File file = new File(localFile);

            // 相当于<input type="file" name="file"/>
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.addBinaryBody("pic", new FileInputStream(file), ContentType.DEFAULT_BINARY, file.getName());
            for (Map.Entry<String, String> entry : param.entrySet()) {
                String key = entry.getKey();
                // 相当于<input type="text" name="userName" value=userName>
                StringBody value = new StringBody(entry.getValue(), ContentType.create("text/plain", Consts.UTF_8));
                builder.addPart(key, value);
            }
            HttpEntity reqEntity = builder.build();

            httpPost.setEntity(reqEntity);

            // 发起请求 并返回请求的响应
            response = httpClient.execute(httpPost);

            // 获取响应对象
            HttpEntity resEntity = response.getEntity();
            if (resEntity != null) {
                // 打印响应内容
                logger.info("upload: " + EntityUtils.toString(resEntity, Charset.forName("UTF-8")));
            }

            // 销毁
            EntityUtils.consume(resEntity);
        } catch (Exception e) {
            logger.error("upload Exception ", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                logger.error("upload IOException ", e);
            }

            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                logger.error("upload IOException ", e);
            }
        }
    }

    private static HttpPost getHttpPost(String url, Integer timeOutLimitMils) {
        HttpPost post = new HttpPost(url);
        RequestConfig config = RequestConfig.custom()
                .setConnectionRequestTimeout(timeOutLimitMils)
                .setConnectTimeout(timeOutLimitMils).setRedirectsEnabled(true)
                .setSocketTimeout(timeOutLimitMils).build();
        post.setConfig(config);
        return post;
    }

    public static String wechatPost(String requestUrl, String xmlData) {
        try {
            URL orderUrl = new URL(requestUrl);
            HttpURLConnection conn = (HttpURLConnection) orderUrl.openConnection();
            conn.setConnectTimeout(2000); // 设置连接主机超时（单位：毫秒)
            conn.setReadTimeout(10000); // 设置从主机读取数据超时（单位：毫秒)
            conn.setDoOutput(true); // post请求参数要放在http正文内，顾设置成true，默认是false
            conn.setDoInput(true); // 设置是否从httpUrlConnection读入，默认情况下是true
            conn.setUseCaches(false); // Post 请求不能使用缓存
            // 设定传送的内容类型是可序列化的java对象(如果不设此项,在传送序列化对象时,当WEB服务默认的不是这种类型时可能抛java.io.EOFException)
            conn.setRequestProperty("Content-Type","application/x-www-form-urlencoded");
            conn.setRequestMethod("POST");// 设定请求的方法为"POST"，默认是GET
            conn.setRequestProperty("Content-Length",xmlData.length()+"");
            String encode = "utf-8";
            OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), encode);
            out.write(xmlData.toString());
            out.flush();
            out.close();
            String response = getOut(conn);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("send url error!", e.getMessage());
        }
        return null;
    }

    public static String getOut(HttpURLConnection conn) throws IOException{
        if (conn.getResponseCode() != HttpURLConnection.HTTP_OK) {
            return null;
        }
        // 获取响应内容体
        BufferedReader in = new BufferedReader(new InputStreamReader(
                conn.getInputStream(), "UTF-8"));
        String line = "";
        StringBuffer strBuf = new StringBuffer();
        while ((line = in.readLine()) != null) {
            strBuf.append(line).append("\n");
        }
        in.close();
        return  strBuf.toString().trim();
    }

    /**
     * 获取cookie
     *
     * @param cookies    Cookies
     * @param cookieName key
     * @return value
     */
    public static String getCookieValue(Cookie[] cookies, String cookieName) {
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookieName.equals(cookie.getName()))
                    return cookie.getValue();
            }
        }
        return "";
    }

}

package com.cas.nc.questionnaire.server.strategy.answeroptionstrategy;

import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateReqDto;
import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import com.cas.nc.questionnaire.dao.query.QstOptionQuery;
import com.cas.nc.questionnaire.service.QstOptionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.isLiangBiao;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.isXiaLaXuanZe;
import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.constructBaseAnswerOption;

@Component("answerOptionOneDimensionalDanXuanStrategy")
public class AnswerOptionOneDimensionalDanXuanStrategy implements AnswerOptionStrategy {
    @Resource
    private QstOptionService qstOptionService;

    @Override
    public List<AnswerOptionPo> construct(AnswerCreateReqDto reqDto, String answerId, Map.Entry<String, Object> dataEntry, TitleTypeEnum titleTypeEnum) {
        List<AnswerOptionPo> resultList = new ArrayList<>();

        String k = dataEntry.getKey();
        Object v = dataEntry.getValue();

        AnswerOptionPo option = constructBaseAnswerOption(reqDto, answerId, k, titleTypeEnum);
        if (v instanceof Map) {
            option.setSerialNumber((Integer) ((Map) v).get("serialNumber"));
            option.setWriteContent((String) ((Map) v).get("blankContent"));
        } else {
            if (isXiaLaXuanZe(titleTypeEnum.key())) {
                option.setSerialNumber((Integer) v + 1);
            } else {
                option.setSerialNumber((Integer) v);
                if (isLiangBiao(titleTypeEnum.key())) {
                    QstOptionQuery query = new QstOptionQuery();
                    query.setQuestionnaireId(reqDto.getId());
                    query.setTitleSerialNumber(Integer.valueOf(k));
                    query.setSerialNumber((Integer) v);
                    query.setUserId(SequenceUtil.getInstance().parse2UserId4Long(reqDto.getId()));
                    QstOptionPo qstOptionPo = qstOptionService.selectOne(query);
                    if (qstOptionPo != null) {
                        option.setScore(qstOptionPo.getScore());
                    }
                }
            }
        }

        resultList.add(option);
        return resultList;
    }
}

package com.cas.nc.questionnaire.common.dto.analysis;

import java.math.BigDecimal;

public class AnalysisReportOptionRepDto {
    /*选项序号*/
    private Integer serialNumber;
    /*选项内容*/
    private String content;
    /*行标题*/
    private String rowTitle;
    /*行坐标*/
    private Integer rowNumber;
    /*列标题*/
    private String columnTitle;
    /*列坐标*/
    private Integer columnNumber;
    /*垂直坐标*/
    private Integer verticalNumber;
    /*有效答题数量*/
    private Integer total;
    /*平均值*/
    private BigDecimal averageValue;
    /*百分比*/
    private BigDecimal percentage;
    /*排列序号*/
    private Integer orderNumber;

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRowTitle() {
        return rowTitle;
    }

    public void setRowTitle(String rowTitle) {
        this.rowTitle = rowTitle;
    }

    public Integer getRowNumber() {
        return rowNumber;
    }

    public void setRowNumber(Integer rowNumber) {
        this.rowNumber = rowNumber;
    }

    public String getColumnTitle() {
        return columnTitle;
    }

    public void setColumnTitle(String columnTitle) {
        this.columnTitle = columnTitle;
    }

    public Integer getColumnNumber() {
        return columnNumber;
    }

    public void setColumnNumber(Integer columnNumber) {
        this.columnNumber = columnNumber;
    }

    public Integer getVerticalNumber() {
        return verticalNumber;
    }

    public void setVerticalNumber(Integer verticalNumber) {
        this.verticalNumber = verticalNumber;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public BigDecimal getAverageValue() {
        return averageValue;
    }

    public void setAverageValue(BigDecimal averageValue) {
        this.averageValue = averageValue;
    }

    public BigDecimal getPercentage() {
        return percentage;
    }

    public void setPercentage(BigDecimal percentage) {
        this.percentage = percentage;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
    }
}

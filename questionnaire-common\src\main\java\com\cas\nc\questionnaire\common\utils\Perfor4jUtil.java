package com.cas.nc.questionnaire.common.utils;

import org.perf4j.StopWatch;
import org.perf4j.slf4j.Slf4JStopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class Perfor4jUtil {

    private static ThreadLocal<StopWatch> local = new ThreadLocal<StopWatch>();

    private final Logger switchLogger = LoggerFactory.getLogger("org.perf4j.TimingLogger");

    public static StopWatch start() {
        StopWatch stopWatch = new Slf4JStopWatch("shell");
//        local.set(stopWatch);
        return stopWatch;
    }

    public static void after(String key, StopWatch stopWatch) {
        StopWatch watch = stopWatch;
        if (watch != null) {
            watch.stop(generateOperatonIdendifier(key, watch.getElapsedTime()));
//            local.remove();
        }
    }

    public static void stop(String key, StopWatch stopWatch) {
        StopWatch watch = stopWatch;
        if (watch != null) {
            watch.stop(generateOperatonIdendifier1(key, watch.getElapsedTime()));
//            local.remove();
        }
    }

    public static void stop1(String key, StopWatch stopWatch) {
        StopWatch watch = stopWatch;
        if (watch != null) {
            watch.stop(generateOperatonIdendifier2(key, watch.getElapsedTime()));
//            local.remove();
        }
    }

    private static String generateOperatonIdendifier(String key, long exeTime) {
        StringBuilder sb = new StringBuilder(64);

        sb.append(key).append("###");
        // 记录慢得url,
        if (exeTime >= 1000 && exeTime < 2000) {
            sb.append("|SLOW|1-2s");
        } else if (exeTime >= 2000 && exeTime < 3000) {
            sb.append("|SLOW|2-3s");
        } else if (exeTime >= 3000 && exeTime < 4000) {
            sb.append("|SLOW|3-4s");
        }  else if (exeTime >= 4000 && exeTime < 5000) {
            sb.append("|SLOW|4-5s");
        }  else if (exeTime >= 5000 && exeTime < 6000) {
            sb.append("|SLOW|5-6s");
        }  else if (exeTime >= 6000 && exeTime < 7000) {
            sb.append("|SLOW|6-7s");
        }  else if (exeTime >= 7000 && exeTime < 8000) {
            sb.append("|SLOW|7-8s");
        }  else if (exeTime >= 8000 && exeTime < 9000) {
            sb.append("|SLOW|8-9s");
        }  else if (exeTime >= 9000 && exeTime < 10000) {
            sb.append("|SLOW|9-10s");
        }  else if (exeTime >= 10000) {
            sb.append("|SLOW|10-s");
        }

        return sb.toString();
    }

    private static String generateOperatonIdendifier1(String key, long exeTime) {
        StringBuilder sb = new StringBuilder(64);

        sb.append(key).append("###");
        // 记录慢得url,
        if (exeTime >= 100 && exeTime < 200) {
            sb.append("|SLOW|100-200ms");
        } else if (exeTime >= 200 && exeTime < 300) {
            sb.append("|SLOW|200-300ms");
        } else if (exeTime >= 300 && exeTime < 400) {
            sb.append("|SLOW|300-400ms");
        }  else if (exeTime >= 400 && exeTime < 500) {
            sb.append("|SLOW|400-500ms");
        }  else if (exeTime >= 500) {
            sb.append("|SLOW|500-ms");
        }

        return sb.toString();
    }

    private static String generateOperatonIdendifier2(String key, long exeTime) {
        StringBuilder sb = new StringBuilder(64);

        sb.append(key).append("###");
        // 记录慢得url,
        if (exeTime >= 10 && exeTime < 20) {
            sb.append("|SLOW|10-20ms");
        } else if (exeTime >= 20 && exeTime < 30) {
            sb.append("|SLOW|20-30ms");
        } else if (exeTime >= 30 && exeTime < 40) {
            sb.append("|SLOW|30-40ms");
        }  else if (exeTime >= 40 && exeTime < 50) {
            sb.append("|SLOW|40-50ms");
        }  else if (exeTime >= 50) {
            sb.append("|SLOW|50-ms");
        }

        return sb.toString();
    }

}

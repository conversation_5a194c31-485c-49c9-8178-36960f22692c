package com.cas.nc.questionnaire.dao.po;


import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-13
 */
public class LoginauthPo  {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 认证id
     */

    private String authenId;

    /**
     * 修改时间
     */
    private Date modifytime;

    /**
     * 登录认证类型 0科技云 1密码
     */
    private Integer type;

    /**
     * 认证信息备注
     */

    private String authInfo;

    /**
     * 科技云账号
     */
    private String authUserId;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAuthenId() {
        return authenId;
    }

    public void setAuthenId(String authenId) {
        this.authenId = authenId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public String getAuthInfo() {
        return authInfo;
    }

    public void setAuthInfo(String authInfo) {
        this.authInfo = authInfo;
    }

    @Override
    public String toString() {
        return "Loginauth{" +
            "id=" + id +
            ", userId=" + userId +
            ", authenId=" + authenId +
            ", modifytime=" + modifytime +
            ", type=" + type +
            ", authInfo=" + authInfo +
        "}";
    }

    public String getAuthUserId() {
        return authUserId;
    }

    public void setAuthUserId(String authUserId) {
        this.authUserId = authUserId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getModifytime() {
        return modifytime;
    }

    public void setModifytime(Date modifytime) {
        this.modifytime = modifytime;
    }
}

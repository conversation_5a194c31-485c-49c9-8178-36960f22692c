package com.cas.nc.questionnaire.common.enums;


public enum FilterDataSourceEnum {
    QUALITY_CONTROL_FILTER(1, "质量控制-筛选"),
    SET_RETURN(2, "问卷设置-跳转"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    FilterDataSourceEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

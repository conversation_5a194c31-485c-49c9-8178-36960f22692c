<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.sharddao.QstFilterRuleDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.QstFilterRulePo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="questionnaire_id" jdbcType="VARCHAR" property="questionnaireId"/>
        <result column="filter_rule_id" jdbcType="VARCHAR" property="filterRuleId"/>
        <result column="foreign_id" jdbcType="VARCHAR" property="foreignId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="rule_type" jdbcType="INTEGER" property="ruleType"/>
        <result column="judge_type" jdbcType="INTEGER" property="judgeType"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="province" jdbcType="BIGINT" property="province"/>
        <result column="filter_num" jdbcType="INTEGER" property="filterNum"/>
        <result column="filter_type" jdbcType="INTEGER" property="filterType"/>
        <result column="data_source" jdbcType="INTEGER" property="dataSource"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,user_id,questionnaire_id,filter_rule_id,foreign_id,name,rule_type,
    judge_type,content,province,filter_num,filter_type,data_source,
    update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id">and id = #{item.id}</if>
            <if test="null != item.userId">and user_id = #{item.userId}</if>
            <if test="null != item.questionnaireId">and questionnaire_id = #{item.questionnaireId}</if>
            <if test="null != item.filterRuleId">and filter_rule_id = #{item.filterRuleId}</if>
            <if test="null != item.foreignId">and foreign_id = #{item.foreignId}</if>
            <if test="null != item.name">and name = #{item.name}</if>
            <if test="null != item.ruleType">and rule_type = #{item.ruleType}</if>
            <if test="null != item.judgeType">and judge_type = #{item.judgeType}</if>
            <if test="null != item.content">and content = #{item.content}</if>
            <if test="null != item.province">and province = #{item.province}</if>
            <if test="null != item.filterNum">and filter_num = #{item.filterNum}</if>
            <if test="null != item.filterType">and filter_type = #{item.filterType}</if>
            <if test="null != item.dataSource">and data_source = #{item.dataSource}</if>
            <if test="null != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime">and create_time = #{item.createTime}</if>

            <if test="null != item.filterIdList and item.filterIdList.size > 0">
                and filter_rule_id in
                <foreach collection="item.filterIdList" index="index" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_filter_rule
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_filter_rule
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from qst_filter_rule
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into qst_filter_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">id,</if>
            <if test="null != item.userId">user_id,</if>
            <if test="null != item.questionnaireId">questionnaire_id,</if>
            <if test="null != item.filterRuleId">filter_rule_id,</if>
            <if test="null != item.foreignId">foreign_id,</if>
            <if test="null != item.name">name,</if>
            <if test="null != item.ruleType">rule_type,</if>
            <if test="null != item.judgeType">judge_type,</if>
            <if test="null != item.content">content,</if>
            <if test="null != item.province">province,</if>
            <if test="null != item.filterNum">filter_num,</if>
            <if test="null != item.filterType">filter_type,</if>
            <if test="null != item.dataSource">data_source,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.id">#{item.id},</if>
            <if test="null != item.userId">#{item.userId},</if>
            <if test="null != item.questionnaireId">#{item.questionnaireId},</if>
            <if test="null != item.filterRuleId">#{item.filterRuleId},</if>
            <if test="null != item.foreignId">#{item.foreignId},</if>
            <if test="null != item.name">#{item.name},</if>
            <if test="null != item.ruleType">#{item.ruleType},</if>
            <if test="null != item.judgeType">#{item.judgeType},</if>
            <if test="null != item.content">#{item.content},</if>
            <if test="null != item.province">#{item.province},</if>
            <if test="null != item.filterNum">#{item.filterNum},</if>
            <if test="null != item.filterType">#{item.filterType},</if>
            <if test="null != item.dataSource">#{item.dataSource},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.id">id = values(id),</if>
            <if test="null != item.userId">user_id = values(user_id),</if>
            <if test="null != item.questionnaireId">questionnaire_id = values(questionnaire_id),</if>
            <if test="null != item.filterRuleId">filter_rule_id = values(filter_rule_id),</if>
            <if test="null != item.foreignId">foreign_id = values(foreign_id),</if>
            <if test="null != item.name">name = values(name),</if>
            <if test="null != item.ruleType">rule_type = values(rule_type),</if>
            <if test="null != item.judgeType">judge_type = values(judge_type),</if>
            <if test="null != item.content">content = values(content),</if>
            <if test="null != item.province">province = values(province),</if>
            <if test="null != item.filterNum">filter_num = values(filter_num),</if>
            <if test="null != item.filterType">filter_type = values(filter_type),</if>
            <if test="null != item.dataSource">data_source = values(data_source),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update qst_filter_rule
        <set>
            <if test="null != item.id">id = #{item.id},</if>
            <if test="null != item.userId">user_id = #{item.userId},</if>
            <if test="null != item.questionnaireId">questionnaire_id = #{item.questionnaireId},</if>
            <if test="null != item.filterRuleId">filter_rule_id = #{item.filterRuleId},</if>
            <if test="null != item.foreignId">foreign_id = #{item.foreignId},</if>
            <if test="null != item.name">name = #{item.name},</if>
            <if test="null != item.ruleType">rule_type = #{item.ruleType},</if>
            <if test="null != item.judgeType">judge_type = #{item.judgeType},</if>
            <if test="null != item.content">content = #{item.content},</if>
            <if test="null != item.province">province = #{item.province},</if>
            <if test="null != item.filterNum">filter_num = #{item.filterNum},</if>
            <if test="null != item.filterType">filter_type = #{item.filterType},</if>
            <if test="null != item.dataSource">data_source = #{item.dataSource},</if>
        </set>
        where filter_rule_id = #{item.filterRuleId}
        and user_id = #{item.userId}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from qst_filter_rule
        <include refid="sql_where"/>
    </delete>
</mapper>
package com.cas.nc.questionnaire.common.vo.answer;

import java.util.Date;
import java.util.Map;

public class AnswerCreateReqVo {
    /*开始答题时间*/
    private Date beginTime;
    /*硬件编码*/
    private String deviceCode;
    /*答案集合*/
    private Map<String, Object> data;
    /*问卷id*/
    private String id;
    /*没有回答问题数*/
    private Integer noAnswerCount;
    /*答题时长，单位秒*/
    private Long passTime;
    /*问题数*/
    private Integer questionCount;

    private String emailId;

    private String smsId;

    /*pwd*/
    private String pwd;

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getNoAnswerCount() {
        return noAnswerCount;
    }

    public void setNoAnswerCount(Integer noAnswerCount) {
        this.noAnswerCount = noAnswerCount;
    }

    public Long getPassTime() {
        return passTime;
    }

    public void setPassTime(Long passTime) {
        this.passTime = passTime;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public String getSmsId() {
        return smsId;
    }

    public void setSmsId(String smsId) {
        this.smsId = smsId;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }
}

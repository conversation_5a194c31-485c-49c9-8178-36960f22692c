package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.dao.nosharddao.EmailServerConfigDao;
import com.cas.nc.questionnaire.dao.po.EmailServerConfigPo;
import com.cas.nc.questionnaire.service.EmailServerConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class EmailServerConfigServiceImpl implements EmailServerConfigService {

    @Resource
    private EmailServerConfigDao emailServerConfigDao;

    @Override
    public List<EmailServerConfigPo> selectAll() {
        return emailServerConfigDao.selectAll();
    }
}

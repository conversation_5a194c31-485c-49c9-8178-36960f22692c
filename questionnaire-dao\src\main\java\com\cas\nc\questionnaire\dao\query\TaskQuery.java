package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.TaskPo;

import java.util.List;


public class TaskQuery extends TaskPo {
    private String tableColumns;
    private Integer oldTaskStatus;
    private Integer oldFailNum;
    private Integer failMaxNum;
    private Integer batchNum;
    private List<Integer> taskStatusList;
    private List<Integer> taskTypeList;

    public Integer getOldTaskStatus() {
        return oldTaskStatus;
    }

    public void setOldTaskStatus(Integer oldTaskStatus) {
        this.oldTaskStatus = oldTaskStatus;
    }

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public Integer getOldFailNum() {
        return oldFailNum;
    }

    public void setOldFailNum(Integer oldFailNum) {
        this.oldFailNum = oldFailNum;
    }

    public Integer getFailMaxNum() {
        return failMaxNum;
    }

    public void setFailMaxNum(Integer failMaxNum) {
        this.failMaxNum = failMaxNum;
    }

    public Integer getBatchNum() {
        return batchNum;
    }

    public void setBatchNum(Integer batchNum) {
        this.batchNum = batchNum;
    }

    public List<Integer> getTaskStatusList() {
        return taskStatusList;
    }

    public void setTaskStatusList(List<Integer> taskStatusList) {
        this.taskStatusList = taskStatusList;
    }

    public List<Integer> getTaskTypeList() {
        return taskTypeList;
    }

    public void setTaskTypeList(List<Integer> taskTypeList) {
        this.taskTypeList = taskTypeList;
    }
}

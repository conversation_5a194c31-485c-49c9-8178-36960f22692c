<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="false"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="aggressiveLazyLoading" value="true"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="useGeneratedKeys" value="false"/>
        <setting name="autoMappingBehavior" value="PARTIAL"/>
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <setting name="defaultStatementTimeout" value="300"/>
        <setting name="mapUnderscoreToCamelCase" value="true"/>
    </settings>

    <typeAliases>
        <typeAlias type="com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo" alias="QstQuestionnaireInfoPo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.bo.AnswerAnalysisBo" alias="AnswerAnalysisBo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.bo.AnswerAnalysisOptionBo" alias="AnswerAnalysisOptionBo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.bo.SourceAnalysisBo" alias="SourceAnalysisBo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.bo.ProvinceAnalysisBo" alias="ProvinceAnalysisBo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.bo.TimeAnalysisBo" alias="TimeAnalysisBo"/>


        <typeAlias type="com.cas.nc.questionnaire.dao.query.QstQuestionnaireInfoQuery"
                   alias="QstQuestionnaireInfoQuery"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.query.QstLimitRuleQuery" alias="QstLimitRuleQuery"/>
    </typeAliases>
    <typeHandlers>
        <typeHandler javaType="com.cas.nc.questionnaire.dao.po.LimitAttributeJson"
                     handler="com.cas.nc.questionnaire.dao.typehandler.LimitAttributeJsonHandler"/>
    </typeHandlers>

    <mappers>
        <mapper resource="mapper/AnswerInfoDao.xml"/>
        <mapper resource="mapper/AnswerOptionDao.xml"/>
        <mapper resource="mapper/QstEmailDao.xml"/>
        <mapper resource="mapper/QstFilterRuleDao.xml"/>
        <mapper resource="mapper/QstLimitConditionDao.xml"/>
        <mapper resource="mapper/QstLimitRuleDao.xml"/>
        <mapper resource="mapper/QstOptionDao.xml"/>
        <mapper resource="mapper/QstQuestionnaireInfoDao.xml"/>
        <mapper resource="mapper/QstQuotaOptionRuleDao.xml"/>
        <mapper resource="mapper/QstQuotaRuleDao.xml"/>
        <mapper resource="mapper/QstSmsDao.xml"/>
        <mapper resource="mapper/QstTitleDao.xml"/>
        <mapper resource="mapper/QstBrowseRecordsDao.xml"/>


    </mappers>

</configuration>

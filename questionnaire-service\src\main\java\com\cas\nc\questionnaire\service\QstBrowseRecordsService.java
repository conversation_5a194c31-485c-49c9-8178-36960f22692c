package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.bo.ProvinceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.SourceAnalysisBo;
import com.cas.nc.questionnaire.dao.bo.TimeAnalysisBo;
import com.cas.nc.questionnaire.dao.po.QstBrowseRecordsPo;

import java.util.List;

public interface QstBrowseRecordsService {
    void insert(QstBrowseRecordsPo po);

    List<SourceAnalysisBo> selectSourceStatistics(String questionnaireId);

    List<ProvinceAnalysisBo> selectProvinceStatistics(String questionnaireId);

    List<TimeAnalysisBo> selectDayStatistics(String questionnaireId, String beginTime, String endTime);

    List<TimeAnalysisBo> selectWeekDayStatistics(String questionnaireId, String beginTime, String endTime);

    List<TimeAnalysisBo> selectMonthStatistics(String questionnaireId, String beginTime, String endTime);
}

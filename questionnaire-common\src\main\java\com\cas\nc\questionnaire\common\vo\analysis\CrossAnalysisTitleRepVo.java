package com.cas.nc.questionnaire.common.vo.analysis;

import java.util.List;

public class CrossAnalysisTitleRepVo {
    /*题目序号*/
    private Integer serialNumber;
    /*题目名称*/
    private String name;
    /*横标题*/
    private String rowTitleName;
    /*题目类型*/
    private Integer type;

    private List<CrossAnalysisOptionRepVo> optionList;

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRowTitleName() {
        return rowTitleName;
    }

    public void setRowTitleName(String rowTitleName) {
        this.rowTitleName = rowTitleName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<CrossAnalysisOptionRepVo> getOptionList() {
        return optionList;
    }

    public void setOptionList(List<CrossAnalysisOptionRepVo> optionList) {
        this.optionList = optionList;
    }
}

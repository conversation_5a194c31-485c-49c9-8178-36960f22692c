package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class TempletCategoryPo {
    private Long id;

    /*模板类目id*/
    private String templetCategoryId;

    /*标题*/
    private String title;

    /*优先级*/
    private Integer priority;

    /*是否有效*/
    private Integer yn;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTempletCategoryId() {
        return templetCategoryId;
    }

    public void setTempletCategoryId(String templetCategoryId) {
        this.templetCategoryId = templetCategoryId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
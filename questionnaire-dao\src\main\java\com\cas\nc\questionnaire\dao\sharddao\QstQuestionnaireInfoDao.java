package com.cas.nc.questionnaire.dao.sharddao;

import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireListRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireRepDto;
import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.query.QstQuestionnaireInfoQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QstQuestionnaireInfoDao extends BaseDao<QstQuestionnaireInfoPo, QstQuestionnaireInfoQuery> {

    /**
     * 查询符合条件的条数
     *
     * @param query
     * @return
     */
    int selectCount(@Param("item") QstQuestionnaireInfoQuery query);

    /**
     * 我的列表页分页查询
     *
     * @param query
     * @return
     */
    List<QstQuestionnaireInfoPo> selectMyListByPage(@Param("item") QstQuestionnaireInfoQuery query);

    /**
     * 试卷列表
     *
     * @param query
     * @return
     */
    List<QuestionnaireRepDto> selectQuestionnaireListByPage(@Param("item") QstQuestionnaireInfoQuery query);

    /**
     * 试卷列表大小
     *
     * @param query
     * @return
     */
    int selectQuestionnaireListCount(@Param("item") QstQuestionnaireInfoQuery query);

    /**
     * 问卷填写数量
     *
     * @param query
     * @return
     */
    int selectAnswerCount(@Param("item") QstQuestionnaireInfoQuery query);
}
package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.dao.nosharddao.QstReportQueryConditionTitleDao;
import com.cas.nc.questionnaire.dao.po.QstReportQueryConditionTitlePo;
import com.cas.nc.questionnaire.service.QstReportQueryConditionTitleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 测评报告查询条件题目Service实现类
 */
@Service
public class QstReportQueryConditionTitleServiceImpl implements QstReportQueryConditionTitleService {
    
    @Resource
    private QstReportQueryConditionTitleDao qstReportQueryConditionTitleDao;
    
    @Override
    public Long insert(QstReportQueryConditionTitlePo po) {
        qstReportQueryConditionTitleDao.insert(po);
        return po.getId();
    }
    
    @Override
    public int insertBatch(List<QstReportQueryConditionTitlePo> poList) {
        if (poList == null || poList.isEmpty()) {
            return 0;
        }
        return qstReportQueryConditionTitleDao.insertList(poList);
    }
    
    @Override
    public List<QstReportQueryConditionTitlePo> selectByConditionId(Long conditionId) {
        return qstReportQueryConditionTitleDao.selectByConditionId(conditionId);
    }
    
    @Override
    public int deleteByConditionId(Long conditionId) {
        return qstReportQueryConditionTitleDao.deleteByConditionId(conditionId);
    }
} 
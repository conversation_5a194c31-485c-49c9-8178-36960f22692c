package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.enums.AreaConvertEnum;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.IpRpcConfigPo;
import com.cas.nc.questionnaire.rpc.wechat.entity.ipentity.IpAreaResult;
import com.cas.nc.questionnaire.rpc.wechat.iphandler.IpHandler;
import com.cas.nc.questionnaire.rpc.wechat.iphandler.PconlineIpHandler;
import com.cas.nc.questionnaire.rpc.wechat.iphandler.TaoBaoIpHandler;
import com.cas.nc.questionnaire.rpc.wechat.iphandler.WsIpHandler;
import com.cas.nc.questionnaire.server.IpServer;
import com.cas.nc.questionnaire.service.IpRpcConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.cas.nc.questionnaire.common.utils.Constants.PROVINCE_CN_NAME;
import static com.cas.nc.questionnaire.common.utils.Constants.UNKNOWN_REGION;


@Component
public class IpServerImpl implements IpServer {
    private static Logger logger = LoggerFactory.getLogger(IpServerImpl.class);

    @Resource
    private IpRpcConfigService ipRpcConfigService;

    @Override
    public IpAreaResult getIpInfo(String ip) {
        try {
            IpHandler handler = getHandler();
            if (handler == null) {
                return defaultResult();
            }

            IpAreaResult result = getHandler().execute(ip);
            if (StringUtil.isBlank(result.getProvince())) {
                return defaultResult();
            }

            AreaConvertEnum areaConvertEnum = AreaConvertEnum.toEnum(result.getProvince());
            if (areaConvertEnum != null) {
                result.setProvince(areaConvertEnum.value());
            } else {
                if (!result.getProvince().contains(PROVINCE_CN_NAME)) {
                    result.setProvince(result.getProvince() + PROVINCE_CN_NAME);
                }
            }

            if (StringUtil.isBlank(result.getCity())) {
                result.setCity(result.getProvince());
            }

            return result;
        } catch (Exception e) {
            logger.error("IpRpcImpl.getIpInfo param[{}] exception", ip, e);
        }

        return defaultResult();
    }

    private IpHandler getHandler() {
        List<IpRpcConfigPo> configPoList = ipRpcConfigService.selectEffective();

        IpHandler handler = null;
        IpHandler tempHandler = null;
        for (IpRpcConfigPo bean : configPoList) {
            IpHandler currentHandler = getInstance(bean.getResourceId());
            if (currentHandler == null) {
                continue;
            }

            if (handler == null) {
                handler = currentHandler;
                continue;
            }

            tempHandler = handler;
            while (tempHandler.getNextHandler() != null) {
                tempHandler = tempHandler.getNextHandler();
            }
            tempHandler.setNextHandler(currentHandler);
        }
        return handler;
    }

    private IpHandler getInstance(String id) {
        if ("pconlineIpHandler".equalsIgnoreCase(id)) {
            return new PconlineIpHandler();
        }
        if ("wsIpHandler".equalsIgnoreCase(id)) {
            return new WsIpHandler();
        }
        if ("taoBaoIpHandler".equalsIgnoreCase(id)) {
            return new TaoBaoIpHandler();
        }
        return null;
    }

    private IpAreaResult defaultResult() {
        IpAreaResult result = new IpAreaResult();
        result.setCity(UNKNOWN_REGION);
        result.setProvince(UNKNOWN_REGION);

        return result;
    }
}

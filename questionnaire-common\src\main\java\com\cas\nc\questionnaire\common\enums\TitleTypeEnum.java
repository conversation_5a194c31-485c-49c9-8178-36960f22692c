package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.TITLE_TYPE_ERROR;
import static com.cas.nc.questionnaire.common.utils.Constants.LEFT_CHINESE_BRACKET;
import static com.cas.nc.questionnaire.common.utils.Constants.RIGHT_CHINESE_BRACKET;

public enum TitleTypeEnum {
    //选择题
    DANXUAN(1101, "单选"),
    DUOXUAN(1102, "多选"),
    TUPIANDANXUAN(1103, "图片单选"),
    TUPIANDUOXUAN(1104, "图片多选"),
    BIAOGEDANXUAN(1105, "表格单选"),
    BIAOGEDUOXUAN(1106, "表格多选"),
    XIALAXUANZE(1107, "下拉选择"),
    DUOJIXIALAXUANZE(1108, "多级下拉选择"),
    BIAOGEXIALAXUANZE(1109, "表格下拉选择"),
    PAIXU(1110, "排序"),

    //填空题
    DANHANGTIANKONG(1201, "单项填空"),
    DUOXIANGDANHANGTIANKONG(1202, "多项单行填空"),
    DUOXIANGTIANKONG(1203, "多项填空"),
    BIAOGEWENBEN(1204, "表格文本"),
    BIAOGESHUZHI(1205, "表格数值"),

    //打分题
    LIANGBIAO(1301, "量表"),
    NPSLIANGBIAO(1302, "NPS量表"),
    JUZHENLIANGBIAO(1303, "矩阵量表"),
    JUZHENHUADONGTIAO(1304, "矩阵滑动条"),
    PINGFENDANXUAN(1305, "评分单选"),
    PINGFENDUOXUAN(1306, "评分多选"),
    BIZHONG(1307, "比重"),
    HUADONGTIAO(1308, "滑动条"),

    //备注分页
    MIAOSHUSHUOMING(1401, "描述说明"),
    FENYEFU(1402, "分页符"),

    //常规题型
    XINGMING(1501, "姓名"),
    XINGBIE(1502, "性别"),
    SHOUJI(1503, "手机"),
    YOUXIANG(1504, "邮箱"),
    RIQISHIJIAN(1505, "日期时间"),
    NIANLIANG(1506, "年龄"),
    ZHIYE(1507, "职业"),
    GONGZUONIANXIN(1508, "工作年限"),
    JIAOYUCHENGDU(1509, "教育程度"),
    BIYEYUANXIAO(1510, "毕业院校"),
    HUNYINZHUANGKUANG(1511, "婚姻状况"),
    CHENGSHIDIZHI(1512, "城市/地址"),
    DILIWEIZHI(1513, "地理位置"),
    TONGXUNDIZHI(1514, "通讯地址"),

    //其他题型
    WENJIAN(1601, "文件"),
    QINGJINGSUIJI(1602, "情景随机"),
    ELSE(99, "其他"),
    ;

    private final static Logger logger = LoggerFactory.getLogger(TitleTypeEnum.class);

    private final Integer key;
    private final String value;

    TitleTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static TitleTypeEnum toEnum(int key) {
        for (TitleTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        logger.error("TitleTypeEnum.toEnum exception param[{}]", key);
        throw new ServerException(TITLE_TYPE_ERROR);
    }

    public static TitleTypeEnum toEnum(String name) {
        for (TitleTypeEnum bean : values()) {
            if (bean.name().equalsIgnoreCase(name)) {
                return bean;
            }
        }
        logger.error("TitleTypeEnum.toEnum exception param[{}]", name);
        throw new ServerException(TITLE_TYPE_ERROR);
    }

    public static List<TitleTypeEnum> DANXUAN_CATEGORY_LIST = Arrays.asList(
            BIAOGEDANXUAN,
            BIAOGEDUOXUAN,
            BIAOGEXIALAXUANZE,
            DANXUAN,
            DUOXIANGDANHANGTIANKONG,
            LIANGBIAO,
            DUOXUAN
    );

    public static List<TitleTypeEnum> ANALYSIS_ONE_DIMENSIONAL_LIST = Arrays.asList(
            DANXUAN,
            XIALAXUANZE,
            XINGBIE,
            NIANLIANG,
            ZHIYE,
            GONGZUONIANXIN,
            JIAOYUCHENGDU,
            HUNYINZHUANGKUANG,
            DUOXUAN,
            PINGFENDUOXUAN,
            PINGFENDANXUAN,
            LIANGBIAO,
            PAIXU
    );

    public static List<TitleTypeEnum> ONE_DIMENSIONAL_DANXUAN_LIST = Arrays.asList(
            DANXUAN,
            XIALAXUANZE,
            XINGBIE,
            NIANLIANG,
            ZHIYE,
            GONGZUONIANXIN,
            JIAOYUCHENGDU,
            HUNYINZHUANGKUANG,
            PINGFENDANXUAN,
            LIANGBIAO
    );

    public static List<TitleTypeEnum> ONE_DIMENSIONAL_DUOXUAN_LIST = Arrays.asList(
            DUOXUAN,
            PINGFENDUOXUAN
    );

    public static List<TitleTypeEnum> ANALYSIS_TWO_DIMENSIONAL_LIST = Arrays.asList(
            BIAOGEDANXUAN,
            BIAOGEDUOXUAN,
            JUZHENLIANGBIAO,
            JUZHENHUADONGTIAO,
            BIZHONG
    );

    public static List<TitleTypeEnum> ANALYSIS_THREE_DIMENSIONAL_LIST = Arrays.asList(
            BIAOGEXIALAXUANZE
    );

    public static List<TitleTypeEnum> ONE_DIMENSIONAL_OPTION_LIST = Arrays.asList(
            DANXUAN,
            DUOXUAN,
            XIALAXUANZE,
            LIANGBIAO,
            XINGBIE,
            NIANLIANG,
            ZHIYE,
            GONGZUONIANXIN,
            JIAOYUCHENGDU,
            HUNYINZHUANGKUANG
    );

    public static List<TitleTypeEnum> TWO_DIMENSIONAL_OPTION_LIST = Arrays.asList(
            BIAOGEDANXUAN,
            BIAOGEDUOXUAN,
            BIAOGEXIALAXUANZE
    );

    public static List<TitleTypeEnum> ONE_DIMENSIONAL_FILL_BLANK_LIST = Arrays.asList(
            DANHANGTIANKONG,
            HUADONGTIAO,
            XINGMING,
            SHOUJI,
            YOUXIANG,
            RIQISHIJIAN,
            CHENGSHIDIZHI,
            DILIWEIZHI,
            TONGXUNDIZHI,
            DUOXIANGDANHANGTIANKONG
    );

    public static List<TitleTypeEnum> TWO_DIMENSIONAL_FILL_BLANK_LIST = Arrays.asList(
            BIAOGEWENBEN,
            BIAOGESHUZHI
    );

    public static List<TitleTypeEnum> FILL_BLANK_LIST = Arrays.asList(
            DANHANGTIANKONG,
            DUOXIANGDANHANGTIANKONG,
//            DUOXIANGTIANKONG,
            BIAOGEWENBEN,
            BIAOGESHUZHI,
            XINGMING,
            SHOUJI,
            YOUXIANG,
            RIQISHIJIAN,
            BIYEYUANXIAO,
            CHENGSHIDIZHI,
            DILIWEIZHI,
            TONGXUNDIZHI
    );

    public static List<TitleTypeEnum> XIA_LA_XUAN_ZE_LIST = Arrays.asList(
            XIALAXUANZE,
            ZHIYE,
            BIYEYUANXIAO,
            CHENGSHIDIZHI
    );

    public static List<Integer> NOT_SHOW_REPORT_LIST = Arrays.asList(
            MIAOSHUSHUOMING.key,
            FENYEFU.key
    );

    public static List<TitleTypeEnum> CUSTOM_CONTAIN_LIST = Arrays.asList(
            DANXUAN,
            DUOXUAN,
            BIAOGEDANXUAN,
            BIAOGEDUOXUAN,
            XIALAXUANZE,
            BIAOGEXIALAXUANZE,
            PINGFENDANXUAN,
            PINGFENDUOXUAN,
            TUPIANDANXUAN,
            TUPIANDUOXUAN,
            LIANGBIAO
    );

    public static List<TitleTypeEnum> CUSTOM_FILL_BLANK_LIST = Arrays.asList(
            DANHANGTIANKONG,
            DUOXIANGDANHANGTIANKONG,
            BIAOGEWENBEN
    );

    public static List<TitleTypeEnum> CUSTOM_NUM_LIST = Arrays.asList(
            BIAOGESHUZHI,
            JUZHENHUADONGTIAO,
            BIZHONG,
            HUADONGTIAO
    );

    public static List<TitleTypeEnum> NO_OPTION_LIST = Arrays.asList(
            FENYEFU,
            MIAOSHUSHUOMING,
            WENJIAN,
            QINGJINGSUIJI
    );


    public static Map<String, TitleTypeEnum> MANUAL_TITLE_MAP = new HashMap<>();

    public static boolean isOneDimensionalOption(int key) {
        return ONE_DIMENSIONAL_OPTION_LIST.contains(toEnum(key));
    }

    public static boolean isOneDimensionalFillBlank(int key) {
        return ONE_DIMENSIONAL_FILL_BLANK_LIST.contains(toEnum(key));
    }

    public static boolean isTwoDimensionalOption(int key) {
        return TWO_DIMENSIONAL_OPTION_LIST.contains(toEnum(key));
    }

    public static boolean isTwoDimensionalFillBlank(int key) {
        return TWO_DIMENSIONAL_FILL_BLANK_LIST.contains(toEnum(key));
    }

    public static boolean isAnalysisOneDimensional(int key) {
        return ANALYSIS_ONE_DIMENSIONAL_LIST.contains(toEnum(key));
    }

    public static boolean isAnalysisTwoDimensional(int key) {
        return ANALYSIS_TWO_DIMENSIONAL_LIST.contains(toEnum(key));
    }

    public static boolean isAnalysisThreeDimensional(int key) {
        return ANALYSIS_THREE_DIMENSIONAL_LIST.contains(toEnum(key));
    }

    public static boolean isOneDimensionalDuoXuanList(int key) {
        return ONE_DIMENSIONAL_DUOXUAN_LIST.contains(toEnum(key));
    }

    public static boolean isOneDimensionalDanXuanList(int key) {
        return ONE_DIMENSIONAL_DANXUAN_LIST.contains(toEnum(key));
    }

    public static boolean isFillBlank(int key) {
        return FILL_BLANK_LIST.contains(toEnum(key));
    }

    public static boolean isXiaLaXuanZe(int key) {
        return XIA_LA_XUAN_ZE_LIST.contains(toEnum(key));
    }

    public static boolean isLiangBiao(int key) {
        return LIANGBIAO.key.equals(key);
    }

    public static boolean isBiZhong(int key) {
        return BIZHONG.key.equals(key);
    }

    public static boolean isXiaLaXuanZe(TitleTypeEnum typeEnum) {
        return XIA_LA_XUAN_ZE_LIST.contains(typeEnum);
    }

    public static boolean isPingFen(int key) {
        return PINGFENDUOXUAN.key.equals(key) || PINGFENDANXUAN.key.equals(key);
    }

    public static boolean isPaiXu(int key) {
        return PAIXU.key.equals(key);
    }

    public static boolean isHuaDongTiao(int key) {
        return HUADONGTIAO.key.equals(key);
    }
    public static boolean isJuZhenHuaDongTiao(int key) {
        return JUZHENHUADONGTIAO.key.equals(key);
    }

    public static boolean isDuoXiangDanHangTianKong(int key) {
        return DUOXIANGDANHANGTIANKONG.key.equals(key);
    }
    public static boolean isBiaoGeDuoXuan(int key) {
        return BIAOGEDUOXUAN.key.equals(key);
    }
    public static boolean isBiaoGeDanXuan(int key) {
        return BIAOGEDANXUAN.key.equals(key);
    }

    public static boolean isDanXuan(int key) {
        return DANXUAN.key.equals(key);
    }

    public static boolean isDuoXuan(int key) {
        return DUOXUAN.key.equals(key);
    }

    public static boolean isDanHangTianKong(int key) {
        return DANHANGTIANKONG.key.equals(key);
    }

    public static boolean isBiaoGeXiaLaXuanZe(int key) {
        return BIAOGEXIALAXUANZE.key.equals(key);
    }

    public static boolean isMiaoShuShuoMing(int key) {
        return MIAOSHUSHUOMING.key.equals(key);
    }

    public static TitleTypeEnum conventionalTitleTypeConvert(TitleTypeEnum timeTypeEnum) {
        if (XINGMING.equals(timeTypeEnum)) {
            return DANHANGTIANKONG;
        } else if (XINGBIE.equals(timeTypeEnum)) {
            return DANXUAN;
        } else if (SHOUJI.equals(timeTypeEnum)) {
            return DANHANGTIANKONG;
        } else if (YOUXIANG.equals(timeTypeEnum)) {
            return DANHANGTIANKONG;
        } else if (RIQISHIJIAN.equals(timeTypeEnum)) {
            return DANHANGTIANKONG;
        } else if (NIANLIANG.equals(timeTypeEnum)) {
            return DANXUAN;
        } else if (ZHIYE.equals(timeTypeEnum)) {
            return XIALAXUANZE;
        } else if (GONGZUONIANXIN.equals(timeTypeEnum)) {
            return XIALAXUANZE;
        } else if (JIAOYUCHENGDU.equals(timeTypeEnum)) {
            return XIALAXUANZE;
        } else if (BIYEYUANXIAO.equals(timeTypeEnum)) {
            return DANHANGTIANKONG;
        } else if (HUNYINZHUANGKUANG.equals(timeTypeEnum)) {
            return DANXUAN;
        } else if (CHENGSHIDIZHI.equals(timeTypeEnum)) {
            return DANHANGTIANKONG;
        } else if (DILIWEIZHI.equals(timeTypeEnum)) {
            return DANHANGTIANKONG;
        } else if (TONGXUNDIZHI.equals(timeTypeEnum)) {
            return DANHANGTIANKONG;
        } else {
            return timeTypeEnum;
        }
    }

    public static Map<String, TitleTypeEnum> listManualTitle() {
        if (MANUAL_TITLE_MAP.size() == 0) {
            MANUAL_TITLE_MAP.put(constructChineseBracket(DANXUAN), DANXUAN);
            MANUAL_TITLE_MAP.put(constructChineseBracket(DUOXUAN), DUOXUAN);
            MANUAL_TITLE_MAP.put(constructChineseBracket(BIAOGEDANXUAN), BIAOGEDANXUAN);
            MANUAL_TITLE_MAP.put(constructChineseBracket(BIAOGEDUOXUAN), BIAOGEDUOXUAN);
            MANUAL_TITLE_MAP.put(constructChineseBracket(LIANGBIAO), LIANGBIAO);
            MANUAL_TITLE_MAP.put(constructChineseBracket(DANHANGTIANKONG), DANHANGTIANKONG);
        }

        return MANUAL_TITLE_MAP;
    }

    public static String constructChineseBracket(TitleTypeEnum typeEnum) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(LEFT_CHINESE_BRACKET);
        buffer.append(typeEnum.value);
        buffer.append(RIGHT_CHINESE_BRACKET);

        return buffer.toString();
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

package com.cas.nc.questionnaire.common.vo.questionnaire;

import java.util.Date;

public class QuestionnaireRepVo {
    /*问卷id*/
    private String questionnaireId;
    /*标题*/
    private String title;
    /*状态*/
    private Integer status;
    /*答卷数量*/
    private Integer answerCount;
    /*问卷数量*/
    private String answerCountStr;
    /*创建人*/
    private String name;
    /*账号*/
    private String userName;
    /*时间*/
    private String time;
    /*开始时间*/
    private Date beginTime;
    /*结束时间*/
    private Date endTime;
    /*账号id*/
    private Long userId;

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getAnswerCount() {
        return answerCount;
    }

    public void setAnswerCount(Integer answerCount) {
        this.answerCount = answerCount;
    }

    public String getAnswerCountStr() {
        return answerCountStr;
    }

    public void setAnswerCountStr(String answerCountStr) {
        this.answerCountStr = answerCountStr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}

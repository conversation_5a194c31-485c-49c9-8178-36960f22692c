package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.QstQuotaRulePo;
import com.cas.nc.questionnaire.dao.query.QstQuotaRuleQuery;
import com.cas.nc.questionnaire.dao.sharddao.QstQuotaRuleDao;
import com.cas.nc.questionnaire.service.QstQuotaRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class QstQuotaRuleServiceImpl implements QstQuotaRuleService {
    private static Logger logger = LoggerFactory.getLogger(QstQuotaRuleServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private QstQuotaRuleDao qstQuotaRuleDao;

    @Override
    public int insert(QstQuotaRulePo qstQuotaRulePo) {
        validateUserId(qstQuotaRulePo.getUserId());

        if (StringUtil.isBlank(qstQuotaRulePo.getQuotaRuleId())) {
            String id = SequenceUtil.getInstance().generateQuotaRuleId(qstQuotaRulePo.getUserId().toString());
            qstQuotaRulePo.setQuotaRuleId(id);
        }

        return qstQuotaRuleDao.insert(qstQuotaRulePo);
    }

    @Override
    public int delete(QstQuotaRuleQuery query) {
        validateUserId(query);
        return qstQuotaRuleDao.delete(query);
    }

    @Override
    public List<QstQuotaRulePo> selectList(QstQuotaRuleQuery query) {
        filterCondition(query);
        return qstQuotaRuleDao.selectList(query);
    }

    @Override
    public QstQuotaRulePo selectOne(QstQuotaRuleQuery query) {
        filterCondition(query);
        return qstQuotaRuleDao.selectOne(query);
    }

    @Override
    public int update(QstQuotaRuleQuery query) {
        filterCondition(query);
        return qstQuotaRuleDao.update(query);
    }

    @Override
    public List<QstQuotaRulePo> selectList(String questionnaireId) {
        QstQuotaRuleQuery query = new QstQuotaRuleQuery();
        query.setQuestionnaireId(questionnaireId);
        filterCondition(query);

        return selectList(query);
    }

    private void validateUserId(QstQuotaRuleQuery query) {
        Assert.notNull(query.getUserId(), "userId");
    }

    private void validateUserId(Long userId) {
        Assert.notNull(userId, "userId");
    }

    private void filterCondition(QstQuotaRuleQuery query) {
        Assert.notNull(query.getQuestionnaireId(), "questionnaireId");
        if (query.getUserId() == null) {
            String userId = SequenceUtil.getInstance().parse2UserId(query.getQuestionnaireId());
            query.setUserId(Long.valueOf(userId));
        }
    }
}

package com.cas.nc.questionnaire.common.enums;

import java.util.ArrayList;
import java.util.List;

import static com.cas.nc.questionnaire.common.utils.Constants.ZERO;


public enum LimitReturnTypeEnum {
    DISPLAY_INFORMATION(1, "显示信息"),
    RETUR<PERSON>(2, "跳转"),
    SEND_EMAIL(3, "发邮件"),
    SEND_SMS(4, "发短信"),
    ELSE(99, "其他"),
    ;

    public static List<Integer> RETURN_TYPE_LIST = new ArrayList<>();

    private final Integer key;
    private final String value;

    LimitReturnTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static List<Integer> returnTypeList() {
        if (RETURN_TYPE_LIST.size() == ZERO) {
            RETURN_TYPE_LIST.add(DISPLAY_INFORMATION.key);
            RETURN_TYPE_LIST.add(RETURN.key);
            RETURN_TYPE_LIST.add(SEND_EMAIL.key);
            RETURN_TYPE_LIST.add(SEND_SMS.key);
        }
        return RETURN_TYPE_LIST;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

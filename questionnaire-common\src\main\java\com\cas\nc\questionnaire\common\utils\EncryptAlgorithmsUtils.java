package com.cas.nc.questionnaire.common.utils;

import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

public class EncryptAlgorithmsUtils {
    public static Logger logger = LoggerFactory.getLogger(EncryptAlgorithmsUtils.class);
    private static final String RSA_ALGORITHM = "RSA";
    private final static String AES_ALGORITHM = "AES";
    private final static String SHA = "SHA-512";
    private final static int DEFAULT_RASKEY_LENGTH = 2048;

    /**
     * AES加密方法
     *
     * @param encryptKey 加密的Key
     * @param data       被加密数据
     * @return
     */
    public static String encryptByAES(String encryptKey, String data) {
        try {
//			KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
//			keyGenerator.init(128, new SecureRandom(encryptKey.getBytes()));
//			SecretKey secretKey = keyGenerator.generateKey();
            SecretKey secretKey = getKey(encryptKey);
            byte[] encodeFormat = secretKey.getEncoded();
            SecretKeySpec secretKeySpec = new SecretKeySpec(encodeFormat, AES_ALGORITHM);
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM); // 创建密码器
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec); // 初始化
            byte[] result = cipher.doFinal(data.getBytes());
            return Base64Utils.encodeBase64String(result);
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("EncryptAlgorithmsUtils.encryptByAES Exception data[{}]", data, e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
    }

    /**
     * AES解密方法
     *
     * @param decryptKey
     * @param data
     * @return
     */
    public static String decryptByAES(String decryptKey, String data) {
        try {
//			KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
//			SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
//			secureRandom.setSeed(decryptKey.getBytes());
//			keyGenerator.init(128, secureRandom);
//			SecretKey secretKey = keyGenerator.generateKey();
            SecretKey secretKey = getKey(decryptKey);
            byte[] encodeFormat = secretKey.getEncoded();
            SecretKeySpec secretKeySpec = new SecretKeySpec(encodeFormat, AES_ALGORITHM);
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);// 创建密码器
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);// 初始化
            byte[] encodeByte = Base64Utils.decodeBase64(data);
            byte[] decryptedByte = cipher.doFinal(encodeByte);
            return new String(decryptedByte);
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("EncryptAlgorithmsUtils.decryptByAES Exception data[{}]", data, e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
    }

    /**
     * 将16进制字符串转为转换成字符串
     */
    public static byte[] hex2Bytes(String source) {
        byte[] sourceBytes = new byte[source.length() / 2];
        for (int i = 0; i < sourceBytes.length; i++) {
            sourceBytes[i] = (byte) Integer.parseInt(source.substring(i * 2, i * 2 + 2), 16);
        }
        return sourceBytes;
    }

    private static SecretKey getKey(String key) throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(key.getBytes());
        keyGenerator.init(128, secureRandom);
        return keyGenerator.generateKey();
    }

    /**
     * 获取公钥
     *
     * @param pubKey 公钥字符串
     * @return
     */
    private static Key getPublicKey(String pubKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        // 对密钥解密
        byte[] keyBytes = str2Byte(pubKey);
        // 取得公钥
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        Key publicKey = keyFactory.generatePublic(x509KeySpec);
        return publicKey;
    }

    /**
     * 获取私钥
     *
     * @param pubKey 私钥字符串
     * @return
     */
    private static Key getPrivateKey(String pubKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        // 对密钥解密
        byte[] keyBytes = str2Byte(pubKey);
        // 取得私钥
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
        return privateKey;
    }

    /**
     * 用公钥解密
     *
     * @param pubKey 公钥
     * @param data
     * @return
     */
    public static String decryptByPublicKey(String pubKey, String data) {
        try {
            // 取得公钥
            Key publicKey = getPublicKey(pubKey);
            // 对数据解密
            return new String(decrypt(data, publicKey));
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("EncryptAlgorithmsUtils.decryptByPublicKey Exception data[{}]", data, e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
    }

    /**
     * 用私钥解密
     *
     * @param priKey 私钥
     * @param data
     * @return
     */
    public static String decryptByPrivateKey(String priKey, String data) {
        try {
            // 取得私钥
            Key privateKey = getPrivateKey(priKey);
            return new String(decrypt(data, privateKey));
        } catch (ServerException e) {
            throw e;
        } catch (BadPaddingException e) {
            throw new ServerException(CodeEnum.SIGN_EXCEPTION);
        } catch (Exception e) {
            logger.error("EncryptAlgorithmsUtils.decryptByPrivateKey Exception data[{}]", data, e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
    }

    /**
     * 用公钥加密
     *
     * @param pubKey 公钥
     * @param data   需加密的数据
     * @return
     */
    public static String encryptByPublicKey(String pubKey, String data) {
        try {
            Key publicKey = getPublicKey(pubKey);
            // 对数据加密
            return Base64Utils.encodeBase64String(encrypt(data, publicKey));
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("EncryptAlgorithmsUtils.encryptByPublicKey Exception data[{}]", data, e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
    }

    /**
     * 用私钥加密
     *
     * @param priKey
     * @param data
     * @return
     */
    public static String encryptByPrivateKey(String priKey, String data) {
        try {
            // 取得私钥
            Key privateKey = getPrivateKey(priKey);
            ;
            // 对数据加密
            return Base64Utils.encodeBase64String(encrypt(data, privateKey));
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("EncryptAlgorithmsUtils.encryptByPrivateKey Exception data[{}]", data, e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
    }

    /**
     * 获取RSA 的加密密钥对 默认长度是2048
     *
     * @return 密钥对：(公钥 私钥)
     */
    public static String[] getRSAKey() {
        String key = GeneratorWorkKeyUtils.generateWorkKey();
        return getRSAKey(key, DEFAULT_RASKEY_LENGTH);
    }

    /**
     * 获取RSA 的加密密钥对
     * key：  加密因子
     * length：  加密长度
     *
     * @return 密钥对：(公钥 私钥)
     */
    public static String[] getRSAKey(String key, int length) {
        try {
            KeyPairGenerator keygen = KeyPairGenerator.getInstance(RSA_ALGORITHM);
            SecureRandom random = new SecureRandom();
            random.setSeed(key.getBytes());
            keygen.initialize(length, random);
            KeyPair kp = keygen.generateKeyPair();
            PublicKey publicKey = kp.getPublic();
            PrivateKey privateKey = kp.getPrivate();
            String pubKey = Base64Utils.encodeBase64String(publicKey.getEncoded());
            String priKey = Base64Utils.encodeBase64String(privateKey.getEncoded());
            return new String[]{pubKey, priKey};
        } catch (NoSuchAlgorithmException e) {
        }
        return null;
    }

    /**
     * 对字符串进行sha-1加密
     *
     * @param
     * @return 加密后字符串
     */
    public static String encryptBySHA(String str) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance(SHA);
            md.update(str.getBytes("UTF-8"));
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            logger.error("EncryptAlgorithmsUtils.encryptBySHA Exception str[{}]", str, e);
            throw new ServerException(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
        byte[] result = md.digest();
        return byte2HexStr(result);
    }

    /**
     * 字节数组转化为大写16进制字符串
     *
     * @param
     * @return
     */
    private static String byte2HexStr(byte[] b) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < b.length; i++) {
            String s = Integer.toHexString(b[i] & 0xFF);
            if (s.length() == 1) {
                sb.append("0");
            }
            sb.append(s.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 将字符串转换成二进制
     *
     * @param keyStr
     * @return
     */
    private static byte[] str2Byte(String keyStr) {
        return Base64Utils.decodeBase64(keyStr);
    }

    /**
     * 加密,key可以是公钥，也可以是私钥
     *
     * @param
     * @return
     * @throws Exception
     */
    private static byte[] encrypt(String data, Key key) throws Exception {
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        return cipher.doFinal(data.getBytes());
    }

    /**
     * 解密，key可以是公钥，也可以是私钥，如果是公钥加密就用私钥解密，反之亦然
     *
     * @param message
     * @return
     * @throws Exception
     */
    private static byte[] decrypt(String message, Key key) throws Exception {
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, key);
        return cipher.doFinal(Base64Utils.decodeBase64(message));
    }
}

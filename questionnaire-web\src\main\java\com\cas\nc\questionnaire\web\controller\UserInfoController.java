package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.user.*;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.ValidateUtils;
import com.cas.nc.questionnaire.common.vo.user.*;
import com.cas.nc.questionnaire.server.UserInfoServer;
import com.cas.nc.questionnaire.server.mapstruct.UserInfoConverter;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/questionnaire/userinfo")
public class UserInfoController extends BaseController {

    @Resource
    private UserInfoServer userInfoServer;

    @RequestMapping("/list")
    public ApiReturnResult list(@RequestBody UserInfoListReqVo vo) {
        logger.info("UserInfoController.list param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullContain(vo, "pageSize", "page");

        UserInfoListReqDto reqDto = UserInfoConverter.INSTANCE.to(vo);
        UserInfoListRespDto repDto = userInfoServer.list(reqDto);
        UserInfoListRespVo repVo = UserInfoConverter.INSTANCE.to(repDto);

        repVo.setPage(vo.getPage());
        repVo.setPageSize(vo.getPageSize());

        result.setData(repVo);

        logger.info("UserInfoController.list result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/save")
    public ApiReturnResult save(@RequestBody UserInfoSaveReqVo vo) {
        logger.info("UserInfoController.save param[{}]", JSONUtil.toJSONString(vo));

        UserInfoSaveReqDto reqDto = UserInfoConverter.INSTANCE.to(vo);
        userInfoServer.save(reqDto);

        return new ApiReturnResult(CodeEnum.SUCCESS);
    }

    @RequestMapping("/update")
    public ApiReturnResult update(@RequestBody UserInfoUpdateReqVo vo) {
        logger.info("UserInfoController.update param[{}]", JSONUtil.toJSONString(vo));

        UserInfoUpdateReqDto reqDto = UserInfoConverter.INSTANCE.to(vo);
        userInfoServer.update(reqDto);

        return new ApiReturnResult(CodeEnum.SUCCESS);
    }

    @RequestMapping("/detail")
    public ApiReturnResult getDetail(@RequestBody UserInfoSimpleReqVo vo) {
        logger.info("UserInfoController.getDetail param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        UserInfoRespDto userInfoRespDto = userInfoServer.getUserInfo(vo.getUserId());
        UserInfoRespVo userInfoRespVo = UserInfoConverter.INSTANCE.to(userInfoRespDto);
        result.setData(userInfoRespVo);
        return result;
    }

    @RequestMapping("/updateStatus")
    public ApiReturnResult updateStatus(@RequestBody UserInfoSetStatusReqVo vo) {
        logger.info("UserInfoController.updateStatus param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        UserInfoSetStatusReqDto reqDto = UserInfoConverter.INSTANCE.to(vo);
        userInfoServer.setStatus(reqDto);

        return result;
    }

}

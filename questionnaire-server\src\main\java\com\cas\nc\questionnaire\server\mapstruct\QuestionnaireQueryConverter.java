package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireListRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireListReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireQueryRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireQueryReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingSetQueryReqDto;
import com.cas.nc.questionnaire.common.vo.questionnaire.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface QuestionnaireQueryConverter {
    QuestionnaireQueryConverter INSTANCE = Mappers.getMapper(QuestionnaireQueryConverter.class);

    QuestionnaireQueryReqDto to(QuestionnaireQueryReqVo vo);

    QuestionnaireQueryRepVo to(QuestionnaireQueryRepDto repDto);

    SettingSetQueryReqDto to(QuestionnaireQueryReqDto reqDto);

    QuestionnaireQueryReqDto to(QuestionnaireExternalQueryReqVo vo);

    QuestionnaireListReqDto to(QuestionnaireListReqVo vo);

    QuestionnaireListRepVo to(QuestionnaireListRepDto repDto);
}
package com.cas.nc.questionnaire.web.oauth;

import com.cas.nc.questionnaire.dao.nosharddao.UserInfoDao;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.UserInfoQuery;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.crypto.hash.Md5Hash;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.util.ByteSource;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

/**
 * 角色认证
 */
public class CustomRealm extends AuthorizingRealm {
	
	@Resource
	private UserInfoDao userInfoDao;
	
	
	@Override
	protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
		String username = (String) principals.getPrimaryPrincipal();
		// 从数据库或者缓存中获得角色数据
		Set<String> roles = getRolesByUserName(username);
		Set<String> permissions = getPermissionsByUserName(username);
		
		SimpleAuthorizationInfo simpleAuthorizationInfo = new SimpleAuthorizationInfo();
		simpleAuthorizationInfo.setStringPermissions(permissions);
		simpleAuthorizationInfo.setRoles(roles);
		
		return simpleAuthorizationInfo;
	}

	private Set<String> getPermissionsByUserName(String username) {
		Set<String> sets = new HashSet<>();
		sets.add("user:delete");
		sets.add("user:add");
		return sets;
	}

	private Set<String> getRolesByUserName(String username) {
		System.out.println("从数据库中获取数据");
		UserInfoQuery userInfoQuery = new UserInfoQuery();
		userInfoQuery.setName(username);
		UserInfoPo userInfo = userInfoDao.selectOne(userInfoQuery);
//		Set<String> sets = new HashSet<>(Lists.newArrayList(userInfo.getRoles().split(",")));
//		return sets; todo
		return null;
	}

	@Override
	protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
		// 1.从主体传过来的认证信息中，获得用户名
		String username = (String) token.getPrincipal();
		
		// 2.通过用户名到数据库中获取凭证
		String password = getPasswordByUsername(username);
		if(password == null) {
			return null;
		}
		SimpleAuthenticationInfo simpleAuthenticationInfo = new SimpleAuthenticationInfo(username, password, "customRealm");
		//加盐
		simpleAuthenticationInfo.setCredentialsSalt(ByteSource.Util.bytes(username));
		return simpleAuthenticationInfo;
	}

	private String getPasswordByUsername(String username) {
		UserInfoQuery query = new UserInfoQuery();
		query.setUserName(username);
		UserInfoPo user = userInfoDao.selectOne(query);
		if (user != null) {
			return user.getPwd();
		}
		return null;
	}	
	public static void main(String[] args) {
		Md5Hash md5Hash = new Md5Hash("123456", "Mark");
		System.out.println(md5Hash);
	}

}
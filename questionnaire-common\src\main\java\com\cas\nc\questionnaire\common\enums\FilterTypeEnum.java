package com.cas.nc.questionnaire.common.enums;

import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.utils.Assert;


public enum FilterTypeEnum {
    AUTO(1, "自动筛选"),
    MANUAL(2, "手动筛选"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    FilterTypeEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public static boolean filterLegal(int key) {
        for (FilterTypeEnum bean : values()) {
            if (bean.equals(ELSE)) {
                continue;
            }
            if (bean.key.intValue() == key) {
                return true;
            }
        }
        return false;
    }

    public static FilterTypeEnum toEnum(int key){
        for (FilterTypeEnum bean : values()) {
            if (bean.key.intValue() == key) {
                return bean;
            }
        }
        throw new ServerException(CodeEnum.DATA_EXCEPTION);
    }

    public static boolean filterLegalException(int key) {
        Assert.isTrue(filterLegal(key), "filterType", CodeEnum.ILLEGAL);
        return true;
    }

    public static boolean isAuto(int key) {
        return AUTO.key.intValue() == key;
    }

    public static boolean isManual(int key) {
        return MANUAL.key.intValue() == key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

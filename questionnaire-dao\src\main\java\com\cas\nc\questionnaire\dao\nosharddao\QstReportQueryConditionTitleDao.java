package com.cas.nc.questionnaire.dao.nosharddao;

import com.cas.nc.questionnaire.dao.basedao.BaseDao;
import com.cas.nc.questionnaire.dao.po.QstReportQueryConditionTitlePo;
import com.cas.nc.questionnaire.dao.query.QstReportQueryConditionTitleQuery;

import java.util.List;

/**
 * 测评报告查询条件题目DAO接口
 */
public interface QstReportQueryConditionTitleDao extends BaseDao<QstReportQueryConditionTitlePo, QstReportQueryConditionTitleQuery> {
    
    /**
     * 根据条件ID查询题目列表
     * @param conditionId 条件ID
     * @return 题目列表
     */
    List<QstReportQueryConditionTitlePo> selectByConditionId(Long conditionId);
    
    /**
     * 根据条件ID删除题目
     * @param conditionId 条件ID
     * @return 影响行数
     */
    int deleteByConditionId(Long conditionId);
} 
package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class QstQuotaRulePo {
    /*自增id*/
    private Long id;

    /*名称*/
    private String name;

    /*问卷id*/
    private String questionnaireId;

    /*配额id*/
    private String quotaRuleId;

    /*用户id*/
    private Long userId;

    /*条件类型，1：省，2：市，3：题目*/
    private Integer conditionType;

    /*配额*/
    private Integer quota;

    /*配额类型，1：显式，2：隐式*/
    private Integer quotaType;

    /*题目序号*/
    private Integer titleSerialNumber;

    /*题目名称*/
    private String titleName;

    /*题号以及选项，json格式*/
    private String optionJsonInfo;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public String getQuotaRuleId() {
        return quotaRuleId;
    }

    public void setQuotaRuleId(String quotaRuleId) {
        this.quotaRuleId = quotaRuleId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getConditionType() {
        return conditionType;
    }

    public void setConditionType(Integer conditionType) {
        this.conditionType = conditionType;
    }

    public Integer getQuota() {
        return quota;
    }

    public void setQuota(Integer quota) {
        this.quota = quota;
    }

    public Integer getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(Integer quotaType) {
        this.quotaType = quotaType;
    }

    public Integer getTitleSerialNumber() {
        return titleSerialNumber;
    }

    public void setTitleSerialNumber(Integer titleSerialNumber) {
        this.titleSerialNumber = titleSerialNumber;
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public String getOptionJsonInfo() {
        return optionJsonInfo;
    }

    public void setOptionJsonInfo(String optionJsonInfo) {
        this.optionJsonInfo = optionJsonInfo;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
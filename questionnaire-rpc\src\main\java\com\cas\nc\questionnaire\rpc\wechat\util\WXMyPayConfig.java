package com.cas.nc.questionnaire.rpc.wechat.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.InputStream;


@Component
public class WXMyPayConfig extends WXPayConfig {
    @Value("${wechat.appid}")
    private String appId;
    @Value("${wechat.mch_id}")
    private String mch_id;
    @Value("${wechat.sign_key}")
    private String sign_key;

    @Override
    String getAppID() {
        return appId;
    }

    @Override
    String getMchID() {
        return mch_id;
    }

    @Override
    String getKey() {
        return sign_key;
    }

    @Override
    InputStream getCertStream() {
        return null;
    }

    @Override
    IWXPayDomain getWXPayDomain() {
        IWXPayDomain iwxPayDomain = new IWXPayDomain() {
            @Override
            public void report(String domain, long elapsedTimeMillis, Exception ex) {

            }
            @Override
            public DomainInfo getDomain(WXPayConfig config) {
                return new IWXPayDomain.DomainInfo(WXPayConstants.DOMAIN_API, true);
            }
        };
        return iwxPayDomain;
    }

    @Override
    public int getHttpConnectTimeoutMs() {
        return 10 * 1000;
    }

    @Override
    public int getHttpReadTimeoutMs() {
        return 10 * 1000;
    }
}

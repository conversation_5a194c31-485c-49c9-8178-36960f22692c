package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.answer.GetAnswerRepDto;
import com.cas.nc.questionnaire.common.dto.answer.GetAnswerReqDto;
import com.cas.nc.questionnaire.common.dto.answer.GetExternalAnswerReqDto;
import com.cas.nc.questionnaire.common.vo.answer.GetAnswerRepVo;
import com.cas.nc.questionnaire.common.vo.answer.GetAnswerReqVo;
import com.cas.nc.questionnaire.common.vo.answer.GetExternalAnswerReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GetAnswerConverter {
    GetAnswerConverter INSTANCE = Mappers.getMapper(GetAnswerConverter.class);

    GetAnswerReqDto to(GetAnswerReqVo vo);

    GetAnswerRepVo to(GetAnswerRepDto repDto);

    @Mapping(target = "answerUserId", source = "userId")
    GetExternalAnswerReqDto to(GetExternalAnswerReqVo vo);
}
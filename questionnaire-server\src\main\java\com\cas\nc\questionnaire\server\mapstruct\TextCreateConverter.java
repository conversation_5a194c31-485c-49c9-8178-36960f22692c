package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TextCreateConverter {
    TextCreateConverter INSTANCE = Mappers.getMapper(TextCreateConverter.class);

    QstOptionPo to(QstOptionPo qstOptionPoTemp);

    QstTitlePo to(QstTitlePo qstTitlePo);
}
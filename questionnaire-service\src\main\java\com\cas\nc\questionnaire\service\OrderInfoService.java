package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.OrderInfoPo;
import com.cas.nc.questionnaire.dao.query.OrderInfoQuery;
import org.aspectj.weaver.ast.Or;

import java.util.List;

public interface OrderInfoService {
    /**
     * 数据插入
     *
     * @param orderInfoPo
     * @return
     */
    int insert(OrderInfoPo orderInfoPo);

    /**
     * 查询唯一一条数据，若是出现多条出抛出异常
     *
     * @param query
     * @return
     */
    OrderInfoPo selectOne(OrderInfoQuery query);

    /**
     * 根据订单号查询唯一一条数据，若是出现多条出抛出异常
     *
     * @param orderNo
     * @return
     */
    OrderInfoPo selectOne(String orderNo);

    /**
     * 更新
     *
     * @param query
     * @return
     */
    int update(OrderInfoQuery query);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(OrderInfoQuery query);

    int updatePrePay(String orderNo, String payUrl, String prePayId);

    int selectMaxVersion(OrderInfoQuery query);

    List<OrderInfoPo> selectList(Long userId);

    List<OrderInfoPo> selectListByPage(OrderInfoQuery query);

    int selectCount(OrderInfoQuery query);
}

package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.WinXin.WinXinEntity;
import com.cas.nc.questionnaire.common.dto.transmit.QstEmailDto;
import com.cas.nc.questionnaire.common.dto.transmit.QstEmailRepDto;
import com.cas.nc.questionnaire.common.dto.transmit.QstEmailReqDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.EmailSmsSourceTypeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.utils.Constants;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.WeiXinSign;
import com.cas.nc.questionnaire.common.utils.WeiXinUnitl;
import com.cas.nc.questionnaire.common.vo.transmit.QstEmailRepVo;
import com.cas.nc.questionnaire.common.vo.transmit.QstEmailReqVo;
import com.cas.nc.questionnaire.common.vo.transmit.QstEmailVo;
import com.cas.nc.questionnaire.dao.po.QstEmailPo;
import com.cas.nc.questionnaire.server.impl.MailServerImpl;
import com.cas.nc.questionnaire.server.mapstruct.EmailConverter;
import com.cas.nc.questionnaire.service.QstEmailService;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.*;

@RequestMapping("transmit")
@RestController
public class TransmitController extends BaseController {


    @Value("${front.questionnaire.uri}")
    private String frontQuestionnaireUri;

    @Value("${ques.quesPrefix}")
    private String quesPrefix = "/qr";

    @Value("${ques.content.template}")
    private String quesContentTemplate;
    @Value("${ques.title.template}")
    private String quesTitleTemplate;

    @Value("${wechat.appid}")
    private String wxShareAppId;
    @Value("${wechat.secret}")
    private String wxShareSecret;


    private final static Logger logger = LoggerFactory.getLogger(TransmitController.class);

    @Resource
    private MailServerImpl mailService;

    @Resource
    private QstEmailService qstEmailService;

    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;

    /**
     * 生成二维码
     *
     * @param size 生成二维码图片的大小 如150*150，256*256 等 (仅传一个数字即可)
     * @return
     */
    @RequestMapping("transmit")
    private Object generateQRCode(@RequestParam(defaultValue = "150") Integer size, HttpServletResponse response, String questionnaireId) throws Exception {
        String path = frontQuestionnaireUri + "id=" + questionnaireId;
        qstQuestionnaireInfoService.updateStatus2Publish(questionnaireId, getUserId());

        //生成二维码
        BitMatrix bitMatrix = new MultiFormatWriter().encode(path, BarcodeFormat.QR_CODE, size, size);
        //image to response
        BufferedImage img = MatrixToImageWriter.toBufferedImage(bitMatrix);
        return ImageIO.write(img, "png", response.getOutputStream());
    }


    /**
     * 问卷信息初始化
     */
    @RequestMapping("emailInit")
    public Object sendEmailInit() {
        Map<String, String> mailInitInfo = new HashMap<>();
        mailInitInfo.put("content", quesContentTemplate);
        mailInitInfo.put("title", quesTitleTemplate);
        return mailInitInfo;
    }

    /**
     * 邮件持久化
     */
    @RequestMapping("sendMail")
    public ApiReturnResult sendMail(@RequestBody QstEmailVo qstEmailVo) {
        sendMailCommon(qstEmailVo);
        return new ApiReturnResult(CodeEnum.SUCCESS.key(), "发邮件任务已经添加到队列中，发送状态页面中可以查状态！");
    }

    /**
     * 邮件重发
     */
    @RequestMapping("reSendMail")
    public ApiReturnResult reSendMail(@RequestBody QstEmailVo qstEmailVoIn) {
        QstEmailPo qstEmailPo = mailService.selectMailByEmailId(qstEmailVoIn.getEmailId());
        if (qstEmailPo == null) {
            return new ApiReturnResult(CodeEnum.VALIDATE_PARAM_EXCEPTION);
        }
        QstEmailDto qstEmailDto = EmailConverter.INSTANCE.to(qstEmailPo);

        mailService.save(qstEmailDto);
        return new ApiReturnResult(CodeEnum.SUCCESS);
    }

    public void sendMailCommon(QstEmailVo qstEmailVo) {
        QstEmailDto qstEmailDto = EmailConverter.INSTANCE.to(qstEmailVo);
        qstEmailDto.setEmailContent(qstEmailVo.getContent());
        qstEmailDto.setUserId(getUserId());
        qstEmailDto.setEmailTitle(qstEmailVo.getTitle());
        qstEmailDto.setSourceType(EmailSmsSourceTypeEnum.QST_SEND.key());

        mailService.save(qstEmailDto);
    }


    /**
     * 邮件验重
     */
    @RequestMapping("addressCheck")
    public ApiReturnResult addressCheck(@RequestBody QstEmailVo qstEmailVo) {
        ApiReturnResult apiReturnResult = new ApiReturnResult(CodeEnum.SUCCESS);
        Map<String, String> stringStringMap = mailService.adressesCheck(qstEmailVo.getQuestionnaireId(), qstEmailVo.getAddressees(), getUserId());
//        Map<String, String> stringStringMap = mailService.adressesCheck(qstEmailVo.getQuestionnaireId(), qstEmailVo.getAddressees(), 13579L);
        apiReturnResult.setData(stringStringMap);
        return apiReturnResult;
    }


    /**
     * 邮件发送详情列表
     */
    @RequestMapping("getEmailStatus")
    public ApiReturnResult getEmailStatus(@RequestBody QstEmailReqVo qstEmailReqVo) {
        ApiReturnResult apiReturnResult = new ApiReturnResult(CodeEnum.SUCCESS);
        QstEmailReqDto qstEmailReqDto = EmailConverter.INSTANCE.to(qstEmailReqVo);
        qstEmailReqDto.setMailType(qstEmailReqVo.getMailType() == null ? 0 : qstEmailReqVo.getMailType());
        qstEmailReqDto.setUserId(getUserId());
//        qstEmailReqDto.setUserId(qstEmailReqVo.getUserId());
        QstEmailRepDto emailSendDetail = mailService.getEmailSendDetail(qstEmailReqDto);

        QstEmailRepVo qstEmailRepVo = new QstEmailRepVo();
        qstEmailRepVo.setTotal(emailSendDetail.getTotal());
        qstEmailRepVo.setQstEmailList(emailSendDetail.getQstEmailList());
        qstEmailRepVo.setPage(qstEmailReqVo.getPage());
        qstEmailRepVo.setPageSize(qstEmailReqVo.getPageSize());
        qstEmailRepVo.setQuestionnaireId(qstEmailReqVo.getQuestionnaireId());
        qstEmailRepVo.setMailType(qstEmailReqVo.getMailType());
        apiReturnResult.setData(qstEmailRepVo);

        return apiReturnResult;
    }

    /**
     * 按ID 删除 邮件任务
     */
    @RequestMapping("deletEmailTaskById")
    public ApiReturnResult deletEmailTaskById(@RequestBody QstEmailVo qstEmailVoIn) {
        ApiReturnResult apiReturnResult = new ApiReturnResult(CodeEnum.SUCCESS);
//        int delete = qstEmailService.delete(qstEmailVoIn.getEmailId(), 13579L);
        int delete = qstEmailService.delete(qstEmailVoIn.getEmailId(), getUserId());
        if (delete != Constants.ONE) {
            QstEmailPo qstEmailPo = qstEmailService.selectOne(qstEmailVoIn.getEmailId(), getUserId());
            if (qstEmailPo == null) delete = Constants.ONE;
            else delete = Constants.ZERO;
        }
        if (delete == Constants.ONE)
            return apiReturnResult;
        else
            return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
    }


    /**
     * 邮件状态统计
     */
    @RequestMapping("analiceMailCount")
    public ApiReturnResult analiceMailCount(@RequestBody QstEmailVo qstEmailVoIn) {
        ApiReturnResult apiReturnResult = new ApiReturnResult(CodeEnum.SUCCESS);
        Map<Integer, Integer> countMap = qstEmailService.analiceMailCount(qstEmailVoIn.getQuestionnaireId(), getUserId());
        apiReturnResult.setData(countMap);
        return apiReturnResult;
    }



    /**
     * 图片上传
     *
     * @param file
     * @param request
     * @return
     */
    @RequestMapping("uploadImg")
    public ApiReturnResult uploadPicture(@RequestParam(value = "file", required = false) MultipartFile file,
                                         HttpServletRequest request) {
        ApiReturnResult apiReturnResult = new ApiReturnResult(CodeEnum.SUCCESS);
        String fileName = file.getOriginalFilename();//获取文件名加后缀
        if (fileName != null && !Objects.equals(fileName, "")) {
            String returnUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/upload/imgs/";//存储路径
            String path = request.getSession().getServletContext().getRealPath("upload/imgs"); //文件存储位置
            String fileF = fileName.substring(fileName.lastIndexOf("."), fileName.length());//文件后缀
            fileName = System.currentTimeMillis() + "_" + new Random().nextInt(1000) + fileF;//新的文件名

            //先判断文件是否存在
            String fileAdd = DateUtil.format(new Date(), "yyyyMMdd");
            File file1 = new File(path + "/" + fileAdd);
            //如果文件夹不存在则创建
            if (!file1.exists() && !file1.isDirectory()) {
                file1.mkdir();
            }
            File targetFile = new File(file1, fileName);
            try {
                file.transferTo(targetFile);
                String msg = returnUrl + fileAdd + "/" + fileName;
                apiReturnResult.setMsg(msg);
            } catch (Exception e) {
                apiReturnResult.setMsg(e.getMessage());
                apiReturnResult.setCode(CodeEnum.UNKNOWN_RETURN_PAGE.key());
            }
        }
        return apiReturnResult;
    }

    /**
     * 微信分享
     * @param body
     * @return
     */
    @RequestMapping("sgture")
    public Map<String, Object> sgture(@RequestBody String body) {
        String strUrl = body;
        logger.info("TransmitController.sgture url[{}]", strUrl);

        WinXinEntity wx = WeiXinUnitl.getWinXinEntity(strUrl,wxShareAppId,wxShareSecret);
        // 将wx的信息到给页面
        Map<String, Object> map = new HashMap<>();
        String sgture = WeiXinSign.getSignature(wx.getTicket(), wx.getNoncestr(), wx.getTimestamp(), strUrl);
        map.put("sgture", sgture.trim());//签名
        map.put("timestamp", wx.getTimestamp().trim());//时间戳
        map.put("noncestr", wx.getNoncestr().trim());//随即串
        map.put("appid", wxShareAppId);//appID

        logger.info("TransmitController.sgture result[{}]", map);
        return map;
    }

}

package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.ShareTypeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.to.AnswerShareTo;
import com.cas.nc.questionnaire.common.to.ReportShareTo;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;
import com.cas.nc.questionnaire.service.QstLimitRuleService;
import com.cas.nc.questionnaire.service.ShareSettingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.*;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.PLEASE_LOGIN;

@Service
public class ShareSettingServiceImpl implements ShareSettingService {

    @Resource
    private QstLimitRuleService qstLimitRuleService;

    @Override
    public ReportShareTo getReportShareTo(String sharePwd, String questionnaireId, Long loginUserId) {
        ReportShareTo shareTo = new ReportShareTo();
        Long parseUserId = SequenceUtil.getInstance().parse2UserId4Long(questionnaireId);

        if (parseUserId.equals(loginUserId)) {
            shareTo.setUserId(loginUserId);
            return shareTo;
        }

        QstLimitRulePo qstLimitRulePo = qstLimitRuleService.selectOne(questionnaireId);

        if (qstLimitRulePo != null) {
            if (ShareTypeEnum.PART_OPEN.key().equals(qstLimitRulePo.getReportShareType())) {
                Assert.notNull(sharePwd, ANSWER_PWD_MUST);
                Assert.isTrue(sharePwd.equalsIgnoreCase(qstLimitRulePo.getReportPwd()), ANSWER_PWD_ERROR);
                shareTo.setUserId(parseUserId);
            } else if (ShareTypeEnum.OPEN.key().equals(qstLimitRulePo.getReportShareType())) {
                shareTo.setUserId(parseUserId);
            } else {
                Assert.notNull(loginUserId, PLEASE_LOGIN);
                shareTo.setUserId(parseUserId);
            }
        } else {
            Assert.notNull(loginUserId, PLEASE_LOGIN);
            shareTo.setUserId(loginUserId);
        }

        return shareTo;
    }

    @Override
    public AnswerShareTo getAnswerShareTo(String sharePwd, String questionnaireId, Long loginUserId) {
        AnswerShareTo shareTo = new AnswerShareTo();
        Long parseUserId = SequenceUtil.getInstance().parse2UserId4Long(questionnaireId);

        if (parseUserId.equals(loginUserId)) {
            shareTo.setUserId(loginUserId);
            return shareTo;
        }

        QstLimitRulePo qstLimitRulePo = qstLimitRuleService.selectOne(questionnaireId);

        if (qstLimitRulePo != null) {
            if (ShareTypeEnum.PART_OPEN.key().equals(qstLimitRulePo.getAnswerShareType())) {
                Assert.notNull(sharePwd, ANSWER_PWD_MUST);
                Assert.isTrue(sharePwd.equalsIgnoreCase(qstLimitRulePo.getAnswerPwd()), ANSWER_PWD_ERROR);
                shareTo.setUserId(parseUserId);
            } else if (ShareTypeEnum.NOT_OPEN.key().equals(qstLimitRulePo.getAnswerShareType())) {
                Assert.notNull(loginUserId, PLEASE_LOGIN);
                shareTo.setUserId(parseUserId);
            } else {
                throw new ServerException(UNKNOWN_RETURN_PAGE);
            }
        } else {
            Assert.notNull(loginUserId, PLEASE_LOGIN);
            shareTo.setUserId(loginUserId);
        }

        return shareTo;
    }
}

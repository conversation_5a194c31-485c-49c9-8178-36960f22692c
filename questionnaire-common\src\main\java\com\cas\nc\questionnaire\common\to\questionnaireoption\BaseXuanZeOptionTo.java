package com.cas.nc.questionnaire.common.to.questionnaireoption;

import java.util.Objects;

public class BaseXuanZeOptionTo extends BaseOptionTo {
    /*是否允许填空*/
    private Boolean canInput;
    /*是否默认*/
    private Boolean isDefault;
    /*图片*/
    private String image;

    public Boolean getCanInput() {
        return canInput;
    }

    public void setCanInput(Boolean canInput) {
        this.canInput = canInput;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        isDefault = isDefault;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        BaseXuanZeOptionTo that = (BaseXuanZeOptionTo) o;
        return Objects.equals(canInput, that.canInput) &&
                Objects.equals(isDefault, that.isDefault) &&
                Objects.equals(image, that.image);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), canInput, isDefault, image);
    }
}

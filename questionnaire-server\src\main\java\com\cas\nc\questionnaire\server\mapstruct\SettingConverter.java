package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.setting.SettingConditionReturnAddReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingConditionReturnEmailDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingConditionReturnListReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingConditionReturnQueryRepDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingConditionReturnQueryReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingConditionReturnSmsDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingConditionReturnUpdateReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingDeleteConditionReturnReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingFilterRuleAddReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingFilterRuleDeleteReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingFilterRuleListRepDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingFilterRuleListReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingFilterRuleUpdateReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingFilterRuleUseReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingQuotaRuleAddReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingQuotaRuleDeleteReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingQuotaRuleListRepDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingQuotaRuleListReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingQuotaRuleUpdateReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingSetQueryRepDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingSetQueryReqDto;
import com.cas.nc.questionnaire.common.dto.setting.SettingSetReqDto;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnAddReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnListReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnQueryRepVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnQueryReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingConditionReturnUpdateReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingDeleteConditionReturnReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleAddReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleDeleteReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleListRepVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleListReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleUpdateReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingFilterRuleUseReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleAddReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleDeleteReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleListReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingQuotaRuleUpdateReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingSetQueryRepVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingSetQueryReqVo;
import com.cas.nc.questionnaire.common.vo.setting.SettingSetReqVo;
import com.cas.nc.questionnaire.dao.po.QstEmailPo;
import com.cas.nc.questionnaire.dao.po.QstFilterRulePo;
import com.cas.nc.questionnaire.dao.po.QstLimitConditionPo;
import com.cas.nc.questionnaire.dao.po.QstQuotaRulePo;
import com.cas.nc.questionnaire.dao.po.QstSmsPo;
import com.cas.nc.questionnaire.dao.query.QstQuotaRuleQuery;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SettingConverter {
    SettingConverter INSTANCE = Mappers.getMapper(SettingConverter.class);

    SettingFilterRuleListReqDto to(SettingFilterRuleListReqVo reqVo);

    List<SettingFilterRuleListRepVo> to(List<SettingFilterRuleListRepDto> repDtoList);

    SettingFilterRuleAddReqDto to(SettingFilterRuleAddReqVo reqVo);

    SettingFilterRuleDeleteReqDto to(SettingFilterRuleDeleteReqVo reqVo);

    SettingFilterRuleUpdateReqDto to(SettingFilterRuleUpdateReqVo reqVo);

    SettingFilterRuleUseReqDto to(SettingFilterRuleUseReqVo reqVo);

    SettingQuotaRuleListReqDto to(SettingQuotaRuleListReqVo reqVo);

    SettingQuotaRuleAddReqDto to(SettingQuotaRuleAddReqVo reqVo);

    SettingQuotaRuleDeleteReqDto to(SettingQuotaRuleDeleteReqVo reqVo);

    SettingQuotaRuleUpdateReqDto to(SettingQuotaRuleUpdateReqVo reqVo);

    SettingSetReqDto to(SettingSetReqVo reqVo);

    SettingSetQueryReqDto to(SettingSetQueryReqVo reqVo);

    SettingSetQueryRepVo to(SettingSetQueryRepDto repDto);

    SettingConditionReturnListReqDto to(SettingConditionReturnListReqVo reqVo);

    SettingConditionReturnAddReqDto to(SettingConditionReturnAddReqVo reqVo);

    SettingConditionReturnUpdateReqDto to(SettingConditionReturnUpdateReqVo reqVo);

    SettingConditionReturnQueryReqDto to(SettingConditionReturnQueryReqVo reqVo);

    SettingConditionReturnQueryRepVo to(SettingConditionReturnQueryRepDto repDto);

    SettingDeleteConditionReturnReqDto to(SettingDeleteConditionReturnReqVo reqVo);

    QstFilterRulePo to(SettingFilterRuleAddReqDto reqDto);

    SettingQuotaRuleListRepDto to(QstQuotaRulePo bean);

    QstQuotaRuleQuery to(SettingQuotaRuleUpdateReqDto reqDto);

    SettingConditionReturnQueryRepDto to(QstLimitConditionPo qstLimitConditionPo);

    SettingConditionReturnEmailDto to(QstEmailPo qstEmailPo);

    SettingConditionReturnSmsDto to(QstSmsPo qstSmsPo);

    QstSmsPo to(SettingConditionReturnSmsDto smsInfo);

    QstEmailPo to(SettingConditionReturnEmailDto emailInfo);
}
package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.pay.*;
import com.cas.nc.questionnaire.common.dto.pay.OrderRepTo;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.utils.*;
import com.cas.nc.questionnaire.common.vo.pay.*;
import com.cas.nc.questionnaire.rpc.wechat.util.WXPayUtil;
import com.cas.nc.questionnaire.server.PayServer;
import com.cas.nc.questionnaire.server.mapstruct.PayConverter;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.SIGN_EXCEPTION;
import static com.cas.nc.questionnaire.common.utils.SignGenerateUtil.getSign;

@RequestMapping("/questionnaire/pay")
@RestController
public class PayController extends BaseController {

    @Resource
    private PayServer payServer;

    @RequestMapping("/wechatnotify")
    public Object weChatNotify(@RequestBody String paramXml) throws Exception {
        Map<String, String> resultMap = new HashMap<>();
        String result = "";
        logger.info("PayController.wechatNotify param[{}]", paramXml);
        try {
            payServer.weChatNotify(paramXml, null);
            resultMap.put("return_code", "SUCCESS");
            resultMap.put("return_msg", "OK");
        } catch (Exception e) {
            resultMap.put("return_code", "FAIL");
            resultMap.put("return_msg", "FAIL");
        }
        result = WXPayUtil.mapToXml(resultMap);
        logger.info("PayController.wechatNotify param[{}] result[{}]", paramXml, result);
        return result;
    }

    @RequestMapping("/listproduct")
    public ApiReturnResult listProduct() throws Exception {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Long userId = getUserId();
        Assert.notNull(userId, CodeEnum.PLEASE_LOGIN);

        List<ListProductRepDto> repDtoList = payServer.listProduct(userId);

        String repDtoJson = JSONUtil.toJSONString(repDtoList);
        List<ListProductRepVo> repVoList = JSONUtil.parseArray(repDtoJson, ListProductRepVo.class);

        result.setData(repVoList);

        return result;
    }

    @RequestMapping("/getpayurl")
    public ApiReturnResult getPayUrl(@RequestBody GetPayUrlReqVo reqVo) {
        logger.info("PayController.getPayUrl param[{}]", JSONUtil.toJSONString(reqVo));

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Long userId = getUserId();
        Assert.notNull(userId, CodeEnum.PLEASE_LOGIN);
        ValidateUtils.validateNotNullExclude(reqVo);
        validateSign(reqVo, userId);

        GetPayUrlReqDto reqDto = PayConverter.INSTANCE.to(reqVo);
        reqDto.setUserId(userId);

        GetPayUrlRepDto repDto = payServer.getPayUrl(reqDto);
        GetPayUrlRepVo repVo = PayConverter.INSTANCE.to(repDto);
        repVo.setSign(getSign(userId, repVo.getOrderNo()));

        result.setData(repVo);

        logger.info("PayController.getPayUrl result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/getpaystatus")
    public ApiReturnResult getPayStatus(@RequestBody GetPayStatusReqVo reqVo) throws Exception {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        Long userId = getUserId();
        Assert.notNull(userId, CodeEnum.PLEASE_LOGIN);
        ValidateUtils.validateNotNullExclude(reqVo);
        validateSign(reqVo, userId);

        GetPayStatusReqDto reqDto = PayConverter.INSTANCE.to(reqVo);
        reqDto.setUserId(userId);

        GetPayStatusRepDto repDto = payServer.getPayStatus(reqDto);
        result.setData(PayConverter.INSTANCE.to(repDto));

        return result;
    }

    @RequestMapping("/listorder")
    public ApiReturnResult listOrder(@RequestBody ListOrderReqVo reqVo) {
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        ValidateUtils.validateNotNullExclude(reqVo);
        Long userId = getUserId();
        Assert.notNull(userId, CodeEnum.PLEASE_LOGIN);
        ListOrderReqDto reqDto = new ListOrderReqDto();
        reqDto.setPage(reqVo.getPage());
        reqDto.setPageSize(reqVo.getPageSize());
        reqDto.setUserId(23L);

        ListOrderRepDto repDto = payServer.listOrder(reqDto);
        List<OrderRepVo> repToList = new ArrayList<>();
        if (repDto != null) {
            SafeUtil.of(repDto.getOrderList()).forEach(v -> {
                OrderRepVo repTo = new OrderRepVo();
                repTo.setStatus("支付成功");
                repTo.setProductName("会员充值");
                repTo.setAmount(MoneyUtils.changeF2YBig(v.getAmount()));
                repTo.setOrderNo(v.getOrderNo());
                repTo.setPayTime(DateUtil.formatTime(v.getPayTime()));

                repToList.add(repTo);
            });
        }

        ListOrderRepVo repVo = new ListOrderRepVo();
        repVo.setOrderList(repToList);
        repVo.setPage(reqVo.getPage());
        repVo.setPageSize(reqVo.getPageSize());
        repVo.setTotal(repDto.getTotal());

        result.setData(repVo);
        return result;
    }

    private void validateSign(GetPayUrlReqVo reqVo, Long userId) {
        String sign = getSign(reqVo.getSellingPrice(), reqVo.getProductId(), userId);
        Assert.isTrue(sign.equals(reqVo.getSign()), SIGN_EXCEPTION);
    }

    private void validateSign(GetPayStatusReqVo reqVo, Long userId) {
        String sign = getSign(userId, reqVo.getOrderNo());
        Assert.isTrue(sign.equals(reqVo.getSign()), SIGN_EXCEPTION);
    }

}

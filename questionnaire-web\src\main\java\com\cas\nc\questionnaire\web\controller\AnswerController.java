package com.cas.nc.questionnaire.web.controller;

import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateRepDto;
import com.cas.nc.questionnaire.common.dto.answer.AnswerCreateReqDto;
import com.cas.nc.questionnaire.common.dto.answer.CountExternalAnswererReqDto;
import com.cas.nc.questionnaire.common.dto.answer.DeleteAnswerReqDto;
import com.cas.nc.questionnaire.common.dto.answer.GetAnswerRepDto;
import com.cas.nc.questionnaire.common.dto.answer.GetAnswerReqDto;
import com.cas.nc.questionnaire.common.dto.answer.GetExternalAnswerReqDto;
import com.cas.nc.questionnaire.common.dto.answer.ListAnswerRepDto;
import com.cas.nc.questionnaire.common.dto.answer.ListAnswerReqDto;
import com.cas.nc.questionnaire.common.dto.answer.ListExternalAnswererReqDto;
import com.cas.nc.questionnaire.common.dto.answer.ValidateExternalAnsweredReqDto;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.obj.ApiReturnResult;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.to.AnswerShareTo;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.PaginateUtils;
import com.cas.nc.questionnaire.common.utils.SignGenerateUtil;
import com.cas.nc.questionnaire.common.utils.UserAgentUtil;
import com.cas.nc.questionnaire.common.utils.ValidateUtils;
import com.cas.nc.questionnaire.common.vo.answer.AnswerCreateRepVo;
import com.cas.nc.questionnaire.common.vo.answer.AnswerCreateReqVo;
import com.cas.nc.questionnaire.common.vo.answer.AnswerExternalCreateReqVo;
import com.cas.nc.questionnaire.common.vo.answer.CountExternalAnswererRepVo;
import com.cas.nc.questionnaire.common.vo.answer.CountExternalAnswererReqVo;
import com.cas.nc.questionnaire.common.vo.answer.GetAnswerRepVo;
import com.cas.nc.questionnaire.common.vo.answer.GetAnswerReqVo;
import com.cas.nc.questionnaire.common.vo.answer.GetExternalAnswerReqVo;
import com.cas.nc.questionnaire.common.vo.answer.ListAnswerRepVo;
import com.cas.nc.questionnaire.common.vo.answer.ListAnswerReqVo;
import com.cas.nc.questionnaire.common.vo.answer.ListExternalAnswererRepVo;
import com.cas.nc.questionnaire.common.vo.answer.ListExternalAnswererReqVo;
import com.cas.nc.questionnaire.common.vo.answer.ValidateExternalAnsweredRepVo;
import com.cas.nc.questionnaire.common.vo.answer.ValidateExternalAnsweredReqVo;
import com.cas.nc.questionnaire.dao.po.BizConfigPo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.server.AnswerServer;
import com.cas.nc.questionnaire.server.UserServer;
import com.cas.nc.questionnaire.server.mapstruct.AnswerCreateConverter;
import com.cas.nc.questionnaire.server.mapstruct.DeleteAnswerConverter;
import com.cas.nc.questionnaire.server.mapstruct.GetAnswerConverter;
import com.cas.nc.questionnaire.server.mapstruct.ListAnswerConverter;
import com.cas.nc.questionnaire.service.BizConfigService;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import com.cas.nc.questionnaire.service.ShareSettingService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.BIZ_ID_NOT_EXIST;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.REQUEST_INVALID_RETRY;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.SIGN_EXCEPTION;
import static com.cas.nc.questionnaire.common.utils.Constants.REQUEST_TIME_VALID_TIME;


@RestController
@RequestMapping("/questionnaire/answer")
public class AnswerController extends BaseController {

    @Resource
    private AnswerServer answerServer;
    @Resource
    private ShareSettingService shareSettingService;
    @Resource
    private BizConfigService bizConfigService;
    @Resource
    private UserServer userServer;
    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;

    @RequestMapping("/create")
    public ApiReturnResult create(@RequestBody AnswerCreateReqVo vo) {
        logger.info("AnswerController.create param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result;
        try {
            ValidateUtils.validateNotNullExclude(vo, "data", "emailId", "smsId");
            Assert.notNull(vo.getData(), "data");

            AnswerCreateReqDto reqDto = AnswerCreateConverter.INSTANCE.to(vo);
            reqDto.setIp(getIp());
            reqDto.setSource(UserAgentUtil.parseSource(getHttpServletRequest()).key());

            AnswerCreateRepDto repDto = answerServer.create(reqDto);
            AnswerCreateRepVo repVo = AnswerCreateConverter.INSTANCE.to(repDto);

            result = new ApiReturnResult(CodeEnum.SUCCESS);
            result.setData(repVo);
        } catch (Error error) {
            logger.error("AnswerController.create error", error);
            return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
        logger.info("AnswerController.create result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/listanswer")
    public ApiReturnResult listAnswer(@RequestBody ListAnswerReqVo vo) {
        logger.info("AnalysisController.listAnswer param[{}]", JSONUtil.toJSONString(vo));
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getQuestionnaireId(), "questionnaireId");
        Assert.notNull(vo.getEffective());
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        ListAnswerReqDto reqDto = ListAnswerConverter.INSTANCE.to(vo);

        Long loginUserId = getUserIdNoException();
        AnswerShareTo shareTo = shareSettingService.getAnswerShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
        reqDto.setUserId(shareTo.getUserId());

        PaginateUtils<ListAnswerRepDto> repDtoList = answerServer.listAnswer(reqDto);
        ListAnswerRepVo repVo = new ListAnswerRepVo();
        repVo.setCount(repDtoList.getRecordTotal());
        repVo.setPage(repDtoList.getCurrentPage());
        repVo.setPageSize(repDtoList.getPageSize());
        repVo.setAnswerList(ListAnswerConverter.INSTANCE.to(repDtoList.getData()));
        repVo.setIndependentShowFlag(shareTo.getIndependentShowFlag());

        result.setData(repVo);
        logger.info("AnalysisController.listAnswer result[{}]", JSONUtil.toJSONString(result));

        return result;
    }

    @RequestMapping(value = "/getanswer")
    public ApiReturnResult getAnswer(@RequestBody GetAnswerReqVo vo) {
        logger.info("AnalysisController.getAnswer param[{}]", JSONUtil.toJSONString(vo));
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getQuestionnaireId(), "questionnaireId");
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        GetAnswerReqDto reqDto = GetAnswerConverter.INSTANCE.to(vo);
        Long loginUserId = getUserIdNoException();

        AnswerShareTo shareTo = shareSettingService.getAnswerShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
        reqDto.setUserId(shareTo.getUserId());

        GetAnswerRepDto repDto = answerServer.getAnswer(reqDto);
        GetAnswerRepVo repVo = GetAnswerConverter.INSTANCE.to(repDto);

        result.setData(repVo);
        logger.info("AnalysisController.getAnswer result[{}]", JSONUtil.toJSONString(result));

        return result;
    }

    @RequestMapping(value = "/deleteanswer")
    public ApiReturnResult deleteAnswer(@RequestBody GetAnswerReqVo vo) {
        logger.info("AnalysisController.deleteAnswer param[{}]", JSONUtil.toJSONString(vo));
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getQuestionnaireId(), "questionnaireId");
        Assert.notNull(vo.getAnswerId(), "answerId");

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        DeleteAnswerReqDto reqDto = DeleteAnswerConverter.INSTANCE.to(vo);
        reqDto.setUserId(getUserId());

        answerServer.deleteAnswer(reqDto);

        logger.info("AnalysisController.deleteAnswer result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/externalcreate")
    public ApiReturnResult externalCreate(@RequestBody AnswerExternalCreateReqVo vo) {
        logger.info("AnswerController.externalCreate param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result;
        try {
            ValidateUtils.validateNotNullExclude(vo, "userId", "email");
            validateRequestTime(vo.getRequestTime());
            validateSign(vo, vo.getBizId(), vo.getSign());
            if (vo.getBizId() == 2 && StringUtils.isNotBlank(vo.getEmail())) {
                vo.setUserId(userServer.getUserInfo(vo.getEmail()).getUserId());
            }

            AnswerCreateReqDto reqDto = AnswerCreateConverter.INSTANCE.to(vo);
            reqDto.setIp(getIp());
            reqDto.setSource(UserAgentUtil.parseSource(getHttpServletRequest()).key());

            AnswerCreateRepDto repDto = answerServer.create(reqDto);
            AnswerCreateRepVo repVo = AnswerCreateConverter.INSTANCE.to(repDto);

            result = new ApiReturnResult(CodeEnum.SUCCESS);
            result.setData(repVo);
        } catch (Error error) {
            logger.error("AnswerController.externalCreate error", error);
            return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
        logger.info("AnswerController.externalCreate result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/listexternalanswerer")
    public ApiReturnResult listExternalAnswerer(@RequestBody ListExternalAnswererReqVo vo) {
        logger.info("AnswerController.listExternalAnswerer param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result;
        try {
            ValidateUtils.validateNotNullExclude(vo);
            String key = validateSign(vo, vo.getBizId(), vo.getSign());

            ListExternalAnswererReqDto reqDto = AnswerCreateConverter.INSTANCE.to(vo);
            reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));
            List<String> answerUserIdList = answerServer.listExternalAnswerer(reqDto);

            ListExternalAnswererRepVo repVo = new ListExternalAnswererRepVo();
            repVo.setQuestionnaireId(reqDto.getQuestionnaireId());
            repVo.setUserIdList(answerUserIdList);

            result = new ApiReturnResult(CodeEnum.SUCCESS);
            result.setData(JSONUtil.toJSONString(repVo));

            result.setSign(SignGenerateUtil.generateSignByObject(result, key));
        } catch (Error error) {
            logger.error("AnswerController.listExternalAnswerer error", error);
            return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
        logger.info("AnswerController.listExternalAnswerer result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/countexternalanswerer")
    public ApiReturnResult countExternalAnswerer(@RequestBody CountExternalAnswererReqVo vo) {
        logger.info("AnswerController.countExternalAnswerer param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result;
        try {
            ValidateUtils.validateNotNullExclude(vo);
            String key = validateSign(vo, vo.getBizId(), vo.getSign());

            CountExternalAnswererReqDto reqDto = AnswerCreateConverter.INSTANCE.to(vo);
            reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));
            Integer count = answerServer.countExternalAnswerer(reqDto);

            CountExternalAnswererRepVo repVo = new CountExternalAnswererRepVo();
            repVo.setQuestionnaireId(reqDto.getQuestionnaireId());
            repVo.setAnswererCount(count == null ? 0 : Long.valueOf(count));

            result = new ApiReturnResult(CodeEnum.SUCCESS);
            result.setData(JSONUtil.toJSONString(repVo));

            result.setSign(SignGenerateUtil.generateSignByObject(result, key));
        } catch (Error error) {
            logger.error("AnswerController.countExternalAnswerer error", error);
            return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
        logger.info("AnswerController.countExternalAnswerer result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping("/validateexternalanswered")
    public ApiReturnResult validateExternalAnswered(@RequestBody ValidateExternalAnsweredReqVo vo) {
        logger.info("AnswerController.validateExternalAnswered param[{}]", JSONUtil.toJSONString(vo));
        ApiReturnResult result;
        try {
            ValidateUtils.validateNotNullExclude(vo, "userId", "email");
            validateRequestTime(vo.getRequestTime());
            String key = validateSign(vo, vo.getBizId(), vo.getSign());
            if (vo.getBizId() == 2) {
                Assert.notBlank(vo.getEmail(), "email");
                vo.setUserId(userServer.getUserInfo(vo.getEmail()).getUserId());
            } else {
                Assert.notNull(vo.getUserId(), "userId");
            }

            ValidateExternalAnsweredReqDto reqDto = AnswerCreateConverter.INSTANCE.to(vo);
            reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));
            Boolean answered = answerServer.validateExternalAnswered(reqDto);

            ValidateExternalAnsweredRepVo repVo = new ValidateExternalAnsweredRepVo();
            repVo.setQuestionnaireId(reqDto.getQuestionnaireId());
            repVo.setAnswered(answered);

            result = new ApiReturnResult(CodeEnum.SUCCESS);
            result.setData(JSONUtil.toJSONString(repVo));

            result.setSign(SignGenerateUtil.generateSignByObject(result, key));
        } catch (Error error) {
            logger.error("AnswerController.validateExternalAnswered error", error);
            return new ApiReturnResult(CodeEnum.UNKNOWN_RETURN_PAGE);
        }
        logger.info("AnswerController.validateExternalAnswered result[{}]", JSONUtil.toJSONString(result));
        return result;
    }

    @RequestMapping(value = "/getexternalanswer")
    public ApiReturnResult getExternalAnswer(@RequestBody GetExternalAnswerReqVo vo) {
        logger.info("AnalysisController.getExternalAnswer param[{}]", JSONUtil.toJSONString(vo));

        ValidateUtils.validateNotNullExclude(vo, "userId", "email");
        validateRequestTime(vo.getRequestTime());
        String key = validateSign(vo, vo.getBizId(), vo.getSign());
        if (vo.getBizId() == 2) {
            Assert.notBlank(vo.getEmail(), "email");
            vo.setUserId(userServer.getUserInfo(vo.getEmail()).getUserId());
        } else {
            Assert.notNull(vo.getUserId(), "userId");
        }

        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);
        GetExternalAnswerReqDto reqDto = GetAnswerConverter.INSTANCE.to(vo);
        reqDto.setUserId(Long.valueOf(SequenceUtil.getInstance().parse2UserId(reqDto.getQuestionnaireId())));

        GetAnswerRepDto repDto = answerServer.getExternalAnswer(reqDto);

        result.setData(JSONUtil.toJSONString(repDto));
        result.setSign(SignGenerateUtil.generateSignByObject(result, key));

        logger.info("AnalysisController.getExternalAnswer result[{}]", result);

        return result;
    }

    public String validateSign(Object param, Long bizId, String paramSign) {
        BizConfigPo bizConfigPo = bizConfigService.selectOne(bizId);
        Assert.notNull(bizConfigPo, BIZ_ID_NOT_EXIST);
        String sign = SignGenerateUtil.generateSignByObject(param, bizConfigPo.getMd5Key());
        Assert.isTrue(paramSign.equalsIgnoreCase(sign), SIGN_EXCEPTION);

        return bizConfigPo.getMd5Key();
    }

    public void validateRequestTime(String requestTime) {
        Date validDate = DateUtil.addSeconds(DateUtil.parseDateTime(requestTime), REQUEST_TIME_VALID_TIME);
        int i = DateUtil.compareDate(validDate);
        Assert.isTrue(i == -1, REQUEST_INVALID_RETRY);
    }

    @RequestMapping(value = "/commonlistanswer")
    public ApiReturnResult commonListAnswer(@RequestBody ListAnswerReqVo vo) {
        logger.info("AnalysisController.listAnswer param[{}]", JSONUtil.toJSONString(vo));
        Assert.notNull(vo, "param");
        Assert.notNull(vo.getQuestionnaireId(), "questionnaireId");
        Assert.notNull(vo.getEffective());
        ApiReturnResult result = new ApiReturnResult(CodeEnum.SUCCESS);

        ListAnswerReqDto reqDto = ListAnswerConverter.INSTANCE.to(vo);

        QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(vo.getQuestionnaireId());
        Long loginUserId = qstQuestionnaireInfoPo.getUserId();
        AnswerShareTo shareTo = shareSettingService.getAnswerShareTo(vo.getSharePwd(), reqDto.getQuestionnaireId(), loginUserId);
        reqDto.setUserId(shareTo.getUserId());

        PaginateUtils<ListAnswerRepDto> repDtoList = answerServer.listAnswer(reqDto);
        ListAnswerRepVo repVo = new ListAnswerRepVo();
        repVo.setCount(repDtoList.getRecordTotal());
        repVo.setPage(repDtoList.getCurrentPage());
        repVo.setPageSize(repDtoList.getPageSize());
        repVo.setAnswerList(ListAnswerConverter.INSTANCE.to(repDtoList.getData()));
        repVo.setIndependentShowFlag(shareTo.getIndependentShowFlag());

        result.setData(repVo);
        logger.info("AnalysisController.listAnswer result[{}]", JSONUtil.toJSONString(result));

        return result;
    }
}

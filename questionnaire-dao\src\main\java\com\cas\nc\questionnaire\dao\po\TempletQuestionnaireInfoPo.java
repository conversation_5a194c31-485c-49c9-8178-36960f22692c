package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class TempletQuestionnaireInfoPo {
    /*自增id*/
    private Long id;

    /*模板id*/
    private String templetId;

    /*模板类目id*/
    private String templetCategoryId;

    /*标题*/
    private String title;

    /*描述*/
    private String des;

    /*状态，1：有效，2：无效*/
    private Integer status;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTempletId() {
        return templetId;
    }

    public void setTempletId(String templetId) {
        this.templetId = templetId;
    }

    public String getTempletCategoryId() {
        return templetCategoryId;
    }

    public void setTempletCategoryId(String templetCategoryId) {
        this.templetCategoryId = templetCategoryId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
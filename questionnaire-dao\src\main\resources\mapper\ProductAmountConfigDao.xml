<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cas.nc.questionnaire.dao.nosharddao.ProductAmountConfigDao">
    <resultMap id="BaseResultMap" type="com.cas.nc.questionnaire.dao.po.ProductAmountConfigPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="selling_price" jdbcType="BIGINT" property="sellingPrice"/>
        <result column="sales_pricing" jdbcType="BIGINT" property="salesPricing"/>
        <result column="time_count" jdbcType="INTEGER" property="timeCount"/>
        <result column="time_type" jdbcType="INTEGER" property="timeType"/>
        <result column="activity_copywriting" jdbcType="VARCHAR" property="activityCopywriting"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="sql_columns">
    id,selling_price,sales_pricing,time_count,time_type,activity_copywriting,priority,
    yn,update_time,create_time
  </sql>
    <sql id="sql_where">
        <where>
            <if test="null != item.id and '' != item.id">and id = #{item.id}</if>
            <if test="null != item.sellingPrice and '' != item.sellingPrice">and selling_price = #{item.sellingPrice}
            </if>
            <if test="null != item.salesPricing and '' != item.salesPricing">and sales_pricing = #{item.salesPricing}
            </if>
            <if test="null != item.timeCount and '' != item.timeCount">and time_count = #{item.timeCount}</if>
            <if test="null != item.timeType and '' != item.timeType">and time_type = #{item.timeType}</if>
            <if test="null != item.activityCopywriting and '' != item.activityCopywriting">and activity_copywriting =
                #{item.activityCopywriting}
            </if>
            <if test="null != item.priority and '' != item.priority">and priority = #{item.priority}</if>
            <if test="null != item.yn and '' != item.yn">and yn = #{item.yn}</if>
            <if test="null != item.updateTime and '' != item.updateTime">and update_time = #{item.updateTime}</if>
            <if test="null != item.createTime and '' != item.createTime">and create_time = #{item.createTime}</if>
        </where>
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from product_amount_config
        where id = #{id}
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from product_amount_config
        <include refid="sql_where"/>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <if test="null != item.tableColumns and '' != item.tableColumns">
            ${item.tableColumns}
        </if>
        <if test="null == item.tableColumns or '' == item.tableColumns">
            <include refid="sql_columns"/>
        </if>
        from product_amount_config
        <include refid="sql_where"/>
    </select>
    <sql id="sql_insert_columns">
        insert into product_amount_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.sellingPrice">selling_price,</if>
            <if test="null != item.salesPricing">sales_pricing,</if>
            <if test="null != item.timeCount">time_count,</if>
            <if test="null != item.timeType">time_type,</if>
            <if test="null != item.activityCopywriting">activity_copywriting,</if>
            <if test="null != item.priority">priority,</if>
            <if test="null != item.yn">yn,</if>
            <if test="null != item.updateTime">update_time,</if>
            <if test="null != item.createTime">create_time,</if>
        </trim>
        values
    </sql>
    <sql id="sql_insert_values">

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != item.sellingPrice">#{item.sellingPrice},</if>
            <if test="null != item.salesPricing">#{item.salesPricing},</if>
            <if test="null != item.timeCount">#{item.timeCount},</if>
            <if test="null != item.timeType">#{item.timeType},</if>
            <if test="null != item.activityCopywriting">#{item.activityCopywriting},</if>
            <if test="null != item.priority">#{item.priority},</if>
            <if test="null != item.yn">#{item.yn},</if>
            <if test="null != item.updateTime">#{item.updateTime},</if>
            <if test="null != item.createTime">#{item.createTime},</if>
        </trim>

    </sql>
    <sql id="duplicate_sql">
        ON DUPLICATE KEY UPDATE
        <trim suffix="" suffixOverrides=",">
            <if test="null != item.sellingPrice">selling_price = values(selling_price),</if>
            <if test="null != item.salesPricing">sales_pricing = values(sales_pricing),</if>
            <if test="null != item.timeCount">time_count = values(time_count),</if>
            <if test="null != item.timeType">time_type = values(time_type),</if>
            <if test="null != item.activityCopywriting">activity_copywriting = values(activity_copywriting),</if>
            <if test="null != item.priority">priority = values(priority),</if>
            <if test="null != item.yn">yn = values(yn),</if>
        </trim>

    </sql>
    <insert id="insert" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
    </insert>
    <insert id="insertUpdate" keyProperty="item.id" useGeneratedKeys="true">
        <include refid="sql_insert_columns"/>
        <include refid="sql_insert_values"/>
        <include refid="duplicate_sql"/>
    </insert>
    <sql id="sql_update">
        update product_amount_config
        <set>
            <if test="null != item.sellingPrice">selling_price = #{item.sellingPrice},</if>
            <if test="null != item.salesPricing">sales_pricing = #{item.salesPricing},</if>
            <if test="null != item.timeCount">time_count = #{item.timeCount},</if>
            <if test="null != item.timeType">time_type = #{item.timeType},</if>
            <if test="null != item.activityCopywriting">activity_copywriting = #{item.activityCopywriting},</if>
            <if test="null != item.priority">priority = #{item.priority},</if>
            <if test="null != item.yn">yn = #{item.yn},</if>
        </set>
        where id = #{item.id}
    </sql>
    <update id="update">
        <include refid="sql_update"/>
    </update>
    <delete id="delete">
        delete from product_amount_config
        <include refid="sql_where"/>
    </delete>
</mapper>
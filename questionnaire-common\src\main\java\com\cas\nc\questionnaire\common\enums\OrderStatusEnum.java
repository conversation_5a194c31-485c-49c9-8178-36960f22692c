package com.cas.nc.questionnaire.common.enums;


public enum OrderStatusEnum {
    INITIALIZATION(1, "初始化"),
    SUCCESS(2, "成功"),
    FAIL(3, "失败"),
    ELSE(99, "其他"),
    ;
    private final Integer key;
    private final String value;

    OrderStatusEnum(int key, String value) {
        this.value = value;
        this.key = key;
    }

    public String value() {
        return value;
    }

    public Integer key() {
        return key;
    }

}

package com.cas.nc.questionnaire.server.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cas.nc.questionnaire.common.dto.answer.*;
import com.cas.nc.questionnaire.common.enums.AnswerStatusEnum;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.enums.ConditionTypeEnum;
import com.cas.nc.questionnaire.common.enums.FilterJudgeTypeEnum;
import com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum;
import com.cas.nc.questionnaire.common.enums.QuestionnaireStatusEnum;
import com.cas.nc.questionnaire.common.enums.SourceEnum;
import com.cas.nc.questionnaire.common.enums.TitleTypeEnum;
import com.cas.nc.questionnaire.common.enums.task.TaskTypeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.to.CustomQueryTitleJsonInfoTo;
import com.cas.nc.questionnaire.common.to.FilterTitleJsonInfoTo;
import com.cas.nc.questionnaire.common.to.QuotaOptionTo;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.common.utils.IpUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.common.utils.PaginateUtils;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.AnswerInfoPo;
import com.cas.nc.questionnaire.dao.po.AnswerOptionPo;
import com.cas.nc.questionnaire.dao.po.AreaPo;
import com.cas.nc.questionnaire.dao.po.QstFilterRulePo;
import com.cas.nc.questionnaire.dao.po.QstLimitConditionPo;
import com.cas.nc.questionnaire.dao.po.QstLimitRulePo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.po.QstQuotaRulePo;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.cas.nc.questionnaire.dao.po.UserInfoPo;
import com.cas.nc.questionnaire.dao.query.AnswerInfoQuery;
import com.cas.nc.questionnaire.dao.query.AnswerOptionQuery;
import com.cas.nc.questionnaire.rpc.wechat.entity.ipentity.IpAreaResult;
import com.cas.nc.questionnaire.server.AnswerServer;
import com.cas.nc.questionnaire.server.IpServer;
import com.cas.nc.questionnaire.server.ValidateQstLimitRuleServer;
import com.cas.nc.questionnaire.server.strategy.answeroptionstrategy.AnswerOptionStrategy;
import com.cas.nc.questionnaire.server.strategyfactory.AnswerOptionStrategyFactory;
import com.cas.nc.questionnaire.service.AnswerInfoService;
import com.cas.nc.questionnaire.service.AnswerOptionService;
import com.cas.nc.questionnaire.service.AreaService;
import com.cas.nc.questionnaire.service.GlobalConfigService;
import com.cas.nc.questionnaire.service.QstEmailService;
import com.cas.nc.questionnaire.service.QstFilterRuleService;
import com.cas.nc.questionnaire.service.QstLimitConditionService;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import com.cas.nc.questionnaire.service.QstQuotaRuleService;
import com.cas.nc.questionnaire.service.QstSmsService;
import com.cas.nc.questionnaire.service.QstTitleService;
import com.cas.nc.questionnaire.service.TaskService;
import com.cas.nc.questionnaire.service.UserInfoService;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.AnswerStatusEnum.getInvalidStatusList;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.*;
import static com.cas.nc.questionnaire.common.enums.FilterDataSourceEnum.QUALITY_CONTROL_FILTER;
import static com.cas.nc.questionnaire.common.enums.FilterDataSourceEnum.SET_RETURN;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.ANSWER_DATE;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.ANSWER_DURATION;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.CITY;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.IP;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.PROVINCE;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.SOURCE_CHANNEL;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.TIME_SEGMENT;
import static com.cas.nc.questionnaire.common.enums.FilterRuleTypeEnum.TITILE;
import static com.cas.nc.questionnaire.common.enums.QuotaConditionTypeEnum.isCity;
import static com.cas.nc.questionnaire.common.enums.QuotaConditionTypeEnum.isProvince;
import static com.cas.nc.questionnaire.common.enums.QuotaConditionTypeEnum.isTitle;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.BIAOGESHUZHI;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.CUSTOM_CONTAIN_LIST;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.CUSTOM_FILL_BLANK_LIST;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.CUSTOM_NUM_LIST;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.isOneDimensionalFillBlank;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.isOneDimensionalOption;
import static com.cas.nc.questionnaire.common.enums.TitleTypeEnum.isXiaLaXuanZe;
import static com.cas.nc.questionnaire.common.enums.UserTypeEnum.*;
import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_LIMIT;
import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_LIMIT_TIP;
import static com.cas.nc.questionnaire.common.utils.Constants.ANSWER_TITLE_SPLIT;
import static com.cas.nc.questionnaire.common.utils.Constants.HINT;
import static com.cas.nc.questionnaire.common.utils.Constants.INVALID_ANSWER_HINT;
import static com.cas.nc.questionnaire.common.utils.Constants.SPLIT_FLAG;
import static com.cas.nc.questionnaire.common.utils.DateUtil.YYYYMMDD;
import static com.cas.nc.questionnaire.common.utils.DateUtil.compareDateBetween;

@Component("answerServer")
public class AnswerServerImpl implements AnswerServer {

    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;
    @Resource
    private QstTitleService qstTitleService;
    @Resource
    private AnswerInfoService answerInfoService;
    @Resource
    private AnswerOptionService answerOptionService;
    @Resource
    private IpServer ipServer;
    @Resource
    private AreaService areaService;
    @Resource
    private QstFilterRuleService qstFilterRuleService;
    @Resource
    private QstLimitConditionService qstLimitConditionService;
    @Resource
    private QstEmailService qstEmailService;
    @Resource
    private QstSmsService qstSmsService;
    @Resource
    private TaskService taskService;
    @Resource
    private QstQuotaRuleService qstQuotaRuleService;
    @Resource
    private ValidateQstLimitRuleServer validateQstLimitRuleServer;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private GlobalConfigService globalConfigService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AnswerCreateRepDto create(AnswerCreateReqDto reqDto) {
        AnswerCreateRepDto repDto = new AnswerCreateRepDto();
        validateQuestionnaire(reqDto);
        QstLimitRulePo qstLimitRulePo = validateQstLimitRuleServer.validateQstLimitRule(reqDto.getId(), reqDto.getIp(), reqDto.getDeviceCode(), reqDto.getPwd());

        AnswerInfoPo answerInfoPo = constructAnswerInfo(reqDto);
        List<QstTitlePo> qstTitlePoList = qstTitleService.selectList(reqDto.getId());

        Map<Integer, QstTitlePo> qstTitleMap = Maps.newHashMap();
        qstTitlePoList.forEach(v -> qstTitleMap.put(v.getSerialNumber(), v));

        List<AnswerOptionPo> answerOptionPoList = constructAnswerPo(reqDto, answerInfoPo, qstTitleMap);
        ListMultimap<Integer, AnswerOptionPo> answerOptionListMap = ArrayListMultimap.create();
        answerOptionPoList.forEach(v -> answerOptionListMap.put(v.getTitleSerialNumber(), v));

        boolean matchFilterResult = isMatchFilterRule(answerOptionListMap, reqDto);
        boolean matchQuotaResult = isMatchQuota(reqDto.getId(), reqDto.getIp());
        if (matchFilterResult || matchQuotaResult) {
            constructAnswerHint(qstLimitRulePo, repDto);
            return repDto;
        }

        String answerLimitTip = answerLimitTip(reqDto.getId());
        if (StringUtil.isNotBlank(answerLimitTip)) {
            repDto.setHint(answerLimitTip);
            return repDto;
        }

        List<QstLimitConditionPo> limitConditionPoList = qstLimitConditionService.selectList(reqDto.getId());
        QstLimitConditionPo filter = filterLimit(reqDto, answerOptionListMap, limitConditionPoList);

        answerInfoService.insert(answerInfoPo);
        insertAnswerOption(answerOptionPoList);
        updateEmailOrSms(reqDto);

        constructAnswerHint(filter, repDto);
        return repDto;
    }

    @Override
    public PaginateUtils<ListAnswerRepDto> listAnswer(ListAnswerReqDto reqDto) {
        PaginateUtils<ListAnswerRepDto> result = new PaginateUtils<>(reqDto.getPage(), reqDto.getPageSize());
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setUserId(reqDto.getUserId());
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        if (reqDto.getEffective()) {
            query.setStatus(AnswerStatusEnum.FINISH.key());
        } else {
            query.setStatusList(getInvalidStatusList());
        }
        if (reqDto.getRuleType() != null
                && reqDto.getJudgeType() != null
                && StringUtil.isNotBlank(reqDto.getContent())) {
            List<String> answerIdList = listAnswerId(reqDto);
            if (CollectionUtils.isEmpty(answerIdList)) {
                return result;
            }
            query.setAnswerIdList(answerIdList);
        }

        int count = answerInfoService.selectCount(query);
        result.setRecordTotal(count);
        if (count == 0) {
            return result;
        }

        query.setPageSize(result.getPageSize());
        query.setStartIndex(result.getStartIndex());
        List<AnswerInfoPo> infoPoList = answerInfoService.selectListByPage(query);
        List<ListAnswerRepDto> repDtoList = constructListAnswer(infoPoList);
        result.setData(repDtoList);

        return result;
    }

    private List<String> listAnswerId(ListAnswerReqDto reqDto) {
        AnswerOptionQuery query = new AnswerOptionQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setStatus(AnswerStatusEnum.FINISH.key());

        parseRule(reqDto.getRuleType(), reqDto.getJudgeType(), reqDto.getContent(), query);
        List<String> answerIdList = answerOptionService.selectAnswerIdList(query);
        List<String> resultList = answerIdList.stream().distinct().collect(Collectors.toList());

        return resultList;
    }

    private List<ListAnswerRepDto> constructListAnswer(List<AnswerInfoPo> infoPoList) {
        List<ListAnswerRepDto> result = new ArrayList<>();
        infoPoList.forEach(v -> {
            ListAnswerRepDto repDto = new ListAnswerRepDto();
            repDto.setDuration(v.getDuration());
            repDto.setIp(v.getIp());
            repDto.setSource(SourceEnum.toEnum(v.getSource()).value());
            repDto.setAnswerId(v.getAnswerId());
            repDto.setCommitTime(v.getCreateTime());
            repDto.setIpArea(areaService.getAddress(v.getProvince(), v.getCity()));

            result.add(repDto);
        });

        return result;
    }

    @Override
    public GetAnswerRepDto getAnswer(GetAnswerReqDto reqDto) {
        GetAnswerRepDto result = new GetAnswerRepDto();
        AnswerInfoPo answerInfoPo = answerInfoService.selectEffective(reqDto.getAnswerId(), reqDto.getUserId());
        if (answerInfoPo == null) {
            result.setTitleAnswerMap(new HashMap<>());
            return result;
        }

        List<AnswerOptionPo> answerOptionPoList = answerOptionService.selectList(reqDto.getUserId(), reqDto.getAnswerId());

        result.setTitleAnswerMap(constructAnswerMap(answerOptionPoList));
        return result;
    }

    @Override
    public void deleteAnswer(DeleteAnswerReqDto reqDto) {
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setUserId(reqDto.getUserId());
        query.setAnswerId(reqDto.getAnswerId());
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setStatus(AnswerStatusEnum.MANUAL_FILTER_INVALID.key());

        int update = answerInfoService.update(query);
        Assert.isTrue(update == 1, UNKNOWN_RETURN_PAGE);

        AnswerOptionQuery optionQuery = new AnswerOptionQuery();
        optionQuery.setUserId(reqDto.getUserId());
        optionQuery.setAnswerId(reqDto.getAnswerId());
        optionQuery.setStatus(AnswerStatusEnum.MANUAL_FILTER_INVALID.key());
        int optionUpdate = answerOptionService.update(optionQuery);
        Assert.isTrue(optionUpdate > 1, UNKNOWN_RETURN_PAGE);
    }

    @Override
    public List<String> listExternalAnswerer(ListExternalAnswererReqDto reqDto) {
        QstQuestionnaireInfoPo po = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId());

        if (po == null || StringUtil.isBlank(reqDto.getBizNo()) || reqDto.getBizId() == null) {
            return new ArrayList<>();
        }
        if (!reqDto.getBizNo().equals(po.getBizNo()) || !reqDto.getBizId().equals(po.getBizId())) {
            return new ArrayList<>();
        }

        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setTableColumns("answer_user_id");

        List<AnswerInfoPo> answerInfoPoList = answerInfoService.selectList(query);
        if (CollectionUtils.isEmpty(answerInfoPoList)) {
            return new ArrayList<>();
        }

        return answerInfoPoList.stream().filter(v -> StringUtil.isNotBlank(v.getAnswerUserId())).map(AnswerInfoPo::getAnswerUserId).collect(Collectors.toList());
    }

    @Override
    public Integer countExternalAnswerer(CountExternalAnswererReqDto reqDto) {
        QstQuestionnaireInfoPo po = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId());

        if (po == null || StringUtil.isBlank(reqDto.getBizNo()) || reqDto.getBizId() == null) {
            return 0;
        }
        if (!reqDto.getBizNo().equals(po.getBizNo()) || !reqDto.getBizId().equals(po.getBizId())) {
            return 0;
        }

        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setTableColumns("answer_user_id");

        return answerInfoService.selectCount(query);
    }

    @Override
    public Boolean validateExternalAnswered(ValidateExternalAnsweredReqDto reqDto) {
        QstQuestionnaireInfoPo po = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId());

        if (po == null || StringUtil.isBlank(reqDto.getBizNo()) || reqDto.getBizId() == null) {
            return false;
        }
        if (!reqDto.getBizNo().equals(po.getBizNo()) || !reqDto.getBizId().equals(po.getBizId())) {
            return false;
        }

        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setAnswerUserId(reqDto.getAnswerUserId());
        query.setTableColumns("answer_user_id");

        return answerInfoService.selectCount(query) > 0;
    }

    @Override
    public GetAnswerRepDto getExternalAnswer(GetExternalAnswerReqDto reqDto) {
        GetAnswerRepDto result = new GetAnswerRepDto();
        result.setTitleAnswerMap(new HashMap<>());

        QstQuestionnaireInfoPo po = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId());

        if (po == null || StringUtil.isBlank(reqDto.getBizNo()) || reqDto.getBizId() == null) {
            return result;
        }
        if (!reqDto.getBizNo().equals(po.getBizNo()) || !reqDto.getBizId().equals(po.getBizId())) {
            return result;
        }
        AnswerInfoQuery query = new AnswerInfoQuery();
        query.setQuestionnaireId(reqDto.getQuestionnaireId());
        query.setUserId(reqDto.getUserId());
        query.setAnswerUserId(reqDto.getAnswerUserId());
        query.setStatus(AnswerStatusEnum.FINISH.key());

        List<AnswerInfoPo> answerInfoPoList = answerInfoService.selectList(query);
        if (!CollectionUtils.isEmpty(answerInfoPoList)) {
            Optional<AnswerInfoPo> max = answerInfoPoList.stream().max(Comparator.comparing(AnswerInfoPo::getId));
            List<AnswerOptionPo> answerOptionPoList = answerOptionService.selectList(reqDto.getUserId(), max.get().getAnswerId());

            result.setTitleAnswerMap(constructAnswerMap(answerOptionPoList));
        }
        return result;
    }

    @Override
    public void parseRule(Integer ruleType, Integer judgeType, String content, AnswerOptionQuery query) {
        if (FilterRuleTypeEnum.isProvince(ruleType)) {
            query.setProvinceList(JSONUtil.parseArray(content, Long.class));
        } else if (FilterRuleTypeEnum.isCity(ruleType)) {
            query.setCityList(JSONUtil.parseArray(content, Long.class));
        } else if (FilterRuleTypeEnum.isSourceChannel(ruleType)) {
            query.setSourceList(JSONUtil.parseArray(content, Long.class));
        } else if (FilterRuleTypeEnum.isIp(ruleType)) {
            query.setIp(content);
        } else if (FilterRuleTypeEnum.isAnswerDate(ruleType)) {
            List<String> dateList = JSONUtil.parseArray(content, String.class);
            if (FilterJudgeTypeEnum.isEqual(judgeType)) {
                query.setCreateTime(DateUtil.parseDate(YYYYMMDD, dateList.get(0)));
            } else if (FilterJudgeTypeEnum.isNotEqual(judgeType)) {
                query.setNeqCreateTime(DateUtil.parseDate(YYYYMMDD, dateList.get(0)));
            } else if (FilterJudgeTypeEnum.isLessThan(judgeType)) {
                query.setLessThanCreateTime(DateUtil.parseDate(YYYYMMDD, dateList.get(0)));
            } else if (FilterJudgeTypeEnum.isLessThan(judgeType)) {
                query.setGreaterThanCreateTime(DateUtil.parseDate(YYYYMMDD, dateList.get(0)));
            } else if (FilterJudgeTypeEnum.isBetween(judgeType)) {
                query.setBeginCreateTime(DateUtil.parseDate(YYYYMMDD, dateList.get(0)));
                query.setEndCreateTime(DateUtil.parseDate(YYYYMMDD, dateList.get(1)));
            } else {
                throw new ServerException(JUDGE_TYPE_ERROR);
            }
        } else if (FilterRuleTypeEnum.isAnswerDuration(ruleType)) {
            List<Integer> durationList = JSONUtil.parseArray(content, Integer.class);
            if (FilterJudgeTypeEnum.isEqual(judgeType)) {
                query.setDuration(durationList.get(0));
            } else if (FilterJudgeTypeEnum.isNotEqual(judgeType)) {
                query.setNeqDuration(durationList.get(0));
            } else if (FilterJudgeTypeEnum.isLessThan(judgeType)) {
                query.setLessThanDuration(durationList.get(0));
            } else if (FilterJudgeTypeEnum.isLessThan(judgeType)) {
                query.setGreaterThanDuration(durationList.get(0));
            } else if (FilterJudgeTypeEnum.isBetween(judgeType)) {
                query.setBeginDuration(durationList.get(0));
                query.setEndDuration(durationList.get(1));
            } else {
                throw new ServerException(JUDGE_TYPE_ERROR);
            }
        } else if (FilterRuleTypeEnum.isTitle(ruleType)) {
            List<String> optionList = new ArrayList<>();

            CustomQueryTitleJsonInfoTo jsonInfoTo = JSONUtil.parseObject(content, CustomQueryTitleJsonInfoTo.class);;
            JSONObject jsonObject = JSONUtil.parseObject(content);
            Object optionObj = jsonObject.get("optionList");
            if (optionObj instanceof JSONArray) {
                JSONArray optionArray = jsonObject.getJSONArray("optionList");
                List<String> optionListTemp = optionArray.stream().map(v -> (String) v).collect(Collectors.toList());
                optionList.addAll(optionListTemp);
            } else {
                optionList.add((String) optionObj);
            }

            if (jsonInfoTo.getRowNumber() != null) {
                query.setRowNumber(jsonInfoTo.getRowNumber() + 1);
            }
            if (jsonInfoTo.getColumnNumber() != null) {
                query.setColumnNumber(jsonInfoTo.getColumnNumber() + 1);
            }

            if (CUSTOM_CONTAIN_LIST.contains(TitleTypeEnum.toEnum(jsonInfoTo.getTitleType()))) {
                List<Integer> serialNumberList = optionList.stream().mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                query.setSerialNumberList(serialNumberList);
            } else if (CUSTOM_FILL_BLANK_LIST.contains(TitleTypeEnum.toEnum(jsonInfoTo.getTitleType()))) {
                if (FilterJudgeTypeEnum.isEqual(judgeType)) {
                    query.setWriteContent(optionList.get(0));
                } else if (FilterJudgeTypeEnum.isNotEqual(judgeType)) {
                    query.setNeqWriteContent(optionList.get(0));
                } else if (FilterJudgeTypeEnum.isContain(judgeType)) {
                    query.setContainWriteContent(optionList.get(0));
                } else if (FilterJudgeTypeEnum.isNotContain(judgeType)) {
                    query.setNotContainWriteContent(optionList.get(0));
                } else {
                    throw new ServerException(JUDGE_TYPE_ERROR);
                }
            } else if (CUSTOM_NUM_LIST.contains(TitleTypeEnum.toEnum(jsonInfoTo.getTitleType()))) {
                if (FilterJudgeTypeEnum.isEqual(judgeType)) {
                    if (BIAOGESHUZHI.equals(TitleTypeEnum.toEnum(jsonInfoTo.getTitleType()))) {
                        query.setWriteContent(optionList.get(0));
                    } else {
                        query.setSerialNumber(Integer.valueOf(optionList.get(0)));
                    }
                } else if (FilterJudgeTypeEnum.isNotEqual(judgeType)) {
                    if (BIAOGESHUZHI.equals(TitleTypeEnum.toEnum(jsonInfoTo.getTitleType()))) {
                        query.setNeqWriteContent(optionList.get(0));
                    } else {
                        query.setNeqSerialNumber(Integer.valueOf(optionList.get(0)));
                    }
                } else if (FilterJudgeTypeEnum.isLessThan(judgeType)) {
                    if (BIAOGESHUZHI.equals(TitleTypeEnum.toEnum(jsonInfoTo.getTitleType()))) {
                        query.setLessThanWriteContent(optionList.get(0));
                    } else {
                        query.setLessThanSerialNumber(Integer.valueOf(optionList.get(0)));
                    }
                } else if (FilterJudgeTypeEnum.isLessThan(judgeType)) {
                    if (BIAOGESHUZHI.equals(TitleTypeEnum.toEnum(jsonInfoTo.getTitleType()))) {
                        query.setGreaterThanWriteContent(optionList.get(0));
                    } else {
                        query.setGreaterThanSerialNumber(Integer.valueOf(optionList.get(0)));
                    }
                } else if (FilterJudgeTypeEnum.isBetween(judgeType)) {
                    if (BIAOGESHUZHI.equals(TitleTypeEnum.toEnum(jsonInfoTo.getTitleType()))) {
                        query.setBeginWriteContent(Integer.parseInt(optionList.get(0)));
                        query.setEndWriteContent(Integer.parseInt(optionList.get(1)));
                    } else {
                        query.setBeginSerialNumber(Integer.parseInt(optionList.get(0)));
                        query.setEndSerialNumber(Integer.parseInt(optionList.get(1)));
                    }
                } else {
                    throw new ServerException(JUDGE_TYPE_ERROR);
                }
            }
        }
    }

    private String answerLimitTip(String id) {
        Long userId = SequenceUtil.getInstance().parse2UserId4Long(id);
        UserInfoPo userInfoPo = userInfoService.selectOne(userId);
        boolean isVip = isVip(userInfoPo.getType());
        if (isVip(userInfoPo.getType()) || isAdmin(userInfoPo.getType()) || isSuperAdmin(userInfoPo.getType())) {
            return null;
        }

        int answerCount = answerInfoService.selectCount(id);

        Integer answerLimit = globalConfigService.selectAnswerLimit();
        if (answerLimit == null) {
            answerLimit = ANSWER_LIMIT;
        }

        if (answerCount >= answerLimit) {
            return String.format(ANSWER_LIMIT_TIP, answerLimit);
        }

        return null;
    }

    private boolean isMatchQuota(String questionnaireId, String ip) {
        List<QstQuotaRulePo> quotaRuleList = qstQuotaRuleService.selectList(questionnaireId);
        if (CollectionUtils.isEmpty(quotaRuleList)) {
            return false;
        }
        IpAreaResult ipInfo = ipServer.getIpInfo(ip);

        for (QstQuotaRulePo bean : quotaRuleList) {
            List<Long> idList = new ArrayList<>();
            idList.add(Long.valueOf(bean.getTitleSerialNumber()));

            if (isProvince(bean.getConditionType())) {
                String validateName = ipInfo.getProvince() == null ? "" : ipInfo.getProvince();
                AreaPo areaPo = areaService.queryProvince(validateName);
                if (areaPo == null) {
                    continue;
                }
                int count = answerInfoService.selectProvinceCount(questionnaireId, areaPo.getId());
                if (count >= bean.getQuota()) {
                    return true;
                }
            } else if (isCity(bean.getConditionType())) {
                String validateName = ipInfo.getCity() == null ? "" : ipInfo.getCity();
                AreaPo areaPo = areaService.queryCity(validateName);
                if (areaPo == null) {
                    continue;
                }
                int count = answerInfoService.selectCityCount(questionnaireId, areaPo.getId());
                if (count >= bean.getQuota()) {
                    return true;
                }
            } else if (isTitle(bean.getConditionType())) {
                List<QuotaOptionTo> optionToList = JSONUtil.parseArray(bean.getOptionJsonInfo(), QuotaOptionTo.class);
                if (CollectionUtils.isEmpty(optionToList)) {
                    continue;
                }

                for (QuotaOptionTo optionTo : optionToList) {
                    String[] options = optionTo.getOptionSerialNumber().split(ANSWER_TITLE_SPLIT);
                    int length = options.length;
                    if (length < 1 || length > 3) {
                        continue;
                    }
                    AnswerOptionQuery query = new AnswerOptionQuery();
                    query.setQuestionnaireId(questionnaireId);
                    query.setTitleSerialNumber(bean.getTitleSerialNumber());
                    if (length == 1) {
                        query.setSerialNumber(Integer.valueOf(options[0]));
                    } else if (length == 2) {
                        query.setRowNumber(Integer.valueOf(options[0]));
                        query.setSerialNumber(Integer.valueOf(options[1]));
                    } else if (length == 3) {
                        query.setRowNumber(Integer.valueOf(options[0]));
                        query.setColumnNumber(Integer.valueOf(options[1]));
                        query.setSerialNumber(Integer.valueOf(options[2]));
                    }
                    int count = answerOptionService.selectCount(query);
                    if (count >= optionTo.getQuotaNum()) {
                        return true;
                    }
                }

            }
        }
        return false;
    }

    private boolean isMatchFilterRule(ListMultimap<Integer, AnswerOptionPo> answerOptionListMap, AnswerCreateReqDto reqDto) {
        List<QstFilterRulePo> filterRuleList = qstFilterRuleService.selectList(reqDto.getId(), QUALITY_CONTROL_FILTER.key());
        if (CollectionUtils.isEmpty(filterRuleList)) {
            return false;
        }
        return filter(answerOptionListMap, filterRuleList, reqDto);
    }

    private void constructAnswerHint(QstLimitConditionPo filter, AnswerCreateRepDto repDto) {
        if (filter != null) {
            if (StringUtil.isBlank(filter.getEndHint())) {
                repDto.setHint(HINT);
            } else {
                repDto.setHint(filter.getEndHint());
            }
            repDto.setReturnHint(filter.getReturnHint());
            repDto.setReturnUrl(filter.getReturnUrl());
        }
    }

    private void constructAnswerHint(QstLimitRulePo qstLimitRulePo, AnswerCreateRepDto repDto) {
        if (StringUtil.isBlank(qstLimitRulePo.getInvalidAnswerHint())) {
            repDto.setHint(INVALID_ANSWER_HINT);
        } else {
            repDto.setHint(qstLimitRulePo.getInvalidAnswerHint());
        }
    }

    private QstLimitConditionPo filterLimit(AnswerCreateReqDto reqDto, ListMultimap<Integer, AnswerOptionPo> answerOptionListMap, List<QstLimitConditionPo> limitConditionPoList) {
        if (CollectionUtils.isEmpty(limitConditionPoList)) {
            return null;
        }
        for (QstLimitConditionPo bean : limitConditionPoList) {
            if (ConditionTypeEnum.isConditional(bean.getType())) {
                List<QstFilterRulePo> filterRuleList = qstFilterRuleService.selectList(reqDto.getId(), bean.getLimitConditionId(), SET_RETURN.key());
                Assert.notNull(filterRuleList, CodeEnum.DATA_EXCEPTION);

                if (filter(answerOptionListMap, filterRuleList, reqDto)) {
                    asynOperate(bean);
                    return bean;
                }
            } else {
                asynOperate(bean);
                return bean;
            }
        }
        return null;
    }

    private boolean filter(ListMultimap<Integer, AnswerOptionPo> answerOptionListMap, List<QstFilterRulePo> filterRuleList, AnswerCreateReqDto reqDto) {
        for (QstFilterRulePo rule : filterRuleList) {
            if (StringUtil.isBlank(rule.getContent())) {
                continue;
            }

            String content = rule.getContent();

            if (StringUtil.isNotBlank(content) && content.startsWith("\"")) {
                content = content.substring(1, content.length() - 1);
            }

            FilterRuleTypeEnum ruleTypeEnum = FilterRuleTypeEnum.toEnum(rule.getRuleType());
            if (TITILE.equals(ruleTypeEnum)) {
                FilterTitleJsonInfoTo jsonInfoTo = JSON.parseObject(content, FilterTitleJsonInfoTo.class);
                Integer key = TitleTypeEnum.toEnum(jsonInfoTo.getTitleType()).key();
                List<AnswerOptionPo> optionPoList = answerOptionListMap.get(jsonInfoTo.getSerialNumber());
                if (CollectionUtils.isEmpty(optionPoList)) {
                    continue;
                }
                if (isOneDimensionalOption(key)) {
                    if (FilterJudgeTypeEnum.isY(rule.getJudgeType())) {
                        for (AnswerOptionPo option : optionPoList) {
                            boolean flag = jsonInfoTo.getOptionList().contains(option.getSerialNumber());
                            if (flag) {
                                return true;
                            }
                        }
                    } else if (FilterJudgeTypeEnum.isN(rule.getJudgeType())) {
                        for (AnswerOptionPo option : optionPoList) {
                            boolean flag = jsonInfoTo.getOptionList().contains(option.getSerialNumber());
                            if (!flag) {
                                return true;
                            }
                        }
                    }
                } else if (isOneDimensionalFillBlank(key)) {
                    if (FilterJudgeTypeEnum.isY(rule.getJudgeType())) {
                        for (AnswerOptionPo option : optionPoList) {
                            boolean flag = jsonInfoTo.getTextContent().contains(option.getWriteContent());
                            if (flag) {
                                return true;
                            }
                        }
                    } else if (FilterJudgeTypeEnum.isN(rule.getJudgeType())) {
                        for (AnswerOptionPo option : optionPoList) {
                            boolean flag = jsonInfoTo.getTextContent().contains(option.getWriteContent());
                            if (!flag) {
                                return true;
                            }
                        }
                    }
                }
            } else if (PROVINCE.equals(ruleTypeEnum) || CITY.equals(ruleTypeEnum)) {
                List<AreaPo> areaList = new ArrayList<>();
                IpAreaResult ipInfo = ipServer.getIpInfo(reqDto.getIp());
                if (ipInfo == null) {
                    continue;
                }
                String validateName = "";
                List<Long> idList = JSONUtil.parseArray(content, Long.class);
                if (PROVINCE.equals(ruleTypeEnum)) {
                    validateName = ipInfo.getProvince();
                    areaList = areaService.selectProvinceList(idList);
                } else if (CITY.equals(ruleTypeEnum)) {
                    validateName = ipInfo.getCity();
                    areaList = areaService.selectCityList(idList);
                }
                if (CollectionUtils.isEmpty(areaList)) {
                    continue;
                }

                boolean flag = validateIpInScope(areaList, validateName);
                if (FilterJudgeTypeEnum.isY(rule.getJudgeType())) {
                    if (flag) {
                        return true;
                    }
                } else if (FilterJudgeTypeEnum.isN(rule.getJudgeType())) {
                    if (!flag) {
                        return true;
                    }
                }
            } else if (TIME_SEGMENT.equals(ruleTypeEnum)) {
                List<String> timeList = JSONUtil.parseArray(content, String.class);
                if (CollectionUtils.isEmpty(timeList) || timeList.size() != 2) {
                    continue;
                }
                boolean flag = compareDateBetween(timeList.get(0), timeList.get(1));
                if (FilterJudgeTypeEnum.isY(rule.getJudgeType())) {
                    if (flag) {
                        return true;
                    }
                } else if (FilterJudgeTypeEnum.isN(rule.getJudgeType())) {
                    if (!flag) {
                        return true;
                    }
                }
            } else if (SOURCE_CHANNEL.equals(ruleTypeEnum)) {
                List<Integer> channelResourceList = JSONUtil.parseArray(content, Integer.class);
                if (CollectionUtils.isEmpty(channelResourceList)) {
                    continue;
                }
                boolean flag = channelResourceList.contains(reqDto.getSource());
                if (FilterJudgeTypeEnum.isY(rule.getJudgeType())) {
                    if (flag) {
                        return true;
                    }
                } else if (FilterJudgeTypeEnum.isN(rule.getJudgeType())) {
                    if (!flag) {
                        return true;
                    }
                }
            } else if (IP.equals(ruleTypeEnum)) {
                if (content.equals(reqDto.getIp())) {
                    return true;
                }
            } else if (ANSWER_DURATION.equals(ruleTypeEnum)) {
                Long timeLimit = Long.valueOf(content);//分钟
                Long answerTime = reqDto.getPassTime() / 60;//秒转分钟
                boolean flag = timeLimit.longValue() == answerTime.longValue();
                if (FilterJudgeTypeEnum.isY(rule.getJudgeType())) {
                    if (flag) {
                        return true;
                    }
                } else if (FilterJudgeTypeEnum.isN(rule.getJudgeType())) {
                    if (!flag) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean validateIpInScope(List<AreaPo> areaList, String name) {
        List<AreaPo> list = new ArrayList<>(areaList);
        List<String> nameList = list.stream().map(AreaPo::getName).collect(Collectors.toList());
        return nameList.contains(name);
    }

    private void asynOperate(QstLimitConditionPo bean) {
        if (StringUtil.isNotBlank(bean.getEmailId())) {
            taskService.createTask(bean.getEmailId(), TaskTypeEnum.SEND_BY_EMAIL_TASK.key(), "");
        }
    }

    private void updateEmailOrSms(AnswerCreateReqDto reqDto) {
        if (StringUtil.isNotBlank(reqDto.getEmailId())) {
            qstEmailService.updateStatus2Answered(reqDto.getEmailId());
        } else if (StringUtil.isNotBlank(reqDto.getSmsId())) {
            qstSmsService.updateStatus2Answered(reqDto.getSmsId());
        }
    }

    private void insertAnswerOption(List<AnswerOptionPo> answerOptionPoList) {
        answerOptionPoList.forEach(v -> answerOptionService.insert(v));
    }

    private List<AnswerOptionPo> constructAnswerPo(AnswerCreateReqDto reqDto, AnswerInfoPo answerInfoPo, Map<Integer, QstTitlePo> qstTitleMap) {
        List<AnswerOptionPo> answerOptionPoList = new ArrayList<>();

        for (Map.Entry<String, Object> entry : reqDto.getData().entrySet()) {
            String k = entry.getKey();
            Object v = entry.getValue();
            if (v == null) {
                continue;
            }
            if (v instanceof String && StringUtil.isBlank((String) v)) {
                continue;
            }
            if (v instanceof List && CollectionUtils.isEmpty((List) v)) {
                continue;
            }
            if (v instanceof Map && CollectionUtils.isEmpty((Map) v)) {
                continue;
            }
            String[] titles = k.split(ANSWER_TITLE_SPLIT);
            TitleTypeEnum titleTypeEnum = TitleTypeEnum.toEnum(qstTitleMap.get(Integer.valueOf(titles[0])).getType());
            AnswerOptionStrategy strategy = AnswerOptionStrategyFactory.getInstance().getStrategy(titleTypeEnum);
            Assert.notNull(strategy, TITLE_TYPE_ERROR);
            answerOptionPoList.addAll(strategy.construct(reqDto, answerInfoPo.getAnswerId(), entry, titleTypeEnum));
        }

        return answerOptionPoList;
    }

//    private List<AnswerOptionPo> constructAnswerPo(AnswerCreateReqDto reqDto, AnswerInfoPo answerInfoPo, Map<Integer, QstTitlePo> qstTitleMap) {
//        List<AnswerOptionPo> answerOptionPoList = new ArrayList<>();
//
//        reqDto.getData().forEach((k, v) -> {
//            if (v == null) {
//                return;
//            }
//            if (v instanceof List && CollectionUtils.isEmpty((List) v)) {
//                return;
//            }
//            String[] titles = k.split(ANSWER_TITLE_SPLIT);
//            TitleTypeEnum titleTypeEnum = TitleTypeEnum.toEnum(qstTitleMap.get(Integer.valueOf(titles[0])).getType());
//            switch (titleTypeEnum) {
//                case DANHANGTIANKONG:
//                case HUADONGTIAO:
//                case XINGMING:
//                case SHOUJI:
//                case YOUXIANG:
//                case RIQISHIJIAN:
//                case CHENGSHIDIZHI:
//                case DILIWEIZHI:
//                case TONGXUNDIZHI:
//                    AnswerOptionPo tianKongAnswerOption = constructSingleAnswerOption(reqDto, answerInfoPo.getAnswerId(), k, titleTypeEnum);
//                    tianKongAnswerOption.setWriteContent(v.toString());
//                    answerOptionPoList.add(tianKongAnswerOption);
//                    break;
//                case DANXUAN:
//                case XIALAXUANZE:
//                case XINGBIE:
//                case NIANLIANG:
//                case ZHIYE:
//                case GONGZUONIANXIN:
//                case JIAOYUCHENGDU:
//                case BIYEYUANXIAO:
//                case HUNYINZHUANGKUANG:
//                    AnswerOptionPo danXuanAnswerOption = constructSingleAnswerOption(reqDto, answerInfoPo.getAnswerId(), k, titleTypeEnum);
//                    if (v instanceof Map) {
//                        danXuanAnswerOption.setSerialNumber((Integer) ((Map) v).get("serialNumber") + 1);
//                        danXuanAnswerOption.setWriteContent((String) ((Map) v).get("blankContent"));
//                    } else {
//                        if (isXiaLaXuanZe(titleTypeEnum.key())) {
//                            danXuanAnswerOption.setSerialNumber((Integer) v + 1);
//                        } else {
//                            danXuanAnswerOption.setSerialNumber((Integer) v);
//                        }
//                    }
//                    answerOptionPoList.add(danXuanAnswerOption);
//                    break;
//                case DUOXUAN:
//                case PINGFENDUOXUAN:
//                    List<Object> duoXuanOptionList = (List<Object>) v;
//                    duoXuanOptionList.forEach(p -> {
//                        AnswerOptionPo duoXuanAnswerOption = constructSingleAnswerOption(reqDto, answerInfoPo.getAnswerId(), k, titleTypeEnum);
//                        if (p instanceof Map) {
//                            duoXuanAnswerOption.setSerialNumber((Integer) ((Map) p).get("serialNumber"));
//                            duoXuanAnswerOption.setWriteContent((String) ((Map) p).get("blankContent"));
//                        } else {
//                            duoXuanAnswerOption.setSerialNumber((Integer) p);
//                        }
//                        answerOptionPoList.add(duoXuanAnswerOption);
//                    });
//                    break;
//                case BIAOGEDANXUAN:
//                case BIAOGEDUOXUAN:
//                case LIANGBIAO:
//                case JUZHENLIANGBIAO:
//                case JUZHENHUADONGTIAO:
//                case BIZHONG:
//                    AnswerOptionPo doubleAnswerOption = constructSingleAnswerOption(reqDto, answerInfoPo.getAnswerId(), titles[0], titleTypeEnum);
//                    doubleAnswerOption.setRowNumber(Integer.valueOf(titles[1]) + 1);
//                    if (v instanceof List) {
//                        List<Integer> optionList = (List<Integer>) v;
//                        if (!CollectionUtils.isEmpty(optionList)) {
//                            optionList.forEach(intValue -> {
//                                doubleAnswerOption.setSerialNumber(intValue + 1);
//                                answerOptionPoList.add(doubleAnswerOption);
//                            });
//                        }
//                    } else {
//                        doubleAnswerOption.setSerialNumber((Integer) v + 1);
//                        answerOptionPoList.add(doubleAnswerOption);
//                    }
//                    break;
//                case BIAOGEXIALAXUANZE:
//                    AnswerOptionPo biaoGeXiaLaAnswerOption = constructSingleAnswerOption(reqDto, answerInfoPo.getAnswerId(), titles[0], titleTypeEnum);
//                    biaoGeXiaLaAnswerOption.setRowNumber(Integer.valueOf(titles[1]) + 1);
//                    biaoGeXiaLaAnswerOption.setColumnNumber(Integer.valueOf(titles[2]) + 1);
//                    biaoGeXiaLaAnswerOption.setSerialNumber((Integer) v + 1);
//                    answerOptionPoList.add(biaoGeXiaLaAnswerOption);
//                    break;
//                case BIAOGESHUZHI:
//                case BIAOGEWENBEN:
//                    AnswerOptionPo biaoGeShuZhiOption = constructSingleAnswerOption(reqDto, answerInfoPo.getAnswerId(), titles[0], titleTypeEnum);
//                    biaoGeShuZhiOption.setRowNumber(Integer.valueOf(titles[1]) + 1);
//                    biaoGeShuZhiOption.setColumnNumber(Integer.valueOf(titles[2]) + 1);
//                    biaoGeShuZhiOption.setWriteContent(v.toString());
//                    answerOptionPoList.add(biaoGeShuZhiOption);
//                    break;
//                case DUOXIANGDANHANGTIANKONG:
//                    AnswerOptionPo duoXiangDanHangTianKongPo = constructSingleAnswerOption(reqDto, answerInfoPo.getAnswerId(), titles[0], titleTypeEnum);
//                    duoXiangDanHangTianKongPo.setRowNumber(Integer.valueOf(titles[1]) + 1);
//                    duoXiangDanHangTianKongPo.setWriteContent(v.toString());
//                    answerOptionPoList.add(duoXiangDanHangTianKongPo);
//                    break;
//                default:
//                    break;
//            }
//        });
//
//        return answerOptionPoList;
//    }

    private AnswerInfoPo constructAnswerInfo(AnswerCreateReqDto reqDto) {
        AnswerInfoPo po = new AnswerInfoPo();
        String userId = SequenceUtil.getInstance().parse2UserId(reqDto.getId());
        po.setAnswerUserId(reqDto.getAnswerUserId());
        po.setAnswerId(SequenceUtil.getInstance().generateAnswerId(userId));
        po.setBeginTime(reqDto.getBeginTime());
        po.setDeviceId(reqDto.getDeviceCode());
        po.setDuration(reqDto.getPassTime());
        po.setEndTime(new Date());
        po.setIp(reqDto.getIp());
        po.setIpLong(IpUtil.getIp2long(reqDto.getIp()));
        po.setQuestionnaireId(reqDto.getId());
        po.setSource(reqDto.getSource());
        po.setUserId(Long.valueOf(userId));
        po.setStatus(AnswerStatusEnum.FINISH.key());
        // 设置微信openid
        po.setOpenid(reqDto.getOpenid());

        IpAreaResult ipInfo = ipServer.getIpInfo(reqDto.getIp());
        if (ipInfo != null) {
            AreaPo province = areaService.queryProvince(ipInfo.getProvince());
            po.setProvince(province == null ? null : province.getId());
            String key = ipInfo.getCity() + SPLIT_FLAG + (province == null ? "" : province.getId());
            AreaPo city = areaService.queryCity(key);
            po.setCity(city == null ? null : city.getId());
        }

        return po;
    }

    private AnswerOptionPo constructSingleAnswerOption(AnswerCreateReqDto reqDto, String answerId, String titleSerialNumber, TitleTypeEnum titleTypeEnum) {
        AnswerOptionPo po = new AnswerOptionPo();
        po.setAnswerId(answerId);
        po.setQuestionnaireId(reqDto.getId());
        po.setTitleSerialNumber(Integer.valueOf(titleSerialNumber));
        po.setType(titleTypeEnum.key());
        po.setStatus(AnswerStatusEnum.FINISH.key());
        po.setUserId(SequenceUtil.getInstance().parse2UserId4Long(reqDto.getId()));
        return po;
    }

    private void validateQuestionnaire(AnswerCreateReqDto reqDto) {
        QstQuestionnaireInfoPo po = qstQuestionnaireInfoService.selectOne(reqDto.getId());
        Assert.notNull(po, CodeEnum.QST_NOT_EXIST);
        if (StringUtil.isNotBlank(reqDto.getBizNo()) && reqDto.getBizId() != null) {
            Assert.notNull(reqDto.getBizNo().equals(po.getBizNo()) && reqDto.getBizId().equals(po.getBizId()), QST_NOT_EXIST);
        }
        Assert.isTrue(QuestionnaireStatusEnum.isPublished(po.getStatus()), CodeEnum.QST_UPDATING);
    }

    private Map<String, Object> constructAnswerMap(List<AnswerOptionPo> answerOptionPoList) {
        Map<String, Object> result = new HashMap<>();
        Map<Integer, List<AnswerOptionPo>> optionPoListMap = SafeUtil.of(answerOptionPoList).stream().collect(Collectors.groupingBy(AnswerOptionPo::getTitleSerialNumber));
        List<AnswerOptionPo> optionPoList;
        AnswerOptionPo answerOptionPo;

        for (Map.Entry<Integer, List<AnswerOptionPo>> entry : optionPoListMap.entrySet()) {
            optionPoList = entry.getValue();
            answerOptionPo = optionPoList.get(0);
            String title = entry.getKey().toString();

            TitleTypeEnum titleTypeEnum = TitleTypeEnum.toEnum(answerOptionPo.getType());
            switch (titleTypeEnum) {
                case DANHANGTIANKONG:
                case HUADONGTIAO:
                case XINGMING:
                case SHOUJI:
                case YOUXIANG:
                case RIQISHIJIAN:
                case CHENGSHIDIZHI:
                case DILIWEIZHI:
                case TONGXUNDIZHI:
                case WENJIAN:
                    result.put(title, answerOptionPo.getWriteContent());
                    break;
                case DANXUAN:
                case XIALAXUANZE:
                case XINGBIE:
                case NIANLIANG:
                case ZHIYE:
                case GONGZUONIANXIN:
                case JIAOYUCHENGDU:
                case BIYEYUANXIAO:
                case HUNYINZHUANGKUANG:
                case LIANGBIAO:
                    if (isXiaLaXuanZe(titleTypeEnum.key())) {
                        result.put(title, answerOptionPo.getSerialNumber() - 1);
                    } else {
                        if (StringUtil.isNotBlank(answerOptionPo.getWriteContent())) {
                            Map<String, String> writeContentMap = new HashMap<>();
                            writeContentMap.put("serialNumber", String.valueOf(answerOptionPo.getSerialNumber()));
                            writeContentMap.put("blankContent", answerOptionPo.getWriteContent());

                            result.put(title, writeContentMap);
                        } else {
                            result.put(title, answerOptionPo.getSerialNumber());
                        }
                    }
                    break;
                case DUOXUAN:
                case PINGFENDUOXUAN:
                    List<Map<String, String>> writeContentMapList = new ArrayList<>();
                    optionPoList.forEach(p -> {
                        Map<String, String> writeContentMap = new HashMap<>();
                        writeContentMap.put("serialNumber", String.valueOf(p.getSerialNumber()));
                        if (StringUtil.isNotBlank(p.getWriteContent())) {
                            writeContentMap.put("blankContent", p.getWriteContent());
                        }
                        writeContentMapList.add(writeContentMap);
                    });
                    result.put(title, writeContentMapList);
                    break;
                case BIAOGEDANXUAN:
                case JUZHENLIANGBIAO:
                case JUZHENHUADONGTIAO:
                case BIZHONG:
                    optionPoList.forEach(p -> result.put(constructTitle(title, p.getRowNumber() - 1), p.getSerialNumber()));
                    break;
                case BIAOGEXIALAXUANZE:
                    optionPoList.forEach(p -> result.put(constructTitle(title, p.getRowNumber() - 1, p.getColumnNumber() - 1), p.getSerialNumber()));
                    break;
                case BIAOGESHUZHI:
                case BIAOGEWENBEN:
                    optionPoList.forEach(p -> result.put(constructTitle(title, p.getRowNumber() - 1, p.getColumnNumber() - 1), p.getWriteContent()));
                    break;
                case DUOXIANGDANHANGTIANKONG:
                    optionPoList.forEach(p -> result.put(constructTitle(title, p.getRowNumber() - 1), p.getWriteContent()));
                    break;
                case BIAOGEDUOXUAN:
                    Map<Integer, List<AnswerOptionPo>> biaoGeDuoXuanMapList = optionPoList.stream().collect(Collectors.groupingBy(AnswerOptionPo::getRowNumber));
                    for (Map.Entry<Integer, List<AnswerOptionPo>> optionEntry : biaoGeDuoXuanMapList.entrySet()) {
                        List<Integer> listAnswer = new ArrayList<>();
                        result.put(constructTitle(title, optionEntry.getKey() - 1), listAnswer);
                        for (AnswerOptionPo optionPo : optionEntry.getValue()) {
                            listAnswer.add(optionPo.getSerialNumber());
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        return result;
    }

    private String constructTitle(String title, int rowNum) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(title);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(rowNum);

        return buffer.toString();
    }

    private String constructTitle(String title, int rowNum, int columnNum) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(title);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(rowNum);
        buffer.append(ANSWER_TITLE_SPLIT);
        buffer.append(columnNum);

        return buffer.toString();
    }
}

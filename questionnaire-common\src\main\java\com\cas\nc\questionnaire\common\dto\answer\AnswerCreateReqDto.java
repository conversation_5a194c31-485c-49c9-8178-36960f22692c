package com.cas.nc.questionnaire.common.dto.answer;

import java.util.Date;
import java.util.Map;

public class AnswerCreateReqDto {
    /*开始答题时间*/
    private Date beginTime;
    /*硬件编码*/
    private String deviceCode;
    /*答案集合*/
    private Map<String, Object> data;
    /*问卷id*/
    private String id;
    /*没有回答问题数*/
    private Integer noAnswerCount;
    /*答题时长，单位秒*/
    private Long passTime;
    /*问题数*/
    private Integer questionCount;
    /*答题ip*/
    private String ip;
    /*pwd*/
    private String pwd;
    /*请求来源*/
    private Integer source;

    private String emailId;

    private String smsId;

    private String answerUserId;

    /*业务id，提前定义*/
    private Long bizId;
    /*业务单号*/
    private String bizNo;

    /*微信用户唯一标识*/
    private String openid;

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getNoAnswerCount() {
        return noAnswerCount;
    }

    public void setNoAnswerCount(Integer noAnswerCount) {
        this.noAnswerCount = noAnswerCount;
    }

    public Long getPassTime() {
        return passTime;
    }

    public void setPassTime(Long passTime) {
        this.passTime = passTime;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public String getSmsId() {
        return smsId;
    }

    public void setSmsId(String smsId) {
        this.smsId = smsId;
    }

    public String getAnswerUserId() {
        return answerUserId;
    }

    public void setAnswerUserId(String answerUserId) {
        this.answerUserId = answerUserId;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }
    
    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }
}

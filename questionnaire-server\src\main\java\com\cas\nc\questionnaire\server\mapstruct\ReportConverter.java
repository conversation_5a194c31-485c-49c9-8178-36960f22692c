package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.AnalysisReportOptionRepDto;
import com.cas.nc.questionnaire.common.dto.analysis.AnalysisReportRepDto;
import com.cas.nc.questionnaire.common.dto.analysis.AnalysisReportReqDto;
import com.cas.nc.questionnaire.common.dto.analysis.CustomReportReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.AnalysisReportRepVo;
import com.cas.nc.questionnaire.common.vo.analysis.AnalysisReportReqVo;
import com.cas.nc.questionnaire.common.vo.analysis.CustomReportReqVo;
import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ReportConverter {
    ReportConverter INSTANCE = Mappers.getMapper(ReportConverter.class);

    AnalysisReportReqDto to(AnalysisReportReqVo vo);

    AnalysisReportRepVo to(AnalysisReportRepDto repDto);

    AnalysisReportOptionRepDto to(QstOptionPo option);

    List<AnalysisReportOptionRepDto> to(List<QstOptionPo> optionPoList);

    CustomReportReqDto to(CustomReportReqVo reqVo);
}
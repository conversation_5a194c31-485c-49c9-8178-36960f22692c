package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.TempletQuestionnaireInfoPo;
import com.cas.nc.questionnaire.dao.query.TempletQuestionnaireInfoQuery;

import java.util.List;

public interface TempletQuestionnaireInfoService {
    /**
     * 数据插入
     *
     * @param templetQuestionnaireInfoPo
     * @return
     */
    int insert(TempletQuestionnaireInfoPo templetQuestionnaireInfoPo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(TempletQuestionnaireInfoQuery query);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<TempletQuestionnaireInfoPo> selectList(TempletQuestionnaireInfoQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    TempletQuestionnaireInfoPo selectOne(TempletQuestionnaireInfoQuery query);

    List<TempletQuestionnaireInfoPo> selectList(String templetCategoryId);

    TempletQuestionnaireInfoPo selectOne(String templetId);
}

package com.cas.nc.questionnaire.common.dto.transmit;

import com.cas.nc.questionnaire.common.vo.base.BaseRequestVo;

public class QstEmailReqDto extends BaseRequestVo {

    /*发送邮件地址*/
    private String address;
    /*每页行数*/
    private Integer pageSize;
    /*导向页*/
    private Integer page;

    /**查询的邮件状态*/
    private Integer mailType;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getMailType() {
        return mailType;
    }

    public void setMailType(Integer mailType) {
        this.mailType = mailType;
    }
}

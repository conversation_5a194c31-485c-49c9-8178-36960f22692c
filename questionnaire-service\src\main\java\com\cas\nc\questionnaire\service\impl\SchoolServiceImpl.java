package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.common.enums.YnEnum;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.nosharddao.SchoolDao;
import com.cas.nc.questionnaire.dao.po.SchoolPo;
import com.cas.nc.questionnaire.dao.query.SchoolQuery;
import com.cas.nc.questionnaire.service.SchoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class SchoolServiceImpl implements SchoolService {
    private static Logger logger = LoggerFactory.getLogger(SchoolServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private SchoolDao schoolDao;

    @Override
    public List<SchoolPo> selectAll() {
        SchoolQuery query = new SchoolQuery();
        return selectList(query);
    }

    @Override
    public List<SchoolPo> selectAllEffective(String columns) {
        SchoolQuery query = new SchoolQuery();
        query.setStatus(YnEnum.Y.key());
        if (StringUtil.isNotBlank(columns)) {
            query.setTableColumns(columns);
        }
        return selectList(query);
    }

    @Override
    public int insert(SchoolPo schoolPo) {
        return schoolDao.insert(schoolPo);
    }

    @Override
    public List<SchoolPo> selectList(SchoolQuery query) {
        return schoolDao.selectList(query);
    }
}

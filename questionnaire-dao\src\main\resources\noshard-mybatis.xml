<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="false"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="aggressiveLazyLoading" value="true"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="useGeneratedKeys" value="false"/>
        <setting name="autoMappingBehavior" value="PARTIAL"/>
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <setting name="defaultStatementTimeout" value="300"/>
        <setting name="mapUnderscoreToCamelCase" value="true"/>
    </settings>

    <typeAliases>
        <typeAlias type="com.cas.nc.questionnaire.dao.po.TestPo" alias="TestPo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.po.TaskConfigPo" alias="TaskConfigPo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.po.TaskPo" alias="TaskPo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.po.UserInfoPo" alias="UserInfoPo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.po.QstReportQueryConditionPo" alias="QstReportQueryConditionPo"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.po.QstReportQueryConditionTitlePo" alias="QstReportQueryConditionTitlePo"/>


        <typeAlias type="com.cas.nc.questionnaire.dao.query.TestQuery" alias="TestQuery"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.query.TaskQuery" alias="TaskQuery"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.query.UserInfoQuery" alias="UserInfoQuery"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.query.QstReportQueryConditionQuery" alias="QstReportQueryConditionQuery"/>
        <typeAlias type="com.cas.nc.questionnaire.dao.query.QstReportQueryConditionTitleQuery" alias="QstReportQueryConditionTitleQuery"/>
    </typeAliases>

    <typeHandlers>
        <typeHandler javaType="com.cas.nc.questionnaire.dao.po.LimitAttributeJson"
                     handler="com.cas.nc.questionnaire.dao.typehandler.LimitAttributeJsonHandler"/>
    </typeHandlers>

    <mappers>
        <mapper resource="mapper/TestDao.xml"/>
        <mapper resource="mapper/AreaDao.xml"/>
        <mapper resource="mapper/OrderInfoDao.xml"/>
        <mapper resource="mapper/SchoolDao.xml"/>
        <mapper resource="mapper/TempletOptionDao.xml"/>
        <mapper resource="mapper/TempletQuestionnaireInfoDao.xml"/>
        <mapper resource="mapper/TempletTitleDao.xml"/>
        <mapper resource="mapper/UserInfoDao.xml"/>
        <mapper resource="mapper/LoginauthDao.xml"/>
        <mapper resource="mapper/UrlConfigDao.xml"/>
        <mapper resource="mapper/TaskConfigDao.xml"/>
        <mapper resource="mapper/TaskDao.xml"/>
        <mapper resource="mapper/EmailServerConfigDao.xml"/>
        <mapper resource="mapper/UrlConfigDao.xml"/>
        <mapper resource="mapper/ProductAmountConfigDao.xml"/>
        <mapper resource="mapper/TempletCategoryDao.xml"/>
        <mapper resource="mapper/IpRpcConfigDao.xml"/>
        <mapper resource="mapper/BizConfigDao.xml"/>
        <mapper resource="mapper/FileRelationDao.xml"/>
        <mapper resource="mapper/GlobalConfigDao.xml"/>
        <mapper resource="mapper/QstReportQueryConditionDao.xml"/>
        <mapper resource="mapper/QstReportQueryConditionTitleDao.xml"/>
    </mappers>
</configuration>

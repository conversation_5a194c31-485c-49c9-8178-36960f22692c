package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstOptionPo;
import com.cas.nc.questionnaire.dao.query.QstOptionQuery;

import java.util.List;


public interface QstOptionService {
    /**
     * 数据插入
     *
     * @param qstOptionPo
     * @return
     */
    int insert(QstOptionPo qstOptionPo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(QstOptionQuery query);

    /**
     * 删除
     *
     * @param questionnaireId
     * @param userId
     * @return
     */
    int delete(String questionnaireId, Long userId);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<QstOptionPo> selectList(QstOptionQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    QstOptionPo selectOne(QstOptionQuery query);

    /**
     * 复制
     *
     * @param query
     */
    void copy(QstOptionQuery query);

    /**
     * 根据问卷id查询选项
     *
     * @param questionnaireId
     * @return
     */
    List<QstOptionPo> selectList(String questionnaireId);

    /**
     * 根据问卷id查询选项
     *
     * @param questionnaireId
     * @return
     */
    List<QstOptionPo> selectList(String questionnaireId, Long userId);

    /**
     * 批量插入更新
     *
     * @param poList
     * @return
     */
    int insertUpdateList(List<QstOptionPo> poList);

    /**
     * 数据插入or更新
     *
     * @param qstOptionPo
     * @return
     */
    int insertUpdate(QstOptionPo qstOptionPo);

    int selectCount(String questionnaireId, Long userId);

    int selectCount(String questionnaireId, Long userId, Integer titleSerialNumber);
}

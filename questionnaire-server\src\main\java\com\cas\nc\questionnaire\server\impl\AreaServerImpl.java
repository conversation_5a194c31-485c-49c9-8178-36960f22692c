package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.area.AreaCityRepDto;
import com.cas.nc.questionnaire.common.dto.area.AreaCountyRepDto;
import com.cas.nc.questionnaire.common.dto.area.AreaProvinceRepDto;
import com.cas.nc.questionnaire.common.dto.area.AreaQueryProvince2CountyRepDto;
import com.cas.nc.questionnaire.common.utils.DateUtil;
import com.cas.nc.questionnaire.dao.po.AreaPo;
import com.cas.nc.questionnaire.server.AreaServer;
import com.cas.nc.questionnaire.service.AreaService;
import com.cas.nc.questionnaire.service.util.LocalCacheUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.cas.nc.questionnaire.common.enums.AreaRankEnum.THREE;
import static com.cas.nc.questionnaire.common.enums.AreaRankEnum.TWO;
import static com.cas.nc.questionnaire.common.utils.ColumnConstants.AREA_COLUMNS_2;
import static com.cas.nc.questionnaire.common.utils.ColumnConstants.AREA_COLUMNS_3;
import static com.cas.nc.questionnaire.common.utils.Constants.ONE;
import static com.cas.nc.questionnaire.common.utils.Constants.ZERO;
import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.convertAreaPoAreaCityRepDto;
import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.convertAreaPoAreaCountyRepDto;
import static com.cas.nc.questionnaire.server.util.ConvertBeanUtil.convertAreaPoAreaProvinceRepDto;

@Component
public class AreaServerImpl implements AreaServer {

    /*下次刷新的时间*/
    private static Date PROVINCE_2_COUNTY_NEXT_UPDATE_TIME = new Date();
    /*缓存刷新时间频率，单位：小时*/
    private static final int PROVINCE_2_COUNTY_UPDATE_FREQUENCY = 4;

    /*下次刷新的时间*/
    private static Date CITY_2_COUNTY_NEXT_UPDATE_TIME = new Date();
    /*缓存刷新时间频率，单位：小时*/
    private static final int CITY_2_COUNTY_UPDATE_FREQUENCY = 4;

    @Resource
    private AreaService areaService;

    @Override
    public AreaQueryProvince2CountyRepDto queryProvince2County() {
        AreaQueryProvince2CountyRepDto repDto = new AreaQueryProvince2CountyRepDto();

        if (DateUtil.compareDate(PROVINCE_2_COUNTY_NEXT_UPDATE_TIME) == ONE || LocalCacheUtil.PROVINCE_2_COUNTY_LIST.size() == ZERO) {
            PROVINCE_2_COUNTY_NEXT_UPDATE_TIME = DateUtil.add(new Date(), ZERO, PROVINCE_2_COUNTY_UPDATE_FREQUENCY, ZERO, ZERO);
            reloadProvince2County();
        }
        repDto.setProvinceList(LocalCacheUtil.PROVINCE_2_COUNTY_LIST);
        return repDto;
    }

    @Override
    public AreaQueryProvince2CountyRepDto queryProvince2City() {
        AreaQueryProvince2CountyRepDto repDto = new AreaQueryProvince2CountyRepDto();

        if (DateUtil.compareDate(CITY_2_COUNTY_NEXT_UPDATE_TIME) == ONE || LocalCacheUtil.PROVINCE_2_CITY_LIST.size() == ZERO) {
            CITY_2_COUNTY_NEXT_UPDATE_TIME = DateUtil.add(new Date(), ZERO, CITY_2_COUNTY_UPDATE_FREQUENCY, ZERO, ZERO);
            reloadProvince2City();
        }
        repDto.setProvinceList(LocalCacheUtil.PROVINCE_2_CITY_LIST);
        return repDto;
    }

    private void reloadProvince2City() {
        List<AreaProvinceRepDto> resultList = new ArrayList<>();
        List<AreaPo> provincePoList = areaService.selectAllProvince(AREA_COLUMNS_2);
        ListMultimap<Long, AreaPo> cityListMap = queryCityListMap();

        for (AreaPo province : provincePoList) {
            List<AreaPo> cityPoList = cityListMap.get(province.getId());

            if (CollectionUtils.isEmpty(cityPoList)) {
                continue;
            }
            List<AreaCityRepDto> cityList = new ArrayList<>();

            for (AreaPo city : cityPoList) {
                AreaCityRepDto cityRepDto = convertAreaPoAreaCityRepDto(city);
                cityList.add(cityRepDto);
            }
            AreaProvinceRepDto repDto = convertAreaPoAreaProvinceRepDto(province);
            repDto.setCityList(cityList);
            resultList.add(repDto);
        }
        LocalCacheUtil.PROVINCE_2_CITY_LIST.clear();
        LocalCacheUtil.PROVINCE_2_CITY_LIST.addAll(resultList);
    }

    private void reloadProvince2County() {
        List<AreaProvinceRepDto> resultList = new ArrayList<>();
        Map<String, AreaProvinceRepDto> resultMap = new HashMap<>();

        List<AreaPo> provincePoList = areaService.selectAllProvince(AREA_COLUMNS_2);
        ListMultimap<Long, AreaPo> cityListMap = queryCityListMap();
        ListMultimap<Long, AreaPo> countyListMap = queryCountyListMap();

        for (AreaPo province : provincePoList) {
            AreaProvinceRepDto repDto = convertAreaPoAreaProvinceRepDto(province);
            List<AreaPo> cityPoList = cityListMap.get(province.getId());

            if (CollectionUtils.isEmpty(cityPoList)) {
                continue;
            }
            List<AreaCityRepDto> cityList = new ArrayList<>();
            Map<String, AreaCityRepDto> cityMap = new HashMap<>();

            for (AreaPo city : cityPoList) {
                AreaCityRepDto cityRepDto = convertAreaPoAreaCityRepDto(city);
                List<AreaPo> countyPoList = countyListMap.get(city.getId());

                if (CollectionUtils.isEmpty(countyPoList)) {
                    continue;
                }
                List<AreaCountyRepDto> countyList = new ArrayList<>();

                countyPoList.forEach(county -> {
                    AreaCountyRepDto countyRepDto = convertAreaPoAreaCountyRepDto(county);
                    countyList.add(countyRepDto);
                });

                cityRepDto.setCountyList(countyList);
                cityList.add(cityRepDto);
                cityMap.put(city.getName(), cityRepDto);
            }

            repDto.setCityList(cityList);
            repDto.setCityMap(cityMap);
            resultList.add(repDto);
            resultMap.put(province.getName(), repDto);
        }
        LocalCacheUtil.PROVINCE_2_COUNTY_LIST.clear();
        LocalCacheUtil.PROVINCE_2_COUNTY_LIST.addAll(resultList);
        LocalCacheUtil.PROVINCE_2_CITY_MAP.clear();
        LocalCacheUtil.PROVINCE_2_CITY_MAP.putAll(resultMap);
    }

    private ListMultimap<Long, AreaPo> queryCountyListMap() {
        List<AreaPo> countyPoList = areaService.selectList(THREE.key(), AREA_COLUMNS_3);
        ListMultimap<Long, AreaPo> countyListMap = getLongAreaPoListMultimap(countyPoList);
        return countyListMap;
    }

    private ListMultimap<Long, AreaPo> getLongAreaPoListMultimap(List<AreaPo> countyPoList) {
        ListMultimap<Long, AreaPo> countyListMap = ArrayListMultimap.create();
        if (!CollectionUtils.isEmpty(countyPoList)) {
            countyPoList.forEach(v -> countyListMap.put(v.getParentId(), v));
        }
        return countyListMap;
    }

    private ListMultimap<Long, AreaPo> queryCityListMap() {
        List<AreaPo> cityPoList = areaService.selectList(TWO.key(), AREA_COLUMNS_3);
        ListMultimap<Long, AreaPo> cityListMap = getLongAreaPoListMultimap(cityPoList);
        return cityListMap;
    }
}

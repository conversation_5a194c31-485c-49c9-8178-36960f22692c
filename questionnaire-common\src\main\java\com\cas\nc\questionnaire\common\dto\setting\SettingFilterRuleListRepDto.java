package com.cas.nc.questionnaire.common.dto.setting;

import com.cas.nc.questionnaire.common.to.FilterTitleJsonInfoTo;

import java.util.Date;
import java.util.List;

public class SettingFilterRuleListRepDto {
    /*业务主键*/
    private String filterRuleId;

    /*名称*/
    private String name;

    /*规则类型*/
    private Integer ruleType;

    /*判断类型*/
    private Integer judgeType;

    /*根据ruleType来判断内容以及格式*/
    private String content;

    private List<FilterTitleJsonInfoTo> titleInfoList;

    /*省*/
    private Long province;

    /*市*/
    private Long city;

    /*来源渠道*/
    private Integer source;

    /*ip*/
    private String ip;

    /*答题时间，单位秒*/
    private Long answerTime;

    /*过滤问卷数*/
    private Integer filterNum;

    /*创建时间*/
    private Date createTime;

    public String getFilterRuleId() {
        return filterRuleId;
    }

    public void setFilterRuleId(String filterRuleId) {
        this.filterRuleId = filterRuleId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getJudgeType() {
        return judgeType;
    }

    public void setJudgeType(Integer judgeType) {
        this.judgeType = judgeType;
    }

    public List<FilterTitleJsonInfoTo> getTitleInfoList() {
        return titleInfoList;
    }

    public void setTitleInfoList(List<FilterTitleJsonInfoTo> titleInfoList) {
        this.titleInfoList = titleInfoList;
    }

    public Long getProvince() {
        return province;
    }

    public void setProvince(Long province) {
        this.province = province;
    }

    public Long getCity() {
        return city;
    }

    public void setCity(Long city) {
        this.city = city;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Long getAnswerTime() {
        return answerTime;
    }

    public void setAnswerTime(Long answerTime) {
        this.answerTime = answerTime;
    }

    public Integer getFilterNum() {
        return filterNum;
    }

    public void setFilterNum(Integer filterNum) {
        this.filterNum = filterNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}

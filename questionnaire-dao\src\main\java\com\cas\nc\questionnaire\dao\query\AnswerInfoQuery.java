package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.AnswerInfoPo;

import java.util.List;


public class AnswerInfoQuery extends AnswerInfoPo {
    private String tableColumns;
    private List<Long> idList;
    /**
     * 答题ID列表
     */
    private List<String> answerIdList;
    private List<Long> provinceList;
    private List<Long> cityList;
    private List<Integer> sourceList;
    private Long durationStart;
    private Long durationEnd;
    private Integer judgeCondition;
    private String beginCreateTime;
    private String endCreateTime;
    /*开始索引*/
    private int startIndex;
    /*页面大小*/
    private Integer pageSize;
    private List<Integer> statusList;

    private Boolean provinceGroupBy;
    private Boolean cityGroupBy;
    private Boolean sourceGroupBy;
    private List<String> questionnaireIdList;

    public List<Integer> getSourceList() {
        return sourceList;
    }

    public void setSourceList(List<Integer> sourceList) {
        this.sourceList = sourceList;
    }

    public Long getDurationStart() {
        return durationStart;
    }

    public void setDurationStart(Long durationStart) {
        this.durationStart = durationStart;
    }

    public Long getDurationEnd() {
        return durationEnd;
    }

    public void setDurationEnd(Long durationEnd) {
        this.durationEnd = durationEnd;
    }

    public Integer getJudgeCondition() {
        return judgeCondition;
    }

    public void setJudgeCondition(Integer judgeCondition) {
        this.judgeCondition = judgeCondition;
    }

    public List<Long> getProvinceList() {
        return provinceList;
    }

    public void setProvinceList(List<Long> provinceList) {
        this.provinceList = provinceList;
    }

    public List<Long> getCityList() {
        return cityList;
    }

    public void setCityList(List<Long> cityList) {
        this.cityList = cityList;
    }

    public List<String> getAnswerIdList() {
        return answerIdList;
    }

    public void setAnswerIdList(List<String> answerIdList) {
        this.answerIdList = answerIdList;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public String getBeginCreateTime() {
        return beginCreateTime;
    }

    public void setBeginCreateTime(String beginCreateTime) {
        this.beginCreateTime = beginCreateTime;
    }

    public String getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(String endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public Boolean getProvinceGroupBy() {
        return provinceGroupBy;
    }

    public void setProvinceGroupBy(Boolean provinceGroupBy) {
        this.provinceGroupBy = provinceGroupBy;
    }

    public Boolean getCityGroupBy() {
        return cityGroupBy;
    }

    public void setCityGroupBy(Boolean cityGroupBy) {
        this.cityGroupBy = cityGroupBy;
    }

    public Boolean getSourceGroupBy() {
        return sourceGroupBy;
    }

    public void setSourceGroupBy(Boolean sourceGroupBy) {
        this.sourceGroupBy = sourceGroupBy;
    }

    public List<String> getQuestionnaireIdList() {
        return questionnaireIdList;
    }

    public void setQuestionnaireIdList(List<String> questionnaireIdList) {
        this.questionnaireIdList = questionnaireIdList;
    }
}


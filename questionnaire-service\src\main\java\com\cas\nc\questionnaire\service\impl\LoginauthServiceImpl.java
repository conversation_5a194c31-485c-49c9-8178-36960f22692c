package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.dao.nosharddao.LoginauthDao;
import com.cas.nc.questionnaire.dao.po.LoginauthPo;
import com.cas.nc.questionnaire.dao.query.LoginauthQuery;
import com.cas.nc.questionnaire.service.LoginauthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class LoginauthServiceImpl implements LoginauthService {
    private static Logger logger = LoggerFactory.getLogger(LoginauthServiceImpl.class);

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource
    private LoginauthDao loginauthDao;

    @Override
    public int insert(LoginauthPo loginauthPo) {
        return loginauthDao.insert(loginauthPo);
    }

    @Override
    public LoginauthPo selectOne(LoginauthQuery query) {
        return loginauthDao.selectOne(query);
    }



    @Override
    public LoginauthPo selectOne(Integer id) {
        LoginauthQuery query = new LoginauthQuery();
        query.setId(id);
        return selectOne(query);
    }

    @Override
    public List<LoginauthPo> selectList(LoginauthQuery query) {
        return loginauthDao.selectList(query);
    }

    @Override
    public int update(LoginauthQuery query) {
        return loginauthDao.update(query);
    }

    @Override
    public int insertUpdate(LoginauthPo loginauthPo) {
        return loginauthDao.insertUpdate(loginauthPo);
    }


}

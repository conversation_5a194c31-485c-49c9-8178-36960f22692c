package com.cas.nc.questionnaire.rpc.wechat.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cas.nc.questionnaire.common.dto.WinXin.WeixinOAuthResult;
import com.cas.nc.questionnaire.common.dto.WinXin.WeixinUserInfo;
import com.cas.nc.questionnaire.common.enums.CodeEnum;
import com.cas.nc.questionnaire.common.exception.ServerException;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.IpUtil;
import com.cas.nc.questionnaire.common.utils.JSONUtil;
import com.cas.nc.questionnaire.rpc.wechat.WeChatRpc;
import com.cas.nc.questionnaire.rpc.wechat.entity.wechat.UnifiedOrderParam;
import com.cas.nc.questionnaire.rpc.wechat.entity.wechat.UnifiedOrderReturnResult;
import com.cas.nc.questionnaire.rpc.wechat.util.WXMyPayConfig;
import com.cas.nc.questionnaire.rpc.wechat.util.WXPay;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.UUID;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.UNKNOWN_RETURN_PAGE;

@Component
public class WeChatRpcImpl implements WeChatRpc {

    private static Logger logger = LoggerFactory.getLogger(WeChatRpcImpl.class);

    @Value("${wechat.notify_url}")
    private String notify_url;
    
    @Value("${wechat.appid}")
    private String appid;
    
    @Value("${wechat.secret}")
    private String secret;
    
    @Resource
    private WXMyPayConfig wxMyPayConfig;

    @Override
    public UnifiedOrderReturnResult unifiedOrder(UnifiedOrderParam unifiedOrderParam) {
        UnifiedOrderReturnResult result = new UnifiedOrderReturnResult(CodeEnum.SUCCESS.key(), CodeEnum.SUCCESS.value());
        SortedMap param = new TreeMap();
        try {
            constructUnifiedOrderParam(unifiedOrderParam, param);
            logger.info("WeChatRpcImpl.unifiedOrder request param[{}]", JSON.toJSON(param));

            WXPay wxPay = new WXPay(wxMyPayConfig);
            Map resultMap = wxPay.unifiedOrder(param);

            logger.info("WeChatRpcImpl.unifiedOrder result param[{}] result[{}]", JSON.toJSONString(param), JSON.toJSONString(resultMap));
            parseUnifiedOrderReturnResult(resultMap, result);
        } catch (Exception e) {
            logger.error("WeChatRpcImpl.unifiedOrder Exception param[{}]", JSON.toJSONString(param), e);
            throw new ServerException(CodeEnum.WECHAT_API_EXCEPTION);
        }
        return result;
    }

    @Override
    public Map<String, String> wechatNotify(String paramXml) {
        Map<String, String> result;
        try {
            WXPay wxPay = new WXPay(wxMyPayConfig);
            result = wxPay.processResponseXml(paramXml);
        } catch (Exception e) {
            logger.error("WeChatRpcImpl.wechatNotify Exception param[{}]", paramXml, e);
            throw new ServerException(CodeEnum.WECHAT_API_EXCEPTION);
        }
        return result;
    }

    @Override
    public Map<String, String> orderQuery(String orderNo) {
        Map<String, String> result;
        try {
            WXPay wxPay = new WXPay(wxMyPayConfig);

            Map<String, String> data = new HashMap<String, String>();
            data.put("out_trade_no", orderNo);
            logger.info("WeChatRpcImpl.orderQuery request param[{}] wxMyPayConfig[{}]", JSONUtil.toJSONString(data), JSONUtil.toJSONString(wxMyPayConfig));

            result = wxPay.orderQuery(data);

            logger.info("WeChatRpcImpl.orderQuery request param[{}] result[{}]", JSONUtil.toJSONString(data), JSONUtil.toJSONString(result));
        } catch (Exception e) {
            logger.error("WeChatRpcImpl.orderQuery Exception param[{}]", orderNo, e);
            throw new ServerException(CodeEnum.WECHAT_API_EXCEPTION);
        }
        return result;
    }
    
    @Override
    public WeixinOAuthResult getOAuthAccessToken(String code) {
        WeixinOAuthResult result = null;
        try {
            String grant_type = "authorization_code"; // 固定值
            String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + appid + "&secret=" + secret + "&code=" + code + "&grant_type=" + grant_type;
            
            logger.info("WeChatRpcImpl.getOAuthAccessToken request url[{}]", url);
            
            URL urlGet = new URL(url);
            HttpURLConnection http = (HttpURLConnection) urlGet.openConnection();
            http.setRequestMethod("GET");
            http.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            http.setDoOutput(true);
            http.setDoInput(true);
            http.connect();
            
            InputStream is = http.getInputStream();
            int size = is.available();
            byte[] jsonBytes = new byte[size];
            is.read(jsonBytes);
            String message = new String(jsonBytes, "UTF-8");
            
            logger.info("WeChatRpcImpl.getOAuthAccessToken response[{}]", message);
            
            JSONObject jsonObject = JSONObject.parseObject(message);
            
            // 检查是否有错误
            if (jsonObject.containsKey("errcode")) {
                logger.error("WeChatRpcImpl.getOAuthAccessToken Error code[{}] msg[{}]", 
                    jsonObject.getString("errcode"), 
                    jsonObject.getString("errmsg"));
                throw new ServerException(CodeEnum.WECHAT_API_EXCEPTION);
            }
            
            // 解析结果
            result = new WeixinOAuthResult();
            result.setAccess_token(jsonObject.getString("access_token"));
            result.setExpires_in(jsonObject.getInteger("expires_in"));
            result.setRefresh_token(jsonObject.getString("refresh_token"));
            result.setOpenid(jsonObject.getString("openid"));
            result.setScope(jsonObject.getString("scope"));
            
            // 可选字段
            if (jsonObject.containsKey("is_snapshotuser")) {
                result.setIs_snapshotuser(jsonObject.getInteger("is_snapshotuser"));
            }
            if (jsonObject.containsKey("unionid")) {
                result.setUnionid(jsonObject.getString("unionid"));
            }
            
            is.close();
        } catch (Exception e) {
            logger.error("WeChatRpcImpl.getOAuthAccessToken Exception code[{}]", code, e);
            throw new ServerException(CodeEnum.WECHAT_API_EXCEPTION);
        }
        return result;
    }
    
    @Override
    public WeixinUserInfo getUserInfo(String accessToken, String openid) {
        WeixinUserInfo userInfo = null;
        try {
            String lang = "zh_CN"; // 返回国家地区语言版本，zh_CN 简体，zh_TW 繁体，en 英语
            String url = "https://api.weixin.qq.com/sns/userinfo?access_token=" + accessToken + "&openid=" + openid + "&lang=" + lang;
            
            logger.info("WeChatRpcImpl.getUserInfo request url[{}]", url);
            
            URL urlGet = new URL(url);
            HttpURLConnection http = (HttpURLConnection) urlGet.openConnection();
            http.setRequestMethod("GET");
            http.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            http.setDoOutput(true);
            http.setDoInput(true);
            http.connect();
            
            InputStream is = http.getInputStream();
            int size = is.available();
            byte[] jsonBytes = new byte[size];
            is.read(jsonBytes);
            String message = new String(jsonBytes, "UTF-8");
            
            logger.info("WeChatRpcImpl.getUserInfo response[{}]", message);
            
            JSONObject jsonObject = JSONObject.parseObject(message);
            
            // 检查是否有错误
            if (jsonObject.containsKey("errcode")) {
                logger.error("WeChatRpcImpl.getUserInfo Error code[{}] msg[{}]", 
                    jsonObject.getString("errcode"), 
                    jsonObject.getString("errmsg"));
                throw new ServerException(CodeEnum.WECHAT_API_EXCEPTION);
            }
            
            // 解析结果
            userInfo = new WeixinUserInfo();
            userInfo.setOpenid(jsonObject.getString("openid"));
            userInfo.setNickname(jsonObject.getString("nickname"));
            userInfo.setSex(jsonObject.getInteger("sex"));
            userInfo.setProvince(jsonObject.getString("province"));
            userInfo.setCity(jsonObject.getString("city"));
            userInfo.setCountry(jsonObject.getString("country"));
            userInfo.setHeadimgurl(jsonObject.getString("headimgurl"));
            
            // 可选字段
            if (jsonObject.containsKey("privilege")) {
                userInfo.setPrivilege(JSON.parseArray(jsonObject.getString("privilege"), String.class));
            }
            if (jsonObject.containsKey("unionid")) {
                userInfo.setUnionid(jsonObject.getString("unionid"));
            }
            
            is.close();
        } catch (Exception e) {
            logger.error("WeChatRpcImpl.getUserInfo Exception accessToken[{}] openid[{}]", accessToken, openid, e);
            throw new ServerException(CodeEnum.WECHAT_API_EXCEPTION);
        }
        return userInfo;
    }

    private void parseUnifiedOrderReturnResult(Map map, UnifiedOrderReturnResult result) {
        Assert.notNull(map, "unifiedOrder return result");
        Assert.isTrue("SUCCESS".equals(map.get("return_code")), UNKNOWN_RETURN_PAGE);
        if ("FAIL".equals(map.get("result_code")) && "ORDERPAID".equals(map.get("err_code"))) {
            result.setOrderPaid(true);
            return;
        } else {
            Assert.isTrue("SUCCESS".equals(map.get("result_code")), UNKNOWN_RETURN_PAGE);
        }
        result.setCodeUrl(map.get("code_url").toString());
        result.setPrepayId(map.get("prepay_id").toString());
    }

    private void constructUnifiedOrderParam(UnifiedOrderParam unifiedOrderParam, SortedMap param) {
        param.put("device_info", "WEB");//设备号
        param.put("nonce_str", UUID.randomUUID());//随机字符串
        param.put("body", unifiedOrderParam.getBody());//商品描述
        param.put("out_trade_no", unifiedOrderParam.getOutTradeNo());//商户订单号
        param.put("total_fee", unifiedOrderParam.getTotalFee().toString());//标价金额
        param.put("spbill_create_ip", IpUtil.getLocalIP());//终端IP
        param.put("notify_url", notify_url);//通知地址
        param.put("trade_type", "NATIVE");//交易类型
        param.put("product_id", unifiedOrderParam.getProductId());//商品ID
    }
}

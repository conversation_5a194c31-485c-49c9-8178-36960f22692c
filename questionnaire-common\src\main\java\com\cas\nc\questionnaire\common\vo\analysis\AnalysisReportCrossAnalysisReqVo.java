package com.cas.nc.questionnaire.common.vo.analysis;

import com.cas.nc.questionnaire.common.vo.base.BaseRequestVo;

import java.util.List;

public class AnalysisReportCrossAnalysisReqVo extends BaseRequestVo {

    /*交叉分析因子集合*/
    private List<CrossAnalysisDependentVariableVo> crossAnalysisDependentVariableList;

    /*自变量题目*/
    private List<Integer> independentVariableTitleList;

    /*是否隐藏null*/
    private Boolean hideNull;

    /*是否隐藏跳过*/
    private Boolean hideSkip;

    private String sharePwd;

    public List<CrossAnalysisDependentVariableVo> getCrossAnalysisDependentVariableList() {
        return crossAnalysisDependentVariableList;
    }

    public void setCrossAnalysisDependentVariableList(List<CrossAnalysisDependentVariableVo> crossAnalysisDependentVariableList) {
        this.crossAnalysisDependentVariableList = crossAnalysisDependentVariableList;
    }

    public List<Integer> getIndependentVariableTitleList() {
        return independentVariableTitleList;
    }

    public void setIndependentVariableTitleList(List<Integer> independentVariableTitleList) {
        this.independentVariableTitleList = independentVariableTitleList;
    }

    public Boolean getHideNull() {
        return hideNull;
    }

    public void setHideNull(Boolean hideNull) {
        this.hideNull = hideNull;
    }

    public Boolean getHideSkip() {
        return hideSkip;
    }

    public void setHideSkip(Boolean hideSkip) {
        this.hideSkip = hideSkip;
    }

    public String getSharePwd() {
        return sharePwd;
    }

    public void setSharePwd(String sharePwd) {
        this.sharePwd = sharePwd;
    }
}

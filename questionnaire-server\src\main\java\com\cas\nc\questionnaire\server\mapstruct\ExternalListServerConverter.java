package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.mylist.ExternalListRepDto;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ExternalListServerConverter {
    ExternalListServerConverter INSTANCE = Mappers.getMapper(ExternalListServerConverter.class);

    List<ExternalListRepDto> to(List<QstQuestionnaireInfoPo> questionnaireInfoPoList);
}
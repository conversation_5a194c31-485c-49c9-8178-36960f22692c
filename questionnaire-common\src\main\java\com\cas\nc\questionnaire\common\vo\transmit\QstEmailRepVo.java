package com.cas.nc.questionnaire.common.vo.transmit;

import com.cas.nc.questionnaire.common.dto.transmit.QstEmailVoDto;

import java.util.List;

public class QstEmailRepVo {
    /*问卷id*/
    private String questionnaireId;

    private Integer page;

    private Integer pageSize;

    private Integer total;
    /**查询的邮件状态*/
    private Integer mailType;

    private List<QstEmailVoDto> qstEmailList;

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<QstEmailVoDto> getQstEmailList() {
        return qstEmailList;
    }

    public void setQstEmailList(List<QstEmailVoDto> qstEmailList) {
        this.qstEmailList = qstEmailList;
    }

    public Integer getMailType() {
        return mailType;
    }

    public void setMailType(Integer mailType) {
        this.mailType = mailType;
    }
}

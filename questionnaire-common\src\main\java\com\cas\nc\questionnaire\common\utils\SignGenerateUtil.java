package com.cas.nc.questionnaire.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static com.cas.nc.questionnaire.common.utils.Constants.PSK;

public class SignGenerateUtil {

    public static String generateSignByObject(Object params, String psk) {
        Map map = JSONUtil.parseObject(JSONUtil.toJSONString2(params), Map.class);
        return generateSign(map, psk);
    }

    /**
     * 生成签名
     *
     * @param params
     * @param psk
     * @return
     */
    public static String generateSign(Map<String, String> params, String psk) {
        Map<String, Object> data = new TreeMap<>();
        data.putAll(params);
        data.remove("sign");
        StringBuilder buffer = new StringBuilder();
        for (Object k : data.keySet()) {
            Object v = data.get(k);
            if (v != null) {
                buffer.append(k + "=" + v + "&");
            }
        }
        buffer.append("key=" + psk);
        return Md5Encrypt.md5(buffer.toString());
    }

    public static String getSign(BigDecimal sellingPrice, Long productId, Long userId) {
        Map<String, String> signMap = new HashMap<>();
        signMap.put("userId", userId.toString());
        signMap.put("productId", productId.toString());
        signMap.put("sellingPrice", sellingPrice.toString());
        return SignGenerateUtil.generateSign(signMap, PSK);
    }

    public static String getSign(Long userId, String orderNo) {
        Map<String, String> signMap = new HashMap<>();
        signMap.put("userId", userId.toString());
        signMap.put("orderNo", orderNo);
        return SignGenerateUtil.generateSign(signMap, PSK);
    }
}

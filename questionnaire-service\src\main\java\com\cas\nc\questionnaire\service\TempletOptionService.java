package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.TempletOptionPo;
import com.cas.nc.questionnaire.dao.query.TempletOptionQuery;

import java.util.List;

public interface TempletOptionService {
    /**
     * 数据插入
     *
     * @param templetOptionPo
     * @return
     */
    int insert(TempletOptionPo templetOptionPo);

    /**
     * 删除
     *
     * @param query
     * @return
     */
    int delete(TempletOptionQuery query);

    /**
     * 查询list
     *
     * @param query
     * @return
     */
    List<TempletOptionPo> selectList(TempletOptionQuery query);

    /**
     * 查询单条记录
     *
     * @param query
     * @return
     */
    TempletOptionPo selectOne(TempletOptionQuery query);

    List<TempletOptionPo> selectList(String templetId);
}

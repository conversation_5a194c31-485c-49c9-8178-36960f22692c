package com.cas.nc.questionnaire.common.utils;

import com.cas.nc.questionnaire.common.exception.ServerException;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


public class ConvertUtil {

    public static Map<String, Object> transBean2Map(Object obj) throws Exception {
        Map<String, Object> mapValue = new HashMap<String, Object>();
        Class<?> cls = obj.getClass();
        Field[] fields = cls.getDeclaredFields();
        for (Field field : fields) {
            String name = field.getName();
            String strGet = "get" + name.substring(0, 1).toUpperCase() + name.substring(1, name.length());
            Method methodGet = cls.getDeclaredMethod(strGet);
            Object object = methodGet.invoke(obj);
            String value = object != null ? object.toString() : "";
            mapValue.put(name, value);
        }
        return mapValue;
    }

    public static Map<String, String> transBean2MapStr(Object obj) {
        Map<String, String> mapValue = new HashMap<String, String>();
        try {
            Class<?> cls = obj.getClass();
            Field[] fields = cls.getDeclaredFields();
            for (Field field : fields) {
                String name = field.getName();
                String strGet = "get" + name.substring(0, 1).toUpperCase() + name.substring(1, name.length());
                Method methodGet = cls.getDeclaredMethod(strGet);
                Object object = methodGet.invoke(obj);
                String value = "";
                if (object instanceof Date) {
                    value = object != null ? DateUtil.formatTime((Date) object) : "";
                } else {
                    value = object != null ? object.toString() : "";
                }
                mapValue.put(name, value);
            }
        } catch (Exception e) {
            throw new ServerException(e);
        }
        return mapValue;
    }

    public static void transMap2Bean(Map<String, Object> map, Object obj) throws Exception {
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();
                if (map.containsKey(key)) {
                    Object value = map.get(key);
                    Method setter = property.getWriteMethod();
                    setter.invoke(obj, value);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }
}

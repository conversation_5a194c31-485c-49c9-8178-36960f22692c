package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.design.DesignOptionReqDto;
import com.cas.nc.questionnaire.common.vo.design.DesignOptionReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DesignConverter {
    DesignConverter INSTANCE = Mappers.getMapper(DesignConverter.class);

    DesignOptionReqDto to(DesignOptionReqVo vo);
}
package com.cas.nc.questionnaire.service.impl;

import com.cas.nc.questionnaire.dao.nosharddao.QstReportQueryConditionDao;
import com.cas.nc.questionnaire.dao.po.QstReportQueryConditionPo;
import com.cas.nc.questionnaire.service.QstReportQueryConditionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.cas.nc.questionnaire.dao.query.QstReportQueryConditionQuery;

/**
 * 测评报告查询条件Service实现类
 */
@Service
public class QstReportQueryConditionServiceImpl implements QstReportQueryConditionService {
    
    @Resource
    private QstReportQueryConditionDao qstReportQueryConditionDao;
    
    @Override
    public Long insert(QstReportQueryConditionPo po) {
        qstReportQueryConditionDao.insert(po);
        return po.getId();
    }
    
    @Override
    public int update(QstReportQueryConditionPo po) {
        // 需要将Po转换为Query类型
        QstReportQueryConditionQuery query = new QstReportQueryConditionQuery();
        query.setId(po.getId());
        query.setBeginTime(po.getBeginTime());
        query.setEndTime(po.getEndTime());
        query.setIsEnabled(po.getIsEnabled());
        query.setUpdateTime(po.getUpdateTime());
        return qstReportQueryConditionDao.update(query);
    }
    
    @Override
    public QstReportQueryConditionPo selectByQuestionnaireId(String questionnaireId) {
        return qstReportQueryConditionDao.selectByQuestionnaireId(questionnaireId);
    }
    
    @Override
    public int deleteByQuestionnaireId(String questionnaireId) {
        return qstReportQueryConditionDao.deleteByQuestionnaireId(questionnaireId);
    }
} 
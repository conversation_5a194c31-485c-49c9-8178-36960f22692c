package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class QstSmsPo {
    /*自增id*/
    private Long id;

    /*短信id*/
    private String smsId;

    private String questionnaireId;

    /*用户id*/
    private Long userId;

    /*外部id*/
    private String foreignId;

    /*来源类型，1：问卷设置，2：发送问卷*/
    private Integer sourceType;

    /*短信内容*/
    private String content;

    /*状态：1，初始化，2：发送完成，3：发送失败*/
    private Integer status;

    /* 手机号类型，1：指定手机号，2：问卷填写的手机号*/
    private Integer phoneType;

    /*题目编号*/
    private Integer serialNumber;

    /*发送时间*/
    private Date sendTime;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    /*电话号码，用分号分隔*/
    private String phones;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSmsId() {
        return smsId;
    }

    public void setSmsId(String smsId) {
        this.smsId = smsId;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getForeignId() {
        return foreignId;
    }

    public void setForeignId(String foreignId) {
        this.foreignId = foreignId;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(Integer phoneType) {
        this.phoneType = phoneType;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getPhones() {
        return phones;
    }

    public void setPhones(String phones) {
        this.phones = phones;
    }
}
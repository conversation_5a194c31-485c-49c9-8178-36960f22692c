package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireCreateRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.QuestionnaireCreateReqDto;
import com.cas.nc.questionnaire.common.dto.templet.*;
import com.cas.nc.questionnaire.common.shard.SequenceUtil;
import com.cas.nc.questionnaire.common.to.QuestionnaireTo;
import com.cas.nc.questionnaire.common.utils.Assert;
import com.cas.nc.questionnaire.common.utils.SafeUtil;
import com.cas.nc.questionnaire.common.utils.StringUtil;
import com.cas.nc.questionnaire.dao.po.*;
import com.cas.nc.questionnaire.server.QuestionnaireServer;
import com.cas.nc.questionnaire.server.TempletServer;
import com.cas.nc.questionnaire.server.mapstruct.CreateTempletConverter;
import com.cas.nc.questionnaire.server.mapstruct.GetTempletOptionConverter;
import com.cas.nc.questionnaire.server.mapstruct.GetTempletTitleConverter;
import com.cas.nc.questionnaire.service.*;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.cas.nc.questionnaire.common.enums.CodeEnum.QST_NOT_EXIST;
import static com.cas.nc.questionnaire.common.enums.CodeEnum.TEMPLET_NOT_EXIST;

@Component("templetServer")
public class TempletServerImpl implements TempletServer {

    @Resource
    private TempletCategoryService templetCategoryService;
    @Resource
    private TempletQuestionnaireInfoService templetQuestionnaireInfoService;
    @Resource
    private TempletTitleService templetTitleService;
    @Resource
    private TempletOptionService templetOptionService;
    @Resource
    private QuestionnaireServer questionnaireServer;
    @Resource
    private QstTitleService qstTitleService;
    @Resource
    private QstOptionService qstOptionService;
    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;

    @Override
    public List<ListCategoryRepDto> listCategory() {
        List<TempletCategoryPo> poList = templetCategoryService.selectAllEffective();

        List<TempletCategoryPo> resultData = SafeUtil.of(poList).stream()
                .sorted(Comparator.comparing(TempletCategoryPo::getPriority).reversed())
                .collect(Collectors.toList());

        List<ListCategoryRepDto> result = new ArrayList<>();

        resultData.forEach(v -> {
            ListCategoryRepDto templetRepDto = new ListCategoryRepDto();
            templetRepDto.setTempCategoryId(v.getTempletCategoryId());
            templetRepDto.setTitle(v.getTitle());
            templetRepDto.setPriority(v.getPriority());

            result.add(templetRepDto);
        });

        return result;
    }

    @Override
    public List<ListTempletRepDto> listTemplet(ListTempletReqDto reqDto) {
        List<TempletQuestionnaireInfoPo> templetList = templetQuestionnaireInfoService.selectList(reqDto.getTempCategoryId());

        List<ListTempletRepDto> result = new ArrayList<>();

        SafeUtil.of(templetList).forEach(v -> {
            ListTempletRepDto templetRepDto = new ListTempletRepDto();
            templetRepDto.setTempletId(v.getTempletId());
            templetRepDto.setTitle(v.getTitle());

            result.add(templetRepDto);
        });

        return result;
    }

    @Override
    public GetTempletRepDto getTemplet(GetTempletReqDto reqDto) {
        GetTempletRepDto result = new GetTempletRepDto();

        TempletQuestionnaireInfoPo templetQuestionnaireInfoPo = templetQuestionnaireInfoService.selectOne(reqDto.getTempletId());
        if (templetQuestionnaireInfoPo == null) {
            return result;
        }

        List<TempletTitlePo> titlePoList = templetTitleService.selectList(reqDto.getTempletId());
        if (CollectionUtils.isEmpty(titlePoList)) {
            return result;
        }

        List<TempletOptionPo> templetOptionPoList = templetOptionService.selectList(reqDto.getTempletId());
        if (CollectionUtils.isEmpty(templetOptionPoList)) {
            return result;
        }

        List<QstTitlePo> qstTitlePoList = GetTempletTitleConverter.INSTANCE.to(titlePoList);
        List<QstOptionPo> qstOptionPoList = GetTempletOptionConverter.INSTANCE.to(templetOptionPoList);
        ListMultimap<Integer, QstOptionPo> optionListMultiMap = ArrayListMultimap.create();
        qstOptionPoList.forEach(v -> optionListMultiMap.put(v.getTitleSerialNumber(), v));

        List<Object> objectList = questionnaireServer.constructTitles(qstTitlePoList, optionListMultiMap);

        QuestionnaireTo questionnaireTo = new QuestionnaireTo();
        questionnaireTo.setId(templetQuestionnaireInfoPo.getTempletId());
        questionnaireTo.setContent(objectList);
        questionnaireTo.setDes(templetQuestionnaireInfoPo.getDes());
        questionnaireTo.setTitle(templetQuestionnaireInfoPo.getTitle());

        result.setQuestionnairePojo(questionnaireTo);
        return result;
    }

    @Override
    public ConvertTempletRepDto convertTemplet(ConvertTempletReqDto reqDto) {
        ConvertTempletRepDto result = new ConvertTempletRepDto();

        TempletQuestionnaireInfoPo templetQuestionnaireInfoPo = templetQuestionnaireInfoService.selectOne(reqDto.getTempletId());
        Assert.notNull(templetQuestionnaireInfoPo, TEMPLET_NOT_EXIST);

        List<TempletTitlePo> titlePoList = templetTitleService.selectList(reqDto.getTempletId());
        Assert.notNull(titlePoList, TEMPLET_NOT_EXIST);

        List<TempletOptionPo> templetOptionPoList = templetOptionService.selectList(reqDto.getTempletId());
        Assert.notNull(templetOptionPoList, TEMPLET_NOT_EXIST);
        String questionnaireId = createQuestionnaireInfo(templetQuestionnaireInfoPo, reqDto);

        createTitle(titlePoList, questionnaireId, reqDto.getUserId());
        createOption(templetOptionPoList, questionnaireId, reqDto.getUserId());

        result.setQuestionnaireId(questionnaireId);
        return result;
    }

    @Override
    public void createTemplet(CreateTempletReqDto reqDto) {
        TempletCategoryPo templetCategoryPo = templetCategoryService.selectOneValid(reqDto.getTempletCategoryId());
        Assert.notNull(templetCategoryPo, TEMPLET_NOT_EXIST);

        QstQuestionnaireInfoPo qstQuestionnaireInfoPo = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId());
        Assert.notNull(qstQuestionnaireInfoPo, QST_NOT_EXIST);

        List<QstTitlePo> qstTitlePoList = qstTitleService.selectList(reqDto.getQuestionnaireId());
        Assert.notNull(qstTitlePoList, QST_NOT_EXIST);

        List<QstOptionPo> qstOptionPoList = qstOptionService.selectList(reqDto.getQuestionnaireId());
        Assert.notNull(qstOptionPoList, QST_NOT_EXIST);

        TempletQuestionnaireInfoPo templetQuestionnaireInfoPo = CreateTempletConverter.INSTANCE.to(qstQuestionnaireInfoPo);
        templetQuestionnaireInfoPo.setTempletId(SequenceUtil.getInstance().generateTemplateId(reqDto.getUserId().toString()));
        templetQuestionnaireInfoPo.setTempletCategoryId(reqDto.getTempletCategoryId());

        templetQuestionnaireInfoService.insert(templetQuestionnaireInfoPo);

        List<TempletTitlePo> templetTitlePoList = CreateTempletConverter.INSTANCE.toTitleList(qstTitlePoList);
        templetTitlePoList.forEach(v -> {
            v.setTempletId(templetQuestionnaireInfoPo.getTempletId());
            templetTitleService.insert(v);
        });

        List<TempletOptionPo> templetOptionPoList = CreateTempletConverter.INSTANCE.toOptionList(qstOptionPoList);
        templetOptionPoList.forEach(v -> {
            v.setTempletId(templetQuestionnaireInfoPo.getTempletId());
            templetOptionService.insert(v);
        });

    }

    private void createOption(List<TempletOptionPo> templetOptionPoList, String questionnaireId, Long userId) {
        List<QstOptionPo> qstOptionPoList = GetTempletOptionConverter.INSTANCE.to(templetOptionPoList);

        qstOptionPoList.forEach(v -> {
            v.setQuestionnaireId(questionnaireId);
            v.setUserId(userId);

            qstOptionService.insert(v);
        });
    }

    private void createTitle(List<TempletTitlePo> titlePoList, String questionnaireId, Long userId) {
        List<QstTitlePo> qstTitlePoList = GetTempletTitleConverter.INSTANCE.to(titlePoList);

        qstTitlePoList.forEach(v -> {
            v.setQuestionnaireId(questionnaireId);
            v.setUserId(userId);

            qstTitleService.insert(v);
        });
    }

    private String createQuestionnaireInfo(TempletQuestionnaireInfoPo templetQuestionnaireInfoPo, ConvertTempletReqDto templetReqDto) {
        QuestionnaireCreateReqDto reqDto = new QuestionnaireCreateReqDto();
        reqDto.setUserId(templetReqDto.getUserId());
        if (StringUtil.isNotBlank(templetReqDto.getTitle())) {
            reqDto.setTitle(templetReqDto.getTitle());
        } else {
            reqDto.setTitle(templetQuestionnaireInfoPo.getTitle());
        }
        reqDto.setDes(templetQuestionnaireInfoPo.getDes());
        reqDto.setBizId(templetReqDto.getBizId());
        reqDto.setBizNo(templetReqDto.getBizNo());

        QuestionnaireCreateRepDto questionnaireCreateRepDto = questionnaireServer.create(reqDto);
        return questionnaireCreateRepDto.getQuestionnaireId();
    }
}

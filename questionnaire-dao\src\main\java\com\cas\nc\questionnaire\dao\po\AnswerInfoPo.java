package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class AnswerInfoPo {
    /*自增id*/
    private Long id;

    /*答案id*/
    private String answerId;

    /*问卷id*/
    private String questionnaireId;

    /*用户id*/
    private Long userId;

    /*答题用户id*/
    private String answerUserId;

    /*微信用户唯一标识*/
    private String openid;

    /*答题开始时间*/
    private Date beginTime;

    /*答题结束时间*/
    private Date endTime;

    /*答题时长，单位秒*/
    private Long duration;

    /*状态，1：初始化，2：完成，3：自动筛选无效，4：手动筛选无效*/
    private Integer status;

    /*答题ip*/
    private String ip;

    /*long类型的ip*/
    private Long ipLong;

    /*省*/
    private Long province;

    /*市*/
    private Long city;

    /*设备信息*/
    private String deviceId;

    /*来源，1：手机浏览器，2：微信，3：pc，99：其他*/
    private Integer source;

    /*过滤规则id*/
    private Long filterRuleId;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAnswerId() {
        return answerId;
    }

    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getAnswerUserId() {
        return answerUserId;
    }

    public void setAnswerUserId(String answerUserId) {
        this.answerUserId = answerUserId;
    }
    
    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Long getIpLong() {
        return ipLong;
    }

    public void setIpLong(Long ipLong) {
        this.ipLong = ipLong;
    }

    public Long getProvince() {
        return province;
    }

    public void setProvince(Long province) {
        this.province = province;
    }

    public Long getCity() {
        return city;
    }

    public void setCity(Long city) {
        this.city = city;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getFilterRuleId() {
        return filterRuleId;
    }

    public void setFilterRuleId(Long filterRuleId) {
        this.filterRuleId = filterRuleId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
package com.cas.nc.questionnaire.server.mapstruct;

import com.cas.nc.questionnaire.common.dto.analysis.AnalysisReportReqDto;
import com.cas.nc.questionnaire.common.dto.analysis.DownloadWordReqDto;
import com.cas.nc.questionnaire.common.vo.analysis.DownloadWordReqVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DownloadWordConverter {
    DownloadWordConverter INSTANCE = Mappers.getMapper(DownloadWordConverter.class);

    DownloadWordReqDto to(DownloadWordReqVo vo);

    AnalysisReportReqDto to(DownloadWordReqDto reqDto);
}

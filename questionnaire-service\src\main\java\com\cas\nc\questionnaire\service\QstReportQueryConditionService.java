package com.cas.nc.questionnaire.service;

import com.cas.nc.questionnaire.dao.po.QstReportQueryConditionPo;

/**
 * 测评报告查询条件Service接口
 */
public interface QstReportQueryConditionService {
    
    /**
     * 插入记录
     * @param po 记录对象
     * @return 插入后的ID
     */
    Long insert(QstReportQueryConditionPo po);
    
    /**
     * 更新记录
     * @param po 记录对象
     * @return 影响行数
     */
    int update(QstReportQueryConditionPo po);
    
    /**
     * 根据问卷ID查询条件
     * @param questionnaireId 问卷ID
     * @return 查询条件
     */
    QstReportQueryConditionPo selectByQuestionnaireId(String questionnaireId);
    
    /**
     * 根据问卷ID删除条件
     * @param questionnaireId 问卷ID
     * @return 影响行数
     */
    int deleteByQuestionnaireId(String questionnaireId);
} 
package com.cas.nc.questionnaire.dao.po;

import java.util.Date;

public class ProductAmountConfigPo {
    private Long id;

    /*售价，单位分*/
    private Long sellingPrice;

    /*销售定价，单位分*/
    private Long salesPricing;

    /*时间数量*/
    private Integer timeCount;

    /*时间类型*/
    private Integer timeType;

    /*活动文案*/
    private String activityCopywriting;

    /*优先级*/
    private Integer priority;

    /*是否有效*/
    private Integer yn;

    /*更新时间*/
    private Date updateTime;

    /*创建时间*/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(Long sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public Long getSalesPricing() {
        return salesPricing;
    }

    public void setSalesPricing(Long salesPricing) {
        this.salesPricing = salesPricing;
    }

    public Integer getTimeCount() {
        return timeCount;
    }

    public void setTimeCount(Integer timeCount) {
        this.timeCount = timeCount;
    }

    public Integer getTimeType() {
        return timeType;
    }

    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    public String getActivityCopywriting() {
        return activityCopywriting;
    }

    public void setActivityCopywriting(String activityCopywriting) {
        this.activityCopywriting = activityCopywriting;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
package com.cas.nc.questionnaire.dao.query;

import com.cas.nc.questionnaire.dao.po.OrderInfoPo;

import java.util.List;


public class OrderInfoQuery extends OrderInfoPo {
    private String tableColumns;
    private Integer oldStatus;
    /*开始索引*/
    private int startIndex;
    /*页面大小*/
    private Integer pageSize;

    private List<Integer> statusList;

    public String getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(String tableColumns) {
        this.tableColumns = tableColumns;
    }

    public Integer getOldStatus() {
        return oldStatus;
    }

    public void setOldStatus(Integer oldStatus) {
        this.oldStatus = oldStatus;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }
}

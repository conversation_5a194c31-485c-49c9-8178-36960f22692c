package com.cas.nc.questionnaire.server.impl;

import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionGetReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionGetRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionSaveReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionSaveRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionUserReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryConditionUserRepDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryAnswerListReqDto;
import com.cas.nc.questionnaire.common.dto.questionnaire.ReportQueryAnswerListRepDto;
import com.cas.nc.questionnaire.dao.po.AnswerInfoPo;
import com.cas.nc.questionnaire.dao.po.QstReportQueryConditionPo;
import com.cas.nc.questionnaire.dao.po.QstReportQueryConditionTitlePo;
import com.cas.nc.questionnaire.dao.po.QstTitlePo;
import com.cas.nc.questionnaire.dao.po.QstQuestionnaireInfoPo;
import com.cas.nc.questionnaire.server.ReportQueryConditionServer;
import com.cas.nc.questionnaire.service.QstReportQueryConditionService;
import com.cas.nc.questionnaire.service.QstReportQueryConditionTitleService;
import com.cas.nc.questionnaire.service.QstTitleService;
import com.cas.nc.questionnaire.service.AnswerInfoService;
import com.cas.nc.questionnaire.service.AnswerOptionService;
import com.cas.nc.questionnaire.service.QstQuestionnaireInfoService;
import org.apache.commons.httpclient.util.DateUtil;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import com.cas.nc.questionnaire.common.enums.SourceEnum;

/**
 * 测评报告查询条件服务实现类
 */
@Component
public class ReportQueryConditionServerImpl implements ReportQueryConditionServer {

    @Resource
    private QstReportQueryConditionService qstReportQueryConditionService;

    @Resource
    private QstReportQueryConditionTitleService qstReportQueryConditionTitleService;

    @Resource
    private QstTitleService qstTitleService;

    @Resource
    private AnswerInfoService answerInfoService;

    @Resource
    private AnswerOptionService answerOptionService;

    @Resource
    private QstQuestionnaireInfoService qstQuestionnaireInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReportQueryConditionSaveRepDto saveReportQueryCondition(ReportQueryConditionSaveReqDto reqDto) {
        ReportQueryConditionSaveRepDto result = new ReportQueryConditionSaveRepDto();

        // 1. 查询是否已存在条件
        QstReportQueryConditionPo existCondition = qstReportQueryConditionService.selectByQuestionnaireId(reqDto.getQuestionnaireId());

        // 2. 构建条件对象
        QstReportQueryConditionPo condition = new QstReportQueryConditionPo();
        condition.setQuestionnaireId(reqDto.getQuestionnaireId());
        condition.setUserId(reqDto.getUserId());
        condition.setBeginTime(reqDto.getBeginTime());
        condition.setEndTime(reqDto.getEndTime());
        // 默认设置为启用
        condition.setIsEnabled(1);
        Date now = new Date();

        Long conditionId;
        // 3. 保存或更新条件
        if (existCondition == null) {
            // 新增
            condition.setCreateTime(now);
            condition.setUpdateTime(now);
            conditionId = qstReportQueryConditionService.insert(condition);
        } else {
            // 更新
            condition.setId(existCondition.getId());
            condition.setUpdateTime(now);
            qstReportQueryConditionService.update(condition);
            // 先删除旧的题目关联
            qstReportQueryConditionTitleService.deleteByConditionId(existCondition.getId());
            conditionId = existCondition.getId();
        }

        // 4. 保存题目关联
        if (reqDto.getRegularQuestions() != null && !reqDto.getRegularQuestions().isEmpty()) {
            List<QstReportQueryConditionTitlePo> titlePoList = new ArrayList<>();
            for (ReportQueryConditionSaveReqDto.RegularQuestionItemDto item : reqDto.getRegularQuestions()) {
                QstReportQueryConditionTitlePo titlePo = new QstReportQueryConditionTitlePo();
                titlePo.setConditionId(conditionId);
                titlePo.setTitleId(item.getTitleId());
                titlePo.setSerialNumber(item.getSerialNumber());
                titlePo.setValidateType(item.getValidateType());
                titlePoList.add(titlePo);
            }
            qstReportQueryConditionTitleService.insertBatch(titlePoList);
        }

        result.setSuccess(true);
        return result;
    }

    @Override
    public ReportQueryConditionGetRepDto getReportQueryCondition(ReportQueryConditionGetReqDto reqDto) {
        ReportQueryConditionGetRepDto result = new ReportQueryConditionGetRepDto();
        result.setQuestionnaireId(reqDto.getQuestionnaireId());

        // 1. 查询条件信息
        QstReportQueryConditionPo condition = qstReportQueryConditionService.selectByQuestionnaireId(reqDto.getQuestionnaireId());

        if (condition == null) {
            // 如果不存在条件，直接返回
            result.setExists(false);
            result.setRegularQuestions(new ArrayList<>());
            return result;
        }

        // 设置存在标志和基本信息
        result.setExists(true);
        result.setBeginTime(condition.getBeginTime());
        result.setEndTime(condition.getEndTime());

        // 2. 查询关联的题目信息
        List<QstReportQueryConditionTitlePo> titlePoList = qstReportQueryConditionTitleService.selectByConditionId(condition.getId());

        if (CollectionUtils.isEmpty(titlePoList)) {
            result.setRegularQuestions(new ArrayList<>());
            return result;
        }

        // 获取问卷中的所有题目
        List<QstTitlePo> qstTitleList = qstTitleService.selectList(reqDto.getQuestionnaireId());
        Map<Long, QstTitlePo> titleMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(qstTitleList)) {
            for (QstTitlePo titlePo : qstTitleList) {
                titleMap.put(titlePo.getId(), titlePo);
            }
        }

        // 3. 组装响应数据 - 只返回当前问卷中仍然存在的题目
        List<ReportQueryConditionGetRepDto.RegularQuestionItemDto> regularQuestions = new ArrayList<>();
        for (QstReportQueryConditionTitlePo titlePo : titlePoList) {
            // 检查题目是否仍然存在于问卷中
            QstTitlePo qstTitlePo = titleMap.get(titlePo.getTitleId());
            if (qstTitlePo == null) {
                // 题目不存在，跳过
                continue;
            }

            ReportQueryConditionGetRepDto.RegularQuestionItemDto itemDto = new ReportQueryConditionGetRepDto.RegularQuestionItemDto();
            itemDto.setTitleId(titlePo.getTitleId());
            itemDto.setSerialNumber(titlePo.getSerialNumber());
            itemDto.setValidateType(titlePo.getValidateType());
            itemDto.setName(qstTitlePo.getName());

            // 设置校验类型名称
            switch (titlePo.getValidateType()) {
                case 1:
                    itemDto.setValidateTypeName("姓名");
                    break;
                case 2:
                    itemDto.setValidateTypeName("邮箱");
                    break;
                case 3:
                    itemDto.setValidateTypeName("手机");
                    break;
                case 10:
                    itemDto.setValidateTypeName("身份证号");
                    break;
                default:
                    break;
            }

            regularQuestions.add(itemDto);
        }

        result.setRegularQuestions(regularQuestions);
        return result;
    }

    @Override
    public ReportQueryConditionUserRepDto getUserReportQueryCondition(ReportQueryConditionUserReqDto reqDto) {
        ReportQueryConditionUserRepDto result = new ReportQueryConditionUserRepDto();
        result.setQuestionnaireId(reqDto.getQuestionnaireId());

        // 1. 查询条件信息
        QstReportQueryConditionPo condition = qstReportQueryConditionService.selectByQuestionnaireId(reqDto.getQuestionnaireId());

        if (condition == null || condition.getIsEnabled() == 0) {
            // 如果不存在条件或条件被禁用，直接返回
            result.setExists(false);
            result.setRegularQuestions(new ArrayList<>());
            return result;
        }

        // 设置存在标志和基本信息
        result.setExists(true);
        result.setBeginTime(condition.getBeginTime());
        result.setEndTime(condition.getEndTime());

        // 2. 检查时间状态
        Date now = new Date();
        Integer timeStatus;
        String statusDesc;

        // 判断时间范围
        if (condition.getBeginTime() == null && condition.getEndTime() == null) {
            // 没有时间限制
            timeStatus = 3;
            statusDesc = "可以查询";
        } else if (condition.getBeginTime() != null && condition.getEndTime() != null) {
            // 有开始时间和结束时间
            if (now.before(condition.getBeginTime())) {
                timeStatus = 0;
                statusDesc = "查询未开始";
            } else if (now.after(condition.getEndTime())) {
                timeStatus = 2;
                statusDesc = "查询已结束";
            } else {
                timeStatus = 1;
                statusDesc = "可以查询";
            }
        } else if (condition.getBeginTime() != null) {
            // 只有开始时间
            if (now.before(condition.getBeginTime())) {
                timeStatus = 0;
                statusDesc = "查询未开始";
            } else {
                timeStatus = 1;
                statusDesc = "可以查询";
            }
        } else {
            // 只有结束时间
            if (now.after(condition.getEndTime())) {
                timeStatus = 2;
                statusDesc = "查询已结束";
            } else {
                timeStatus = 1;
                statusDesc = "可以查询";
            }
        }

        result.setTimeStatus(timeStatus);
        result.setStatusDesc(statusDesc);

        // 如果不在可查询时间范围内，不返回题目信息
        if (timeStatus != 1 && timeStatus != 3) {
            result.setRegularQuestions(new ArrayList<>());
            return result;
        }

        // 3. 查询关联的题目信息
        List<QstReportQueryConditionTitlePo> titlePoList = qstReportQueryConditionTitleService.selectByConditionId(condition.getId());

        if (CollectionUtils.isEmpty(titlePoList)) {
            result.setRegularQuestions(new ArrayList<>());
            return result;
        }

        // 获取问卷中的所有题目
        List<QstTitlePo> qstTitleList = qstTitleService.selectList(reqDto.getQuestionnaireId());
        Map<Long, QstTitlePo> titleMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(qstTitleList)) {
            for (QstTitlePo titlePo : qstTitleList) {
                titleMap.put(titlePo.getId(), titlePo);
            }
        }

        // 4. 组装响应数据 - 只返回当前问卷中仍然存在的题目
        List<ReportQueryConditionUserRepDto.RegularQuestionItemDto> regularQuestions = new ArrayList<>();
        for (QstReportQueryConditionTitlePo titlePo : titlePoList) {
            // 检查题目是否仍然存在于问卷中
            QstTitlePo qstTitlePo = titleMap.get(titlePo.getTitleId());
            if (qstTitlePo == null) {
                // 题目不存在，跳过
                continue;
            }

            ReportQueryConditionUserRepDto.RegularQuestionItemDto itemDto = new ReportQueryConditionUserRepDto.RegularQuestionItemDto();
            itemDto.setTitleId(titlePo.getTitleId());
            itemDto.setSerialNumber(titlePo.getSerialNumber());
            itemDto.setValidateType(titlePo.getValidateType());
            itemDto.setName(qstTitlePo.getName());

            // 设置校验类型名称
            switch (titlePo.getValidateType()) {
                case 1:
                    itemDto.setValidateTypeName("姓名");
                    break;
                case 2:
                    itemDto.setValidateTypeName("邮箱");
                    break;
                case 3:
                    itemDto.setValidateTypeName("手机");
                    break;
                case 10:
                    itemDto.setValidateTypeName("身份证号");
                    break;
                default:
                    break;
            }

            regularQuestions.add(itemDto);
        }

        result.setRegularQuestions(regularQuestions);
        return result;
    }

    @Override
    public ReportQueryAnswerListRepDto queryAnswerList(ReportQueryAnswerListReqDto reqDto) {
        ReportQueryAnswerListRepDto repDto = new ReportQueryAnswerListRepDto();

        // 1. 根据查询条件查找符合条件的答题ID列表
        List<String> answerIdList = findAnswerIdsByConditions(reqDto);

        if (answerIdList == null || answerIdList.isEmpty()) {
            repDto.setAnswerRecords(new ArrayList<>());
            return repDto;
        }

        // 2. 根据答题ID列表查询答题基本信息
        List<AnswerInfoPo> answerInfoList = answerInfoService.selectByAnswerIds(reqDto.getQuestionnaireId(), answerIdList);

        // 3. 获取问卷信息
        QstQuestionnaireInfoPo questionnairePo = qstQuestionnaireInfoService.selectOne(reqDto.getQuestionnaireId());
        String questionnaireName = questionnairePo != null ? questionnairePo.getTitle() : "";

        // 4. 构建返回结果
        List<ReportQueryAnswerListRepDto.AnswerRecordDto> answerRecords = new ArrayList<>();
        for (AnswerInfoPo answerInfo : answerInfoList) {
            ReportQueryAnswerListRepDto.AnswerRecordDto recordDto = new ReportQueryAnswerListRepDto.AnswerRecordDto();
            recordDto.setAnswerId(answerInfo.getAnswerId());
            recordDto.setQuestionnaireName(questionnaireName);
            recordDto.setSubmitTime(answerInfo.getCreateTime());
            recordDto.setSubmitTimeStr(DateUtil.formatDate(answerInfo.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            recordDto.setDuration(answerInfo.getDuration());
            // 使用SourceEnum转换source字段
            recordDto.setSource(SourceEnum.toEnum(answerInfo.getSource()).value());

            answerRecords.add(recordDto);
        }

        repDto.setAnswerRecords(answerRecords);
        return repDto;
    }

    /**
     * 根据查询条件查找符合条件的答题ID列表
     */
    private List<String> findAnswerIdsByConditions(ReportQueryAnswerListReqDto reqDto) {
        if (reqDto.getQueryConditions() == null || reqDto.getQueryConditions().isEmpty()) {
            // 如果没有查询条件，返回该问卷的所有答题记录
            return new ArrayList<>();
        }

        // 根据每个查询条件进行筛选
        List<String> resultAnswerIds = null;

        for (ReportQueryAnswerListReqDto.QueryConditionDto condition : reqDto.getQueryConditions()) {
            // 查询符合当前条件的答题ID
            List<String> currentAnswerIds = answerOptionService.selectAnswerIdsByCondition(
                reqDto.getQuestionnaireId(),
                condition.getTitleSerialNumber(),
                condition.getQueryValue()
            );

            if (resultAnswerIds == null) {
                // 第一个条件，直接赋值
                resultAnswerIds = new ArrayList<>(currentAnswerIds);
            } else {
                // 后续条件，取交集
                resultAnswerIds.retainAll(currentAnswerIds);
            }

            // 如果交集为空，直接返回
            if (resultAnswerIds.isEmpty()) {
                break;
            }
        }

        return resultAnswerIds != null ? resultAnswerIds : new ArrayList<>();
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
}

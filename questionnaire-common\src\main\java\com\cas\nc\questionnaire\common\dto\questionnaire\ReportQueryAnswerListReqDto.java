package com.cas.nc.questionnaire.common.dto.questionnaire;

import java.util.List;

/**
 * 测评报告查询答题列表请求DTO
 */
public class ReportQueryAnswerListReqDto {
    
    /**
     * 问卷ID
     */
    private String questionnaireId;
    
    /**
     * 查询条件列表
     */
    private List<QueryConditionDto> queryConditions;
    
    /**
     * 查询条件项
     */
    public static class QueryConditionDto {
        /**
         * 题目序号
         */
        private Integer titleSerialNumber;
        
        /**
         * 查询值
         */
        private String queryValue;
        
        public Integer getTitleSerialNumber() {
            return titleSerialNumber;
        }
        
        public void setTitleSerialNumber(Integer titleSerialNumber) {
            this.titleSerialNumber = titleSerialNumber;
        }
        
        public String getQueryValue() {
            return queryValue;
        }
        
        public void setQueryValue(String queryValue) {
            this.queryValue = queryValue;
        }
    }
    
    public String getQuestionnaireId() {
        return questionnaireId;
    }
    
    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }
    
    public List<QueryConditionDto> getQueryConditions() {
        return queryConditions;
    }
    
    public void setQueryConditions(List<QueryConditionDto> queryConditions) {
        this.queryConditions = queryConditions;
    }
}